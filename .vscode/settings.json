{"editor.insertSpaces": false, "editor.tabSize": 4, "files.associations": {"*.bndlspec": "xml", "__bit_reference": "cpp", "__config": "cpp", "__debug": "cpp", "__errc": "cpp", "__functional_base": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__mutex_base": "cpp", "__node_handle": "cpp", "__nullptr": "cpp", "__split_buffer": "cpp", "__string": "cpp", "__threading_support": "cpp", "__tree": "cpp", "__tuple": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "complex": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "exception": "cpp", "forward_list": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "memory": "cpp", "mutex": "cpp", "new": "cpp", "numeric": "cpp", "optional": "cpp", "ostream": "cpp", "queue": "cpp", "ratio": "cpp", "regex": "cpp", "set": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "utility": "cpp", "vector": "cpp", "*.tcc": "cpp", "compare": "cpp", "concepts": "cpp", "memory_resource": "cpp", "random": "cpp", "ranges": "cpp", "cfenv": "cpp", "__bits": "cpp", "variant": "cpp", "condition_variable": "cpp", "valarray": "cpp", "strstream": "cpp", "future": "cpp", "shared_mutex": "cpp", "stop_token": "cpp", "codecvt": "cpp", "numbers": "cpp", "span": "cpp", "semaphore": "cpp", "__verbose_abort": "cpp", "charconv": "cpp"}, "files.exclude": {"**/.dep": true, "**/bin": true, "**/obj": true}, "git.ignoreLimitWarning": true, "cmake.configureOnOpen": false}