<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="CppParser">
      <UniqueIdentifier>{8e447b1c-2c09-423b-9f6e-a776cbec81db}</UniqueIdentifier>
    </Filter>
    <Filter Include="CppParser\Header Files">
      <UniqueIdentifier>{39d2f587-62d6-49f5-8fd3-1d30c9b229e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="CppParser\Source Files">
      <UniqueIdentifier>{621de0cf-6ba8-40e5-b209-bdae1ed2f142}</UniqueIdentifier>
    </Filter>
    <Filter Include="Symbol Table">
      <UniqueIdentifier>{0e1a79b8-dd30-4cfb-b5cb-339454948441}</UniqueIdentifier>
    </Filter>
    <Filter Include="Symbol Table\Header Files">
      <UniqueIdentifier>{31248b34-4de9-4f86-a1ca-1fd97618b14e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Symbol Table\Source Files">
      <UniqueIdentifier>{017e9405-7dae-4319-a54f-22de1c10d19c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Attributes">
      <UniqueIdentifier>{d985e344-b941-49b2-b4af-38762aee5528}</UniqueIdentifier>
    </Filter>
    <Filter Include="Attributes\Header Files">
      <UniqueIdentifier>{32f5d7e4-e715-416a-92f5-dd62329c66b1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Attributes\Source Files">
      <UniqueIdentifier>{7590f4dd-13ef-4643-86a0-2a984d3c8605}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\CppParser\CppParser.h">
      <Filter>CppParser\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\CppToken.h">
      <Filter>CppParser\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Parser.h">
      <Filter>CppParser\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Tokenizer.h">
      <Filter>CppParser\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Utility.h">
      <Filter>CppParser\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\BuiltIn.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Decl.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Enum.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\EnumValue.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Function.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\NameSpace.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Parameter.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Struct.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Symbol.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\TypeDef.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Variable.h">
      <Filter>Symbol Table\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\Attributes.h">
      <Filter>Attributes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CppParser\AttributesParser.h">
      <Filter>Attributes\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\CppToken.cpp">
      <Filter>CppParser\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Parser.cpp">
      <Filter>CppParser\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Tokenizer.cpp">
      <Filter>CppParser\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>CppParser\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\BuiltIn.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Decl.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Enum.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EnumValue.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Function.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NameSpace.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Parameter.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Struct.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Symbol.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TypeDef.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Variable.cpp">
      <Filter>Symbol Table\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Attributes.cpp">
      <Filter>Attributes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AttributesParser.cpp">
      <Filter>Attributes\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>