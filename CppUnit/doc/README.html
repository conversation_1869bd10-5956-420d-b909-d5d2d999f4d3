<HTML>
<HEAD>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=windows-1252">
<META NAME="Generator" CONTENT="Microsoft Word 97">
<TITLE>CppUnit 1.5</TITLE>
<META NAME="Template" CONTENT="C:\Program Files\MSOffice\Office\html.dot">
</HEAD>
<BODY LINK="#0000ff" VLINK="#800080">

<P><!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN"></P>
<H1>CppUnit 1.5</H1>
<P>Last Revision: 12/15/99 - <PERSON> (<EMAIL>) - written in standard C++, tested under Microsoft Visual C++ 6.0</P>
<P><HR></P>
<H3>Background</H3>
<P>CppUnit is a simple unit test framework for C++. It is a port from JUnit, a testing framework for Java, developed by <PERSON> and <PERSON>. </P>
<H3>Contents</H3>
<PRE>README.html&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; this file
&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp; test&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; the source code
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; framework&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; the testing framework
&#9;&#9;extensions&#9;some framework extension classes
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; textui&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; a command line interface to run tests
&nbsp;&nbsp;&nbsp;&nbsp;ms&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; code for a Microsoft specific TestRunner
&nbsp;&nbsp;&nbsp;&nbsp;samples&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; some example test cases and extensions to the framework
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; multicaster&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; a sample illustrating a publish/subscribe
&#9;&#9;&#9;&#9;multicaster under test
&nbsp;&nbsp;&nbsp; doc&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; documentation</PRE>
<H3>Installation</H3>
<P>To use the test framework, create a makefile or load all files in test\framework into your IDE. In this incarnation of CppUnit, all includes assume the current directory first. A makefile or project can be used to resolve the dependencies. </P>
<P>The directory test\textui contains a simple command line example that uses the framework.</P>
<H3>Documentation</H3>
<P>CppUnit comes with the following documentation: </P>

<UL>
<LI>a cookbook: doc\cookbook.htm </LI>
<LI>this file </LI></UL>

<H3>Samples</H3>
<P>You can find several sample test cases in the samples directory: </P>

<UL>
<LI>ExampleTestCase - some simple tests </LI>
<LI>Multicaster - test cases for a sample publish/subscribe multicaster class </LI></UL>

<P>Also, the wiki page <a HREF="http://c2.com/cgi/wiki?ClassHierarchyTestingInCppUnit">http://c2.com/cgi/wiki?ClassHierarchyTestingInCppUnit</a> shows how to automatically apply tests of classes to the classes' subclasses.</P>

<H3>Extensions</H3>
<P>You can find several classes that illustrate framework extensions in the extensions directory: </P>

<UL>
<LI>TestDecorator - A Decorator for Test. You can use it as the base class for decorators that extend test cases. </LI>
<LI>TestSetup - A Decorator that can be used to set up and tear down additional fixture state. Subclass TestSetup and insert it into your tests when you want to set up additional state once before the test is run. </LI>
<LI>Orthodox - a template class which can be used to verify operations on an arbitrary class.</LI></UL>


<H3>Notes</H3>
<P>Porting this framework has been fun. I've tried to maintain the spirit and utility of JUnit in a C++ environment. Naturally, the move from Java to standard C++ forces out several nice JUnit features:</P>
<OL>

<LI>Platform independent GUI.</LI>
<LI>Stack traces of test failures</LI>
<LI>Active (threaded) tests</LI>
<LI>Direct invocation of test cases via reflection</LI>
<LI>Run-time loading of new tests</LI></OL>

<P>In addition, the lack of garbage collection in C++ requires some careful use of the framework classes. In particular, TestSuites are composites that manage the lifetime of any tests added to them. Holding onto a TestResult past the lifetime of the tests which filled it is a bad idea. This is because TestResults hold TestFailures and TestFailures hold pointers to the Tests that generated them.</P>
<P>On the plus side, we can use the C++ macro preprocessor to get the exact line at which a failure occurs, along with the actual text inside the assert () call that detected the failure. The features of C++ that enable this are the __LINE__ and __FILE__ preprocessor definitions, along with the <I>stringizing</I> operator. If you find that generating this much literal text bulks up your test executables, you can use the CPP_UNIT_SOURCEANNOT define to disable that portion of the reporting.</P>
<P>Note: If you use the C++ macro "assert ()" in your code, or include assert.h, you may have a name clash with CppUnit's assert macro. This can be remedied by changing the name of the macro to "cu_assert ()" in TestCase.h.</P>
<P>I'd like to thank Kent Beck and Erich Gamma for the inspiration, design, and a wonderful cookbook that was easily/shamelessly mutated to describe CppUnit. Double thanks to Erich for thinking up a way to implement TestCaller. Additional thanks to Kent, Ward Cunningham, Ron Jeffries, Martin Fowler, and several other netizens of the WikiWikiWeb. I don't think any other bunch of people could have convinced me that rapid development with unit tests can be both effective and easy.</P>
<P>Thanks also to Fred Huls for mentioning the idea of template-based testing. The <EM>orthodox</EM> template class demonstrates only a small part of what can be done with templated test cases.</P>
<H3>History Of Changes</H3>
<P>1.2 -- Added the TestCaller template class. There is now no need to use the CPP_UNIT_TESTCASEDISPATCH macro unless you are using a C++ compiler which does not support templates well. CPP_UNIT_TESTCASEDISPATCH remains in TestCase.h for backward compatibility. I've also kept the use of the macro in the Multicaster sample to leave in an example.</P>
<P>1.3 -- Retired the CPP_UNIT_TESTCASEDISPATCH macro and cleaned up the include structure.  Fixed bug in the textui version.</P>
<P>1.4 -- Removed using directives for std in CppUnit headers.  Merged the old AssertionFailedError into CppUnitException.  Fixed a memory leak in the TestRunner class of the MS GUI TestRunner.  Removed CppUnit.h file.  Now headers for each class must be included directly.</P></BODY></HTML>
<P>1.5 -- Upgraded projects from VC++ 5.0 to 6.0.</P></BODY></HTML>
