<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="Crypto"
	ProjectGUID="{EEEE7259-32E9-4D56-B023-C733940AB2A0}"
	RootNamespace="Crypto"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="xcopy /y ..\openssl\build\win32\bin\debug\*.dll ..\bin\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\bin\debug\*.lib ..\lib\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\bin\debug\*.pdb ..\lib\* 1&gt;nul&#x0D;&#x0A;exit 0&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\openssl\build\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;Crypto_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="..\bin\PocoCryptod.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\bin\PocoCryptod.pdb"
				SubSystem="1"
				ImportLibrary="..\lib\PocoCryptod.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="xcopy /y ..\openssl\build\win32\bin\release\*.dll ..\bin\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\bin\release\*.lib ..\lib\* 1&gt;nul&#x0D;&#x0A;exit 0&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\openssl\build\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;Crypto_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="..\bin\PocoCrypto.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\lib\PocoCrypto.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="xcopy /y ..\openssl\build\win32\lib\debug\*.lib ..\lib\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\lib\debug\*.pdb ..\lib\* 1&gt;nul&#x0D;&#x0A;exit 0&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\openssl\build\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoCryptoMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoCryptoMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="xcopy /y ..\openssl\build\win32\lib\release\*.lib ..\lib\* 1&gt;nul&#x0D;&#x0A;exit 0&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\openssl\build\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoCryptoMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="xcopy /y ..\openssl\build\win32\bin\debug\*.dll ..\bin\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\bin\debug\*.lib ..\lib\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\bin\debug\*.pdb ..\lib\* 1&gt;nul&#x0D;&#x0A;exit 0&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\openssl\build\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoCryptoMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoCryptoMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="xcopy /y ..\openssl\build\win32\bin\release\*.dll ..\bin\* 1&gt;nul&#x0D;&#x0A;xcopy /y ..\openssl\build\win32\bin\release\*.lib ..\lib\* 1&gt;nul&#x0D;&#x0A;exit 0&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\openssl\build\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoCryptoMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Cipher"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Crypto\Cipher.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CipherFactory.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CipherImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CipherKey.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CipherKeyImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CryptoStream.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CryptoTransform.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\Cipher.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CipherFactory.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CipherImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CipherKey.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CipherKeyImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CryptoStream.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CryptoTransform.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="RSA"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Crypto\RSACipherImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\RSADigestEngine.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\RSAKey.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\RSAKeyImpl.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\RSACipherImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RSADigestEngine.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RSAKey.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RSAKeyImpl.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Certificate"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Crypto\PKCS12Container.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\X509Certificate.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\PKCS12Container.cpp"
					>
				</File>
				<File
					RelativePath=".\src\X509Certificate.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="CryptoCore"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Crypto\Crypto.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\CryptoException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\EVPCipherImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\EVPPKey.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\KeyPair.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\KeyPairImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\OpenSSLInitializer.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\CryptoException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EVPCipherImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EVPPKey.cpp"
					>
				</File>
				<File
					RelativePath=".\src\KeyPair.cpp"
					>
				</File>
				<File
					RelativePath=".\src\KeyPairImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\OpenSSLInitializer.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Digest"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Crypto\DigestEngine.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\DigestEngine.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="EC"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Crypto\ECDSADigestEngine.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\ECKey.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\ECKeyImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Crypto\Envelope.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\ECDSADigestEngine.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ECKey.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ECKeyImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Envelope.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_shared|Win32"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_shared|Win32"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
