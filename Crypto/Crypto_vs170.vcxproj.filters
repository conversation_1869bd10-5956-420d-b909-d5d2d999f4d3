<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Cipher">
      <UniqueIdentifier>{c0d5dbcc-aa3a-4a9f-ace3-312f44552df7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cipher\Header Files">
      <UniqueIdentifier>{fa977baa-99e0-4736-a6c0-0a7d23a6410d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cipher\Source Files">
      <UniqueIdentifier>{64bffeac-1b12-444c-8a98-2c3ab7afca86}</UniqueIdentifier>
    </Filter>
    <Filter Include="RSA">
      <UniqueIdentifier>{2217be8b-7217-450e-850a-4e3cd41a6d17}</UniqueIdentifier>
    </Filter>
    <Filter Include="RSA\Header Files">
      <UniqueIdentifier>{b0099283-5145-4cd9-9dd6-8c894dd1e0bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="RSA\Source Files">
      <UniqueIdentifier>{4b89f81f-5842-41f7-8550-6b2444e42c21}</UniqueIdentifier>
    </Filter>
    <Filter Include="Certificate">
      <UniqueIdentifier>{487598dd-1537-4592-902f-ce62c702de55}</UniqueIdentifier>
    </Filter>
    <Filter Include="Certificate\Header Files">
      <UniqueIdentifier>{30f74beb-7f7c-4d4a-9997-9419df1ad0cc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Certificate\Source Files">
      <UniqueIdentifier>{fe0681a4-a27c-4ec5-8a91-95e16da5e2fb}</UniqueIdentifier>
    </Filter>
    <Filter Include="CryptoCore">
      <UniqueIdentifier>{9dfe8ba5-b357-4387-ae11-1a9dcd93dde6}</UniqueIdentifier>
    </Filter>
    <Filter Include="CryptoCore\Header Files">
      <UniqueIdentifier>{50855057-f60e-4ea6-8cfe-599803e9f87f}</UniqueIdentifier>
    </Filter>
    <Filter Include="CryptoCore\Source Files">
      <UniqueIdentifier>{f1f40084-8b19-40b0-ab0e-5dac38d1f84d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Digest">
      <UniqueIdentifier>{9ee15162-ecd6-4b3f-9440-5b0fef8c7ee2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Digest\Header Files">
      <UniqueIdentifier>{a3d3ac1f-6ab5-40fd-94d5-87223c2616db}</UniqueIdentifier>
    </Filter>
    <Filter Include="Digest\Source Files">
      <UniqueIdentifier>{161574f0-2211-4d00-9470-e3dd2ec14a0f}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC">
      <UniqueIdentifier>{1cbb0712-1cc0-4019-a00a-0029703cb638}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC\Header Files">
      <UniqueIdentifier>{331329bb-c3ad-44e0-8e5b-fde7ac41ce8e}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC\Source Files">
      <UniqueIdentifier>{9bb09b1d-edb6-4f2c-9aad-dcd811b1d7dc}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Crypto\Cipher.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherFactory.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherImpl.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherKey.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherKeyImpl.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CryptoStream.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CryptoTransform.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSACipherImpl.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSADigestEngine.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSAKey.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSAKeyImpl.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\PKCS12Container.h">
      <Filter>Certificate\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\X509Certificate.h">
      <Filter>Certificate\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\Crypto.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CryptoException.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\EVPCipherImpl.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\EVPPKey.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\KeyPair.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\KeyPairImpl.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\OpenSSLInitializer.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\DigestEngine.h">
      <Filter>Digest\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\ECDSADigestEngine.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\ECKey.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\ECKeyImpl.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\Envelope.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Cipher.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherFactory.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherImpl.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherKey.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherKeyImpl.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptoStream.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptoTransform.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSACipherImpl.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSADigestEngine.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSAKey.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSAKeyImpl.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PKCS12Container.cpp">
      <Filter>Certificate\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\X509Certificate.cpp">
      <Filter>Certificate\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptoException.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EVPCipherImpl.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EVPPKey.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyPair.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyPairImpl.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OpenSSLInitializer.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DigestEngine.cpp">
      <Filter>Digest\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ECDSADigestEngine.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ECKey.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ECKeyImpl.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Envelope.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>