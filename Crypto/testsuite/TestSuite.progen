vc.project.guid = C1B1BB96-5198-48EB-AB48-9A0A0B54FB15
vc.project.name = TestSuite
vc.project.target = TestSuite
vc.project.type = testsuite
vc.project.pocobase = ..\\..
vc.project.platforms = Win32
vc.project.vcpkg = true
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = TestSuite_vs90.vcproj
vc.project.compiler.include = ${vc.project.pocobase}\\Foundation\\include
vc.project.compiler.defines = _CRT_SECURE_NO_WARNINGS
vc.project.compiler.defines.release_shared = OPENSSL_REQUIRE_APPLINK
vc.project.compiler.defines.debug_shared = OPENSSL_REQUIRE_APPLINK
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.project.linker.dependencies = ws2_32.lib iphlpapi.lib
vc.project.linker.dependencies.debug_shared =
vc.project.linker.dependencies.release_shared =
vc.project.linker.dependencies.debug_static_md = Crypt32.lib
vc.project.linker.dependencies.release_static_md = Crypt32.lib
vc.project.linker.dependencies.debug_static_mt = Crypt32.lib
vc.project.linker.dependencies.release_static_mt = Crypt32.lib
