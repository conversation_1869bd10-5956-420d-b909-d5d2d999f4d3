//
// DNSSDResponderImpl.cpp
//
// $Id: //poco/1.7/DNSSD/src/DNSSDResponderImpl.cpp#1 $
//
// Library: DNSSD
// Package: Core
// Module:  DNSSDResponderImpl
//
// Copyright (c) 2006-2024, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#include "Poco/DNSSD/DNSSDResponderImpl.h"


namespace Poco {
namespace DNSSD {


DNSSDResponderImpl::DNSSDResponderImpl()
{
}


DNSSDResponderImpl::~DNSSDResponderImpl()
{
}


DNSSDResponderImplFactory::~DNSSDResponderImplFactory()
{
}


} } // namespace Poco::DNSSD
