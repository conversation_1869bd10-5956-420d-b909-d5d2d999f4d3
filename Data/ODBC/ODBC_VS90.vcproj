<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="ODBC"
	ProjectGUID="{1B29820D-375F-11DB-837B-00123FC423B5}"
	RootNamespace="ODBC"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;THREADSAFE;ODBC_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				OutputFile="..\..\bin\PocoDataODBCd.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\..\bin\PocoDataODBCd.pdb"
				SubSystem="1"
				ImportLibrary="..\..\lib\PocoDataODBCd.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;THREADSAFE;ODBC_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				OutputFile="..\..\bin\PocoDataODBC.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\..\lib\PocoDataODBC.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;THREADSAFE"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\..\lib\PocoDataODBCMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataODBCMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;THREADSAFE"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataODBCMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;THREADSAFE"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\..\lib\PocoDataODBCMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataODBCMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;THREADSAFE"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataODBCMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="ODBC"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Binder.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\ConnectionHandle.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Connector.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Diagnostics.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\EnvironmentHandle.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Error.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Extractor.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Handle.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\ODBC.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\ODBCException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\ODBCMetaColumn.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\ODBCStatementImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Parameter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Preparator.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\SessionImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\TypeInfo.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Unicode.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Unicode_UNIXODBC.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Unicode_WIN32.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\ODBC\Utility.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\Binder.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ConnectionHandle.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Connector.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EnvironmentHandle.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Extractor.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ODBCException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ODBCMetaColumn.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ODBCStatementImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Parameter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Preparator.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SessionImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\TypeInfo.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Unicode.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Diagnostics.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Error.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Unicode_UNIXODBC.cpp"
					>
					<FileConfiguration
						Name="debug_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath=".\src\Unicode_WIN32.cpp"
					>
					<FileConfiguration
						Name="debug_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath=".\src\Utility.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="..\..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
