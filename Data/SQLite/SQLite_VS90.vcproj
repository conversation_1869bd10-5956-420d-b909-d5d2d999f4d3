<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="SQLite"
	ProjectGUID="{5B889CE7-AD42-4CFE-BBC3-532B61F8329E}"
	RootNamespace="SQLite"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS3;SQLITE_ENABLE_FTS3_PARENTHESIS;SQLITE_OMIT_UTF16;SQLITE_OMIT_PROGRESS_CALLBACK;SQLITE_OMIT_COMPLETE;SQLITE_OMIT_TCL_VARIABLE;SQLITE_OMIT_DEPRECATED;SQLite_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings="4996;4244;4018"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\..\bin\PocoDataSQLited.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\..\bin\PocoDataSQLited.pdb"
				SubSystem="1"
				ImportLibrary="..\..\lib\PocoDataSQLited.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS3;SQLITE_ENABLE_FTS3_PARENTHESIS;SQLITE_OMIT_UTF16;SQLITE_OMIT_PROGRESS_CALLBACK;SQLITE_OMIT_COMPLETE;SQLITE_OMIT_TCL_VARIABLE;SQLITE_OMIT_DEPRECATED;SQLite_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings="4996;4244;4018"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\..\bin\PocoDataSQLite.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\..\lib\PocoDataSQLite.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS3;SQLITE_ENABLE_FTS3_PARENTHESIS;SQLITE_OMIT_UTF16;SQLITE_OMIT_PROGRESS_CALLBACK;SQLITE_OMIT_COMPLETE;SQLITE_OMIT_TCL_VARIABLE;SQLITE_OMIT_DEPRECATED"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\..\lib\PocoDataSQLiteMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings="4996;4244;4018"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataSQLiteMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS3;SQLITE_ENABLE_FTS3_PARENTHESIS;SQLITE_OMIT_UTF16;SQLITE_OMIT_PROGRESS_CALLBACK;SQLITE_OMIT_COMPLETE;SQLITE_OMIT_TCL_VARIABLE;SQLITE_OMIT_DEPRECATED"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings="4996;4244;4018"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataSQLiteMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS3;SQLITE_ENABLE_FTS3_PARENTHESIS;SQLITE_OMIT_UTF16;SQLITE_OMIT_PROGRESS_CALLBACK;SQLITE_OMIT_COMPLETE;SQLITE_OMIT_TCL_VARIABLE;SQLITE_OMIT_DEPRECATED"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\..\lib\PocoDataSQLiteMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings="4996;4244;4018"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataSQLiteMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\Foundation\include;..\..\Data\include;..\..\Data\SQLParser;..\..\Data\SQLParser\src"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS3;SQLITE_ENABLE_FTS3_PARENTHESIS;SQLITE_OMIT_UTF16;SQLITE_OMIT_PROGRESS_CALLBACK;SQLITE_OMIT_COMPLETE;SQLITE_OMIT_TCL_VARIABLE;SQLITE_OMIT_DEPRECATED"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings="4996;4244;4018"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\..\lib\PocoDataSQLiteMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="SQLite"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Data\SQLite\Binder.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\Connector.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\Extractor.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\Notifier.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\SessionImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\SQLite.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\SQLiteException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\SQLiteStatementImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLite\Utility.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\Binder.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Connector.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Extractor.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Notifier.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SessionImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SQLiteException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SQLiteStatementImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Utility.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="3rdparty"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\sqlite3.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\sqlite3.c"
					>
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="..\..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
