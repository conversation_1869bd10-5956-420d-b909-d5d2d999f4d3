//
// AutoTransaction.h
//
// Library: Data
// Package: DataCore
// Module:  AutoTransaction
//
// Forward header for the Transaction class.
//
// Copyright (c) 2006, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//



#ifndef Data_AutoTransaction_INCLUDED
#define Data_AutoTransaction_INCLUDED


#include "Poco/Data/Transaction.h"


namespace Poco {
namespace Data {


using AutoTransaction = Transaction;


} } // namespace Poco::Data


#endif // Data_AutoTransaction_INCLUDED
