vc.project.guid = ${vc.project.guidFromName}
vc.project.name = ${vc.project.baseName}
vc.project.target = ${vc.project.name}
vc.project.type = executable
vc.project.pocobase = ..\\..\\..
vc.project.platforms = Win32
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = ${vc.project.name}_vs90.vcproj
vc.project.compiler.include = ..\\..\\..\\Foundation\\include;..\\..\\..\\JSON\\include;..\\..\\..\\XML\\include;..\\..\\..\\Util\\include;..\\..\\..\\Data\\include;..\\..\\..\\Data\\SQLParser;..\\Data\\SQLParser\\src;..\\..\\..\\Data\\SQLite\\include
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.project.linker.dependencies = iphlpapi.lib
