//
// ISO8859_6Encoding.cpp
//
// Library: Encodings
// Package: Encodings
// Module:  ISO8859_6Encoding
//
// Copyright (c) 2018, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier: BSL-1.0
//


#include "Poco/ISO8859_6Encoding.h"


namespace Poco {


const char* ISO8859_6Encoding::_names[] =
{
	"ISO-8859-6",
	NULL
};


const TextEncoding::CharacterMap ISO8859_6Encoding::_charMap =
{
	0x0000, 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0008, 0x0009, 0x000A, 0x000B, 0x000C, 0x000D, 0x000E, 0x000F,
	0x0010, 0x0011, 0x0012, 0x0013, 0x0014, 0x0015, 0x0016, 0x0017, 0x0018, 0x0019, 0x001A, 0x001B, 0x001C, 0x001D, 0x001E, 0x001F,
	0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002A, 0x002B, 0x002C, 0x002D, 0x002E, 0x002F,
	0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003A, 0x003B, 0x003C, 0x003D, 0x003E, 0x003F,
	0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004A, 0x004B, 0x004C, 0x004D, 0x004E, 0x004F,
	0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005A, 0x005B, 0x005C, 0x005D, 0x005E, 0x005F,
	0x0060, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006A, 0x006B, 0x006C, 0x006D, 0x006E, 0x006F,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007A, 0x007B, 0x007C, 0x007D, 0x007E, 0x007F,
	0x0080, 0x0081, 0x0082, 0x0083, 0x0084, 0x0085, 0x0086, 0x0087, 0x0088, 0x0089, 0x008A, 0x008B, 0x008C, 0x008D, 0x008E, 0x008F,
	0x0090, 0x0091, 0x0092, 0x0093, 0x0094, 0x0095, 0x0096, 0x0097, 0x0098, 0x0099, 0x009A, 0x009B, 0x009C, 0x009D, 0x009E, 0x009F,
	0x00A0,     -1,     -1,     -1, 0x00A4,     -1,     -1,     -1,     -1,     -1,     -1,     -1, 0x060C, 0x00AD,     -1,     -1,
	    -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1, 0x061B,     -1,     -1,     -1, 0x061F,
	    -1, 0x0621, 0x0622, 0x0623, 0x0624, 0x0625, 0x0626, 0x0627, 0x0628, 0x0629, 0x062A, 0x062B, 0x062C, 0x062D, 0x062E, 0x062F,
	0x0630, 0x0631, 0x0632, 0x0633, 0x0634, 0x0635, 0x0636, 0x0637, 0x0638, 0x0639, 0x063A,     -1,     -1,     -1,     -1,     -1,
	0x0640, 0x0641, 0x0642, 0x0643, 0x0644, 0x0645, 0x0646, 0x0647, 0x0648, 0x0649, 0x064A, 0x064B, 0x064C, 0x064D, 0x064E, 0x064F,
	0x0650, 0x0651, 0x0652,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,
};


const DoubleByteEncoding::Mapping ISO8859_6Encoding::_mappingTable[] = {
	{ 0x0000, 0x0000 } // dummy entry
};


const DoubleByteEncoding::Mapping ISO8859_6Encoding::_reverseMappingTable[] = {
	{ 0x0000, 0x0000 }, { 0x0001, 0x0001 }, { 0x0002, 0x0002 }, { 0x0003, 0x0003 }, { 0x0004, 0x0004 }, { 0x0005, 0x0005 }, { 0x0006, 0x0006 }, { 0x0007, 0x0007 },
	{ 0x0008, 0x0008 }, { 0x0009, 0x0009 }, { 0x000A, 0x000A }, { 0x000B, 0x000B }, { 0x000C, 0x000C }, { 0x000D, 0x000D }, { 0x000E, 0x000E }, { 0x000F, 0x000F },
	{ 0x0010, 0x0010 }, { 0x0011, 0x0011 }, { 0x0012, 0x0012 }, { 0x0013, 0x0013 }, { 0x0014, 0x0014 }, { 0x0015, 0x0015 }, { 0x0016, 0x0016 }, { 0x0017, 0x0017 },
	{ 0x0018, 0x0018 }, { 0x0019, 0x0019 }, { 0x001A, 0x001A }, { 0x001B, 0x001B }, { 0x001C, 0x001C }, { 0x001D, 0x001D }, { 0x001E, 0x001E }, { 0x001F, 0x001F },
	{ 0x0020, 0x0020 }, { 0x0021, 0x0021 }, { 0x0022, 0x0022 }, { 0x0023, 0x0023 }, { 0x0024, 0x0024 }, { 0x0025, 0x0025 }, { 0x0026, 0x0026 }, { 0x0027, 0x0027 },
	{ 0x0028, 0x0028 }, { 0x0029, 0x0029 }, { 0x002A, 0x002A }, { 0x002B, 0x002B }, { 0x002C, 0x002C }, { 0x002D, 0x002D }, { 0x002E, 0x002E }, { 0x002F, 0x002F },
	{ 0x0030, 0x0030 }, { 0x0031, 0x0031 }, { 0x0032, 0x0032 }, { 0x0033, 0x0033 }, { 0x0034, 0x0034 }, { 0x0035, 0x0035 }, { 0x0036, 0x0036 }, { 0x0037, 0x0037 },
	{ 0x0038, 0x0038 }, { 0x0039, 0x0039 }, { 0x003A, 0x003A }, { 0x003B, 0x003B }, { 0x003C, 0x003C }, { 0x003D, 0x003D }, { 0x003E, 0x003E }, { 0x003F, 0x003F },
	{ 0x0040, 0x0040 }, { 0x0041, 0x0041 }, { 0x0042, 0x0042 }, { 0x0043, 0x0043 }, { 0x0044, 0x0044 }, { 0x0045, 0x0045 }, { 0x0046, 0x0046 }, { 0x0047, 0x0047 },
	{ 0x0048, 0x0048 }, { 0x0049, 0x0049 }, { 0x004A, 0x004A }, { 0x004B, 0x004B }, { 0x004C, 0x004C }, { 0x004D, 0x004D }, { 0x004E, 0x004E }, { 0x004F, 0x004F },
	{ 0x0050, 0x0050 }, { 0x0051, 0x0051 }, { 0x0052, 0x0052 }, { 0x0053, 0x0053 }, { 0x0054, 0x0054 }, { 0x0055, 0x0055 }, { 0x0056, 0x0056 }, { 0x0057, 0x0057 },
	{ 0x0058, 0x0058 }, { 0x0059, 0x0059 }, { 0x005A, 0x005A }, { 0x005B, 0x005B }, { 0x005C, 0x005C }, { 0x005D, 0x005D }, { 0x005E, 0x005E }, { 0x005F, 0x005F },
	{ 0x0060, 0x0060 }, { 0x0061, 0x0061 }, { 0x0062, 0x0062 }, { 0x0063, 0x0063 }, { 0x0064, 0x0064 }, { 0x0065, 0x0065 }, { 0x0066, 0x0066 }, { 0x0067, 0x0067 },
	{ 0x0068, 0x0068 }, { 0x0069, 0x0069 }, { 0x006A, 0x006A }, { 0x006B, 0x006B }, { 0x006C, 0x006C }, { 0x006D, 0x006D }, { 0x006E, 0x006E }, { 0x006F, 0x006F },
	{ 0x0070, 0x0070 }, { 0x0071, 0x0071 }, { 0x0072, 0x0072 }, { 0x0073, 0x0073 }, { 0x0074, 0x0074 }, { 0x0075, 0x0075 }, { 0x0076, 0x0076 }, { 0x0077, 0x0077 },
	{ 0x0078, 0x0078 }, { 0x0079, 0x0079 }, { 0x007A, 0x007A }, { 0x007B, 0x007B }, { 0x007C, 0x007C }, { 0x007D, 0x007D }, { 0x007E, 0x007E }, { 0x007F, 0x007F },
	{ 0x0080, 0x0080 }, { 0x0081, 0x0081 }, { 0x0082, 0x0082 }, { 0x0083, 0x0083 }, { 0x0084, 0x0084 }, { 0x0085, 0x0085 }, { 0x0086, 0x0086 }, { 0x0087, 0x0087 },
	{ 0x0088, 0x0088 }, { 0x0089, 0x0089 }, { 0x008A, 0x008A }, { 0x008B, 0x008B }, { 0x008C, 0x008C }, { 0x008D, 0x008D }, { 0x008E, 0x008E }, { 0x008F, 0x008F },
	{ 0x0090, 0x0090 }, { 0x0091, 0x0091 }, { 0x0092, 0x0092 }, { 0x0093, 0x0093 }, { 0x0094, 0x0094 }, { 0x0095, 0x0095 }, { 0x0096, 0x0096 }, { 0x0097, 0x0097 },
	{ 0x0098, 0x0098 }, { 0x0099, 0x0099 }, { 0x009A, 0x009A }, { 0x009B, 0x009B }, { 0x009C, 0x009C }, { 0x009D, 0x009D }, { 0x009E, 0x009E }, { 0x009F, 0x009F },
	{ 0x00A0, 0x00A0 }, { 0x00A4, 0x00A4 }, { 0x00AD, 0x00AD }, { 0x060C, 0x00AC }, { 0x061B, 0x00BB }, { 0x061F, 0x00BF }, { 0x0621, 0x00C1 }, { 0x0622, 0x00C2 },
	{ 0x0623, 0x00C3 }, { 0x0624, 0x00C4 }, { 0x0625, 0x00C5 }, { 0x0626, 0x00C6 }, { 0x0627, 0x00C7 }, { 0x0628, 0x00C8 }, { 0x0629, 0x00C9 }, { 0x062A, 0x00CA },
	{ 0x062B, 0x00CB }, { 0x062C, 0x00CC }, { 0x062D, 0x00CD }, { 0x062E, 0x00CE }, { 0x062F, 0x00CF }, { 0x0630, 0x00D0 }, { 0x0631, 0x00D1 }, { 0x0632, 0x00D2 },
	{ 0x0633, 0x00D3 }, { 0x0634, 0x00D4 }, { 0x0635, 0x00D5 }, { 0x0636, 0x00D6 }, { 0x0637, 0x00D7 }, { 0x0638, 0x00D8 }, { 0x0639, 0x00D9 }, { 0x063A, 0x00DA },
	{ 0x0640, 0x00E0 }, { 0x0641, 0x00E1 }, { 0x0642, 0x00E2 }, { 0x0643, 0x00E3 }, { 0x0644, 0x00E4 }, { 0x0645, 0x00E5 }, { 0x0646, 0x00E6 }, { 0x0647, 0x00E7 },
	{ 0x0648, 0x00E8 }, { 0x0649, 0x00E9 }, { 0x064A, 0x00EA }, { 0x064B, 0x00EB }, { 0x064C, 0x00EC }, { 0x064D, 0x00ED }, { 0x064E, 0x00EE }, { 0x064F, 0x00EF },
	{ 0x0650, 0x00F0 }, { 0x0651, 0x00F1 }, { 0x0652, 0x00F2 },
};


ISO8859_6Encoding::ISO8859_6Encoding():
	DoubleByteEncoding(_names, _charMap, _mappingTable, sizeof(_mappingTable)/sizeof(Mapping), _reverseMappingTable, sizeof(_reverseMappingTable)/sizeof(Mapping))
{
}


ISO8859_6Encoding::~ISO8859_6Encoding()
{
}


} // namespace Poco
