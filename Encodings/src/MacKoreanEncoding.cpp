//
// MacKoreanEncoding.cpp
//
// Library: Encodings
// Package: Encodings
// Module:  MacKoreanEncoding
//
// Copyright (c) 2019, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier: BSL-1.0
//


#include "Poco/MacKoreanEncoding.h"


namespace Poco {


const char* MacKoreanEncoding::_names[] =
{
	"MacKorean",
	NULL
};


const TextEncoding::CharacterMap MacKoreanEncoding::_charMap =
{
	    -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,
	    -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,     -1,
	0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002A, 0x002B, 0x002C, 0x002D, 0x002E, 0x002F,
	0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003A, 0x003B, 0x003C, 0x003D, 0x003E, 0x003F,
	0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004A, 0x004B, 0x004C, 0x004D, 0x004E, 0x004F,
	0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005A, 0x005B, 0x005C, 0x005D, 0x005E, 0x005F,
	0x0060, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006A, 0x006B, 0x006C, 0x006D, 0x006E, 0x006F,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007A, 0x007B, 0x007C, 0x007D, 0x007E,     -1,
	0x00A0, 0x20A9,     -1, 0x00A9,     -1, 0x0085, 0x0086, 0x0087, 0x0088, 0x0089, 0x008A, 0x008B, 0x008C, 0x008D, 0x008E, 0x008F,
	0x0090, 0x0091, 0x0092, 0x0093, 0x0094, 0x0095, 0x0096, 0x0097, 0x0098, 0x0099, 0x009A, 0x009B, 0x009C, 0x009D, 0x009E, 0x009F,
	    -1,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -1,     -1,
	    -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,
	    -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -1,     -2,     -2,     -2,     -2,     -2,     -2,
	    -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,
	    -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,
	    -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -2,     -1,     -1,
};


const DoubleByteEncoding::Mapping MacKoreanEncoding::_mappingTable[] = {
	{ 0xA14D, 0xFE59 }, { 0xA14E, 0xFE5A }, { 0xA159, 0x2985 }, { 0xA15A, 0x2986 }, { 0xA15D, 0x3016 }, { 0xA15E, 0x3017 }, { 0xA15F, 0x3018 }, { 0xA160, 0x3019 },
	{ 0xA16D, 0x2051 }, { 0xA16E, 0xF840 }, { 0xA16F, 0x201F }, { 0xA170, 0x201B }, { 0xA171, 0x207A }, { 0xA172, 0x207B }, { 0xA17A, 0x207C }, { 0xA17C, 0x207D },
	{ 0xA17D, 0x207E }, { 0xA196, 0x204C }, { 0xA197, 0x204D }, { 0xA198, 0x02BC }, { 0xA199, 0x2997 }, { 0xA19A, 0x2998 }, { 0xA1A1, 0x3000 }, { 0xA1A2, 0x3001 },
	{ 0xA1A3, 0x3002 }, { 0xA1A4, 0x00B7 }, { 0xA1A5, 0x2025 }, { 0xA1A6, 0x2026 }, { 0xA1A7, 0x00A8 }, { 0xA1A8, 0x3003 }, { 0xA1A9, 0x2013 }, { 0xA1AA, 0x2014 },
	{ 0xA1AB, 0x2016 }, { 0xA1AC, 0xFF3C }, { 0xA1AD, 0x301C }, { 0xA1AE, 0x2018 }, { 0xA1AF, 0x2019 }, { 0xA1B0, 0x201C }, { 0xA1B1, 0x201D }, { 0xA1B2, 0x3014 },
	{ 0xA1B3, 0x3015 }, { 0xA1B4, 0x3008 }, { 0xA1B5, 0x3009 }, { 0xA1B6, 0x300A }, { 0xA1B7, 0x300B }, { 0xA1B8, 0x300C }, { 0xA1B9, 0x300D }, { 0xA1BA, 0x300E },
	{ 0xA1BB, 0x300F }, { 0xA1BC, 0x3010 }, { 0xA1BD, 0x3011 }, { 0xA1BE, 0x00B1 }, { 0xA1BF, 0x00D7 }, { 0xA1C0, 0x00F7 }, { 0xA1C1, 0x2260 }, { 0xA1C2, 0x2264 },
	{ 0xA1C3, 0x2265 }, { 0xA1C4, 0x221E }, { 0xA1C5, 0x2234 }, { 0xA1C6, 0x00B0 }, { 0xA1C7, 0x2032 }, { 0xA1C8, 0x2033 }, { 0xA1C9, 0x2103 }, { 0xA1CA, 0x212B },
	{ 0xA1CB, 0x00A2 }, { 0xA1CC, 0x00A3 }, { 0xA1CD, 0x00A5 }, { 0xA1CE, 0x2642 }, { 0xA1CF, 0x2640 }, { 0xA1D0, 0x2220 }, { 0xA1D1, 0x22A5 }, { 0xA1D2, 0x2312 },
	{ 0xA1D3, 0x2202 }, { 0xA1D4, 0x2207 }, { 0xA1D5, 0x2261 }, { 0xA1D6, 0x2252 }, { 0xA1D7, 0x00A7 }, { 0xA1D8, 0x203B }, { 0xA1D9, 0x2606 }, { 0xA1DA, 0x2605 },
	{ 0xA1DB, 0x25CB }, { 0xA1DC, 0x25CF }, { 0xA1DD, 0x25CE }, { 0xA1DE, 0x25C7 }, { 0xA1DF, 0x25C6 }, { 0xA1E0, 0x25A1 }, { 0xA1E1, 0x25A0 }, { 0xA1E2, 0x25B3 },
	{ 0xA1E3, 0x25B2 }, { 0xA1E4, 0x25BD }, { 0xA1E5, 0x25BC }, { 0xA1E6, 0x2192 }, { 0xA1E7, 0x2190 }, { 0xA1E8, 0x2191 }, { 0xA1E9, 0x2193 }, { 0xA1EA, 0x2194 },
	{ 0xA1EB, 0x3013 }, { 0xA1EC, 0x226A }, { 0xA1ED, 0x226B }, { 0xA1EE, 0x221A }, { 0xA1EF, 0x223D }, { 0xA1F0, 0x221D }, { 0xA1F1, 0x2235 }, { 0xA1F2, 0x222B },
	{ 0xA1F3, 0x222C }, { 0xA1F4, 0x2208 }, { 0xA1F5, 0x220B }, { 0xA1F6, 0x2286 }, { 0xA1F7, 0x2287 }, { 0xA1F8, 0x2282 }, { 0xA1F9, 0x2283 }, { 0xA1FA, 0x222A },
	{ 0xA1FB, 0x2229 }, { 0xA1FC, 0x2227 }, { 0xA1FD, 0x2228 }, { 0xA1FE, 0x00AC }, { 0xA2A1, 0x21D2 }, { 0xA2A2, 0x21D4 }, { 0xA2A3, 0x2200 }, { 0xA2A4, 0x2203 },
	{ 0xA2A5, 0x00B4 }, { 0xA2A6, 0x02DC }, { 0xA2A7, 0x02C7 }, { 0xA2A8, 0x02D8 }, { 0xA2A9, 0x02DD }, { 0xA2AA, 0x02DA }, { 0xA2AB, 0x02D9 }, { 0xA2AC, 0x00B8 },
	{ 0xA2AD, 0x02DB }, { 0xA2AE, 0x00A1 }, { 0xA2AF, 0x00BF }, { 0xA2B0, 0x02D0 }, { 0xA2B1, 0x222E }, { 0xA2B2, 0x2211 }, { 0xA2B3, 0x220F }, { 0xA2B4, 0x00A4 },
	{ 0xA2B5, 0x2109 }, { 0xA2B6, 0x2030 }, { 0xA2B7, 0x25C1 }, { 0xA2B8, 0x25C0 }, { 0xA2B9, 0x25B7 }, { 0xA2BA, 0x25B6 }, { 0xA2BB, 0x2664 }, { 0xA2BC, 0x2660 },
	{ 0xA2BD, 0x2661 }, { 0xA2BE, 0x2665 }, { 0xA2BF, 0x2667 }, { 0xA2C0, 0x2663 }, { 0xA2C1, 0x25C9 }, { 0xA2C2, 0x25C8 }, { 0xA2C3, 0x25A3 }, { 0xA2C4, 0x25D0 },
	{ 0xA2C5, 0x25D1 }, { 0xA2C6, 0x2592 }, { 0xA2C7, 0x25A4 }, { 0xA2C8, 0x25A5 }, { 0xA2C9, 0x25A8 }, { 0xA2CA, 0x25A7 }, { 0xA2CB, 0x25A6 }, { 0xA2CC, 0x25A9 },
	{ 0xA2CD, 0x2668 }, { 0xA2CE, 0x260F }, { 0xA2CF, 0x260E }, { 0xA2D0, 0x261C }, { 0xA2D1, 0x261E }, { 0xA2D2, 0x00B6 }, { 0xA2D3, 0x2020 }, { 0xA2D4, 0x2021 },
	{ 0xA2D5, 0x2195 }, { 0xA2D6, 0x2197 }, { 0xA2D7, 0x2199 }, { 0xA2D8, 0x2196 }, { 0xA2D9, 0x2198 }, { 0xA2DA, 0x266D }, { 0xA2DB, 0x2669 }, { 0xA2DC, 0x266A },
	{ 0xA2DD, 0x266C }, { 0xA2DE, 0x327F }, { 0xA2DF, 0x321C }, { 0xA2E0, 0x2116 }, { 0xA2E1, 0x33C7 }, { 0xA2E2, 0x2122 }, { 0xA2E3, 0x33C2 }, { 0xA2E4, 0x33D8 },
	{ 0xA2E5, 0x2121 }, { 0xA2FD, 0x22EE }, { 0xA2FE, 0x2237 }, { 0xA355, 0x278A }, { 0xA356, 0x278B }, { 0xA357, 0x278C }, { 0xA358, 0x278D }, { 0xA359, 0x278E },
	{ 0xA35A, 0x278F }, { 0xA35B, 0x2790 }, { 0xA35C, 0x2791 }, { 0xA35D, 0x2792 }, { 0xA35E, 0x2793 }, { 0xA386, 0x24B6 }, { 0xA387, 0x24B7 }, { 0xA388, 0x24B8 },
	{ 0xA389, 0x24B9 }, { 0xA38A, 0x24BA }, { 0xA38B, 0x24BB }, { 0xA38C, 0x24BC }, { 0xA38D, 0x24BD }, { 0xA38E, 0x24BE }, { 0xA38F, 0x24BF }, { 0xA390, 0x24C0 },
	{ 0xA391, 0x24C1 }, { 0xA392, 0x24C2 }, { 0xA393, 0x24C3 }, { 0xA394, 0x24C4 }, { 0xA395, 0x24C5 }, { 0xA396, 0x24C6 }, { 0xA397, 0x24C7 }, { 0xA398, 0x24C8 },
	{ 0xA399, 0x24C9 }, { 0xA39A, 0x24CA }, { 0xA39B, 0x24CB }, { 0xA39C, 0x24CC }, { 0xA39D, 0x24CD }, { 0xA39E, 0x24CE }, { 0xA39F, 0x24CF }, { 0xA3A1, 0xFF01 },
	{ 0xA3A2, 0xFF02 }, { 0xA3A3, 0xFF03 }, { 0xA3A4, 0xFF04 }, { 0xA3A5, 0xFF05 }, { 0xA3A6, 0xFF06 }, { 0xA3A7, 0xFF07 }, { 0xA3A8, 0xFF08 }, { 0xA3A9, 0xFF09 },
	{ 0xA3AA, 0xFF0A }, { 0xA3AB, 0xFF0B }, { 0xA3AC, 0xFF0C }, { 0xA3AD, 0xFF0D }, { 0xA3AE, 0xFF0E }, { 0xA3AF, 0xFF0F }, { 0xA3B0, 0xFF10 }, { 0xA3B1, 0xFF11 },
	{ 0xA3B2, 0xFF12 }, { 0xA3B3, 0xFF13 }, { 0xA3B4, 0xFF14 }, { 0xA3B5, 0xFF15 }, { 0xA3B6, 0xFF16 }, { 0xA3B7, 0xFF17 }, { 0xA3B8, 0xFF18 }, { 0xA3B9, 0xFF19 },
	{ 0xA3BA, 0xFF1A }, { 0xA3BB, 0xFF1B }, { 0xA3BC, 0xFF1C }, { 0xA3BD, 0xFF1D }, { 0xA3BE, 0xFF1E }, { 0xA3BF, 0xFF1F }, { 0xA3C0, 0xFF20 }, { 0xA3C1, 0xFF21 },
	{ 0xA3C2, 0xFF22 }, { 0xA3C3, 0xFF23 }, { 0xA3C4, 0xFF24 }, { 0xA3C5, 0xFF25 }, { 0xA3C6, 0xFF26 }, { 0xA3C7, 0xFF27 }, { 0xA3C8, 0xFF28 }, { 0xA3C9, 0xFF29 },
	{ 0xA3CA, 0xFF2A }, { 0xA3CB, 0xFF2B }, { 0xA3CC, 0xFF2C }, { 0xA3CD, 0xFF2D }, { 0xA3CE, 0xFF2E }, { 0xA3CF, 0xFF2F }, { 0xA3D0, 0xFF30 }, { 0xA3D1, 0xFF31 },
	{ 0xA3D2, 0xFF32 }, { 0xA3D3, 0xFF33 }, { 0xA3D4, 0xFF34 }, { 0xA3D5, 0xFF35 }, { 0xA3D6, 0xFF36 }, { 0xA3D7, 0xFF37 }, { 0xA3D8, 0xFF38 }, { 0xA3D9, 0xFF39 },
	{ 0xA3DA, 0xFF3A }, { 0xA3DB, 0xFF3B }, { 0xA3DC, 0xFFE6 }, { 0xA3DD, 0xFF3D }, { 0xA3DE, 0xFF3E }, { 0xA3DF, 0xFF3F }, { 0xA3E0, 0xFF40 }, { 0xA3E1, 0xFF41 },
	{ 0xA3E2, 0xFF42 }, { 0xA3E3, 0xFF43 }, { 0xA3E4, 0xFF44 }, { 0xA3E5, 0xFF45 }, { 0xA3E6, 0xFF46 }, { 0xA3E7, 0xFF47 }, { 0xA3E8, 0xFF48 }, { 0xA3E9, 0xFF49 },
	{ 0xA3EA, 0xFF4A }, { 0xA3EB, 0xFF4B }, { 0xA3EC, 0xFF4C }, { 0xA3ED, 0xFF4D }, { 0xA3EE, 0xFF4E }, { 0xA3EF, 0xFF4F }, { 0xA3F0, 0xFF50 }, { 0xA3F1, 0xFF51 },
	{ 0xA3F2, 0xFF52 }, { 0xA3F3, 0xFF53 }, { 0xA3F4, 0xFF54 }, { 0xA3F5, 0xFF55 }, { 0xA3F6, 0xFF56 }, { 0xA3F7, 0xFF57 }, { 0xA3F8, 0xFF58 }, { 0xA3F9, 0xFF59 },
	{ 0xA3FA, 0xFF5A }, { 0xA3FB, 0xFF5B }, { 0xA3FC, 0xFF5C }, { 0xA3FD, 0xFF5D }, { 0xA3FE, 0x203E }, { 0xA47D, 0x2A26 }, { 0xA481, 0x227A }, { 0xA482, 0x227B },
	{ 0xA483, 0x22CE }, { 0xA484, 0x22CF }, { 0xA485, 0x2280 }, { 0xA486, 0x2281 }, { 0xA487, 0x2270 }, { 0xA488, 0x2271 }, { 0xA489, 0x2272 }, { 0xA48A, 0x2273 },
	{ 0xA48B, 0x2AC5 }, { 0xA48C, 0x2ACB }, { 0xA48D, 0x2AC6 }, { 0xA48E, 0x2ACC }, { 0xA48F, 0x2276 }, { 0xA490, 0x2277 }, { 0xA491, 0x2279 }, { 0xA492, 0x22DA },
	{ 0xA493, 0x22DB }, { 0xA494, 0x2A8B }, { 0xA495, 0x2A8C }, { 0xA496, 0x2A91 }, { 0xA497, 0x2A92 }, { 0xA499, 0x2245 }, { 0xA49A, 0x2243 }, { 0xA49B, 0x2248 },
	{ 0xA49C, 0x29A3 }, { 0xA49D, 0x22A4 }, { 0xA4A1, 0x3131 }, { 0xA4A2, 0x3132 }, { 0xA4A3, 0x3133 }, { 0xA4A4, 0x3134 }, { 0xA4A5, 0x3135 }, { 0xA4A6, 0x3136 },
	{ 0xA4A7, 0x3137 }, { 0xA4A8, 0x3138 }, { 0xA4A9, 0x3139 }, { 0xA4AA, 0x313A }, { 0xA4AB, 0x313B }, { 0xA4AC, 0x313C }, { 0xA4AD, 0x313D }, { 0xA4AE, 0x313E },
	{ 0xA4AF, 0x313F }, { 0xA4B0, 0x3140 }, { 0xA4B1, 0x3141 }, { 0xA4B2, 0x3142 }, { 0xA4B3, 0x3143 }, { 0xA4B4, 0x3144 }, { 0xA4B5, 0x3145 }, { 0xA4B6, 0x3146 },
	{ 0xA4B7, 0x3147 }, { 0xA4B8, 0x3148 }, { 0xA4B9, 0x3149 }, { 0xA4BA, 0x314A }, { 0xA4BB, 0x314B }, { 0xA4BC, 0x314C }, { 0xA4BD, 0x314D }, { 0xA4BE, 0x314E },
	{ 0xA4BF, 0x314F }, { 0xA4C0, 0x3150 }, { 0xA4C1, 0x3151 }, { 0xA4C2, 0x3152 }, { 0xA4C3, 0x3153 }, { 0xA4C4, 0x3154 }, { 0xA4C5, 0x3155 }, { 0xA4C6, 0x3156 },
	{ 0xA4C7, 0x3157 }, { 0xA4C8, 0x3158 }, { 0xA4C9, 0x3159 }, { 0xA4CA, 0x315A }, { 0xA4CB, 0x315B }, { 0xA4CC, 0x315C }, { 0xA4CD, 0x315D }, { 0xA4CE, 0x315E },
	{ 0xA4CF, 0x315F }, { 0xA4D0, 0x3160 }, { 0xA4D1, 0x3161 }, { 0xA4D2, 0x3162 }, { 0xA4D3, 0x3163 }, { 0xA4D4, 0x3164 }, { 0xA4D5, 0x3165 }, { 0xA4D6, 0x3166 },
	{ 0xA4D7, 0x3167 }, { 0xA4D8, 0x3168 }, { 0xA4D9, 0x3169 }, { 0xA4DA, 0x316A }, { 0xA4DB, 0x316B }, { 0xA4DC, 0x316C }, { 0xA4DD, 0x316D }, { 0xA4DE, 0x316E },
	{ 0xA4DF, 0x316F }, { 0xA4E0, 0x3170 }, { 0xA4E1, 0x3171 }, { 0xA4E2, 0x3172 }, { 0xA4E3, 0x3173 }, { 0xA4E4, 0x3174 }, { 0xA4E5, 0x3175 }, { 0xA4E6, 0x3176 },
	{ 0xA4E7, 0x3177 }, { 0xA4E8, 0x3178 }, { 0xA4E9, 0x3179 }, { 0xA4EA, 0x317A }, { 0xA4EB, 0x317B }, { 0xA4EC, 0x317C }, { 0xA4ED, 0x317D }, { 0xA4EE, 0x317E },
	{ 0xA4EF, 0x317F }, { 0xA4F0, 0x3180 }, { 0xA4F1, 0x3181 }, { 0xA4F2, 0x3182 }, { 0xA4F3, 0x3183 }, { 0xA4F4, 0x3184 }, { 0xA4F5, 0x3185 }, { 0xA4F6, 0x3186 },
	{ 0xA4F7, 0x3187 }, { 0xA4F8, 0x3188 }, { 0xA4F9, 0x3189 }, { 0xA4FA, 0x318A }, { 0xA4FB, 0x318B }, { 0xA4FC, 0x318C }, { 0xA4FD, 0x318D }, { 0xA4FE, 0x318E },
	{ 0xA5A1, 0x2170 }, { 0xA5A2, 0x2171 }, { 0xA5A3, 0x2172 }, { 0xA5A4, 0x2173 }, { 0xA5A5, 0x2174 }, { 0xA5A6, 0x2175 }, { 0xA5A7, 0x2176 }, { 0xA5A8, 0x2177 },
	{ 0xA5A9, 0x2178 }, { 0xA5AA, 0x2179 }, { 0xA5B0, 0x2160 }, { 0xA5B1, 0x2161 }, { 0xA5B2, 0x2162 }, { 0xA5B3, 0x2163 }, { 0xA5B4, 0x2164 }, { 0xA5B5, 0x2165 },
	{ 0xA5B6, 0x2166 }, { 0xA5B7, 0x2167 }, { 0xA5B8, 0x2168 }, { 0xA5B9, 0x2169 }, { 0xA5C1, 0x0391 }, { 0xA5C2, 0x0392 }, { 0xA5C3, 0x0393 }, { 0xA5C4, 0x0394 },
	{ 0xA5C5, 0x0395 }, { 0xA5C6, 0x0396 }, { 0xA5C7, 0x0397 }, { 0xA5C8, 0x0398 }, { 0xA5C9, 0x0399 }, { 0xA5CA, 0x039A }, { 0xA5CB, 0x039B }, { 0xA5CC, 0x039C },
	{ 0xA5CD, 0x039D }, { 0xA5CE, 0x039E }, { 0xA5CF, 0x039F }, { 0xA5D0, 0x03A0 }, { 0xA5D1, 0x03A1 }, { 0xA5D2, 0x03A3 }, { 0xA5D3, 0x03A4 }, { 0xA5D4, 0x03A5 },
	{ 0xA5D5, 0x03A6 }, { 0xA5D6, 0x03A7 }, { 0xA5D7, 0x03A8 }, { 0xA5D8, 0x03A9 }, { 0xA5DE, 0x2034 }, { 0xA5E1, 0x03B1 }, { 0xA5E2, 0x03B2 }, { 0xA5E3, 0x03B3 },
	{ 0xA5E4, 0x03B4 }, { 0xA5E5, 0x03B5 }, { 0xA5E6, 0x03B6 }, { 0xA5E7, 0x03B7 }, { 0xA5E8, 0x03B8 }, { 0xA5E9, 0x03B9 }, { 0xA5EA, 0x03BA }, { 0xA5EB, 0x03BB },
	{ 0xA5EC, 0x03BC }, { 0xA5ED, 0x03BD }, { 0xA5EE, 0x03BE }, { 0xA5EF, 0x03BF }, { 0xA5F0, 0x03C0 }, { 0xA5F1, 0x03C1 }, { 0xA5F2, 0x03C3 }, { 0xA5F3, 0x03C4 },
	{ 0xA5F4, 0x03C5 }, { 0xA5F5, 0x03C6 }, { 0xA5F6, 0x03C7 }, { 0xA5F7, 0x03C8 }, { 0xA5F8, 0x03C9 }, { 0xA642, 0xF83D }, { 0xA648, 0x266F }, { 0xA64D, 0x2042 },
	{ 0xA64E, 0x204E }, { 0xA652, 0x273D }, { 0xA653, 0x2731 }, { 0xA654, 0x2747 }, { 0xA655, 0x2022 }, { 0xA658, 0xF805 }, { 0xA65A, 0x2039 }, { 0xA65B, 0x203A },
	{ 0xA65C, 0x00AB }, { 0xA65D, 0x00BB }, { 0xA663, 0xF806 }, { 0xA664, 0x29C8 }, { 0xA668, 0x29BE }, { 0xA66C, 0x271A }, { 0xA66D, 0x2716 }, { 0xA66E, 0x29BF },
	{ 0xA66F, 0x25EF }, { 0xA672, 0x2723 }, { 0xA673, 0x2756 }, { 0xA674, 0xF80A }, { 0xA675, 0x25CC }, { 0xA677, 0x2610 }, { 0xA678, 0x25A2 }, { 0xA67C, 0x273F },
	{ 0xA681, 0xF809 }, { 0xA683, 0x274D }, { 0xA684, 0x25CD }, { 0xA688, 0x2720 }, { 0xA68D, 0x2741 }, { 0xA68F, 0xF808 }, { 0xA693, 0x262F }, { 0xA696, 0xF80B },
	{ 0xA699, 0x2740 }, { 0xA69A, 0xF80C }, { 0xA69E, 0x3020 }, { 0xA69F, 0xF807 }, { 0xA6A1, 0x2500 }, { 0xA6A2, 0x2502 }, { 0xA6A3, 0x250C }, { 0xA6A4, 0x2510 },
	{ 0xA6A5, 0x2518 }, { 0xA6A6, 0x2514 }, { 0xA6A7, 0x251C }, { 0xA6A8, 0x252C }, { 0xA6A9, 0x2524 }, { 0xA6AA, 0x2534 }, { 0xA6AB, 0x253C }, { 0xA6AC, 0x2501 },
	{ 0xA6AD, 0x2503 }, { 0xA6AE, 0x250F }, { 0xA6AF, 0x2513 }, { 0xA6B0, 0x251B }, { 0xA6B1, 0x2517 }, { 0xA6B2, 0x2523 }, { 0xA6B3, 0x2533 }, { 0xA6B4, 0x252B },
	{ 0xA6B5, 0x253B }, { 0xA6B6, 0x254B }, { 0xA6B7, 0x2520 }, { 0xA6B8, 0x252F }, { 0xA6B9, 0x2528 }, { 0xA6BA, 0x2537 }, { 0xA6BB, 0x253F }, { 0xA6BC, 0x251D },
	{ 0xA6BD, 0x2530 }, { 0xA6BE, 0x2525 }, { 0xA6BF, 0x2538 }, { 0xA6C0, 0x2542 }, { 0xA6C1, 0x2512 }, { 0xA6C2, 0x2511 }, { 0xA6C3, 0x251A }, { 0xA6C4, 0x2519 },
	{ 0xA6C5, 0x2516 }, { 0xA6C6, 0x2515 }, { 0xA6C7, 0x250E }, { 0xA6C8, 0x250D }, { 0xA6C9, 0x251E }, { 0xA6CA, 0x251F }, { 0xA6CB, 0x2521 }, { 0xA6CC, 0x2522 },
	{ 0xA6CD, 0x2526 }, { 0xA6CE, 0x2527 }, { 0xA6CF, 0x2529 }, { 0xA6D0, 0x252A }, { 0xA6D1, 0x252D }, { 0xA6D2, 0x252E }, { 0xA6D3, 0x2531 }, { 0xA6D4, 0x2532 },
	{ 0xA6D5, 0x2535 }, { 0xA6D6, 0x2536 }, { 0xA6D7, 0x2539 }, { 0xA6D8, 0x253A }, { 0xA6D9, 0x253D }, { 0xA6DA, 0x253E }, { 0xA6DB, 0x2540 }, { 0xA6DC, 0x2541 },
	{ 0xA6DD, 0x2543 }, { 0xA6DE, 0x2544 }, { 0xA6DF, 0x2545 }, { 0xA6E0, 0x2546 }, { 0xA6E1, 0x2547 }, { 0xA6E2, 0x2548 }, { 0xA6E3, 0x2549 }, { 0xA6E4, 0x254A },
	{ 0xA6E5, 0x2776 }, { 0xA6E6, 0x2777 }, { 0xA6E7, 0x2778 }, { 0xA6E8, 0x2779 }, { 0xA6E9, 0x277A }, { 0xA6EA, 0x277B }, { 0xA6EB, 0x277C }, { 0xA6EC, 0x277D },
	{ 0xA6ED, 0x277E }, { 0xA6EE, 0x277F }, { 0xA6EF, 0x24EB }, { 0xA6F0, 0x24EC }, { 0xA6F1, 0x24ED }, { 0xA6F2, 0x24EE }, { 0xA6F3, 0x24EF }, { 0xA6F4, 0x24F0 },
	{ 0xA6F5, 0x24F1 }, { 0xA6F6, 0x24F2 }, { 0xA6F7, 0x24F3 }, { 0xA6F8, 0x24F4 }, { 0xA742, 0x3012 }, { 0xA743, 0x3036 }, { 0xA746, 0x25FB }, { 0xA747, 0xF84C },
	{ 0xA74A, 0x25AD }, { 0xA74B, 0xF84D }, { 0xA74C, 0xF84E }, { 0xA74D, 0xF84F }, { 0xA751, 0x2206 }, { 0xA753, 0x221F }, { 0xA755, 0x2225 }, { 0xA756, 0x2226 },
	{ 0xA758, 0x2253 }, { 0xA759, 0x2251 }, { 0xA75A, 0x2266 }, { 0xA75B, 0x2267 }, { 0xA75C, 0x2213 }, { 0xA75D, 0x2295 }, { 0xA75E, 0x2296 }, { 0xA75F, 0x2297 },
	{ 0xA760, 0x2A38 }, { 0xA761, 0x2314 }, { 0xA764, 0x2262 }, { 0xA766, 0x25B1 }, { 0xA768, 0x2222 }, { 0xA769, 0x2250 }, { 0xA76A, 0x03D5 }, { 0xA76B, 0x2AE8 },
	{ 0xA76C, 0x22A3 }, { 0xA76F, 0x226E }, { 0xA770, 0x226F }, { 0xA771, 0x2285 }, { 0xA772, 0x2284 }, { 0xA773, 0x2209 }, { 0xA774, 0x220C }, { 0xA775, 0x22BB },
	{ 0xA776, 0x22BC }, { 0xA777, 0x225A }, { 0xA778, 0x2306 }, { 0xA77B, 0x2A72 }, { 0xA782, 0x329E }, { 0xA784, 0x203C }, { 0xA785, 0x2049 }, { 0xA787, 0x2047 },
	{ 0xA78F, 0x2588 }, { 0xA790, 0x25E6 }, { 0xA794, 0x25BF }, { 0xA795, 0x25B5 }, { 0xA796, 0x25B9 }, { 0xA797, 0x25C3 }, { 0xA798, 0x2666 }, { 0xA799, 0x2981 },
	{ 0xA79A, 0x25FC }, { 0xA79C, 0x25CA }, { 0xA79D, 0x3231 }, { 0xA79E, 0x3239 }, { 0xA79F, 0x33CB }, { 0xA7A1, 0x3395 }, { 0xA7A2, 0x3396 }, { 0xA7A3, 0x3397 },
	{ 0xA7A4, 0x2113 }, { 0xA7A5, 0x3398 }, { 0xA7A6, 0x33C4 }, { 0xA7A7, 0x33A3 }, { 0xA7A8, 0x33A4 }, { 0xA7A9, 0x33A5 }, { 0xA7AA, 0x33A6 }, { 0xA7AB, 0x3399 },
	{ 0xA7AC, 0x339A }, { 0xA7AD, 0x339B }, { 0xA7AE, 0x339C }, { 0xA7AF, 0x339D }, { 0xA7B0, 0x339E }, { 0xA7B1, 0x339F }, { 0xA7B2, 0x33A0 }, { 0xA7B3, 0x33A1 },
	{ 0xA7B4, 0x33A2 }, { 0xA7B5, 0x33CA }, { 0xA7B6, 0x338D }, { 0xA7B7, 0x338E }, { 0xA7B8, 0x338F }, { 0xA7B9, 0x33CF }, { 0xA7BA, 0x3388 }, { 0xA7BB, 0x3389 },
	{ 0xA7BC, 0x33C8 }, { 0xA7BD, 0x33A7 }, { 0xA7BE, 0x33A8 }, { 0xA7BF, 0x33B0 }, { 0xA7C0, 0x33B1 }, { 0xA7C1, 0x33B2 }, { 0xA7C2, 0x33B3 }, { 0xA7C3, 0x33B4 },
	{ 0xA7C4, 0x33B5 }, { 0xA7C5, 0x33B6 }, { 0xA7C6, 0x33B7 }, { 0xA7C7, 0x33B8 }, { 0xA7C8, 0x33B9 }, { 0xA7C9, 0x3380 }, { 0xA7CA, 0x3381 }, { 0xA7CB, 0x3382 },
	{ 0xA7CC, 0x3383 }, { 0xA7CD, 0x3384 }, { 0xA7CE, 0x33BA }, { 0xA7CF, 0x33BB }, { 0xA7D0, 0x33BC }, { 0xA7D1, 0x33BD }, { 0xA7D2, 0x33BE }, { 0xA7D3, 0x33BF },
	{ 0xA7D4, 0x3390 }, { 0xA7D5, 0x3391 }, { 0xA7D6, 0x3392 }, { 0xA7D7, 0x3393 }, { 0xA7D8, 0x3394 }, { 0xA7D9, 0x2126 }, { 0xA7DA, 0x33C0 }, { 0xA7DB, 0x33C1 },
	{ 0xA7DC, 0x338A }, { 0xA7DD, 0x338B }, { 0xA7DE, 0x338C }, { 0xA7DF, 0x33D6 }, { 0xA7E0, 0x33C5 }, { 0xA7E1, 0x33AD }, { 0xA7E2, 0x33AE }, { 0xA7E3, 0x33AF },
	{ 0xA7E4, 0x33DB }, { 0xA7E5, 0x33A9 }, { 0xA7E6, 0x33AA }, { 0xA7E7, 0x33AB }, { 0xA7E8, 0x33AC }, { 0xA7E9, 0x33DD }, { 0xA7EA, 0x33D0 }, { 0xA7EB, 0x33D3 },
	{ 0xA7EC, 0x33C3 }, { 0xA7ED, 0x33C9 }, { 0xA7EE, 0x33DC }, { 0xA7EF, 0x33C6 }, { 0xA7F0, 0x246F }, { 0xA7F1, 0x2470 }, { 0xA7F2, 0x2471 }, { 0xA7F3, 0x2472 },
	{ 0xA7F4, 0x2473 }, { 0xA7F5, 0x3251 }, { 0xA7F6, 0x3252 }, { 0xA7F7, 0x3253 }, { 0xA7F8, 0x3254 }, { 0xA7F9, 0x3255 }, { 0xA7FA, 0x3256 }, { 0xA7FB, 0x3257 },
	{ 0xA7FC, 0x3258 }, { 0xA7FD, 0x3259 }, { 0xA7FE, 0x325A }, { 0xA849, 0x21D0 }, { 0xA84A, 0x21CF }, { 0xA84B, 0x21CD }, { 0xA85C, 0x27B2 }, { 0xA860, 0x279C },
	{ 0xA867, 0xF846 }, { 0xA868, 0xF847 }, { 0xA86A, 0x279B }, { 0xA86F, 0x2962 }, { 0xA870, 0x2964 }, { 0xA871, 0x2963 }, { 0xA872, 0x2965 }, { 0xA874, 0x27A1 },
	{ 0xA878, 0x279E }, { 0xA87B, 0x21B2 }, { 0xA87C, 0x21B1 }, { 0xA881, 0x21B4 }, { 0xA882, 0x21B0 }, { 0xA883, 0x21B3 }, { 0xA886, 0x2936 }, { 0xA889, 0x2935 },
	{ 0xA88B, 0x2937 }, { 0xA88C, 0x2939 }, { 0xA88D, 0x2934 }, { 0xA892, 0x21BC }, { 0xA893, 0x21C0 }, { 0xA894, 0xF841 }, { 0xA89C, 0xF849 }, { 0xA89D, 0xF848 },
	{ 0xA89E, 0x21C4 }, { 0xA89F, 0x21C5 }, { 0xA8A1, 0x00C6 }, { 0xA8A2, 0x00D0 }, { 0xA8A3, 0x00AA }, { 0xA8A4, 0x0126 }, { 0xA8A6, 0x0132 }, { 0xA8A8, 0x013F },
	{ 0xA8A9, 0x0141 }, { 0xA8AA, 0x00D8 }, { 0xA8AB, 0x0152 }, { 0xA8AC, 0x00BA }, { 0xA8AD, 0x00DE }, { 0xA8AE, 0x0166 }, { 0xA8AF, 0x014A }, { 0xA8B1, 0x3260 },
	{ 0xA8B2, 0x3261 }, { 0xA8B3, 0x3262 }, { 0xA8B4, 0x3263 }, { 0xA8B5, 0x3264 }, { 0xA8B6, 0x3265 }, { 0xA8B7, 0x3266 }, { 0xA8B8, 0x3267 }, { 0xA8B9, 0x3268 },
	{ 0xA8BA, 0x3269 }, { 0xA8BB, 0x326A }, { 0xA8BC, 0x326B }, { 0xA8BD, 0x326C }, { 0xA8BE, 0x326D }, { 0xA8BF, 0x326E }, { 0xA8C0, 0x326F }, { 0xA8C1, 0x3270 },
	{ 0xA8C2, 0x3271 }, { 0xA8C3, 0x3272 }, { 0xA8C4, 0x3273 }, { 0xA8C5, 0x3274 }, { 0xA8C6, 0x3275 }, { 0xA8C7, 0x3276 }, { 0xA8C8, 0x3277 }, { 0xA8C9, 0x3278 },
	{ 0xA8CA, 0x3279 }, { 0xA8CB, 0x327A }, { 0xA8CC, 0x327B }, { 0xA8CD, 0x24D0 }, { 0xA8CE, 0x24D1 }, { 0xA8CF, 0x24D2 }, { 0xA8D0, 0x24D3 }, { 0xA8D1, 0x24D4 },
	{ 0xA8D2, 0x24D5 }, { 0xA8D3, 0x24D6 }, { 0xA8D4, 0x24D7 }, { 0xA8D5, 0x24D8 }, { 0xA8D6, 0x24D9 }, { 0xA8D7, 0x24DA }, { 0xA8D8, 0x24DB }, { 0xA8D9, 0x24DC },
	{ 0xA8DA, 0x24DD }, { 0xA8DB, 0x24DE }, { 0xA8DC, 0x24DF }, { 0xA8DD, 0x24E0 }, { 0xA8DE, 0x24E1 }, { 0xA8DF, 0x24E2 }, { 0xA8E0, 0x24E3 }, { 0xA8E1, 0x24E4 },
	{ 0xA8E2, 0x24E5 }, { 0xA8E3, 0x24E6 }, { 0xA8E4, 0x24E7 }, { 0xA8E5, 0x24E8 }, { 0xA8E6, 0x24E9 }, { 0xA8E7, 0x2460 }, { 0xA8E8, 0x2461 }, { 0xA8E9, 0x2462 },
	{ 0xA8EA, 0x2463 }, { 0xA8EB, 0x2464 }, { 0xA8EC, 0x2465 }, { 0xA8ED, 0x2466 }, { 0xA8EE, 0x2467 }, { 0xA8EF, 0x2468 }, { 0xA8F0, 0x2469 }, { 0xA8F1, 0x246A },
	{ 0xA8F2, 0x246B }, { 0xA8F3, 0x246C }, { 0xA8F4, 0x246D }, { 0xA8F5, 0x246E }, { 0xA8F6, 0x00BD }, { 0xA8F7, 0x2153 }, { 0xA8F8, 0x2154 }, { 0xA8F9, 0x00BC },
	{ 0xA8FA, 0x00BE }, { 0xA8FB, 0x215B }, { 0xA8FC, 0x215C }, { 0xA8FD, 0x215D }, { 0xA8FE, 0x215E }, { 0xA9A1, 0x00E6 }, { 0xA9A2, 0x0111 }, { 0xA9A3, 0x00F0 },
	{ 0xA9A4, 0x0127 }, { 0xA9A5, 0x0131 }, { 0xA9A6, 0x0133 }, { 0xA9A7, 0x0138 }, { 0xA9A8, 0x0140 }, { 0xA9A9, 0x0142 }, { 0xA9AA, 0x00F8 }, { 0xA9AB, 0x0153 },
	{ 0xA9AC, 0x00DF }, { 0xA9AD, 0x00FE }, { 0xA9AE, 0x0167 }, { 0xA9AF, 0x014B }, { 0xA9B0, 0x0149 }, { 0xA9B1, 0x3200 }, { 0xA9B2, 0x3201 }, { 0xA9B3, 0x3202 },
	{ 0xA9B4, 0x3203 }, { 0xA9B5, 0x3204 }, { 0xA9B6, 0x3205 }, { 0xA9B7, 0x3206 }, { 0xA9B8, 0x3207 }, { 0xA9B9, 0x3208 }, { 0xA9BA, 0x3209 }, { 0xA9BB, 0x320A },
	{ 0xA9BC, 0x320B }, { 0xA9BD, 0x320C }, { 0xA9BE, 0x320D }, { 0xA9BF, 0x320E }, { 0xA9C0, 0x320F }, { 0xA9C1, 0x3210 }, { 0xA9C2, 0x3211 }, { 0xA9C3, 0x3212 },
	{ 0xA9C4, 0x3213 }, { 0xA9C5, 0x3214 }, { 0xA9C6, 0x3215 }, { 0xA9C7, 0x3216 }, { 0xA9C8, 0x3217 }, { 0xA9C9, 0x3218 }, { 0xA9CA, 0x3219 }, { 0xA9CB, 0x321A },
	{ 0xA9CC, 0x321B }, { 0xA9CD, 0x249C }, { 0xA9CE, 0x249D }, { 0xA9CF, 0x249E }, { 0xA9D0, 0x249F }, { 0xA9D1, 0x24A0 }, { 0xA9D2, 0x24A1 }, { 0xA9D3, 0x24A2 },
	{ 0xA9D4, 0x24A3 }, { 0xA9D5, 0x24A4 }, { 0xA9D6, 0x24A5 }, { 0xA9D7, 0x24A6 }, { 0xA9D8, 0x24A7 }, { 0xA9D9, 0x24A8 }, { 0xA9DA, 0x24A9 }, { 0xA9DB, 0x24AA },
	{ 0xA9DC, 0x24AB }, { 0xA9DD, 0x24AC }, { 0xA9DE, 0x24AD }, { 0xA9DF, 0x24AE }, { 0xA9E0, 0x24AF }, { 0xA9E1, 0x24B0 }, { 0xA9E2, 0x24B1 }, { 0xA9E3, 0x24B2 },
	{ 0xA9E4, 0x24B3 }, { 0xA9E5, 0x24B4 }, { 0xA9E6, 0x24B5 }, { 0xA9E7, 0x2474 }, { 0xA9E8, 0x2475 }, { 0xA9E9, 0x2476 }, { 0xA9EA, 0x2477 }, { 0xA9EB, 0x2478 },
	{ 0xA9EC, 0x2479 }, { 0xA9ED, 0x247A }, { 0xA9EE, 0x247B }, { 0xA9EF, 0x247C }, { 0xA9F0, 0x247D }, { 0xA9F1, 0x247E }, { 0xA9F2, 0x247F }, { 0xA9F3, 0x2480 },
	{ 0xA9F4, 0x2481 }, { 0xA9F5, 0x2482 }, { 0xA9F6, 0x00B9 }, { 0xA9F7, 0x00B2 }, { 0xA9F8, 0x00B3 }, { 0xA9F9, 0x2074 }, { 0xA9FA, 0x207F }, { 0xA9FB, 0x2081 },
	{ 0xA9FC, 0x2082 }, { 0xA9FD, 0x2083 }, { 0xA9FE, 0x2084 }, { 0xAAA1, 0x3041 }, { 0xAAA2, 0x3042 }, { 0xAAA3, 0x3043 }, { 0xAAA4, 0x3044 }, { 0xAAA5, 0x3045 },
	{ 0xAAA6, 0x3046 }, { 0xAAA7, 0x3047 }, { 0xAAA8, 0x3048 }, { 0xAAA9, 0x3049 }, { 0xAAAA, 0x304A }, { 0xAAAB, 0x304B }, { 0xAAAC, 0x304C }, { 0xAAAD, 0x304D },
	{ 0xAAAE, 0x304E }, { 0xAAAF, 0x304F }, { 0xAAB0, 0x3050 }, { 0xAAB1, 0x3051 }, { 0xAAB2, 0x3052 }, { 0xAAB3, 0x3053 }, { 0xAAB4, 0x3054 }, { 0xAAB5, 0x3055 },
	{ 0xAAB6, 0x3056 }, { 0xAAB7, 0x3057 }, { 0xAAB8, 0x3058 }, { 0xAAB9, 0x3059 }, { 0xAABA, 0x305A }, { 0xAABB, 0x305B }, { 0xAABC, 0x305C }, { 0xAABD, 0x305D },
	{ 0xAABE, 0x305E }, { 0xAABF, 0x305F }, { 0xAAC0, 0x3060 }, { 0xAAC1, 0x3061 }, { 0xAAC2, 0x3062 }, { 0xAAC3, 0x3063 }, { 0xAAC4, 0x3064 }, { 0xAAC5, 0x3065 },
	{ 0xAAC6, 0x3066 }, { 0xAAC7, 0x3067 }, { 0xAAC8, 0x3068 }, { 0xAAC9, 0x3069 }, { 0xAACA, 0x306A }, { 0xAACB, 0x306B }, { 0xAACC, 0x306C }, { 0xAACD, 0x306D },
	{ 0xAACE, 0x306E }, { 0xAACF, 0x306F }, { 0xAAD0, 0x3070 }, { 0xAAD1, 0x3071 }, { 0xAAD2, 0x3072 }, { 0xAAD3, 0x3073 }, { 0xAAD4, 0x3074 }, { 0xAAD5, 0x3075 },
	{ 0xAAD6, 0x3076 }, { 0xAAD7, 0x3077 }, { 0xAAD8, 0x3078 }, { 0xAAD9, 0x3079 }, { 0xAADA, 0x307A }, { 0xAADB, 0x307B }, { 0xAADC, 0x307C }, { 0xAADD, 0x307D },
	{ 0xAADE, 0x307E }, { 0xAADF, 0x307F }, { 0xAAE0, 0x3080 }, { 0xAAE1, 0x3081 }, { 0xAAE2, 0x3082 }, { 0xAAE3, 0x3083 }, { 0xAAE4, 0x3084 }, { 0xAAE5, 0x3085 },
	{ 0xAAE6, 0x3086 }, { 0xAAE7, 0x3087 }, { 0xAAE8, 0x3088 }, { 0xAAE9, 0x3089 }, { 0xAAEA, 0x308A }, { 0xAAEB, 0x308B }, { 0xAAEC, 0x308C }, { 0xAAED, 0x308D },
	{ 0xAAEE, 0x308E }, { 0xAAEF, 0x308F }, { 0xAAF0, 0x3090 }, { 0xAAF1, 0x3091 }, { 0xAAF2, 0x3092 }, { 0xAAF3, 0x3093 }, { 0xAAF4, 0x2483 }, { 0xAAF5, 0x2484 },
	{ 0xAAF6, 0x2485 }, { 0xAAF7, 0x2486 }, { 0xAAF8, 0x2487 }, { 0xAB5C, 0x3294 }, { 0xAB6C, 0x32A5 }, { 0xABA1, 0x30A1 }, { 0xABA2, 0x30A2 }, { 0xABA3, 0x30A3 },
	{ 0xABA4, 0x30A4 }, { 0xABA5, 0x30A5 }, { 0xABA6, 0x30A6 }, { 0xABA7, 0x30A7 }, { 0xABA8, 0x30A8 }, { 0xABA9, 0x30A9 }, { 0xABAA, 0x30AA }, { 0xABAB, 0x30AB },
	{ 0xABAC, 0x30AC }, { 0xABAD, 0x30AD }, { 0xABAE, 0x30AE }, { 0xABAF, 0x30AF }, { 0xABB0, 0x30B0 }, { 0xABB1, 0x30B1 }, { 0xABB2, 0x30B2 }, { 0xABB3, 0x30B3 },
	{ 0xABB4, 0x30B4 }, { 0xABB5, 0x30B5 }, { 0xABB6, 0x30B6 }, { 0xABB7, 0x30B7 }, { 0xABB8, 0x30B8 }, { 0xABB9, 0x30B9 }, { 0xABBA, 0x30BA }, { 0xABBB, 0x30BB },
	{ 0xABBC, 0x30BC }, { 0xABBD, 0x30BD }, { 0xABBE, 0x30BE }, { 0xABBF, 0x30BF }, { 0xABC0, 0x30C0 }, { 0xABC1, 0x30C1 }, { 0xABC2, 0x30C2 }, { 0xABC3, 0x30C3 },
	{ 0xABC4, 0x30C4 }, { 0xABC5, 0x30C5 }, { 0xABC6, 0x30C6 }, { 0xABC7, 0x30C7 }, { 0xABC8, 0x30C8 }, { 0xABC9, 0x30C9 }, { 0xABCA, 0x30CA }, { 0xABCB, 0x30CB },
	{ 0xABCC, 0x30CC }, { 0xABCD, 0x30CD }, { 0xABCE, 0x30CE }, { 0xABCF, 0x30CF }, { 0xABD0, 0x30D0 }, { 0xABD1, 0x30D1 }, { 0xABD2, 0x30D2 }, { 0xABD3, 0x30D3 },
	{ 0xABD4, 0x30D4 }, { 0xABD5, 0x30D5 }, { 0xABD6, 0x30D6 }, { 0xABD7, 0x30D7 }, { 0xABD8, 0x30D8 }, { 0xABD9, 0x30D9 }, { 0xABDA, 0x30DA }, { 0xABDB, 0x30DB },
	{ 0xABDC, 0x30DC }, { 0xABDD, 0x30DD }, { 0xABDE, 0x30DE }, { 0xABDF, 0x30DF }, { 0xABE0, 0x30E0 }, { 0xABE1, 0x30E1 }, { 0xABE2, 0x30E2 }, { 0xABE3, 0x30E3 },
	{ 0xABE4, 0x30E4 }, { 0xABE5, 0x30E5 }, { 0xABE6, 0x30E6 }, { 0xABE7, 0x30E7 }, { 0xABE8, 0x30E8 }, { 0xABE9, 0x30E9 }, { 0xABEA, 0x30EA }, { 0xABEB, 0x30EB },
	{ 0xABEC, 0x30EC }, { 0xABED, 0x30ED }, { 0xABEE, 0x30EE }, { 0xABEF, 0x30EF }, { 0xABF0, 0x30F0 }, { 0xABF1, 0x30F1 }, { 0xABF2, 0x30F2 }, { 0xABF3, 0x30F3 },
	{ 0xABF4, 0x30F4 }, { 0xABF5, 0x30F5 }, { 0xABF6, 0x30F6 }, { 0xAC41, 0x21F0 }, { 0xAC42, 0xF843 }, { 0xAC43, 0x27B5 }, { 0xAC48, 0x27A4 }, { 0xAC49, 0xF844 },
	{ 0xAC4A, 0xF84B }, { 0xAC4B, 0xF84A }, { 0xAC50, 0x21B6 }, { 0xAC51, 0x21B7 }, { 0xAC52, 0x219D }, { 0xAC53, 0x219C }, { 0xAC54, 0xF842 }, { 0xAC5E, 0x2794 },
	{ 0xAC5F, 0xF845 }, { 0xAC6A, 0x21E0 }, { 0xAC6B, 0x21E2 }, { 0xAC6C, 0x21E1 }, { 0xAC6D, 0x21E3 }, { 0xAC72, 0x21E6 }, { 0xAC73, 0x21E8 }, { 0xAC74, 0x21E7 },
	{ 0xAC75, 0x21E9 }, { 0xAC8D, 0x261D }, { 0xAC8E, 0x261F }, { 0xACA1, 0x0410 }, { 0xACA2, 0x0411 }, { 0xACA3, 0x0412 }, { 0xACA4, 0x0413 }, { 0xACA5, 0x0414 },
	{ 0xACA6, 0x0415 }, { 0xACA7, 0x0401 }, { 0xACA8, 0x0416 }, { 0xACA9, 0x0417 }, { 0xACAA, 0x0418 }, { 0xACAB, 0x0419 }, { 0xACAC, 0x041A }, { 0xACAD, 0x041B },
	{ 0xACAE, 0x041C }, { 0xACAF, 0x041D }, { 0xACB0, 0x041E }, { 0xACB1, 0x041F }, { 0xACB2, 0x0420 }, { 0xACB3, 0x0421 }, { 0xACB4, 0x0422 }, { 0xACB5, 0x0423 },
	{ 0xACB6, 0x0424 }, { 0xACB7, 0x0425 }, { 0xACB8, 0x0426 }, { 0xACB9, 0x0427 }, { 0xACBA, 0x0428 }, { 0xACBB, 0x0429 }, { 0xACBC, 0x042A }, { 0xACBD, 0x042B },
	{ 0xACBE, 0x042C }, { 0xACBF, 0x042D }, { 0xACC0, 0x042E }, { 0xACC1, 0x042F }, { 0xACD1, 0x0430 }, { 0xACD2, 0x0431 }, { 0xACD3, 0x0432 }, { 0xACD4, 0x0433 },
	{ 0xACD5, 0x0434 }, { 0xACD6, 0x0435 }, { 0xACD7, 0x0451 }, { 0xACD8, 0x0436 }, { 0xACD9, 0x0437 }, { 0xACDA, 0x0438 }, { 0xACDB, 0x0439 }, { 0xACDC, 0x043A },
	{ 0xACDD, 0x043B }, { 0xACDE, 0x043C }, { 0xACDF, 0x043D }, { 0xACE0, 0x043E }, { 0xACE1, 0x043F }, { 0xACE2, 0x0440 }, { 0xACE3, 0x0441 }, { 0xACE4, 0x0442 },
	{ 0xACE5, 0x0443 }, { 0xACE6, 0x0444 }, { 0xACE7, 0x0445 }, { 0xACE8, 0x0446 }, { 0xACE9, 0x0447 }, { 0xACEA, 0x0448 }, { 0xACEB, 0x0449 }, { 0xACEC, 0x044A },
	{ 0xACED, 0x044B }, { 0xACEE, 0x044C }, { 0xACEF, 0x044D }, { 0xACF0, 0x044E }, { 0xACF1, 0x044F }, { 0xAD70, 0x3290 }, { 0xAD71, 0x328A }, { 0xAD72, 0x328B },
	{ 0xAD73, 0x328C }, { 0xAD74, 0x328D }, { 0xAD75, 0x328E }, { 0xAD76, 0x328F }, { 0xADA9, 0x301E }, { 0xADAA, 0x301F }, { 0xADAB, 0x2036 }, { 0xADAD, 0x2035 },
	{ 0xB0A1, 0xAC00 }, { 0xB0A2, 0xAC01 }, { 0xB0A3, 0xAC04 }, { 0xB0A4, 0xAC07 }, { 0xB0A5, 0xAC08 }, { 0xB0A6, 0xAC09 }, { 0xB0A7, 0xAC0A }, { 0xB0A8, 0xAC10 },
	{ 0xB0A9, 0xAC11 }, { 0xB0AA, 0xAC12 }, { 0xB0AB, 0xAC13 }, { 0xB0AC, 0xAC14 }, { 0xB0AD, 0xAC15 }, { 0xB0AE, 0xAC16 }, { 0xB0AF, 0xAC17 }, { 0xB0B0, 0xAC19 },
	{ 0xB0B1, 0xAC1A }, { 0xB0B2, 0xAC1B }, { 0xB0B3, 0xAC1C }, { 0xB0B4, 0xAC1D }, { 0xB0B5, 0xAC20 }, { 0xB0B6, 0xAC24 }, { 0xB0B7, 0xAC2C }, { 0xB0B8, 0xAC2D },
	{ 0xB0B9, 0xAC2F }, { 0xB0BA, 0xAC30 }, { 0xB0BB, 0xAC31 }, { 0xB0BC, 0xAC38 }, { 0xB0BD, 0xAC39 }, { 0xB0BE, 0xAC3C }, { 0xB0BF, 0xAC40 }, { 0xB0C0, 0xAC4B },
	{ 0xB0C1, 0xAC4D }, { 0xB0C2, 0xAC54 }, { 0xB0C3, 0xAC58 }, { 0xB0C4, 0xAC5C }, { 0xB0C5, 0xAC70 }, { 0xB0C6, 0xAC71 }, { 0xB0C7, 0xAC74 }, { 0xB0C8, 0xAC77 },
	{ 0xB0C9, 0xAC78 }, { 0xB0CA, 0xAC7A }, { 0xB0CB, 0xAC80 }, { 0xB0CC, 0xAC81 }, { 0xB0CD, 0xAC83 }, { 0xB0CE, 0xAC84 }, { 0xB0CF, 0xAC85 }, { 0xB0D0, 0xAC86 },
	{ 0xB0D1, 0xAC89 }, { 0xB0D2, 0xAC8A }, { 0xB0D3, 0xAC8B }, { 0xB0D4, 0xAC8C }, { 0xB0D5, 0xAC90 }, { 0xB0D6, 0xAC94 }, { 0xB0D7, 0xAC9C }, { 0xB0D8, 0xAC9D },
	{ 0xB0D9, 0xAC9F }, { 0xB0DA, 0xACA0 }, { 0xB0DB, 0xACA1 }, { 0xB0DC, 0xACA8 }, { 0xB0DD, 0xACA9 }, { 0xB0DE, 0xACAA }, { 0xB0DF, 0xACAC }, { 0xB0E0, 0xACAF },
	{ 0xB0E1, 0xACB0 }, { 0xB0E2, 0xACB8 }, { 0xB0E3, 0xACB9 }, { 0xB0E4, 0xACBB }, { 0xB0E5, 0xACBC }, { 0xB0E6, 0xACBD }, { 0xB0E7, 0xACC1 }, { 0xB0E8, 0xACC4 },
	{ 0xB0E9, 0xACC8 }, { 0xB0EA, 0xACCC }, { 0xB0EB, 0xACD5 }, { 0xB0EC, 0xACD7 }, { 0xB0ED, 0xACE0 }, { 0xB0EE, 0xACE1 }, { 0xB0EF, 0xACE4 }, { 0xB0F0, 0xACE7 },
	{ 0xB0F1, 0xACE8 }, { 0xB0F2, 0xACEA }, { 0xB0F3, 0xACEC }, { 0xB0F4, 0xACEF }, { 0xB0F5, 0xACF0 }, { 0xB0F6, 0xACF1 }, { 0xB0F7, 0xACF3 }, { 0xB0F8, 0xACF5 },
	{ 0xB0F9, 0xACF6 }, { 0xB0FA, 0xACFC }, { 0xB0FB, 0xACFD }, { 0xB0FC, 0xAD00 }, { 0xB0FD, 0xAD04 }, { 0xB0FE, 0xAD06 }, { 0xB1A1, 0xAD0C }, { 0xB1A2, 0xAD0D },
	{ 0xB1A3, 0xAD0F }, { 0xB1A4, 0xAD11 }, { 0xB1A5, 0xAD18 }, { 0xB1A6, 0xAD1C }, { 0xB1A7, 0xAD20 }, { 0xB1A8, 0xAD29 }, { 0xB1A9, 0xAD2C }, { 0xB1AA, 0xAD2D },
	{ 0xB1AB, 0xAD34 }, { 0xB1AC, 0xAD35 }, { 0xB1AD, 0xAD38 }, { 0xB1AE, 0xAD3C }, { 0xB1AF, 0xAD44 }, { 0xB1B0, 0xAD45 }, { 0xB1B1, 0xAD47 }, { 0xB1B2, 0xAD49 },
	{ 0xB1B3, 0xAD50 }, { 0xB1B4, 0xAD54 }, { 0xB1B5, 0xAD58 }, { 0xB1B6, 0xAD61 }, { 0xB1B7, 0xAD63 }, { 0xB1B8, 0xAD6C }, { 0xB1B9, 0xAD6D }, { 0xB1BA, 0xAD70 },
	{ 0xB1BB, 0xAD73 }, { 0xB1BC, 0xAD74 }, { 0xB1BD, 0xAD75 }, { 0xB1BE, 0xAD76 }, { 0xB1BF, 0xAD7B }, { 0xB1C0, 0xAD7C }, { 0xB1C1, 0xAD7D }, { 0xB1C2, 0xAD7F },
	{ 0xB1C3, 0xAD81 }, { 0xB1C4, 0xAD82 }, { 0xB1C5, 0xAD88 }, { 0xB1C6, 0xAD89 }, { 0xB1C7, 0xAD8C }, { 0xB1C8, 0xAD90 }, { 0xB1C9, 0xAD9C }, { 0xB1CA, 0xAD9D },
	{ 0xB1CB, 0xADA4 }, { 0xB1CC, 0xADB7 }, { 0xB1CD, 0xADC0 }, { 0xB1CE, 0xADC1 }, { 0xB1CF, 0xADC4 }, { 0xB1D0, 0xADC8 }, { 0xB1D1, 0xADD0 }, { 0xB1D2, 0xADD1 },
	{ 0xB1D3, 0xADD3 }, { 0xB1D4, 0xADDC }, { 0xB1D5, 0xADE0 }, { 0xB1D6, 0xADE4 }, { 0xB1D7, 0xADF8 }, { 0xB1D8, 0xADF9 }, { 0xB1D9, 0xADFC }, { 0xB1DA, 0xADFF },
	{ 0xB1DB, 0xAE00 }, { 0xB1DC, 0xAE01 }, { 0xB1DD, 0xAE08 }, { 0xB1DE, 0xAE09 }, { 0xB1DF, 0xAE0B }, { 0xB1E0, 0xAE0D }, { 0xB1E1, 0xAE14 }, { 0xB1E2, 0xAE30 },
	{ 0xB1E3, 0xAE31 }, { 0xB1E4, 0xAE34 }, { 0xB1E5, 0xAE37 }, { 0xB1E6, 0xAE38 }, { 0xB1E7, 0xAE3A }, { 0xB1E8, 0xAE40 }, { 0xB1E9, 0xAE41 }, { 0xB1EA, 0xAE43 },
	{ 0xB1EB, 0xAE45 }, { 0xB1EC, 0xAE46 }, { 0xB1ED, 0xAE4A }, { 0xB1EE, 0xAE4C }, { 0xB1EF, 0xAE4D }, { 0xB1F0, 0xAE4E }, { 0xB1F1, 0xAE50 }, { 0xB1F2, 0xAE54 },
	{ 0xB1F3, 0xAE56 }, { 0xB1F4, 0xAE5C }, { 0xB1F5, 0xAE5D }, { 0xB1F6, 0xAE5F }, { 0xB1F7, 0xAE60 }, { 0xB1F8, 0xAE61 }, { 0xB1F9, 0xAE65 }, { 0xB1FA, 0xAE68 },
	{ 0xB1FB, 0xAE69 }, { 0xB1FC, 0xAE6C }, { 0xB1FD, 0xAE70 }, { 0xB1FE, 0xAE78 }, { 0xB2A1, 0xAE79 }, { 0xB2A2, 0xAE7B }, { 0xB2A3, 0xAE7C }, { 0xB2A4, 0xAE7D },
	{ 0xB2A5, 0xAE84 }, { 0xB2A6, 0xAE85 }, { 0xB2A7, 0xAE8C }, { 0xB2A8, 0xAEBC }, { 0xB2A9, 0xAEBD }, { 0xB2AA, 0xAEBE }, { 0xB2AB, 0xAEC0 }, { 0xB2AC, 0xAEC4 },
	{ 0xB2AD, 0xAECC }, { 0xB2AE, 0xAECD }, { 0xB2AF, 0xAECF }, { 0xB2B0, 0xAED0 }, { 0xB2B1, 0xAED1 }, { 0xB2B2, 0xAED8 }, { 0xB2B3, 0xAED9 }, { 0xB2B4, 0xAEDC },
	{ 0xB2B5, 0xAEE8 }, { 0xB2B6, 0xAEEB }, { 0xB2B7, 0xAEED }, { 0xB2B8, 0xAEF4 }, { 0xB2B9, 0xAEF8 }, { 0xB2BA, 0xAEFC }, { 0xB2BB, 0xAF07 }, { 0xB2BC, 0xAF08 },
	{ 0xB2BD, 0xAF0D }, { 0xB2BE, 0xAF10 }, { 0xB2BF, 0xAF2C }, { 0xB2C0, 0xAF2D }, { 0xB2C1, 0xAF30 }, { 0xB2C2, 0xAF32 }, { 0xB2C3, 0xAF34 }, { 0xB2C4, 0xAF3C },
	{ 0xB2C5, 0xAF3D }, { 0xB2C6, 0xAF3F }, { 0xB2C7, 0xAF41 }, { 0xB2C8, 0xAF42 }, { 0xB2C9, 0xAF43 }, { 0xB2CA, 0xAF48 }, { 0xB2CB, 0xAF49 }, { 0xB2CC, 0xAF50 },
	{ 0xB2CD, 0xAF5C }, { 0xB2CE, 0xAF5D }, { 0xB2CF, 0xAF64 }, { 0xB2D0, 0xAF65 }, { 0xB2D1, 0xAF79 }, { 0xB2D2, 0xAF80 }, { 0xB2D3, 0xAF84 }, { 0xB2D4, 0xAF88 },
	{ 0xB2D5, 0xAF90 }, { 0xB2D6, 0xAF91 }, { 0xB2D7, 0xAF95 }, { 0xB2D8, 0xAF9C }, { 0xB2D9, 0xAFB8 }, { 0xB2DA, 0xAFB9 }, { 0xB2DB, 0xAFBC }, { 0xB2DC, 0xAFC0 },
	{ 0xB2DD, 0xAFC7 }, { 0xB2DE, 0xAFC8 }, { 0xB2DF, 0xAFC9 }, { 0xB2E0, 0xAFCB }, { 0xB2E1, 0xAFCD }, { 0xB2E2, 0xAFCE }, { 0xB2E3, 0xAFD4 }, { 0xB2E4, 0xAFDC },
	{ 0xB2E5, 0xAFE8 }, { 0xB2E6, 0xAFE9 }, { 0xB2E7, 0xAFF0 }, { 0xB2E8, 0xAFF1 }, { 0xB2E9, 0xAFF4 }, { 0xB2EA, 0xAFF8 }, { 0xB2EB, 0xB000 }, { 0xB2EC, 0xB001 },
	{ 0xB2ED, 0xB004 }, { 0xB2EE, 0xB00C }, { 0xB2EF, 0xB010 }, { 0xB2F0, 0xB014 }, { 0xB2F1, 0xB01C }, { 0xB2F2, 0xB01D }, { 0xB2F3, 0xB028 }, { 0xB2F4, 0xB044 },
	{ 0xB2F5, 0xB045 }, { 0xB2F6, 0xB048 }, { 0xB2F7, 0xB04A }, { 0xB2F8, 0xB04C }, { 0xB2F9, 0xB04E }, { 0xB2FA, 0xB053 }, { 0xB2FB, 0xB054 }, { 0xB2FC, 0xB055 },
	{ 0xB2FD, 0xB057 }, { 0xB2FE, 0xB059 }, { 0xB3A1, 0xB05D }, { 0xB3A2, 0xB07C }, { 0xB3A3, 0xB07D }, { 0xB3A4, 0xB080 }, { 0xB3A5, 0xB084 }, { 0xB3A6, 0xB08C },
	{ 0xB3A7, 0xB08D }, { 0xB3A8, 0xB08F }, { 0xB3A9, 0xB091 }, { 0xB3AA, 0xB098 }, { 0xB3AB, 0xB099 }, { 0xB3AC, 0xB09A }, { 0xB3AD, 0xB09C }, { 0xB3AE, 0xB09F },
	{ 0xB3AF, 0xB0A0 }, { 0xB3B0, 0xB0A1 }, { 0xB3B1, 0xB0A2 }, { 0xB3B2, 0xB0A8 }, { 0xB3B3, 0xB0A9 }, { 0xB3B4, 0xB0AB }, { 0xB3B5, 0xB0AC }, { 0xB3B6, 0xB0AD },
	{ 0xB3B7, 0xB0AE }, { 0xB3B8, 0xB0AF }, { 0xB3B9, 0xB0B1 }, { 0xB3BA, 0xB0B3 }, { 0xB3BB, 0xB0B4 }, { 0xB3BC, 0xB0B5 }, { 0xB3BD, 0xB0B8 }, { 0xB3BE, 0xB0BC },
	{ 0xB3BF, 0xB0C4 }, { 0xB3C0, 0xB0C5 }, { 0xB3C1, 0xB0C7 }, { 0xB3C2, 0xB0C8 }, { 0xB3C3, 0xB0C9 }, { 0xB3C4, 0xB0D0 }, { 0xB3C5, 0xB0D1 }, { 0xB3C6, 0xB0D4 },
	{ 0xB3C7, 0xB0D8 }, { 0xB3C8, 0xB0E0 }, { 0xB3C9, 0xB0E5 }, { 0xB3CA, 0xB108 }, { 0xB3CB, 0xB109 }, { 0xB3CC, 0xB10B }, { 0xB3CD, 0xB10C }, { 0xB3CE, 0xB110 },
	{ 0xB3CF, 0xB112 }, { 0xB3D0, 0xB113 }, { 0xB3D1, 0xB118 }, { 0xB3D2, 0xB119 }, { 0xB3D3, 0xB11B }, { 0xB3D4, 0xB11C }, { 0xB3D5, 0xB11D }, { 0xB3D6, 0xB123 },
	{ 0xB3D7, 0xB124 }, { 0xB3D8, 0xB125 }, { 0xB3D9, 0xB128 }, { 0xB3DA, 0xB12C }, { 0xB3DB, 0xB134 }, { 0xB3DC, 0xB135 }, { 0xB3DD, 0xB137 }, { 0xB3DE, 0xB138 },
	{ 0xB3DF, 0xB139 }, { 0xB3E0, 0xB140 }, { 0xB3E1, 0xB141 }, { 0xB3E2, 0xB144 }, { 0xB3E3, 0xB148 }, { 0xB3E4, 0xB150 }, { 0xB3E5, 0xB151 }, { 0xB3E6, 0xB154 },
	{ 0xB3E7, 0xB155 }, { 0xB3E8, 0xB158 }, { 0xB3E9, 0xB15C }, { 0xB3EA, 0xB160 }, { 0xB3EB, 0xB178 }, { 0xB3EC, 0xB179 }, { 0xB3ED, 0xB17C }, { 0xB3EE, 0xB180 },
	{ 0xB3EF, 0xB182 }, { 0xB3F0, 0xB188 }, { 0xB3F1, 0xB189 }, { 0xB3F2, 0xB18B }, { 0xB3F3, 0xB18D }, { 0xB3F4, 0xB192 }, { 0xB3F5, 0xB193 }, { 0xB3F6, 0xB194 },
	{ 0xB3F7, 0xB198 }, { 0xB3F8, 0xB19C }, { 0xB3F9, 0xB1A8 }, { 0xB3FA, 0xB1CC }, { 0xB3FB, 0xB1D0 }, { 0xB3FC, 0xB1D4 }, { 0xB3FD, 0xB1DC }, { 0xB3FE, 0xB1DD },
	{ 0xB4A1, 0xB1DF }, { 0xB4A2, 0xB1E8 }, { 0xB4A3, 0xB1E9 }, { 0xB4A4, 0xB1EC }, { 0xB4A5, 0xB1F0 }, { 0xB4A6, 0xB1F9 }, { 0xB4A7, 0xB1FB }, { 0xB4A8, 0xB1FD },
	{ 0xB4A9, 0xB204 }, { 0xB4AA, 0xB205 }, { 0xB4AB, 0xB208 }, { 0xB4AC, 0xB20B }, { 0xB4AD, 0xB20C }, { 0xB4AE, 0xB214 }, { 0xB4AF, 0xB215 }, { 0xB4B0, 0xB217 },
	{ 0xB4B1, 0xB219 }, { 0xB4B2, 0xB220 }, { 0xB4B3, 0xB234 }, { 0xB4B4, 0xB23C }, { 0xB4B5, 0xB258 }, { 0xB4B6, 0xB25C }, { 0xB4B7, 0xB260 }, { 0xB4B8, 0xB268 },
	{ 0xB4B9, 0xB269 }, { 0xB4BA, 0xB274 }, { 0xB4BB, 0xB275 }, { 0xB4BC, 0xB27C }, { 0xB4BD, 0xB284 }, { 0xB4BE, 0xB285 }, { 0xB4BF, 0xB289 }, { 0xB4C0, 0xB290 },
	{ 0xB4C1, 0xB291 }, { 0xB4C2, 0xB294 }, { 0xB4C3, 0xB298 }, { 0xB4C4, 0xB299 }, { 0xB4C5, 0xB29A }, { 0xB4C6, 0xB2A0 }, { 0xB4C7, 0xB2A1 }, { 0xB4C8, 0xB2A3 },
	{ 0xB4C9, 0xB2A5 }, { 0xB4CA, 0xB2A6 }, { 0xB4CB, 0xB2AA }, { 0xB4CC, 0xB2AC }, { 0xB4CD, 0xB2B0 }, { 0xB4CE, 0xB2B4 }, { 0xB4CF, 0xB2C8 }, { 0xB4D0, 0xB2C9 },
	{ 0xB4D1, 0xB2CC }, { 0xB4D2, 0xB2D0 }, { 0xB4D3, 0xB2D2 }, { 0xB4D4, 0xB2D8 }, { 0xB4D5, 0xB2D9 }, { 0xB4D6, 0xB2DB }, { 0xB4D7, 0xB2DD }, { 0xB4D8, 0xB2E2 },
	{ 0xB4D9, 0xB2E4 }, { 0xB4DA, 0xB2E5 }, { 0xB4DB, 0xB2E6 }, { 0xB4DC, 0xB2E8 }, { 0xB4DD, 0xB2EB }, { 0xB4DE, 0xB2EC }, { 0xB4DF, 0xB2ED }, { 0xB4E0, 0xB2EE },
	{ 0xB4E1, 0xB2EF }, { 0xB4E2, 0xB2F3 }, { 0xB4E3, 0xB2F4 }, { 0xB4E4, 0xB2F5 }, { 0xB4E5, 0xB2F7 }, { 0xB4E6, 0xB2F8 }, { 0xB4E7, 0xB2F9 }, { 0xB4E8, 0xB2FA },
	{ 0xB4E9, 0xB2FB }, { 0xB4EA, 0xB2FF }, { 0xB4EB, 0xB300 }, { 0xB4EC, 0xB301 }, { 0xB4ED, 0xB304 }, { 0xB4EE, 0xB308 }, { 0xB4EF, 0xB310 }, { 0xB4F0, 0xB311 },
	{ 0xB4F1, 0xB313 }, { 0xB4F2, 0xB314 }, { 0xB4F3, 0xB315 }, { 0xB4F4, 0xB31C }, { 0xB4F5, 0xB354 }, { 0xB4F6, 0xB355 }, { 0xB4F7, 0xB356 }, { 0xB4F8, 0xB358 },
	{ 0xB4F9, 0xB35B }, { 0xB4FA, 0xB35C }, { 0xB4FB, 0xB35E }, { 0xB4FC, 0xB35F }, { 0xB4FD, 0xB364 }, { 0xB4FE, 0xB365 }, { 0xB5A1, 0xB367 }, { 0xB5A2, 0xB369 },
	{ 0xB5A3, 0xB36B }, { 0xB5A4, 0xB36E }, { 0xB5A5, 0xB370 }, { 0xB5A6, 0xB371 }, { 0xB5A7, 0xB374 }, { 0xB5A8, 0xB378 }, { 0xB5A9, 0xB380 }, { 0xB5AA, 0xB381 },
	{ 0xB5AB, 0xB383 }, { 0xB5AC, 0xB384 }, { 0xB5AD, 0xB385 }, { 0xB5AE, 0xB38C }, { 0xB5AF, 0xB390 }, { 0xB5B0, 0xB394 }, { 0xB5B1, 0xB3A0 }, { 0xB5B2, 0xB3A1 },
	{ 0xB5B3, 0xB3A8 }, { 0xB5B4, 0xB3AC }, { 0xB5B5, 0xB3C4 }, { 0xB5B6, 0xB3C5 }, { 0xB5B7, 0xB3C8 }, { 0xB5B8, 0xB3CB }, { 0xB5B9, 0xB3CC }, { 0xB5BA, 0xB3CE },
	{ 0xB5BB, 0xB3D0 }, { 0xB5BC, 0xB3D4 }, { 0xB5BD, 0xB3D5 }, { 0xB5BE, 0xB3D7 }, { 0xB5BF, 0xB3D9 }, { 0xB5C0, 0xB3DB }, { 0xB5C1, 0xB3DD }, { 0xB5C2, 0xB3E0 },
	{ 0xB5C3, 0xB3E4 }, { 0xB5C4, 0xB3E8 }, { 0xB5C5, 0xB3FC }, { 0xB5C6, 0xB410 }, { 0xB5C7, 0xB418 }, { 0xB5C8, 0xB41C }, { 0xB5C9, 0xB420 }, { 0xB5CA, 0xB428 },
	{ 0xB5CB, 0xB429 }, { 0xB5CC, 0xB42B }, { 0xB5CD, 0xB434 }, { 0xB5CE, 0xB450 }, { 0xB5CF, 0xB451 }, { 0xB5D0, 0xB454 }, { 0xB5D1, 0xB458 }, { 0xB5D2, 0xB460 },
	{ 0xB5D3, 0xB461 }, { 0xB5D4, 0xB463 }, { 0xB5D5, 0xB465 }, { 0xB5D6, 0xB46C }, { 0xB5D7, 0xB480 }, { 0xB5D8, 0xB488 }, { 0xB5D9, 0xB49D }, { 0xB5DA, 0xB4A4 },
	{ 0xB5DB, 0xB4A8 }, { 0xB5DC, 0xB4AC }, { 0xB5DD, 0xB4B5 }, { 0xB5DE, 0xB4B7 }, { 0xB5DF, 0xB4B9 }, { 0xB5E0, 0xB4C0 }, { 0xB5E1, 0xB4C4 }, { 0xB5E2, 0xB4C8 },
	{ 0xB5E3, 0xB4D0 }, { 0xB5E4, 0xB4D5 }, { 0xB5E5, 0xB4DC }, { 0xB5E6, 0xB4DD }, { 0xB5E7, 0xB4E0 }, { 0xB5E8, 0xB4E3 }, { 0xB5E9, 0xB4E4 }, { 0xB5EA, 0xB4E6 },
	{ 0xB5EB, 0xB4EC }, { 0xB5EC, 0xB4ED }, { 0xB5ED, 0xB4EF }, { 0xB5EE, 0xB4F1 }, { 0xB5EF, 0xB4F8 }, { 0xB5F0, 0xB514 }, { 0xB5F1, 0xB515 }, { 0xB5F2, 0xB518 },
	{ 0xB5F3, 0xB51B }, { 0xB5F4, 0xB51C }, { 0xB5F5, 0xB524 }, { 0xB5F6, 0xB525 }, { 0xB5F7, 0xB527 }, { 0xB5F8, 0xB528 }, { 0xB5F9, 0xB529 }, { 0xB5FA, 0xB52A },
	{ 0xB5FB, 0xB530 }, { 0xB5FC, 0xB531 }, { 0xB5FD, 0xB534 }, { 0xB5FE, 0xB538 }, { 0xB6A1, 0xB540 }, { 0xB6A2, 0xB541 }, { 0xB6A3, 0xB543 }, { 0xB6A4, 0xB544 },
	{ 0xB6A5, 0xB545 }, { 0xB6A6, 0xB54B }, { 0xB6A7, 0xB54C }, { 0xB6A8, 0xB54D }, { 0xB6A9, 0xB550 }, { 0xB6AA, 0xB554 }, { 0xB6AB, 0xB55C }, { 0xB6AC, 0xB55D },
	{ 0xB6AD, 0xB55F }, { 0xB6AE, 0xB560 }, { 0xB6AF, 0xB561 }, { 0xB6B0, 0xB5A0 }, { 0xB6B1, 0xB5A1 }, { 0xB6B2, 0xB5A4 }, { 0xB6B3, 0xB5A8 }, { 0xB6B4, 0xB5AA },
	{ 0xB6B5, 0xB5AB }, { 0xB6B6, 0xB5B0 }, { 0xB6B7, 0xB5B1 }, { 0xB6B8, 0xB5B3 }, { 0xB6B9, 0xB5B4 }, { 0xB6BA, 0xB5B5 }, { 0xB6BB, 0xB5BB }, { 0xB6BC, 0xB5BC },
	{ 0xB6BD, 0xB5BD }, { 0xB6BE, 0xB5C0 }, { 0xB6BF, 0xB5C4 }, { 0xB6C0, 0xB5CC }, { 0xB6C1, 0xB5CD }, { 0xB6C2, 0xB5CF }, { 0xB6C3, 0xB5D0 }, { 0xB6C4, 0xB5D1 },
	{ 0xB6C5, 0xB5D8 }, { 0xB6C6, 0xB5EC }, { 0xB6C7, 0xB610 }, { 0xB6C8, 0xB611 }, { 0xB6C9, 0xB614 }, { 0xB6CA, 0xB618 }, { 0xB6CB, 0xB625 }, { 0xB6CC, 0xB62C },
	{ 0xB6CD, 0xB634 }, { 0xB6CE, 0xB648 }, { 0xB6CF, 0xB664 }, { 0xB6D0, 0xB668 }, { 0xB6D1, 0xB69C }, { 0xB6D2, 0xB69D }, { 0xB6D3, 0xB6A0 }, { 0xB6D4, 0xB6A4 },
	{ 0xB6D5, 0xB6AB }, { 0xB6D6, 0xB6AC }, { 0xB6D7, 0xB6B1 }, { 0xB6D8, 0xB6D4 }, { 0xB6D9, 0xB6F0 }, { 0xB6DA, 0xB6F4 }, { 0xB6DB, 0xB6F8 }, { 0xB6DC, 0xB700 },
	{ 0xB6DD, 0xB701 }, { 0xB6DE, 0xB705 }, { 0xB6DF, 0xB728 }, { 0xB6E0, 0xB729 }, { 0xB6E1, 0xB72C }, { 0xB6E2, 0xB72F }, { 0xB6E3, 0xB730 }, { 0xB6E4, 0xB738 },
	{ 0xB6E5, 0xB739 }, { 0xB6E6, 0xB73B }, { 0xB6E7, 0xB744 }, { 0xB6E8, 0xB748 }, { 0xB6E9, 0xB74C }, { 0xB6EA, 0xB754 }, { 0xB6EB, 0xB755 }, { 0xB6EC, 0xB760 },
	{ 0xB6ED, 0xB764 }, { 0xB6EE, 0xB768 }, { 0xB6EF, 0xB770 }, { 0xB6F0, 0xB771 }, { 0xB6F1, 0xB773 }, { 0xB6F2, 0xB775 }, { 0xB6F3, 0xB77C }, { 0xB6F4, 0xB77D },
	{ 0xB6F5, 0xB780 }, { 0xB6F6, 0xB784 }, { 0xB6F7, 0xB78C }, { 0xB6F8, 0xB78D }, { 0xB6F9, 0xB78F }, { 0xB6FA, 0xB790 }, { 0xB6FB, 0xB791 }, { 0xB6FC, 0xB792 },
	{ 0xB6FD, 0xB796 }, { 0xB6FE, 0xB797 }, { 0xB7A1, 0xB798 }, { 0xB7A2, 0xB799 }, { 0xB7A3, 0xB79C }, { 0xB7A4, 0xB7A0 }, { 0xB7A5, 0xB7A8 }, { 0xB7A6, 0xB7A9 },
	{ 0xB7A7, 0xB7AB }, { 0xB7A8, 0xB7AC }, { 0xB7A9, 0xB7AD }, { 0xB7AA, 0xB7B4 }, { 0xB7AB, 0xB7B5 }, { 0xB7AC, 0xB7B8 }, { 0xB7AD, 0xB7C7 }, { 0xB7AE, 0xB7C9 },
	{ 0xB7AF, 0xB7EC }, { 0xB7B0, 0xB7ED }, { 0xB7B1, 0xB7F0 }, { 0xB7B2, 0xB7F4 }, { 0xB7B3, 0xB7FC }, { 0xB7B4, 0xB7FD }, { 0xB7B5, 0xB7FF }, { 0xB7B6, 0xB800 },
	{ 0xB7B7, 0xB801 }, { 0xB7B8, 0xB807 }, { 0xB7B9, 0xB808 }, { 0xB7BA, 0xB809 }, { 0xB7BB, 0xB80C }, { 0xB7BC, 0xB810 }, { 0xB7BD, 0xB818 }, { 0xB7BE, 0xB819 },
	{ 0xB7BF, 0xB81B }, { 0xB7C0, 0xB81D }, { 0xB7C1, 0xB824 }, { 0xB7C2, 0xB825 }, { 0xB7C3, 0xB828 }, { 0xB7C4, 0xB82C }, { 0xB7C5, 0xB834 }, { 0xB7C6, 0xB835 },
	{ 0xB7C7, 0xB837 }, { 0xB7C8, 0xB838 }, { 0xB7C9, 0xB839 }, { 0xB7CA, 0xB840 }, { 0xB7CB, 0xB844 }, { 0xB7CC, 0xB851 }, { 0xB7CD, 0xB853 }, { 0xB7CE, 0xB85C },
	{ 0xB7CF, 0xB85D }, { 0xB7D0, 0xB860 }, { 0xB7D1, 0xB864 }, { 0xB7D2, 0xB86C }, { 0xB7D3, 0xB86D }, { 0xB7D4, 0xB86F }, { 0xB7D5, 0xB871 }, { 0xB7D6, 0xB878 },
	{ 0xB7D7, 0xB87C }, { 0xB7D8, 0xB88D }, { 0xB7D9, 0xB8A8 }, { 0xB7DA, 0xB8B0 }, { 0xB7DB, 0xB8B4 }, { 0xB7DC, 0xB8B8 }, { 0xB7DD, 0xB8C0 }, { 0xB7DE, 0xB8C1 },
	{ 0xB7DF, 0xB8C3 }, { 0xB7E0, 0xB8C5 }, { 0xB7E1, 0xB8CC }, { 0xB7E2, 0xB8D0 }, { 0xB7E3, 0xB8D4 }, { 0xB7E4, 0xB8DD }, { 0xB7E5, 0xB8DF }, { 0xB7E6, 0xB8E1 },
	{ 0xB7E7, 0xB8E8 }, { 0xB7E8, 0xB8E9 }, { 0xB7E9, 0xB8EC }, { 0xB7EA, 0xB8F0 }, { 0xB7EB, 0xB8F8 }, { 0xB7EC, 0xB8F9 }, { 0xB7ED, 0xB8FB }, { 0xB7EE, 0xB8FD },
	{ 0xB7EF, 0xB904 }, { 0xB7F0, 0xB918 }, { 0xB7F1, 0xB920 }, { 0xB7F2, 0xB93C }, { 0xB7F3, 0xB93D }, { 0xB7F4, 0xB940 }, { 0xB7F5, 0xB944 }, { 0xB7F6, 0xB94C },
	{ 0xB7F7, 0xB94F }, { 0xB7F8, 0xB951 }, { 0xB7F9, 0xB958 }, { 0xB7FA, 0xB959 }, { 0xB7FB, 0xB95C }, { 0xB7FC, 0xB960 }, { 0xB7FD, 0xB968 }, { 0xB7FE, 0xB969 },
	{ 0xB8A1, 0xB96B }, { 0xB8A2, 0xB96D }, { 0xB8A3, 0xB974 }, { 0xB8A4, 0xB975 }, { 0xB8A5, 0xB978 }, { 0xB8A6, 0xB97C }, { 0xB8A7, 0xB984 }, { 0xB8A8, 0xB985 },
	{ 0xB8A9, 0xB987 }, { 0xB8AA, 0xB989 }, { 0xB8AB, 0xB98A }, { 0xB8AC, 0xB98D }, { 0xB8AD, 0xB98E }, { 0xB8AE, 0xB9AC }, { 0xB8AF, 0xB9AD }, { 0xB8B0, 0xB9B0 },
	{ 0xB8B1, 0xB9B4 }, { 0xB8B2, 0xB9BC }, { 0xB8B3, 0xB9BD }, { 0xB8B4, 0xB9BF }, { 0xB8B5, 0xB9C1 }, { 0xB8B6, 0xB9C8 }, { 0xB8B7, 0xB9C9 }, { 0xB8B8, 0xB9CC },
	{ 0xB8B9, 0xB9CE }, { 0xB8BA, 0xB9CF }, { 0xB8BB, 0xB9D0 }, { 0xB8BC, 0xB9D1 }, { 0xB8BD, 0xB9D2 }, { 0xB8BE, 0xB9D8 }, { 0xB8BF, 0xB9D9 }, { 0xB8C0, 0xB9DB },
	{ 0xB8C1, 0xB9DD }, { 0xB8C2, 0xB9DE }, { 0xB8C3, 0xB9E1 }, { 0xB8C4, 0xB9E3 }, { 0xB8C5, 0xB9E4 }, { 0xB8C6, 0xB9E5 }, { 0xB8C7, 0xB9E8 }, { 0xB8C8, 0xB9EC },
	{ 0xB8C9, 0xB9F4 }, { 0xB8CA, 0xB9F5 }, { 0xB8CB, 0xB9F7 }, { 0xB8CC, 0xB9F8 }, { 0xB8CD, 0xB9F9 }, { 0xB8CE, 0xB9FA }, { 0xB8CF, 0xBA00 }, { 0xB8D0, 0xBA01 },
	{ 0xB8D1, 0xBA08 }, { 0xB8D2, 0xBA15 }, { 0xB8D3, 0xBA38 }, { 0xB8D4, 0xBA39 }, { 0xB8D5, 0xBA3C }, { 0xB8D6, 0xBA40 }, { 0xB8D7, 0xBA42 }, { 0xB8D8, 0xBA48 },
	{ 0xB8D9, 0xBA49 }, { 0xB8DA, 0xBA4B }, { 0xB8DB, 0xBA4D }, { 0xB8DC, 0xBA4E }, { 0xB8DD, 0xBA53 }, { 0xB8DE, 0xBA54 }, { 0xB8DF, 0xBA55 }, { 0xB8E0, 0xBA58 },
	{ 0xB8E1, 0xBA5C }, { 0xB8E2, 0xBA64 }, { 0xB8E3, 0xBA65 }, { 0xB8E4, 0xBA67 }, { 0xB8E5, 0xBA68 }, { 0xB8E6, 0xBA69 }, { 0xB8E7, 0xBA70 }, { 0xB8E8, 0xBA71 },
	{ 0xB8E9, 0xBA74 }, { 0xB8EA, 0xBA78 }, { 0xB8EB, 0xBA83 }, { 0xB8EC, 0xBA84 }, { 0xB8ED, 0xBA85 }, { 0xB8EE, 0xBA87 }, { 0xB8EF, 0xBA8C }, { 0xB8F0, 0xBAA8 },
	{ 0xB8F1, 0xBAA9 }, { 0xB8F2, 0xBAAB }, { 0xB8F3, 0xBAAC }, { 0xB8F4, 0xBAB0 }, { 0xB8F5, 0xBAB2 }, { 0xB8F6, 0xBAB8 }, { 0xB8F7, 0xBAB9 }, { 0xB8F8, 0xBABB },
	{ 0xB8F9, 0xBABD }, { 0xB8FA, 0xBAC4 }, { 0xB8FB, 0xBAC8 }, { 0xB8FC, 0xBAD8 }, { 0xB8FD, 0xBAD9 }, { 0xB8FE, 0xBAFC }, { 0xB9A1, 0xBB00 }, { 0xB9A2, 0xBB04 },
	{ 0xB9A3, 0xBB0D }, { 0xB9A4, 0xBB0F }, { 0xB9A5, 0xBB11 }, { 0xB9A6, 0xBB18 }, { 0xB9A7, 0xBB1C }, { 0xB9A8, 0xBB20 }, { 0xB9A9, 0xBB29 }, { 0xB9AA, 0xBB2B },
	{ 0xB9AB, 0xBB34 }, { 0xB9AC, 0xBB35 }, { 0xB9AD, 0xBB36 }, { 0xB9AE, 0xBB38 }, { 0xB9AF, 0xBB3B }, { 0xB9B0, 0xBB3C }, { 0xB9B1, 0xBB3D }, { 0xB9B2, 0xBB3E },
	{ 0xB9B3, 0xBB44 }, { 0xB9B4, 0xBB45 }, { 0xB9B5, 0xBB47 }, { 0xB9B6, 0xBB49 }, { 0xB9B7, 0xBB4D }, { 0xB9B8, 0xBB4F }, { 0xB9B9, 0xBB50 }, { 0xB9BA, 0xBB54 },
	{ 0xB9BB, 0xBB58 }, { 0xB9BC, 0xBB61 }, { 0xB9BD, 0xBB63 }, { 0xB9BE, 0xBB6C }, { 0xB9BF, 0xBB88 }, { 0xB9C0, 0xBB8C }, { 0xB9C1, 0xBB90 }, { 0xB9C2, 0xBBA4 },
	{ 0xB9C3, 0xBBA8 }, { 0xB9C4, 0xBBAC }, { 0xB9C5, 0xBBB4 }, { 0xB9C6, 0xBBB7 }, { 0xB9C7, 0xBBC0 }, { 0xB9C8, 0xBBC4 }, { 0xB9C9, 0xBBC8 }, { 0xB9CA, 0xBBD0 },
	{ 0xB9CB, 0xBBD3 }, { 0xB9CC, 0xBBF8 }, { 0xB9CD, 0xBBF9 }, { 0xB9CE, 0xBBFC }, { 0xB9CF, 0xBBFF }, { 0xB9D0, 0xBC00 }, { 0xB9D1, 0xBC02 }, { 0xB9D2, 0xBC08 },
	{ 0xB9D3, 0xBC09 }, { 0xB9D4, 0xBC0B }, { 0xB9D5, 0xBC0C }, { 0xB9D6, 0xBC0D }, { 0xB9D7, 0xBC0F }, { 0xB9D8, 0xBC11 }, { 0xB9D9, 0xBC14 }, { 0xB9DA, 0xBC15 },
	{ 0xB9DB, 0xBC16 }, { 0xB9DC, 0xBC17 }, { 0xB9DD, 0xBC18 }, { 0xB9DE, 0xBC1B }, { 0xB9DF, 0xBC1C }, { 0xB9E0, 0xBC1D }, { 0xB9E1, 0xBC1E }, { 0xB9E2, 0xBC1F },
	{ 0xB9E3, 0xBC24 }, { 0xB9E4, 0xBC25 }, { 0xB9E5, 0xBC27 }, { 0xB9E6, 0xBC29 }, { 0xB9E7, 0xBC2D }, { 0xB9E8, 0xBC30 }, { 0xB9E9, 0xBC31 }, { 0xB9EA, 0xBC34 },
	{ 0xB9EB, 0xBC38 }, { 0xB9EC, 0xBC40 }, { 0xB9ED, 0xBC41 }, { 0xB9EE, 0xBC43 }, { 0xB9EF, 0xBC44 }, { 0xB9F0, 0xBC45 }, { 0xB9F1, 0xBC49 }, { 0xB9F2, 0xBC4C },
	{ 0xB9F3, 0xBC4D }, { 0xB9F4, 0xBC50 }, { 0xB9F5, 0xBC5D }, { 0xB9F6, 0xBC84 }, { 0xB9F7, 0xBC85 }, { 0xB9F8, 0xBC88 }, { 0xB9F9, 0xBC8B }, { 0xB9FA, 0xBC8C },
	{ 0xB9FB, 0xBC8E }, { 0xB9FC, 0xBC94 }, { 0xB9FD, 0xBC95 }, { 0xB9FE, 0xBC97 }, { 0xBAA1, 0xBC99 }, { 0xBAA2, 0xBC9A }, { 0xBAA3, 0xBCA0 }, { 0xBAA4, 0xBCA1 },
	{ 0xBAA5, 0xBCA4 }, { 0xBAA6, 0xBCA7 }, { 0xBAA7, 0xBCA8 }, { 0xBAA8, 0xBCB0 }, { 0xBAA9, 0xBCB1 }, { 0xBAAA, 0xBCB3 }, { 0xBAAB, 0xBCB4 }, { 0xBAAC, 0xBCB5 },
	{ 0xBAAD, 0xBCBC }, { 0xBAAE, 0xBCBD }, { 0xBAAF, 0xBCC0 }, { 0xBAB0, 0xBCC4 }, { 0xBAB1, 0xBCCD }, { 0xBAB2, 0xBCCF }, { 0xBAB3, 0xBCD0 }, { 0xBAB4, 0xBCD1 },
	{ 0xBAB5, 0xBCD5 }, { 0xBAB6, 0xBCD8 }, { 0xBAB7, 0xBCDC }, { 0xBAB8, 0xBCF4 }, { 0xBAB9, 0xBCF5 }, { 0xBABA, 0xBCF6 }, { 0xBABB, 0xBCF8 }, { 0xBABC, 0xBCFC },
	{ 0xBABD, 0xBD04 }, { 0xBABE, 0xBD05 }, { 0xBABF, 0xBD07 }, { 0xBAC0, 0xBD09 }, { 0xBAC1, 0xBD10 }, { 0xBAC2, 0xBD14 }, { 0xBAC3, 0xBD24 }, { 0xBAC4, 0xBD2C },
	{ 0xBAC5, 0xBD40 }, { 0xBAC6, 0xBD48 }, { 0xBAC7, 0xBD49 }, { 0xBAC8, 0xBD4C }, { 0xBAC9, 0xBD50 }, { 0xBACA, 0xBD58 }, { 0xBACB, 0xBD59 }, { 0xBACC, 0xBD64 },
	{ 0xBACD, 0xBD68 }, { 0xBACE, 0xBD80 }, { 0xBACF, 0xBD81 }, { 0xBAD0, 0xBD84 }, { 0xBAD1, 0xBD87 }, { 0xBAD2, 0xBD88 }, { 0xBAD3, 0xBD89 }, { 0xBAD4, 0xBD8A },
	{ 0xBAD5, 0xBD90 }, { 0xBAD6, 0xBD91 }, { 0xBAD7, 0xBD93 }, { 0xBAD8, 0xBD95 }, { 0xBAD9, 0xBD99 }, { 0xBADA, 0xBD9A }, { 0xBADB, 0xBD9C }, { 0xBADC, 0xBDA4 },
	{ 0xBADD, 0xBDB0 }, { 0xBADE, 0xBDB8 }, { 0xBADF, 0xBDD4 }, { 0xBAE0, 0xBDD5 }, { 0xBAE1, 0xBDD8 }, { 0xBAE2, 0xBDDC }, { 0xBAE3, 0xBDE9 }, { 0xBAE4, 0xBDF0 },
	{ 0xBAE5, 0xBDF4 }, { 0xBAE6, 0xBDF8 }, { 0xBAE7, 0xBE00 }, { 0xBAE8, 0xBE03 }, { 0xBAE9, 0xBE05 }, { 0xBAEA, 0xBE0C }, { 0xBAEB, 0xBE0D }, { 0xBAEC, 0xBE10 },
	{ 0xBAED, 0xBE14 }, { 0xBAEE, 0xBE1C }, { 0xBAEF, 0xBE1D }, { 0xBAF0, 0xBE1F }, { 0xBAF1, 0xBE44 }, { 0xBAF2, 0xBE45 }, { 0xBAF3, 0xBE48 }, { 0xBAF4, 0xBE4C },
	{ 0xBAF5, 0xBE4E }, { 0xBAF6, 0xBE54 }, { 0xBAF7, 0xBE55 }, { 0xBAF8, 0xBE57 }, { 0xBAF9, 0xBE59 }, { 0xBAFA, 0xBE5A }, { 0xBAFB, 0xBE5B }, { 0xBAFC, 0xBE60 },
	{ 0xBAFD, 0xBE61 }, { 0xBAFE, 0xBE64 }, { 0xBBA1, 0xBE68 }, { 0xBBA2, 0xBE6A }, { 0xBBA3, 0xBE70 }, { 0xBBA4, 0xBE71 }, { 0xBBA5, 0xBE73 }, { 0xBBA6, 0xBE74 },
	{ 0xBBA7, 0xBE75 }, { 0xBBA8, 0xBE7B }, { 0xBBA9, 0xBE7C }, { 0xBBAA, 0xBE7D }, { 0xBBAB, 0xBE80 }, { 0xBBAC, 0xBE84 }, { 0xBBAD, 0xBE8C }, { 0xBBAE, 0xBE8D },
	{ 0xBBAF, 0xBE8F }, { 0xBBB0, 0xBE90 }, { 0xBBB1, 0xBE91 }, { 0xBBB2, 0xBE98 }, { 0xBBB3, 0xBE99 }, { 0xBBB4, 0xBEA8 }, { 0xBBB5, 0xBED0 }, { 0xBBB6, 0xBED1 },
	{ 0xBBB7, 0xBED4 }, { 0xBBB8, 0xBED7 }, { 0xBBB9, 0xBED8 }, { 0xBBBA, 0xBEE0 }, { 0xBBBB, 0xBEE3 }, { 0xBBBC, 0xBEE4 }, { 0xBBBD, 0xBEE5 }, { 0xBBBE, 0xBEEC },
	{ 0xBBBF, 0xBF01 }, { 0xBBC0, 0xBF08 }, { 0xBBC1, 0xBF09 }, { 0xBBC2, 0xBF18 }, { 0xBBC3, 0xBF19 }, { 0xBBC4, 0xBF1B }, { 0xBBC5, 0xBF1C }, { 0xBBC6, 0xBF1D },
	{ 0xBBC7, 0xBF40 }, { 0xBBC8, 0xBF41 }, { 0xBBC9, 0xBF44 }, { 0xBBCA, 0xBF48 }, { 0xBBCB, 0xBF50 }, { 0xBBCC, 0xBF51 }, { 0xBBCD, 0xBF55 }, { 0xBBCE, 0xBF94 },
	{ 0xBBCF, 0xBFB0 }, { 0xBBD0, 0xBFC5 }, { 0xBBD1, 0xBFCC }, { 0xBBD2, 0xBFCD }, { 0xBBD3, 0xBFD0 }, { 0xBBD4, 0xBFD4 }, { 0xBBD5, 0xBFDC }, { 0xBBD6, 0xBFDF },
	{ 0xBBD7, 0xBFE1 }, { 0xBBD8, 0xC03C }, { 0xBBD9, 0xC051 }, { 0xBBDA, 0xC058 }, { 0xBBDB, 0xC05C }, { 0xBBDC, 0xC060 }, { 0xBBDD, 0xC068 }, { 0xBBDE, 0xC069 },
	{ 0xBBDF, 0xC090 }, { 0xBBE0, 0xC091 }, { 0xBBE1, 0xC094 }, { 0xBBE2, 0xC098 }, { 0xBBE3, 0xC0A0 }, { 0xBBE4, 0xC0A1 }, { 0xBBE5, 0xC0A3 }, { 0xBBE6, 0xC0A5 },
	{ 0xBBE7, 0xC0AC }, { 0xBBE8, 0xC0AD }, { 0xBBE9, 0xC0AF }, { 0xBBEA, 0xC0B0 }, { 0xBBEB, 0xC0B3 }, { 0xBBEC, 0xC0B4 }, { 0xBBED, 0xC0B5 }, { 0xBBEE, 0xC0B6 },
	{ 0xBBEF, 0xC0BC }, { 0xBBF0, 0xC0BD }, { 0xBBF1, 0xC0BF }, { 0xBBF2, 0xC0C0 }, { 0xBBF3, 0xC0C1 }, { 0xBBF4, 0xC0C5 }, { 0xBBF5, 0xC0C8 }, { 0xBBF6, 0xC0C9 },
	{ 0xBBF7, 0xC0CC }, { 0xBBF8, 0xC0D0 }, { 0xBBF9, 0xC0D8 }, { 0xBBFA, 0xC0D9 }, { 0xBBFB, 0xC0DB }, { 0xBBFC, 0xC0DC }, { 0xBBFD, 0xC0DD }, { 0xBBFE, 0xC0E4 },
	{ 0xBCA1, 0xC0E5 }, { 0xBCA2, 0xC0E8 }, { 0xBCA3, 0xC0EC }, { 0xBCA4, 0xC0F4 }, { 0xBCA5, 0xC0F5 }, { 0xBCA6, 0xC0F7 }, { 0xBCA7, 0xC0F9 }, { 0xBCA8, 0xC100 },
	{ 0xBCA9, 0xC104 }, { 0xBCAA, 0xC108 }, { 0xBCAB, 0xC110 }, { 0xBCAC, 0xC115 }, { 0xBCAD, 0xC11C }, { 0xBCAE, 0xC11D }, { 0xBCAF, 0xC11E }, { 0xBCB0, 0xC11F },
	{ 0xBCB1, 0xC120 }, { 0xBCB2, 0xC123 }, { 0xBCB3, 0xC124 }, { 0xBCB4, 0xC126 }, { 0xBCB5, 0xC127 }, { 0xBCB6, 0xC12C }, { 0xBCB7, 0xC12D }, { 0xBCB8, 0xC12F },
	{ 0xBCB9, 0xC130 }, { 0xBCBA, 0xC131 }, { 0xBCBB, 0xC136 }, { 0xBCBC, 0xC138 }, { 0xBCBD, 0xC139 }, { 0xBCBE, 0xC13C }, { 0xBCBF, 0xC140 }, { 0xBCC0, 0xC148 },
	{ 0xBCC1, 0xC149 }, { 0xBCC2, 0xC14B }, { 0xBCC3, 0xC14C }, { 0xBCC4, 0xC14D }, { 0xBCC5, 0xC154 }, { 0xBCC6, 0xC155 }, { 0xBCC7, 0xC158 }, { 0xBCC8, 0xC15C },
	{ 0xBCC9, 0xC164 }, { 0xBCCA, 0xC165 }, { 0xBCCB, 0xC167 }, { 0xBCCC, 0xC168 }, { 0xBCCD, 0xC169 }, { 0xBCCE, 0xC170 }, { 0xBCCF, 0xC174 }, { 0xBCD0, 0xC178 },
	{ 0xBCD1, 0xC185 }, { 0xBCD2, 0xC18C }, { 0xBCD3, 0xC18D }, { 0xBCD4, 0xC18E }, { 0xBCD5, 0xC190 }, { 0xBCD6, 0xC194 }, { 0xBCD7, 0xC196 }, { 0xBCD8, 0xC19C },
	{ 0xBCD9, 0xC19D }, { 0xBCDA, 0xC19F }, { 0xBCDB, 0xC1A1 }, { 0xBCDC, 0xC1A5 }, { 0xBCDD, 0xC1A8 }, { 0xBCDE, 0xC1A9 }, { 0xBCDF, 0xC1AC }, { 0xBCE0, 0xC1B0 },
	{ 0xBCE1, 0xC1BD }, { 0xBCE2, 0xC1C4 }, { 0xBCE3, 0xC1C8 }, { 0xBCE4, 0xC1CC }, { 0xBCE5, 0xC1D4 }, { 0xBCE6, 0xC1D7 }, { 0xBCE7, 0xC1D8 }, { 0xBCE8, 0xC1E0 },
	{ 0xBCE9, 0xC1E4 }, { 0xBCEA, 0xC1E8 }, { 0xBCEB, 0xC1F0 }, { 0xBCEC, 0xC1F1 }, { 0xBCED, 0xC1F3 }, { 0xBCEE, 0xC1FC }, { 0xBCEF, 0xC1FD }, { 0xBCF0, 0xC200 },
	{ 0xBCF1, 0xC204 }, { 0xBCF2, 0xC20C }, { 0xBCF3, 0xC20D }, { 0xBCF4, 0xC20F }, { 0xBCF5, 0xC211 }, { 0xBCF6, 0xC218 }, { 0xBCF7, 0xC219 }, { 0xBCF8, 0xC21C },
	{ 0xBCF9, 0xC21F }, { 0xBCFA, 0xC220 }, { 0xBCFB, 0xC228 }, { 0xBCFC, 0xC229 }, { 0xBCFD, 0xC22B }, { 0xBCFE, 0xC22D }, { 0xBDA1, 0xC22F }, { 0xBDA2, 0xC231 },
	{ 0xBDA3, 0xC232 }, { 0xBDA4, 0xC234 }, { 0xBDA5, 0xC248 }, { 0xBDA6, 0xC250 }, { 0xBDA7, 0xC251 }, { 0xBDA8, 0xC254 }, { 0xBDA9, 0xC258 }, { 0xBDAA, 0xC260 },
	{ 0xBDAB, 0xC265 }, { 0xBDAC, 0xC26C }, { 0xBDAD, 0xC26D }, { 0xBDAE, 0xC270 }, { 0xBDAF, 0xC274 }, { 0xBDB0, 0xC27C }, { 0xBDB1, 0xC27D }, { 0xBDB2, 0xC27F },
	{ 0xBDB3, 0xC281 }, { 0xBDB4, 0xC288 }, { 0xBDB5, 0xC289 }, { 0xBDB6, 0xC290 }, { 0xBDB7, 0xC298 }, { 0xBDB8, 0xC29B }, { 0xBDB9, 0xC29D }, { 0xBDBA, 0xC2A4 },
	{ 0xBDBB, 0xC2A5 }, { 0xBDBC, 0xC2A8 }, { 0xBDBD, 0xC2AC }, { 0xBDBE, 0xC2AD }, { 0xBDBF, 0xC2B4 }, { 0xBDC0, 0xC2B5 }, { 0xBDC1, 0xC2B7 }, { 0xBDC2, 0xC2B9 },
	{ 0xBDC3, 0xC2DC }, { 0xBDC4, 0xC2DD }, { 0xBDC5, 0xC2E0 }, { 0xBDC6, 0xC2E3 }, { 0xBDC7, 0xC2E4 }, { 0xBDC8, 0xC2EB }, { 0xBDC9, 0xC2EC }, { 0xBDCA, 0xC2ED },
	{ 0xBDCB, 0xC2EF }, { 0xBDCC, 0xC2F1 }, { 0xBDCD, 0xC2F6 }, { 0xBDCE, 0xC2F8 }, { 0xBDCF, 0xC2F9 }, { 0xBDD0, 0xC2FB }, { 0xBDD1, 0xC2FC }, { 0xBDD2, 0xC300 },
	{ 0xBDD3, 0xC308 }, { 0xBDD4, 0xC309 }, { 0xBDD5, 0xC30C }, { 0xBDD6, 0xC30D }, { 0xBDD7, 0xC313 }, { 0xBDD8, 0xC314 }, { 0xBDD9, 0xC315 }, { 0xBDDA, 0xC318 },
	{ 0xBDDB, 0xC31C }, { 0xBDDC, 0xC324 }, { 0xBDDD, 0xC325 }, { 0xBDDE, 0xC328 }, { 0xBDDF, 0xC329 }, { 0xBDE0, 0xC345 }, { 0xBDE1, 0xC368 }, { 0xBDE2, 0xC369 },
	{ 0xBDE3, 0xC36C }, { 0xBDE4, 0xC370 }, { 0xBDE5, 0xC372 }, { 0xBDE6, 0xC378 }, { 0xBDE7, 0xC379 }, { 0xBDE8, 0xC37C }, { 0xBDE9, 0xC37D }, { 0xBDEA, 0xC384 },
	{ 0xBDEB, 0xC388 }, { 0xBDEC, 0xC38C }, { 0xBDED, 0xC3C0 }, { 0xBDEE, 0xC3D8 }, { 0xBDEF, 0xC3D9 }, { 0xBDF0, 0xC3DC }, { 0xBDF1, 0xC3DF }, { 0xBDF2, 0xC3E0 },
	{ 0xBDF3, 0xC3E2 }, { 0xBDF4, 0xC3E8 }, { 0xBDF5, 0xC3E9 }, { 0xBDF6, 0xC3ED }, { 0xBDF7, 0xC3F4 }, { 0xBDF8, 0xC3F5 }, { 0xBDF9, 0xC3F8 }, { 0xBDFA, 0xC408 },
	{ 0xBDFB, 0xC410 }, { 0xBDFC, 0xC424 }, { 0xBDFD, 0xC42C }, { 0xBDFE, 0xC430 }, { 0xBEA1, 0xC434 }, { 0xBEA2, 0xC43C }, { 0xBEA3, 0xC43D }, { 0xBEA4, 0xC448 },
	{ 0xBEA5, 0xC464 }, { 0xBEA6, 0xC465 }, { 0xBEA7, 0xC468 }, { 0xBEA8, 0xC46C }, { 0xBEA9, 0xC474 }, { 0xBEAA, 0xC475 }, { 0xBEAB, 0xC479 }, { 0xBEAC, 0xC480 },
	{ 0xBEAD, 0xC494 }, { 0xBEAE, 0xC49C }, { 0xBEAF, 0xC4B8 }, { 0xBEB0, 0xC4BC }, { 0xBEB1, 0xC4E9 }, { 0xBEB2, 0xC4F0 }, { 0xBEB3, 0xC4F1 }, { 0xBEB4, 0xC4F4 },
	{ 0xBEB5, 0xC4F8 }, { 0xBEB6, 0xC4FA }, { 0xBEB7, 0xC4FF }, { 0xBEB8, 0xC500 }, { 0xBEB9, 0xC501 }, { 0xBEBA, 0xC50C }, { 0xBEBB, 0xC510 }, { 0xBEBC, 0xC514 },
	{ 0xBEBD, 0xC51C }, { 0xBEBE, 0xC528 }, { 0xBEBF, 0xC529 }, { 0xBEC0, 0xC52C }, { 0xBEC1, 0xC530 }, { 0xBEC2, 0xC538 }, { 0xBEC3, 0xC539 }, { 0xBEC4, 0xC53B },
	{ 0xBEC5, 0xC53D }, { 0xBEC6, 0xC544 }, { 0xBEC7, 0xC545 }, { 0xBEC8, 0xC548 }, { 0xBEC9, 0xC549 }, { 0xBECA, 0xC54A }, { 0xBECB, 0xC54C }, { 0xBECC, 0xC54D },
	{ 0xBECD, 0xC54E }, { 0xBECE, 0xC553 }, { 0xBECF, 0xC554 }, { 0xBED0, 0xC555 }, { 0xBED1, 0xC557 }, { 0xBED2, 0xC558 }, { 0xBED3, 0xC559 }, { 0xBED4, 0xC55D },
	{ 0xBED5, 0xC55E }, { 0xBED6, 0xC560 }, { 0xBED7, 0xC561 }, { 0xBED8, 0xC564 }, { 0xBED9, 0xC568 }, { 0xBEDA, 0xC570 }, { 0xBEDB, 0xC571 }, { 0xBEDC, 0xC573 },
	{ 0xBEDD, 0xC574 }, { 0xBEDE, 0xC575 }, { 0xBEDF, 0xC57C }, { 0xBEE0, 0xC57D }, { 0xBEE1, 0xC580 }, { 0xBEE2, 0xC584 }, { 0xBEE3, 0xC587 }, { 0xBEE4, 0xC58C },
	{ 0xBEE5, 0xC58D }, { 0xBEE6, 0xC58F }, { 0xBEE7, 0xC591 }, { 0xBEE8, 0xC595 }, { 0xBEE9, 0xC597 }, { 0xBEEA, 0xC598 }, { 0xBEEB, 0xC59C }, { 0xBEEC, 0xC5A0 },
	{ 0xBEED, 0xC5A9 }, { 0xBEEE, 0xC5B4 }, { 0xBEEF, 0xC5B5 }, { 0xBEF0, 0xC5B8 }, { 0xBEF1, 0xC5B9 }, { 0xBEF2, 0xC5BB }, { 0xBEF3, 0xC5BC }, { 0xBEF4, 0xC5BD },
	{ 0xBEF5, 0xC5BE }, { 0xBEF6, 0xC5C4 }, { 0xBEF7, 0xC5C5 }, { 0xBEF8, 0xC5C6 }, { 0xBEF9, 0xC5C7 }, { 0xBEFA, 0xC5C8 }, { 0xBEFB, 0xC5C9 }, { 0xBEFC, 0xC5CA },
	{ 0xBEFD, 0xC5CC }, { 0xBEFE, 0xC5CE }, { 0xBFA1, 0xC5D0 }, { 0xBFA2, 0xC5D1 }, { 0xBFA3, 0xC5D4 }, { 0xBFA4, 0xC5D8 }, { 0xBFA5, 0xC5E0 }, { 0xBFA6, 0xC5E1 },
	{ 0xBFA7, 0xC5E3 }, { 0xBFA8, 0xC5E5 }, { 0xBFA9, 0xC5EC }, { 0xBFAA, 0xC5ED }, { 0xBFAB, 0xC5EE }, { 0xBFAC, 0xC5F0 }, { 0xBFAD, 0xC5F4 }, { 0xBFAE, 0xC5F6 },
	{ 0xBFAF, 0xC5F7 }, { 0xBFB0, 0xC5FC }, { 0xBFB1, 0xC5FD }, { 0xBFB2, 0xC5FE }, { 0xBFB3, 0xC5FF }, { 0xBFB4, 0xC600 }, { 0xBFB5, 0xC601 }, { 0xBFB6, 0xC605 },
	{ 0xBFB7, 0xC606 }, { 0xBFB8, 0xC607 }, { 0xBFB9, 0xC608 }, { 0xBFBA, 0xC60C }, { 0xBFBB, 0xC610 }, { 0xBFBC, 0xC618 }, { 0xBFBD, 0xC619 }, { 0xBFBE, 0xC61B },
	{ 0xBFBF, 0xC61C }, { 0xBFC0, 0xC624 }, { 0xBFC1, 0xC625 }, { 0xBFC2, 0xC628 }, { 0xBFC3, 0xC62C }, { 0xBFC4, 0xC62D }, { 0xBFC5, 0xC62E }, { 0xBFC6, 0xC630 },
	{ 0xBFC7, 0xC633 }, { 0xBFC8, 0xC634 }, { 0xBFC9, 0xC635 }, { 0xBFCA, 0xC637 }, { 0xBFCB, 0xC639 }, { 0xBFCC, 0xC63B }, { 0xBFCD, 0xC640 }, { 0xBFCE, 0xC641 },
	{ 0xBFCF, 0xC644 }, { 0xBFD0, 0xC648 }, { 0xBFD1, 0xC650 }, { 0xBFD2, 0xC651 }, { 0xBFD3, 0xC653 }, { 0xBFD4, 0xC654 }, { 0xBFD5, 0xC655 }, { 0xBFD6, 0xC65C },
	{ 0xBFD7, 0xC65D }, { 0xBFD8, 0xC660 }, { 0xBFD9, 0xC66C }, { 0xBFDA, 0xC66F }, { 0xBFDB, 0xC671 }, { 0xBFDC, 0xC678 }, { 0xBFDD, 0xC679 }, { 0xBFDE, 0xC67C },
	{ 0xBFDF, 0xC680 }, { 0xBFE0, 0xC688 }, { 0xBFE1, 0xC689 }, { 0xBFE2, 0xC68B }, { 0xBFE3, 0xC68D }, { 0xBFE4, 0xC694 }, { 0xBFE5, 0xC695 }, { 0xBFE6, 0xC698 },
	{ 0xBFE7, 0xC69C }, { 0xBFE8, 0xC6A4 }, { 0xBFE9, 0xC6A5 }, { 0xBFEA, 0xC6A7 }, { 0xBFEB, 0xC6A9 }, { 0xBFEC, 0xC6B0 }, { 0xBFED, 0xC6B1 }, { 0xBFEE, 0xC6B4 },
	{ 0xBFEF, 0xC6B8 }, { 0xBFF0, 0xC6B9 }, { 0xBFF1, 0xC6BA }, { 0xBFF2, 0xC6C0 }, { 0xBFF3, 0xC6C1 }, { 0xBFF4, 0xC6C3 }, { 0xBFF5, 0xC6C5 }, { 0xBFF6, 0xC6CC },
	{ 0xBFF7, 0xC6CD }, { 0xBFF8, 0xC6D0 }, { 0xBFF9, 0xC6D4 }, { 0xBFFA, 0xC6DC }, { 0xBFFB, 0xC6DD }, { 0xBFFC, 0xC6E0 }, { 0xBFFD, 0xC6E1 }, { 0xBFFE, 0xC6E8 },
	{ 0xC0A1, 0xC6E9 }, { 0xC0A2, 0xC6EC }, { 0xC0A3, 0xC6F0 }, { 0xC0A4, 0xC6F8 }, { 0xC0A5, 0xC6F9 }, { 0xC0A6, 0xC6FD }, { 0xC0A7, 0xC704 }, { 0xC0A8, 0xC705 },
	{ 0xC0A9, 0xC708 }, { 0xC0AA, 0xC70C }, { 0xC0AB, 0xC714 }, { 0xC0AC, 0xC715 }, { 0xC0AD, 0xC717 }, { 0xC0AE, 0xC719 }, { 0xC0AF, 0xC720 }, { 0xC0B0, 0xC721 },
	{ 0xC0B1, 0xC724 }, { 0xC0B2, 0xC728 }, { 0xC0B3, 0xC730 }, { 0xC0B4, 0xC731 }, { 0xC0B5, 0xC733 }, { 0xC0B6, 0xC735 }, { 0xC0B7, 0xC737 }, { 0xC0B8, 0xC73C },
	{ 0xC0B9, 0xC73D }, { 0xC0BA, 0xC740 }, { 0xC0BB, 0xC744 }, { 0xC0BC, 0xC74A }, { 0xC0BD, 0xC74C }, { 0xC0BE, 0xC74D }, { 0xC0BF, 0xC74F }, { 0xC0C0, 0xC751 },
	{ 0xC0C1, 0xC752 }, { 0xC0C2, 0xC753 }, { 0xC0C3, 0xC754 }, { 0xC0C4, 0xC755 }, { 0xC0C5, 0xC756 }, { 0xC0C6, 0xC757 }, { 0xC0C7, 0xC758 }, { 0xC0C8, 0xC75C },
	{ 0xC0C9, 0xC760 }, { 0xC0CA, 0xC768 }, { 0xC0CB, 0xC76B }, { 0xC0CC, 0xC774 }, { 0xC0CD, 0xC775 }, { 0xC0CE, 0xC778 }, { 0xC0CF, 0xC77C }, { 0xC0D0, 0xC77D },
	{ 0xC0D1, 0xC77E }, { 0xC0D2, 0xC783 }, { 0xC0D3, 0xC784 }, { 0xC0D4, 0xC785 }, { 0xC0D5, 0xC787 }, { 0xC0D6, 0xC788 }, { 0xC0D7, 0xC789 }, { 0xC0D8, 0xC78A },
	{ 0xC0D9, 0xC78E }, { 0xC0DA, 0xC790 }, { 0xC0DB, 0xC791 }, { 0xC0DC, 0xC794 }, { 0xC0DD, 0xC796 }, { 0xC0DE, 0xC797 }, { 0xC0DF, 0xC798 }, { 0xC0E0, 0xC79A },
	{ 0xC0E1, 0xC7A0 }, { 0xC0E2, 0xC7A1 }, { 0xC0E3, 0xC7A3 }, { 0xC0E4, 0xC7A4 }, { 0xC0E5, 0xC7A5 }, { 0xC0E6, 0xC7A6 }, { 0xC0E7, 0xC7AC }, { 0xC0E8, 0xC7AD },
	{ 0xC0E9, 0xC7B0 }, { 0xC0EA, 0xC7B4 }, { 0xC0EB, 0xC7BC }, { 0xC0EC, 0xC7BD }, { 0xC0ED, 0xC7BF }, { 0xC0EE, 0xC7C0 }, { 0xC0EF, 0xC7C1 }, { 0xC0F0, 0xC7C8 },
	{ 0xC0F1, 0xC7C9 }, { 0xC0F2, 0xC7CC }, { 0xC0F3, 0xC7CE }, { 0xC0F4, 0xC7D0 }, { 0xC0F5, 0xC7D8 }, { 0xC0F6, 0xC7DD }, { 0xC0F7, 0xC7E4 }, { 0xC0F8, 0xC7E8 },
	{ 0xC0F9, 0xC7EC }, { 0xC0FA, 0xC800 }, { 0xC0FB, 0xC801 }, { 0xC0FC, 0xC804 }, { 0xC0FD, 0xC808 }, { 0xC0FE, 0xC80A }, { 0xC1A1, 0xC810 }, { 0xC1A2, 0xC811 },
	{ 0xC1A3, 0xC813 }, { 0xC1A4, 0xC815 }, { 0xC1A5, 0xC816 }, { 0xC1A6, 0xC81C }, { 0xC1A7, 0xC81D }, { 0xC1A8, 0xC820 }, { 0xC1A9, 0xC824 }, { 0xC1AA, 0xC82C },
	{ 0xC1AB, 0xC82D }, { 0xC1AC, 0xC82F }, { 0xC1AD, 0xC831 }, { 0xC1AE, 0xC838 }, { 0xC1AF, 0xC83C }, { 0xC1B0, 0xC840 }, { 0xC1B1, 0xC848 }, { 0xC1B2, 0xC849 },
	{ 0xC1B3, 0xC84C }, { 0xC1B4, 0xC84D }, { 0xC1B5, 0xC854 }, { 0xC1B6, 0xC870 }, { 0xC1B7, 0xC871 }, { 0xC1B8, 0xC874 }, { 0xC1B9, 0xC878 }, { 0xC1BA, 0xC87A },
	{ 0xC1BB, 0xC880 }, { 0xC1BC, 0xC881 }, { 0xC1BD, 0xC883 }, { 0xC1BE, 0xC885 }, { 0xC1BF, 0xC886 }, { 0xC1C0, 0xC887 }, { 0xC1C1, 0xC88B }, { 0xC1C2, 0xC88C },
	{ 0xC1C3, 0xC88D }, { 0xC1C4, 0xC894 }, { 0xC1C5, 0xC89D }, { 0xC1C6, 0xC89F }, { 0xC1C7, 0xC8A1 }, { 0xC1C8, 0xC8A8 }, { 0xC1C9, 0xC8BC }, { 0xC1CA, 0xC8BD },
	{ 0xC1CB, 0xC8C4 }, { 0xC1CC, 0xC8C8 }, { 0xC1CD, 0xC8CC }, { 0xC1CE, 0xC8D4 }, { 0xC1CF, 0xC8D5 }, { 0xC1D0, 0xC8D7 }, { 0xC1D1, 0xC8D9 }, { 0xC1D2, 0xC8E0 },
	{ 0xC1D3, 0xC8E1 }, { 0xC1D4, 0xC8E4 }, { 0xC1D5, 0xC8F5 }, { 0xC1D6, 0xC8FC }, { 0xC1D7, 0xC8FD }, { 0xC1D8, 0xC900 }, { 0xC1D9, 0xC904 }, { 0xC1DA, 0xC905 },
	{ 0xC1DB, 0xC906 }, { 0xC1DC, 0xC90C }, { 0xC1DD, 0xC90D }, { 0xC1DE, 0xC90F }, { 0xC1DF, 0xC911 }, { 0xC1E0, 0xC918 }, { 0xC1E1, 0xC92C }, { 0xC1E2, 0xC934 },
	{ 0xC1E3, 0xC950 }, { 0xC1E4, 0xC951 }, { 0xC1E5, 0xC954 }, { 0xC1E6, 0xC958 }, { 0xC1E7, 0xC960 }, { 0xC1E8, 0xC961 }, { 0xC1E9, 0xC963 }, { 0xC1EA, 0xC96C },
	{ 0xC1EB, 0xC970 }, { 0xC1EC, 0xC974 }, { 0xC1ED, 0xC97C }, { 0xC1EE, 0xC988 }, { 0xC1EF, 0xC989 }, { 0xC1F0, 0xC98C }, { 0xC1F1, 0xC990 }, { 0xC1F2, 0xC998 },
	{ 0xC1F3, 0xC999 }, { 0xC1F4, 0xC99B }, { 0xC1F5, 0xC99D }, { 0xC1F6, 0xC9C0 }, { 0xC1F7, 0xC9C1 }, { 0xC1F8, 0xC9C4 }, { 0xC1F9, 0xC9C7 }, { 0xC1FA, 0xC9C8 },
	{ 0xC1FB, 0xC9CA }, { 0xC1FC, 0xC9D0 }, { 0xC1FD, 0xC9D1 }, { 0xC1FE, 0xC9D3 }, { 0xC2A1, 0xC9D5 }, { 0xC2A2, 0xC9D6 }, { 0xC2A3, 0xC9D9 }, { 0xC2A4, 0xC9DA },
	{ 0xC2A5, 0xC9DC }, { 0xC2A6, 0xC9DD }, { 0xC2A7, 0xC9E0 }, { 0xC2A8, 0xC9E2 }, { 0xC2A9, 0xC9E4 }, { 0xC2AA, 0xC9E7 }, { 0xC2AB, 0xC9EC }, { 0xC2AC, 0xC9ED },
	{ 0xC2AD, 0xC9EF }, { 0xC2AE, 0xC9F0 }, { 0xC2AF, 0xC9F1 }, { 0xC2B0, 0xC9F8 }, { 0xC2B1, 0xC9F9 }, { 0xC2B2, 0xC9FC }, { 0xC2B3, 0xCA00 }, { 0xC2B4, 0xCA08 },
	{ 0xC2B5, 0xCA09 }, { 0xC2B6, 0xCA0B }, { 0xC2B7, 0xCA0C }, { 0xC2B8, 0xCA0D }, { 0xC2B9, 0xCA14 }, { 0xC2BA, 0xCA18 }, { 0xC2BB, 0xCA29 }, { 0xC2BC, 0xCA4C },
	{ 0xC2BD, 0xCA4D }, { 0xC2BE, 0xCA50 }, { 0xC2BF, 0xCA54 }, { 0xC2C0, 0xCA5C }, { 0xC2C1, 0xCA5D }, { 0xC2C2, 0xCA5F }, { 0xC2C3, 0xCA60 }, { 0xC2C4, 0xCA61 },
	{ 0xC2C5, 0xCA68 }, { 0xC2C6, 0xCA7D }, { 0xC2C7, 0xCA84 }, { 0xC2C8, 0xCA98 }, { 0xC2C9, 0xCABC }, { 0xC2CA, 0xCABD }, { 0xC2CB, 0xCAC0 }, { 0xC2CC, 0xCAC4 },
	{ 0xC2CD, 0xCACC }, { 0xC2CE, 0xCACD }, { 0xC2CF, 0xCACF }, { 0xC2D0, 0xCAD1 }, { 0xC2D1, 0xCAD3 }, { 0xC2D2, 0xCAD8 }, { 0xC2D3, 0xCAD9 }, { 0xC2D4, 0xCAE0 },
	{ 0xC2D5, 0xCAEC }, { 0xC2D6, 0xCAF4 }, { 0xC2D7, 0xCB08 }, { 0xC2D8, 0xCB10 }, { 0xC2D9, 0xCB14 }, { 0xC2DA, 0xCB18 }, { 0xC2DB, 0xCB20 }, { 0xC2DC, 0xCB21 },
	{ 0xC2DD, 0xCB41 }, { 0xC2DE, 0xCB48 }, { 0xC2DF, 0xCB49 }, { 0xC2E0, 0xCB4C }, { 0xC2E1, 0xCB50 }, { 0xC2E2, 0xCB58 }, { 0xC2E3, 0xCB59 }, { 0xC2E4, 0xCB5D },
	{ 0xC2E5, 0xCB64 }, { 0xC2E6, 0xCB78 }, { 0xC2E7, 0xCB79 }, { 0xC2E8, 0xCB9C }, { 0xC2E9, 0xCBB8 }, { 0xC2EA, 0xCBD4 }, { 0xC2EB, 0xCBE4 }, { 0xC2EC, 0xCBE7 },
	{ 0xC2ED, 0xCBE9 }, { 0xC2EE, 0xCC0C }, { 0xC2EF, 0xCC0D }, { 0xC2F0, 0xCC10 }, { 0xC2F1, 0xCC14 }, { 0xC2F2, 0xCC1C }, { 0xC2F3, 0xCC1D }, { 0xC2F4, 0xCC21 },
	{ 0xC2F5, 0xCC22 }, { 0xC2F6, 0xCC27 }, { 0xC2F7, 0xCC28 }, { 0xC2F8, 0xCC29 }, { 0xC2F9, 0xCC2C }, { 0xC2FA, 0xCC2E }, { 0xC2FB, 0xCC30 }, { 0xC2FC, 0xCC38 },
	{ 0xC2FD, 0xCC39 }, { 0xC2FE, 0xCC3B }, { 0xC3A1, 0xCC3C }, { 0xC3A2, 0xCC3D }, { 0xC3A3, 0xCC3E }, { 0xC3A4, 0xCC44 }, { 0xC3A5, 0xCC45 }, { 0xC3A6, 0xCC48 },
	{ 0xC3A7, 0xCC4C }, { 0xC3A8, 0xCC54 }, { 0xC3A9, 0xCC55 }, { 0xC3AA, 0xCC57 }, { 0xC3AB, 0xCC58 }, { 0xC3AC, 0xCC59 }, { 0xC3AD, 0xCC60 }, { 0xC3AE, 0xCC64 },
	{ 0xC3AF, 0xCC66 }, { 0xC3B0, 0xCC68 }, { 0xC3B1, 0xCC70 }, { 0xC3B2, 0xCC75 }, { 0xC3B3, 0xCC98 }, { 0xC3B4, 0xCC99 }, { 0xC3B5, 0xCC9C }, { 0xC3B6, 0xCCA0 },
	{ 0xC3B7, 0xCCA8 }, { 0xC3B8, 0xCCA9 }, { 0xC3B9, 0xCCAB }, { 0xC3BA, 0xCCAC }, { 0xC3BB, 0xCCAD }, { 0xC3BC, 0xCCB4 }, { 0xC3BD, 0xCCB5 }, { 0xC3BE, 0xCCB8 },
	{ 0xC3BF, 0xCCBC }, { 0xC3C0, 0xCCC4 }, { 0xC3C1, 0xCCC5 }, { 0xC3C2, 0xCCC7 }, { 0xC3C3, 0xCCC9 }, { 0xC3C4, 0xCCD0 }, { 0xC3C5, 0xCCD4 }, { 0xC3C6, 0xCCE4 },
	{ 0xC3C7, 0xCCEC }, { 0xC3C8, 0xCCF0 }, { 0xC3C9, 0xCD01 }, { 0xC3CA, 0xCD08 }, { 0xC3CB, 0xCD09 }, { 0xC3CC, 0xCD0C }, { 0xC3CD, 0xCD10 }, { 0xC3CE, 0xCD18 },
	{ 0xC3CF, 0xCD19 }, { 0xC3D0, 0xCD1B }, { 0xC3D1, 0xCD1D }, { 0xC3D2, 0xCD24 }, { 0xC3D3, 0xCD28 }, { 0xC3D4, 0xCD2C }, { 0xC3D5, 0xCD39 }, { 0xC3D6, 0xCD5C },
	{ 0xC3D7, 0xCD60 }, { 0xC3D8, 0xCD64 }, { 0xC3D9, 0xCD6C }, { 0xC3DA, 0xCD6D }, { 0xC3DB, 0xCD6F }, { 0xC3DC, 0xCD71 }, { 0xC3DD, 0xCD78 }, { 0xC3DE, 0xCD88 },
	{ 0xC3DF, 0xCD94 }, { 0xC3E0, 0xCD95 }, { 0xC3E1, 0xCD98 }, { 0xC3E2, 0xCD9C }, { 0xC3E3, 0xCDA4 }, { 0xC3E4, 0xCDA5 }, { 0xC3E5, 0xCDA7 }, { 0xC3E6, 0xCDA9 },
	{ 0xC3E7, 0xCDB0 }, { 0xC3E8, 0xCDC4 }, { 0xC3E9, 0xCDCC }, { 0xC3EA, 0xCDD0 }, { 0xC3EB, 0xCDE8 }, { 0xC3EC, 0xCDEC }, { 0xC3ED, 0xCDF0 }, { 0xC3EE, 0xCDF8 },
	{ 0xC3EF, 0xCDF9 }, { 0xC3F0, 0xCDFB }, { 0xC3F1, 0xCDFD }, { 0xC3F2, 0xCE04 }, { 0xC3F3, 0xCE08 }, { 0xC3F4, 0xCE0C }, { 0xC3F5, 0xCE14 }, { 0xC3F6, 0xCE19 },
	{ 0xC3F7, 0xCE20 }, { 0xC3F8, 0xCE21 }, { 0xC3F9, 0xCE24 }, { 0xC3FA, 0xCE28 }, { 0xC3FB, 0xCE30 }, { 0xC3FC, 0xCE31 }, { 0xC3FD, 0xCE33 }, { 0xC3FE, 0xCE35 },
	{ 0xC4A1, 0xCE58 }, { 0xC4A2, 0xCE59 }, { 0xC4A3, 0xCE5C }, { 0xC4A4, 0xCE5F }, { 0xC4A5, 0xCE60 }, { 0xC4A6, 0xCE61 }, { 0xC4A7, 0xCE68 }, { 0xC4A8, 0xCE69 },
	{ 0xC4A9, 0xCE6B }, { 0xC4AA, 0xCE6D }, { 0xC4AB, 0xCE74 }, { 0xC4AC, 0xCE75 }, { 0xC4AD, 0xCE78 }, { 0xC4AE, 0xCE7C }, { 0xC4AF, 0xCE84 }, { 0xC4B0, 0xCE85 },
	{ 0xC4B1, 0xCE87 }, { 0xC4B2, 0xCE89 }, { 0xC4B3, 0xCE90 }, { 0xC4B4, 0xCE91 }, { 0xC4B5, 0xCE94 }, { 0xC4B6, 0xCE98 }, { 0xC4B7, 0xCEA0 }, { 0xC4B8, 0xCEA1 },
	{ 0xC4B9, 0xCEA3 }, { 0xC4BA, 0xCEA4 }, { 0xC4BB, 0xCEA5 }, { 0xC4BC, 0xCEAC }, { 0xC4BD, 0xCEAD }, { 0xC4BE, 0xCEC1 }, { 0xC4BF, 0xCEE4 }, { 0xC4C0, 0xCEE5 },
	{ 0xC4C1, 0xCEE8 }, { 0xC4C2, 0xCEEB }, { 0xC4C3, 0xCEEC }, { 0xC4C4, 0xCEF4 }, { 0xC4C5, 0xCEF5 }, { 0xC4C6, 0xCEF7 }, { 0xC4C7, 0xCEF8 }, { 0xC4C8, 0xCEF9 },
	{ 0xC4C9, 0xCF00 }, { 0xC4CA, 0xCF01 }, { 0xC4CB, 0xCF04 }, { 0xC4CC, 0xCF08 }, { 0xC4CD, 0xCF10 }, { 0xC4CE, 0xCF11 }, { 0xC4CF, 0xCF13 }, { 0xC4D0, 0xCF15 },
	{ 0xC4D1, 0xCF1C }, { 0xC4D2, 0xCF20 }, { 0xC4D3, 0xCF24 }, { 0xC4D4, 0xCF2C }, { 0xC4D5, 0xCF2D }, { 0xC4D6, 0xCF2F }, { 0xC4D7, 0xCF30 }, { 0xC4D8, 0xCF31 },
	{ 0xC4D9, 0xCF38 }, { 0xC4DA, 0xCF54 }, { 0xC4DB, 0xCF55 }, { 0xC4DC, 0xCF58 }, { 0xC4DD, 0xCF5C }, { 0xC4DE, 0xCF64 }, { 0xC4DF, 0xCF65 }, { 0xC4E0, 0xCF67 },
	{ 0xC4E1, 0xCF69 }, { 0xC4E2, 0xCF70 }, { 0xC4E3, 0xCF71 }, { 0xC4E4, 0xCF74 }, { 0xC4E5, 0xCF78 }, { 0xC4E6, 0xCF80 }, { 0xC4E7, 0xCF85 }, { 0xC4E8, 0xCF8C },
	{ 0xC4E9, 0xCFA1 }, { 0xC4EA, 0xCFA8 }, { 0xC4EB, 0xCFB0 }, { 0xC4EC, 0xCFC4 }, { 0xC4ED, 0xCFE0 }, { 0xC4EE, 0xCFE1 }, { 0xC4EF, 0xCFE4 }, { 0xC4F0, 0xCFE8 },
	{ 0xC4F1, 0xCFF0 }, { 0xC4F2, 0xCFF1 }, { 0xC4F3, 0xCFF3 }, { 0xC4F4, 0xCFF5 }, { 0xC4F5, 0xCFFC }, { 0xC4F6, 0xD000 }, { 0xC4F7, 0xD004 }, { 0xC4F8, 0xD011 },
	{ 0xC4F9, 0xD018 }, { 0xC4FA, 0xD02D }, { 0xC4FB, 0xD034 }, { 0xC4FC, 0xD035 }, { 0xC4FD, 0xD038 }, { 0xC4FE, 0xD03C }, { 0xC5A1, 0xD044 }, { 0xC5A2, 0xD045 },
	{ 0xC5A3, 0xD047 }, { 0xC5A4, 0xD049 }, { 0xC5A5, 0xD050 }, { 0xC5A6, 0xD054 }, { 0xC5A7, 0xD058 }, { 0xC5A8, 0xD060 }, { 0xC5A9, 0xD06C }, { 0xC5AA, 0xD06D },
	{ 0xC5AB, 0xD070 }, { 0xC5AC, 0xD074 }, { 0xC5AD, 0xD07C }, { 0xC5AE, 0xD07D }, { 0xC5AF, 0xD081 }, { 0xC5B0, 0xD0A4 }, { 0xC5B1, 0xD0A5 }, { 0xC5B2, 0xD0A8 },
	{ 0xC5B3, 0xD0AC }, { 0xC5B4, 0xD0B4 }, { 0xC5B5, 0xD0B5 }, { 0xC5B6, 0xD0B7 }, { 0xC5B7, 0xD0B9 }, { 0xC5B8, 0xD0C0 }, { 0xC5B9, 0xD0C1 }, { 0xC5BA, 0xD0C4 },
	{ 0xC5BB, 0xD0C8 }, { 0xC5BC, 0xD0C9 }, { 0xC5BD, 0xD0D0 }, { 0xC5BE, 0xD0D1 }, { 0xC5BF, 0xD0D3 }, { 0xC5C0, 0xD0D4 }, { 0xC5C1, 0xD0D5 }, { 0xC5C2, 0xD0DC },
	{ 0xC5C3, 0xD0DD }, { 0xC5C4, 0xD0E0 }, { 0xC5C5, 0xD0E4 }, { 0xC5C6, 0xD0EC }, { 0xC5C7, 0xD0ED }, { 0xC5C8, 0xD0EF }, { 0xC5C9, 0xD0F0 }, { 0xC5CA, 0xD0F1 },
	{ 0xC5CB, 0xD0F8 }, { 0xC5CC, 0xD10D }, { 0xC5CD, 0xD130 }, { 0xC5CE, 0xD131 }, { 0xC5CF, 0xD134 }, { 0xC5D0, 0xD138 }, { 0xC5D1, 0xD13A }, { 0xC5D2, 0xD140 },
	{ 0xC5D3, 0xD141 }, { 0xC5D4, 0xD143 }, { 0xC5D5, 0xD144 }, { 0xC5D6, 0xD145 }, { 0xC5D7, 0xD14C }, { 0xC5D8, 0xD14D }, { 0xC5D9, 0xD150 }, { 0xC5DA, 0xD154 },
	{ 0xC5DB, 0xD15C }, { 0xC5DC, 0xD15D }, { 0xC5DD, 0xD15F }, { 0xC5DE, 0xD161 }, { 0xC5DF, 0xD168 }, { 0xC5E0, 0xD16C }, { 0xC5E1, 0xD17C }, { 0xC5E2, 0xD184 },
	{ 0xC5E3, 0xD188 }, { 0xC5E4, 0xD1A0 }, { 0xC5E5, 0xD1A1 }, { 0xC5E6, 0xD1A4 }, { 0xC5E7, 0xD1A8 }, { 0xC5E8, 0xD1B0 }, { 0xC5E9, 0xD1B1 }, { 0xC5EA, 0xD1B3 },
	{ 0xC5EB, 0xD1B5 }, { 0xC5EC, 0xD1BA }, { 0xC5ED, 0xD1BC }, { 0xC5EE, 0xD1C0 }, { 0xC5EF, 0xD1D8 }, { 0xC5F0, 0xD1F4 }, { 0xC5F1, 0xD1F8 }, { 0xC5F2, 0xD207 },
	{ 0xC5F3, 0xD209 }, { 0xC5F4, 0xD210 }, { 0xC5F5, 0xD22C }, { 0xC5F6, 0xD22D }, { 0xC5F7, 0xD230 }, { 0xC5F8, 0xD234 }, { 0xC5F9, 0xD23C }, { 0xC5FA, 0xD23D },
	{ 0xC5FB, 0xD23F }, { 0xC5FC, 0xD241 }, { 0xC5FD, 0xD248 }, { 0xC5FE, 0xD25C }, { 0xC6A1, 0xD264 }, { 0xC6A2, 0xD280 }, { 0xC6A3, 0xD281 }, { 0xC6A4, 0xD284 },
	{ 0xC6A5, 0xD288 }, { 0xC6A6, 0xD290 }, { 0xC6A7, 0xD291 }, { 0xC6A8, 0xD295 }, { 0xC6A9, 0xD29C }, { 0xC6AA, 0xD2A0 }, { 0xC6AB, 0xD2A4 }, { 0xC6AC, 0xD2AC },
	{ 0xC6AD, 0xD2B1 }, { 0xC6AE, 0xD2B8 }, { 0xC6AF, 0xD2B9 }, { 0xC6B0, 0xD2BC }, { 0xC6B1, 0xD2BF }, { 0xC6B2, 0xD2C0 }, { 0xC6B3, 0xD2C2 }, { 0xC6B4, 0xD2C8 },
	{ 0xC6B5, 0xD2C9 }, { 0xC6B6, 0xD2CB }, { 0xC6B7, 0xD2D4 }, { 0xC6B8, 0xD2D8 }, { 0xC6B9, 0xD2DC }, { 0xC6BA, 0xD2E4 }, { 0xC6BB, 0xD2E5 }, { 0xC6BC, 0xD2F0 },
	{ 0xC6BD, 0xD2F1 }, { 0xC6BE, 0xD2F4 }, { 0xC6BF, 0xD2F8 }, { 0xC6C0, 0xD300 }, { 0xC6C1, 0xD301 }, { 0xC6C2, 0xD303 }, { 0xC6C3, 0xD305 }, { 0xC6C4, 0xD30C },
	{ 0xC6C5, 0xD30D }, { 0xC6C6, 0xD30E }, { 0xC6C7, 0xD310 }, { 0xC6C8, 0xD314 }, { 0xC6C9, 0xD316 }, { 0xC6CA, 0xD31C }, { 0xC6CB, 0xD31D }, { 0xC6CC, 0xD31F },
	{ 0xC6CD, 0xD320 }, { 0xC6CE, 0xD321 }, { 0xC6CF, 0xD325 }, { 0xC6D0, 0xD328 }, { 0xC6D1, 0xD329 }, { 0xC6D2, 0xD32C }, { 0xC6D3, 0xD330 }, { 0xC6D4, 0xD338 },
	{ 0xC6D5, 0xD339 }, { 0xC6D6, 0xD33B }, { 0xC6D7, 0xD33C }, { 0xC6D8, 0xD33D }, { 0xC6D9, 0xD344 }, { 0xC6DA, 0xD345 }, { 0xC6DB, 0xD37C }, { 0xC6DC, 0xD37D },
	{ 0xC6DD, 0xD380 }, { 0xC6DE, 0xD384 }, { 0xC6DF, 0xD38C }, { 0xC6E0, 0xD38D }, { 0xC6E1, 0xD38F }, { 0xC6E2, 0xD390 }, { 0xC6E3, 0xD391 }, { 0xC6E4, 0xD398 },
	{ 0xC6E5, 0xD399 }, { 0xC6E6, 0xD39C }, { 0xC6E7, 0xD3A0 }, { 0xC6E8, 0xD3A8 }, { 0xC6E9, 0xD3A9 }, { 0xC6EA, 0xD3AB }, { 0xC6EB, 0xD3AD }, { 0xC6EC, 0xD3B4 },
	{ 0xC6ED, 0xD3B8 }, { 0xC6EE, 0xD3BC }, { 0xC6EF, 0xD3C4 }, { 0xC6F0, 0xD3C5 }, { 0xC6F1, 0xD3C8 }, { 0xC6F2, 0xD3C9 }, { 0xC6F3, 0xD3D0 }, { 0xC6F4, 0xD3D8 },
	{ 0xC6F5, 0xD3E1 }, { 0xC6F6, 0xD3E3 }, { 0xC6F7, 0xD3EC }, { 0xC6F8, 0xD3ED }, { 0xC6F9, 0xD3F0 }, { 0xC6FA, 0xD3F4 }, { 0xC6FB, 0xD3FC }, { 0xC6FC, 0xD3FD },
	{ 0xC6FD, 0xD3FF }, { 0xC6FE, 0xD401 }, { 0xC7A1, 0xD408 }, { 0xC7A2, 0xD41D }, { 0xC7A3, 0xD440 }, { 0xC7A4, 0xD444 }, { 0xC7A5, 0xD45C }, { 0xC7A6, 0xD460 },
	{ 0xC7A7, 0xD464 }, { 0xC7A8, 0xD46D }, { 0xC7A9, 0xD46F }, { 0xC7AA, 0xD478 }, { 0xC7AB, 0xD479 }, { 0xC7AC, 0xD47C }, { 0xC7AD, 0xD47F }, { 0xC7AE, 0xD480 },
	{ 0xC7AF, 0xD482 }, { 0xC7B0, 0xD488 }, { 0xC7B1, 0xD489 }, { 0xC7B2, 0xD48B }, { 0xC7B3, 0xD48D }, { 0xC7B4, 0xD494 }, { 0xC7B5, 0xD4A9 }, { 0xC7B6, 0xD4CC },
	{ 0xC7B7, 0xD4D0 }, { 0xC7B8, 0xD4D4 }, { 0xC7B9, 0xD4DC }, { 0xC7BA, 0xD4DF }, { 0xC7BB, 0xD4E8 }, { 0xC7BC, 0xD4EC }, { 0xC7BD, 0xD4F0 }, { 0xC7BE, 0xD4F8 },
	{ 0xC7BF, 0xD4FB }, { 0xC7C0, 0xD4FD }, { 0xC7C1, 0xD504 }, { 0xC7C2, 0xD508 }, { 0xC7C3, 0xD50C }, { 0xC7C4, 0xD514 }, { 0xC7C5, 0xD515 }, { 0xC7C6, 0xD517 },
	{ 0xC7C7, 0xD53C }, { 0xC7C8, 0xD53D }, { 0xC7C9, 0xD540 }, { 0xC7CA, 0xD544 }, { 0xC7CB, 0xD54C }, { 0xC7CC, 0xD54D }, { 0xC7CD, 0xD54F }, { 0xC7CE, 0xD551 },
	{ 0xC7CF, 0xD558 }, { 0xC7D0, 0xD559 }, { 0xC7D1, 0xD55C }, { 0xC7D2, 0xD560 }, { 0xC7D3, 0xD565 }, { 0xC7D4, 0xD568 }, { 0xC7D5, 0xD569 }, { 0xC7D6, 0xD56B },
	{ 0xC7D7, 0xD56D }, { 0xC7D8, 0xD574 }, { 0xC7D9, 0xD575 }, { 0xC7DA, 0xD578 }, { 0xC7DB, 0xD57C }, { 0xC7DC, 0xD584 }, { 0xC7DD, 0xD585 }, { 0xC7DE, 0xD587 },
	{ 0xC7DF, 0xD588 }, { 0xC7E0, 0xD589 }, { 0xC7E1, 0xD590 }, { 0xC7E2, 0xD5A5 }, { 0xC7E3, 0xD5C8 }, { 0xC7E4, 0xD5C9 }, { 0xC7E5, 0xD5CC }, { 0xC7E6, 0xD5D0 },
	{ 0xC7E7, 0xD5D2 }, { 0xC7E8, 0xD5D8 }, { 0xC7E9, 0xD5D9 }, { 0xC7EA, 0xD5DB }, { 0xC7EB, 0xD5DD }, { 0xC7EC, 0xD5E4 }, { 0xC7ED, 0xD5E5 }, { 0xC7EE, 0xD5E8 },
	{ 0xC7EF, 0xD5EC }, { 0xC7F0, 0xD5F4 }, { 0xC7F1, 0xD5F5 }, { 0xC7F2, 0xD5F7 }, { 0xC7F3, 0xD5F9 }, { 0xC7F4, 0xD600 }, { 0xC7F5, 0xD601 }, { 0xC7F6, 0xD604 },
	{ 0xC7F7, 0xD608 }, { 0xC7F8, 0xD610 }, { 0xC7F9, 0xD611 }, { 0xC7FA, 0xD613 }, { 0xC7FB, 0xD614 }, { 0xC7FC, 0xD615 }, { 0xC7FD, 0xD61C }, { 0xC7FE, 0xD620 },
	{ 0xC8A1, 0xD624 }, { 0xC8A2, 0xD62D }, { 0xC8A3, 0xD638 }, { 0xC8A4, 0xD639 }, { 0xC8A5, 0xD63C }, { 0xC8A6, 0xD640 }, { 0xC8A7, 0xD645 }, { 0xC8A8, 0xD648 },
	{ 0xC8A9, 0xD649 }, { 0xC8AA, 0xD64B }, { 0xC8AB, 0xD64D }, { 0xC8AC, 0xD651 }, { 0xC8AD, 0xD654 }, { 0xC8AE, 0xD655 }, { 0xC8AF, 0xD658 }, { 0xC8B0, 0xD65C },
	{ 0xC8B1, 0xD667 }, { 0xC8B2, 0xD669 }, { 0xC8B3, 0xD670 }, { 0xC8B4, 0xD671 }, { 0xC8B5, 0xD674 }, { 0xC8B6, 0xD683 }, { 0xC8B7, 0xD685 }, { 0xC8B8, 0xD68C },
	{ 0xC8B9, 0xD68D }, { 0xC8BA, 0xD690 }, { 0xC8BB, 0xD694 }, { 0xC8BC, 0xD69D }, { 0xC8BD, 0xD69F }, { 0xC8BE, 0xD6A1 }, { 0xC8BF, 0xD6A8 }, { 0xC8C0, 0xD6AC },
	{ 0xC8C1, 0xD6B0 }, { 0xC8C2, 0xD6B9 }, { 0xC8C3, 0xD6BB }, { 0xC8C4, 0xD6C4 }, { 0xC8C5, 0xD6C5 }, { 0xC8C6, 0xD6C8 }, { 0xC8C7, 0xD6CC }, { 0xC8C8, 0xD6D1 },
	{ 0xC8C9, 0xD6D4 }, { 0xC8CA, 0xD6D7 }, { 0xC8CB, 0xD6D9 }, { 0xC8CC, 0xD6E0 }, { 0xC8CD, 0xD6E4 }, { 0xC8CE, 0xD6E8 }, { 0xC8CF, 0xD6F0 }, { 0xC8D0, 0xD6F5 },
	{ 0xC8D1, 0xD6FC }, { 0xC8D2, 0xD6FD }, { 0xC8D3, 0xD700 }, { 0xC8D4, 0xD704 }, { 0xC8D5, 0xD711 }, { 0xC8D6, 0xD718 }, { 0xC8D7, 0xD719 }, { 0xC8D8, 0xD71C },
	{ 0xC8D9, 0xD720 }, { 0xC8DA, 0xD728 }, { 0xC8DB, 0xD729 }, { 0xC8DC, 0xD72B }, { 0xC8DD, 0xD72D }, { 0xC8DE, 0xD734 }, { 0xC8DF, 0xD735 }, { 0xC8E0, 0xD738 },
	{ 0xC8E1, 0xD73C }, { 0xC8E2, 0xD744 }, { 0xC8E3, 0xD747 }, { 0xC8E4, 0xD749 }, { 0xC8E5, 0xD750 }, { 0xC8E6, 0xD751 }, { 0xC8E7, 0xD754 }, { 0xC8E8, 0xD756 },
	{ 0xC8E9, 0xD757 }, { 0xC8EA, 0xD758 }, { 0xC8EB, 0xD759 }, { 0xC8EC, 0xD760 }, { 0xC8ED, 0xD761 }, { 0xC8EE, 0xD763 }, { 0xC8EF, 0xD765 }, { 0xC8F0, 0xD769 },
	{ 0xC8F1, 0xD76C }, { 0xC8F2, 0xD770 }, { 0xC8F3, 0xD774 }, { 0xC8F4, 0xD77C }, { 0xC8F5, 0xD77D }, { 0xC8F6, 0xD781 }, { 0xC8F7, 0xD788 }, { 0xC8F8, 0xD789 },
	{ 0xC8F9, 0xD78C }, { 0xC8FA, 0xD790 }, { 0xC8FB, 0xD798 }, { 0xC8FC, 0xD799 }, { 0xC8FD, 0xD79B }, { 0xC8FE, 0xD79D }, { 0xCAA1, 0x4F3D }, { 0xCAA2, 0x4F73 },
	{ 0xCAA3, 0x5047 }, { 0xCAA4, 0x50F9 }, { 0xCAA5, 0x52A0 }, { 0xCAA6, 0x53EF }, { 0xCAA7, 0x5475 }, { 0xCAA8, 0x54E5 }, { 0xCAA9, 0x5609 }, { 0xCAAA, 0x5AC1 },
	{ 0xCAAB, 0x5BB6 }, { 0xCAAC, 0x6687 }, { 0xCAAD, 0x67B6 }, { 0xCAAE, 0x67B7 }, { 0xCAAF, 0x67EF }, { 0xCAB0, 0x6B4C }, { 0xCAB1, 0x73C2 }, { 0xCAB2, 0x75C2 },
	{ 0xCAB3, 0x7A3C }, { 0xCAB4, 0x82DB }, { 0xCAB5, 0x8304 }, { 0xCAB6, 0x8857 }, { 0xCAB7, 0x8888 }, { 0xCAB8, 0x8A36 }, { 0xCAB9, 0x8CC8 }, { 0xCABA, 0x8DCF },
	{ 0xCABB, 0x8EFB }, { 0xCABC, 0x8FE6 }, { 0xCABD, 0x99D5 }, { 0xCABE, 0x523B }, { 0xCABF, 0x5374 }, { 0xCAC0, 0x5404 }, { 0xCAC1, 0x606A }, { 0xCAC2, 0x6164 },
	{ 0xCAC3, 0x6BBC }, { 0xCAC4, 0x73CF }, { 0xCAC5, 0x811A }, { 0xCAC6, 0x89BA }, { 0xCAC7, 0x89D2 }, { 0xCAC8, 0x95A3 }, { 0xCAC9, 0x4F83 }, { 0xCACA, 0x520A },
	{ 0xCACB, 0x58BE }, { 0xCACC, 0x5978 }, { 0xCACD, 0x59E6 }, { 0xCACE, 0x5E72 }, { 0xCACF, 0x5E79 }, { 0xCAD0, 0x61C7 }, { 0xCAD1, 0x63C0 }, { 0xCAD2, 0x6746 },
	{ 0xCAD3, 0x67EC }, { 0xCAD4, 0x687F }, { 0xCAD5, 0x6F97 }, { 0xCAD6, 0x764E }, { 0xCAD7, 0x770B }, { 0xCAD8, 0x78F5 }, { 0xCAD9, 0x7A08 }, { 0xCADA, 0x7AFF },
	{ 0xCADB, 0x7C21 }, { 0xCADC, 0x809D }, { 0xCADD, 0x826E }, { 0xCADE, 0x8271 }, { 0xCADF, 0x8AEB }, { 0xCAE0, 0x9593 }, { 0xCAE1, 0x4E6B }, { 0xCAE2, 0x559D },
	{ 0xCAE3, 0x66F7 }, { 0xCAE4, 0x6E34 }, { 0xCAE5, 0x78A3 }, { 0xCAE6, 0x7AED }, { 0xCAE7, 0x845B }, { 0xCAE8, 0x8910 }, { 0xCAE9, 0x874E }, { 0xCAEA, 0x97A8 },
	{ 0xCAEB, 0x52D8 }, { 0xCAEC, 0x574E }, { 0xCAED, 0x582A }, { 0xCAEE, 0x5D4C }, { 0xCAEF, 0x611F }, { 0xCAF0, 0x61BE }, { 0xCAF1, 0x6221 }, { 0xCAF2, 0x6562 },
	{ 0xCAF3, 0x67D1 }, { 0xCAF4, 0x6A44 }, { 0xCAF5, 0x6E1B }, { 0xCAF6, 0x7518 }, { 0xCAF7, 0x75B3 }, { 0xCAF8, 0x76E3 }, { 0xCAF9, 0x77B0 }, { 0xCAFA, 0x7D3A },
	{ 0xCAFB, 0x90AF }, { 0xCAFC, 0x9451 }, { 0xCAFD, 0x9452 }, { 0xCAFE, 0x9F95 }, { 0xCBA1, 0x5323 }, { 0xCBA2, 0x5CAC }, { 0xCBA3, 0x7532 }, { 0xCBA4, 0x80DB },
	{ 0xCBA5, 0x9240 }, { 0xCBA6, 0x9598 }, { 0xCBA7, 0x525B }, { 0xCBA8, 0x5808 }, { 0xCBA9, 0x59DC }, { 0xCBAA, 0x5CA1 }, { 0xCBAB, 0x5D17 }, { 0xCBAC, 0x5EB7 },
	{ 0xCBAD, 0x5F3A }, { 0xCBAE, 0x5F4A }, { 0xCBAF, 0x6177 }, { 0xCBB0, 0x6C5F }, { 0xCBB1, 0x757A }, { 0xCBB2, 0x7586 }, { 0xCBB3, 0x7CE0 }, { 0xCBB4, 0x7D73 },
	{ 0xCBB5, 0x7DB1 }, { 0xCBB6, 0x7F8C }, { 0xCBB7, 0x8154 }, { 0xCBB8, 0x8221 }, { 0xCBB9, 0x8591 }, { 0xCBBA, 0x8941 }, { 0xCBBB, 0x8B1B }, { 0xCBBC, 0x92FC },
	{ 0xCBBD, 0x964D }, { 0xCBBE, 0x9C47 }, { 0xCBBF, 0x4ECB }, { 0xCBC0, 0x4EF7 }, { 0xCBC1, 0x500B }, { 0xCBC2, 0x51F1 }, { 0xCBC3, 0x584F }, { 0xCBC4, 0x6137 },
	{ 0xCBC5, 0x613E }, { 0xCBC6, 0x6168 }, { 0xCBC7, 0x6539 }, { 0xCBC8, 0x69EA }, { 0xCBC9, 0x6F11 }, { 0xCBCA, 0x75A5 }, { 0xCBCB, 0x7686 }, { 0xCBCC, 0x76D6 },
	{ 0xCBCD, 0x7B87 }, { 0xCBCE, 0x82A5 }, { 0xCBCF, 0x84CB }, { 0xCBD0, 0xF900 }, { 0xCBD1, 0x93A7 }, { 0xCBD2, 0x958B }, { 0xCBD3, 0x5580 }, { 0xCBD4, 0x5BA2 },
	{ 0xCBD5, 0x5751 }, { 0xCBD6, 0xF901 }, { 0xCBD7, 0x7CB3 }, { 0xCBD8, 0x7FB9 }, { 0xCBD9, 0x91B5 }, { 0xCBDA, 0x5028 }, { 0xCBDB, 0x53BB }, { 0xCBDC, 0x5C45 },
	{ 0xCBDD, 0x5DE8 }, { 0xCBDE, 0x62D2 }, { 0xCBDF, 0x636E }, { 0xCBE0, 0x64DA }, { 0xCBE1, 0x64E7 }, { 0xCBE2, 0x6E20 }, { 0xCBE3, 0x70AC }, { 0xCBE4, 0x795B },
	{ 0xCBE5, 0x8DDD }, { 0xCBE6, 0x8E1E }, { 0xCBE7, 0xF902 }, { 0xCBE8, 0x907D }, { 0xCBE9, 0x9245 }, { 0xCBEA, 0x92F8 }, { 0xCBEB, 0x4E7E }, { 0xCBEC, 0x4EF6 },
	{ 0xCBED, 0x5065 }, { 0xCBEE, 0x5DFE }, { 0xCBEF, 0x5EFA }, { 0xCBF0, 0x6106 }, { 0xCBF1, 0x6957 }, { 0xCBF2, 0x8171 }, { 0xCBF3, 0x8654 }, { 0xCBF4, 0x8E47 },
	{ 0xCBF5, 0x9375 }, { 0xCBF6, 0x9A2B }, { 0xCBF7, 0x4E5E }, { 0xCBF8, 0x5091 }, { 0xCBF9, 0x6770 }, { 0xCBFA, 0x6840 }, { 0xCBFB, 0x5109 }, { 0xCBFC, 0x528D },
	{ 0xCBFD, 0x5292 }, { 0xCBFE, 0x6AA2 }, { 0xCCA1, 0x77BC }, { 0xCCA2, 0x9210 }, { 0xCCA3, 0x9ED4 }, { 0xCCA4, 0x52AB }, { 0xCCA5, 0x602F }, { 0xCCA6, 0x8FF2 },
	{ 0xCCA7, 0x5048 }, { 0xCCA8, 0x61A9 }, { 0xCCA9, 0x63ED }, { 0xCCAA, 0x64CA }, { 0xCCAB, 0x683C }, { 0xCCAC, 0x6A84 }, { 0xCCAD, 0x6FC0 }, { 0xCCAE, 0x8188 },
	{ 0xCCAF, 0x89A1 }, { 0xCCB0, 0x9694 }, { 0xCCB1, 0x5805 }, { 0xCCB2, 0x727D }, { 0xCCB3, 0x72AC }, { 0xCCB4, 0x7504 }, { 0xCCB5, 0x7D79 }, { 0xCCB6, 0x7E6D },
	{ 0xCCB7, 0x80A9 }, { 0xCCB8, 0x898B }, { 0xCCB9, 0x8B74 }, { 0xCCBA, 0x9063 }, { 0xCCBB, 0x9D51 }, { 0xCCBC, 0x6289 }, { 0xCCBD, 0x6C7A }, { 0xCCBE, 0x6F54 },
	{ 0xCCBF, 0x7D50 }, { 0xCCC0, 0x7F3A }, { 0xCCC1, 0x8A23 }, { 0xCCC2, 0x517C }, { 0xCCC3, 0x614A }, { 0xCCC4, 0x7B9D }, { 0xCCC5, 0x8B19 }, { 0xCCC6, 0x9257 },
	{ 0xCCC7, 0x938C }, { 0xCCC8, 0x4EAC }, { 0xCCC9, 0x4FD3 }, { 0xCCCA, 0x501E }, { 0xCCCB, 0x50BE }, { 0xCCCC, 0x5106 }, { 0xCCCD, 0x52C1 }, { 0xCCCE, 0x52CD },
	{ 0xCCCF, 0x537F }, { 0xCCD0, 0x5770 }, { 0xCCD1, 0x5883 }, { 0xCCD2, 0x5E9A }, { 0xCCD3, 0x5F91 }, { 0xCCD4, 0x6176 }, { 0xCCD5, 0x61AC }, { 0xCCD6, 0x64CE },
	{ 0xCCD7, 0x656C }, { 0xCCD8, 0x666F }, { 0xCCD9, 0x66BB }, { 0xCCDA, 0x66F4 }, { 0xCCDB, 0x6897 }, { 0xCCDC, 0x6D87 }, { 0xCCDD, 0x7085 }, { 0xCCDE, 0x70F1 },
	{ 0xCCDF, 0x749F }, { 0xCCE0, 0x74A5 }, { 0xCCE1, 0x74CA }, { 0xCCE2, 0x75D9 }, { 0xCCE3, 0x786C }, { 0xCCE4, 0x78EC }, { 0xCCE5, 0x7ADF }, { 0xCCE6, 0x7AF6 },
	{ 0xCCE7, 0x7D45 }, { 0xCCE8, 0x7D93 }, { 0xCCE9, 0x8015 }, { 0xCCEA, 0x803F }, { 0xCCEB, 0x811B }, { 0xCCEC, 0x8396 }, { 0xCCED, 0x8B66 }, { 0xCCEE, 0x8F15 },
	{ 0xCCEF, 0x9015 }, { 0xCCF0, 0x93E1 }, { 0xCCF1, 0x9803 }, { 0xCCF2, 0x9838 }, { 0xCCF3, 0x9A5A }, { 0xCCF4, 0x9BE8 }, { 0xCCF5, 0x4FC2 }, { 0xCCF6, 0x5553 },
	{ 0xCCF7, 0x583A }, { 0xCCF8, 0x5951 }, { 0xCCF9, 0x5B63 }, { 0xCCFA, 0x5C46 }, { 0xCCFB, 0x60B8 }, { 0xCCFC, 0x6212 }, { 0xCCFD, 0x6842 }, { 0xCCFE, 0x68B0 },
	{ 0xCDA1, 0x68E8 }, { 0xCDA2, 0x6EAA }, { 0xCDA3, 0x754C }, { 0xCDA4, 0x7678 }, { 0xCDA5, 0x78CE }, { 0xCDA6, 0x7A3D }, { 0xCDA7, 0x7CFB }, { 0xCDA8, 0x7E6B },
	{ 0xCDA9, 0x7E7C }, { 0xCDAA, 0x8A08 }, { 0xCDAB, 0x8AA1 }, { 0xCDAC, 0x8C3F }, { 0xCDAD, 0x968E }, { 0xCDAE, 0x9DC4 }, { 0xCDAF, 0x53E4 }, { 0xCDB0, 0x53E9 },
	{ 0xCDB1, 0x544A }, { 0xCDB2, 0x5471 }, { 0xCDB3, 0x56FA }, { 0xCDB4, 0x59D1 }, { 0xCDB5, 0x5B64 }, { 0xCDB6, 0x5C3B }, { 0xCDB7, 0x5EAB }, { 0xCDB8, 0x62F7 },
	{ 0xCDB9, 0x6537 }, { 0xCDBA, 0x6545 }, { 0xCDBB, 0x6572 }, { 0xCDBC, 0x66A0 }, { 0xCDBD, 0x67AF }, { 0xCDBE, 0x69C1 }, { 0xCDBF, 0x6CBD }, { 0xCDC0, 0x75FC },
	{ 0xCDC1, 0x7690 }, { 0xCDC2, 0x777E }, { 0xCDC3, 0x7A3F }, { 0xCDC4, 0x7F94 }, { 0xCDC5, 0x8003 }, { 0xCDC6, 0x80A1 }, { 0xCDC7, 0x818F }, { 0xCDC8, 0x82E6 },
	{ 0xCDC9, 0x82FD }, { 0xCDCA, 0x83F0 }, { 0xCDCB, 0x85C1 }, { 0xCDCC, 0x8831 }, { 0xCDCD, 0x88B4 }, { 0xCDCE, 0x8AA5 }, { 0xCDCF, 0xF903 }, { 0xCDD0, 0x8F9C },
	{ 0xCDD1, 0x932E }, { 0xCDD2, 0x96C7 }, { 0xCDD3, 0x9867 }, { 0xCDD4, 0x9AD8 }, { 0xCDD5, 0x9F13 }, { 0xCDD6, 0x54ED }, { 0xCDD7, 0x659B }, { 0xCDD8, 0x66F2 },
	{ 0xCDD9, 0x688F }, { 0xCDDA, 0x7A40 }, { 0xCDDB, 0x8C37 }, { 0xCDDC, 0x9D60 }, { 0xCDDD, 0x56F0 }, { 0xCDDE, 0x5764 }, { 0xCDDF, 0x5D11 }, { 0xCDE0, 0x6606 },
	{ 0xCDE1, 0x68B1 }, { 0xCDE2, 0x68CD }, { 0xCDE3, 0x6EFE }, { 0xCDE4, 0x7428 }, { 0xCDE5, 0x889E }, { 0xCDE6, 0x9BE4 }, { 0xCDE7, 0x6C68 }, { 0xCDE8, 0xF904 },
	{ 0xCDE9, 0x9AA8 }, { 0xCDEA, 0x4F9B }, { 0xCDEB, 0x516C }, { 0xCDEC, 0x5171 }, { 0xCDED, 0x529F }, { 0xCDEE, 0x5B54 }, { 0xCDEF, 0x5DE5 }, { 0xCDF0, 0x6050 },
	{ 0xCDF1, 0x606D }, { 0xCDF2, 0x62F1 }, { 0xCDF3, 0x63A7 }, { 0xCDF4, 0x653B }, { 0xCDF5, 0x73D9 }, { 0xCDF6, 0x7A7A }, { 0xCDF7, 0x86A3 }, { 0xCDF8, 0x8CA2 },
	{ 0xCDF9, 0x978F }, { 0xCDFA, 0x4E32 }, { 0xCDFB, 0x5BE1 }, { 0xCDFC, 0x6208 }, { 0xCDFD, 0x679C }, { 0xCDFE, 0x74DC }, { 0xCEA1, 0x79D1 }, { 0xCEA2, 0x83D3 },
	{ 0xCEA3, 0x8A87 }, { 0xCEA4, 0x8AB2 }, { 0xCEA5, 0x8DE8 }, { 0xCEA6, 0x904E }, { 0xCEA7, 0x934B }, { 0xCEA8, 0x9846 }, { 0xCEA9, 0x5ED3 }, { 0xCEAA, 0x69E8 },
	{ 0xCEAB, 0x85FF }, { 0xCEAC, 0x90ED }, { 0xCEAD, 0xF905 }, { 0xCEAE, 0x51A0 }, { 0xCEAF, 0x5B98 }, { 0xCEB0, 0x5BEC }, { 0xCEB1, 0x6163 }, { 0xCEB2, 0x68FA },
	{ 0xCEB3, 0x6B3E }, { 0xCEB4, 0x704C }, { 0xCEB5, 0x742F }, { 0xCEB6, 0x74D8 }, { 0xCEB7, 0x7BA1 }, { 0xCEB8, 0x7F50 }, { 0xCEB9, 0x83C5 }, { 0xCEBA, 0x89C0 },
	{ 0xCEBB, 0x8CAB }, { 0xCEBC, 0x95DC }, { 0xCEBD, 0x9928 }, { 0xCEBE, 0x522E }, { 0xCEBF, 0x605D }, { 0xCEC0, 0x62EC }, { 0xCEC1, 0x9002 }, { 0xCEC2, 0x4F8A },
	{ 0xCEC3, 0x5149 }, { 0xCEC4, 0x5321 }, { 0xCEC5, 0x58D9 }, { 0xCEC6, 0x5EE3 }, { 0xCEC7, 0x66E0 }, { 0xCEC8, 0x6D38 }, { 0xCEC9, 0x709A }, { 0xCECA, 0x72C2 },
	{ 0xCECB, 0x73D6 }, { 0xCECC, 0x7B50 }, { 0xCECD, 0x80F1 }, { 0xCECE, 0x945B }, { 0xCECF, 0x5366 }, { 0xCED0, 0x639B }, { 0xCED1, 0x7F6B }, { 0xCED2, 0x4E56 },
	{ 0xCED3, 0x5080 }, { 0xCED4, 0x584A }, { 0xCED5, 0x58DE }, { 0xCED6, 0x602A }, { 0xCED7, 0x6127 }, { 0xCED8, 0x62D0 }, { 0xCED9, 0x69D0 }, { 0xCEDA, 0x9B41 },
	{ 0xCEDB, 0x5B8F }, { 0xCEDC, 0x7D18 }, { 0xCEDD, 0x80B1 }, { 0xCEDE, 0x8F5F }, { 0xCEDF, 0x4EA4 }, { 0xCEE0, 0x50D1 }, { 0xCEE1, 0x54AC }, { 0xCEE2, 0x55AC },
	{ 0xCEE3, 0x5B0C }, { 0xCEE4, 0x5DA0 }, { 0xCEE5, 0x5DE7 }, { 0xCEE6, 0x652A }, { 0xCEE7, 0x654E }, { 0xCEE8, 0x6821 }, { 0xCEE9, 0x6A4B }, { 0xCEEA, 0x72E1 },
	{ 0xCEEB, 0x768E }, { 0xCEEC, 0x77EF }, { 0xCEED, 0x7D5E }, { 0xCEEE, 0x7FF9 }, { 0xCEEF, 0x81A0 }, { 0xCEF0, 0x854E }, { 0xCEF1, 0x86DF }, { 0xCEF2, 0x8F03 },
	{ 0xCEF3, 0x8F4E }, { 0xCEF4, 0x90CA }, { 0xCEF5, 0x9903 }, { 0xCEF6, 0x9A55 }, { 0xCEF7, 0x9BAB }, { 0xCEF8, 0x4E18 }, { 0xCEF9, 0x4E45 }, { 0xCEFA, 0x4E5D },
	{ 0xCEFB, 0x4EC7 }, { 0xCEFC, 0x4FF1 }, { 0xCEFD, 0x5177 }, { 0xCEFE, 0x52FE }, { 0xCFA1, 0x5340 }, { 0xCFA2, 0x53E3 }, { 0xCFA3, 0x53E5 }, { 0xCFA4, 0x548E },
	{ 0xCFA5, 0x5614 }, { 0xCFA6, 0x5775 }, { 0xCFA7, 0x57A2 }, { 0xCFA8, 0x5BC7 }, { 0xCFA9, 0x5D87 }, { 0xCFAA, 0x5ED0 }, { 0xCFAB, 0x61FC }, { 0xCFAC, 0x62D8 },
	{ 0xCFAD, 0x6551 }, { 0xCFAE, 0x67B8 }, { 0xCFAF, 0x67E9 }, { 0xCFB0, 0x69CB }, { 0xCFB1, 0x6B50 }, { 0xCFB2, 0x6BC6 }, { 0xCFB3, 0x6BEC }, { 0xCFB4, 0x6C42 },
	{ 0xCFB5, 0x6E9D }, { 0xCFB6, 0x7078 }, { 0xCFB7, 0x72D7 }, { 0xCFB8, 0x7396 }, { 0xCFB9, 0x7403 }, { 0xCFBA, 0x77BF }, { 0xCFBB, 0x77E9 }, { 0xCFBC, 0x7A76 },
	{ 0xCFBD, 0x7D7F }, { 0xCFBE, 0x8009 }, { 0xCFBF, 0x81FC }, { 0xCFC0, 0x8205 }, { 0xCFC1, 0x820A }, { 0xCFC2, 0x82DF }, { 0xCFC3, 0x8862 }, { 0xCFC4, 0x8B33 },
	{ 0xCFC5, 0x8CFC }, { 0xCFC6, 0x8EC0 }, { 0xCFC7, 0x9011 }, { 0xCFC8, 0x90B1 }, { 0xCFC9, 0x9264 }, { 0xCFCA, 0x92B6 }, { 0xCFCB, 0x99D2 }, { 0xCFCC, 0x9A45 },
	{ 0xCFCD, 0x9CE9 }, { 0xCFCE, 0x9DD7 }, { 0xCFCF, 0x9F9C }, { 0xCFD0, 0x570B }, { 0xCFD1, 0x5C40 }, { 0xCFD2, 0x83CA }, { 0xCFD3, 0x97A0 }, { 0xCFD4, 0x97AB },
	{ 0xCFD5, 0x9EB4 }, { 0xCFD6, 0x541B }, { 0xCFD7, 0x7A98 }, { 0xCFD8, 0x7FA4 }, { 0xCFD9, 0x88D9 }, { 0xCFDA, 0x8ECD }, { 0xCFDB, 0x90E1 }, { 0xCFDC, 0x5800 },
	{ 0xCFDD, 0x5C48 }, { 0xCFDE, 0x6398 }, { 0xCFDF, 0x7A9F }, { 0xCFE0, 0x5BAE }, { 0xCFE1, 0x5F13 }, { 0xCFE2, 0x7A79 }, { 0xCFE3, 0x7AAE }, { 0xCFE4, 0x828E },
	{ 0xCFE5, 0x8EAC }, { 0xCFE6, 0x5026 }, { 0xCFE7, 0x5238 }, { 0xCFE8, 0x52F8 }, { 0xCFE9, 0x5377 }, { 0xCFEA, 0x5708 }, { 0xCFEB, 0x62F3 }, { 0xCFEC, 0x6372 },
	{ 0xCFED, 0x6B0A }, { 0xCFEE, 0x6DC3 }, { 0xCFEF, 0x7737 }, { 0xCFF0, 0x53A5 }, { 0xCFF1, 0x7357 }, { 0xCFF2, 0x8568 }, { 0xCFF3, 0x8E76 }, { 0xCFF4, 0x95D5 },
	{ 0xCFF5, 0x673A }, { 0xCFF6, 0x6AC3 }, { 0xCFF7, 0x6F70 }, { 0xCFF8, 0x8A6D }, { 0xCFF9, 0x8ECC }, { 0xCFFA, 0x994B }, { 0xCFFB, 0xF906 }, { 0xCFFC, 0x6677 },
	{ 0xCFFD, 0x6B78 }, { 0xCFFE, 0x8CB4 }, { 0xD0A1, 0x9B3C }, { 0xD0A2, 0xF907 }, { 0xD0A3, 0x53EB }, { 0xD0A4, 0x572D }, { 0xD0A5, 0x594E }, { 0xD0A6, 0x63C6 },
	{ 0xD0A7, 0x69FB }, { 0xD0A8, 0x73EA }, { 0xD0A9, 0x7845 }, { 0xD0AA, 0x7ABA }, { 0xD0AB, 0x7AC5 }, { 0xD0AC, 0x7CFE }, { 0xD0AD, 0x8475 }, { 0xD0AE, 0x898F },
	{ 0xD0AF, 0x8D73 }, { 0xD0B0, 0x9035 }, { 0xD0B1, 0x95A8 }, { 0xD0B2, 0x52FB }, { 0xD0B3, 0x5747 }, { 0xD0B4, 0x7547 }, { 0xD0B5, 0x7B60 }, { 0xD0B6, 0x83CC },
	{ 0xD0B7, 0x921E }, { 0xD0B8, 0xF908 }, { 0xD0B9, 0x6A58 }, { 0xD0BA, 0x514B }, { 0xD0BB, 0x524B }, { 0xD0BC, 0x5287 }, { 0xD0BD, 0x621F }, { 0xD0BE, 0x68D8 },
	{ 0xD0BF, 0x6975 }, { 0xD0C0, 0x9699 }, { 0xD0C1, 0x50C5 }, { 0xD0C2, 0x52A4 }, { 0xD0C3, 0x52E4 }, { 0xD0C4, 0x61C3 }, { 0xD0C5, 0x65A4 }, { 0xD0C6, 0x6839 },
	{ 0xD0C7, 0x69FF }, { 0xD0C8, 0x747E }, { 0xD0C9, 0x7B4B }, { 0xD0CA, 0x82B9 }, { 0xD0CB, 0x83EB }, { 0xD0CC, 0x89B2 }, { 0xD0CD, 0x8B39 }, { 0xD0CE, 0x8FD1 },
	{ 0xD0CF, 0x9949 }, { 0xD0D0, 0xF909 }, { 0xD0D1, 0x4ECA }, { 0xD0D2, 0x5997 }, { 0xD0D3, 0x64D2 }, { 0xD0D4, 0x6611 }, { 0xD0D5, 0x6A8E }, { 0xD0D6, 0x7434 },
	{ 0xD0D7, 0x7981 }, { 0xD0D8, 0x79BD }, { 0xD0D9, 0x82A9 }, { 0xD0DA, 0x887E }, { 0xD0DB, 0x887F }, { 0xD0DC, 0x895F }, { 0xD0DD, 0xF90A }, { 0xD0DE, 0x9326 },
	{ 0xD0DF, 0x4F0B }, { 0xD0E0, 0x53CA }, { 0xD0E1, 0x6025 }, { 0xD0E2, 0x6271 }, { 0xD0E3, 0x6C72 }, { 0xD0E4, 0x7D1A }, { 0xD0E5, 0x7D66 }, { 0xD0E6, 0x4E98 },
	{ 0xD0E7, 0x5162 }, { 0xD0E8, 0x77DC }, { 0xD0E9, 0x80AF }, { 0xD0EA, 0x4F01 }, { 0xD0EB, 0x4F0E }, { 0xD0EC, 0x5176 }, { 0xD0ED, 0x5180 }, { 0xD0EE, 0x55DC },
	{ 0xD0EF, 0x5668 }, { 0xD0F0, 0x573B }, { 0xD0F1, 0x57FA }, { 0xD0F2, 0x57FC }, { 0xD0F3, 0x5914 }, { 0xD0F4, 0x5947 }, { 0xD0F5, 0x5993 }, { 0xD0F6, 0x5BC4 },
	{ 0xD0F7, 0x5C90 }, { 0xD0F8, 0x5D0E }, { 0xD0F9, 0x5DF1 }, { 0xD0FA, 0x5E7E }, { 0xD0FB, 0x5FCC }, { 0xD0FC, 0x6280 }, { 0xD0FD, 0x65D7 }, { 0xD0FE, 0x65E3 },
	{ 0xD1A1, 0x671E }, { 0xD1A2, 0x671F }, { 0xD1A3, 0x675E }, { 0xD1A4, 0x68CB }, { 0xD1A5, 0x68C4 }, { 0xD1A6, 0x6A5F }, { 0xD1A7, 0x6B3A }, { 0xD1A8, 0x6C23 },
	{ 0xD1A9, 0x6C7D }, { 0xD1AA, 0x6C82 }, { 0xD1AB, 0x6DC7 }, { 0xD1AC, 0x7398 }, { 0xD1AD, 0x7426 }, { 0xD1AE, 0x742A }, { 0xD1AF, 0x7482 }, { 0xD1B0, 0x74A3 },
	{ 0xD1B1, 0x7578 }, { 0xD1B2, 0x757F }, { 0xD1B3, 0x7881 }, { 0xD1B4, 0x78EF }, { 0xD1B5, 0x7941 }, { 0xD1B6, 0x7947 }, { 0xD1B7, 0x7948 }, { 0xD1B8, 0x797A },
	{ 0xD1B9, 0x7B95 }, { 0xD1BA, 0x7D00 }, { 0xD1BB, 0x7DBA }, { 0xD1BC, 0x7F88 }, { 0xD1BD, 0x8006 }, { 0xD1BE, 0x802D }, { 0xD1BF, 0x808C }, { 0xD1C0, 0x8A18 },
	{ 0xD1C1, 0x8B4F }, { 0xD1C2, 0x8C48 }, { 0xD1C3, 0x8D77 }, { 0xD1C4, 0x9321 }, { 0xD1C5, 0x9324 }, { 0xD1C6, 0x98E2 }, { 0xD1C7, 0x9951 }, { 0xD1C8, 0x9A0E },
	{ 0xD1C9, 0x9A0F }, { 0xD1CA, 0x9A65 }, { 0xD1CB, 0x9E92 }, { 0xD1CC, 0x7DCA }, { 0xD1CD, 0x4F76 }, { 0xD1CE, 0x5409 }, { 0xD1CF, 0x62EE }, { 0xD1D0, 0x6854 },
	{ 0xD1D1, 0x91D1 }, { 0xD1D2, 0x55AB }, { 0xD1D3, 0x513A }, { 0xD1D4, 0xF90B }, { 0xD1D5, 0xF90C }, { 0xD1D6, 0x5A1C }, { 0xD1D7, 0x61E6 }, { 0xD1D8, 0xF90D },
	{ 0xD1D9, 0x62CF }, { 0xD1DA, 0x62FF }, { 0xD1DB, 0xF90E }, { 0xD1DC, 0xF90F }, { 0xD1DD, 0xF910 }, { 0xD1DE, 0xF911 }, { 0xD1DF, 0xF912 }, { 0xD1E0, 0xF913 },
	{ 0xD1E1, 0x90A3 }, { 0xD1E2, 0xF914 }, { 0xD1E3, 0xF915 }, { 0xD1E4, 0xF916 }, { 0xD1E5, 0xF917 }, { 0xD1E6, 0xF918 }, { 0xD1E7, 0x8AFE }, { 0xD1E8, 0xF919 },
	{ 0xD1E9, 0xF91A }, { 0xD1EA, 0xF91B }, { 0xD1EB, 0xF91C }, { 0xD1EC, 0x6696 }, { 0xD1ED, 0xF91D }, { 0xD1EE, 0x7156 }, { 0xD1EF, 0xF91E }, { 0xD1F0, 0xF91F },
	{ 0xD1F1, 0x96E3 }, { 0xD1F2, 0xF920 }, { 0xD1F3, 0x634F }, { 0xD1F4, 0x637A }, { 0xD1F5, 0x5357 }, { 0xD1F6, 0xF921 }, { 0xD1F7, 0x678F }, { 0xD1F8, 0x6960 },
	{ 0xD1F9, 0x6E73 }, { 0xD1FA, 0xF922 }, { 0xD1FB, 0x7537 }, { 0xD1FC, 0xF923 }, { 0xD1FD, 0xF924 }, { 0xD1FE, 0xF925 }, { 0xD2A1, 0x7D0D }, { 0xD2A2, 0xF926 },
	{ 0xD2A3, 0xF927 }, { 0xD2A4, 0x8872 }, { 0xD2A5, 0x56CA }, { 0xD2A6, 0x5A18 }, { 0xD2A7, 0xF928 }, { 0xD2A8, 0xF929 }, { 0xD2A9, 0xF92A }, { 0xD2AA, 0xF92B },
	{ 0xD2AB, 0xF92C }, { 0xD2AC, 0x4E43 }, { 0xD2AD, 0xF92D }, { 0xD2AE, 0x5167 }, { 0xD2AF, 0x5948 }, { 0xD2B0, 0x67F0 }, { 0xD2B1, 0x8010 }, { 0xD2B2, 0xF92E },
	{ 0xD2B3, 0x5973 }, { 0xD2B4, 0x5E74 }, { 0xD2B5, 0x649A }, { 0xD2B6, 0x79CA }, { 0xD2B7, 0x5FF5 }, { 0xD2B8, 0x606C }, { 0xD2B9, 0x62C8 }, { 0xD2BA, 0x637B },
	{ 0xD2BB, 0x5BE7 }, { 0xD2BC, 0x5BD7 }, { 0xD2BD, 0x52AA }, { 0xD2BE, 0xF92F }, { 0xD2BF, 0x5974 }, { 0xD2C0, 0x5F29 }, { 0xD2C1, 0x6012 }, { 0xD2C2, 0xF930 },
	{ 0xD2C3, 0xF931 }, { 0xD2C4, 0xF932 }, { 0xD2C5, 0x7459 }, { 0xD2C6, 0xF933 }, { 0xD2C7, 0xF934 }, { 0xD2C8, 0xF935 }, { 0xD2C9, 0xF936 }, { 0xD2CA, 0xF937 },
	{ 0xD2CB, 0xF938 }, { 0xD2CC, 0x99D1 }, { 0xD2CD, 0xF939 }, { 0xD2CE, 0xF93A }, { 0xD2CF, 0xF93B }, { 0xD2D0, 0xF93C }, { 0xD2D1, 0xF93D }, { 0xD2D2, 0xF93E },
	{ 0xD2D3, 0xF93F }, { 0xD2D4, 0xF940 }, { 0xD2D5, 0xF941 }, { 0xD2D6, 0xF942 }, { 0xD2D7, 0xF943 }, { 0xD2D8, 0x6FC3 }, { 0xD2D9, 0xF944 }, { 0xD2DA, 0xF945 },
	{ 0xD2DB, 0x81BF }, { 0xD2DC, 0x8FB2 }, { 0xD2DD, 0x60F1 }, { 0xD2DE, 0xF946 }, { 0xD2DF, 0xF947 }, { 0xD2E0, 0x8166 }, { 0xD2E1, 0xF948 }, { 0xD2E2, 0xF949 },
	{ 0xD2E3, 0x5C3F }, { 0xD2E4, 0xF94A }, { 0xD2E5, 0xF94B }, { 0xD2E6, 0xF94C }, { 0xD2E7, 0xF94D }, { 0xD2E8, 0xF94E }, { 0xD2E9, 0xF94F }, { 0xD2EA, 0xF950 },
	{ 0xD2EB, 0xF951 }, { 0xD2EC, 0x5AE9 }, { 0xD2ED, 0x8A25 }, { 0xD2EE, 0x677B }, { 0xD2EF, 0x7D10 }, { 0xD2F0, 0xF952 }, { 0xD2F1, 0xF953 }, { 0xD2F2, 0xF954 },
	{ 0xD2F3, 0xF955 }, { 0xD2F4, 0xF956 }, { 0xD2F5, 0xF957 }, { 0xD2F6, 0x80FD }, { 0xD2F7, 0xF958 }, { 0xD2F8, 0xF959 }, { 0xD2F9, 0x5C3C }, { 0xD2FA, 0x6CE5 },
	{ 0xD2FB, 0x533F }, { 0xD2FC, 0x6EBA }, { 0xD2FD, 0x591A }, { 0xD2FE, 0x8336 }, { 0xD3A1, 0x4E39 }, { 0xD3A2, 0x4EB6 }, { 0xD3A3, 0x4F46 }, { 0xD3A4, 0x55AE },
	{ 0xD3A5, 0x5718 }, { 0xD3A6, 0x58C7 }, { 0xD3A7, 0x5F56 }, { 0xD3A8, 0x65B7 }, { 0xD3A9, 0x65E6 }, { 0xD3AA, 0x6A80 }, { 0xD3AB, 0x6BB5 }, { 0xD3AC, 0x6E4D },
	{ 0xD3AD, 0x77ED }, { 0xD3AE, 0x7AEF }, { 0xD3AF, 0x7C1E }, { 0xD3B0, 0x7DDE }, { 0xD3B1, 0x86CB }, { 0xD3B2, 0x8892 }, { 0xD3B3, 0x9132 }, { 0xD3B4, 0x935B },
	{ 0xD3B5, 0x64BB }, { 0xD3B6, 0x6FBE }, { 0xD3B7, 0x737A }, { 0xD3B8, 0x75B8 }, { 0xD3B9, 0x9054 }, { 0xD3BA, 0x5556 }, { 0xD3BB, 0x574D }, { 0xD3BC, 0x61BA },
	{ 0xD3BD, 0x64D4 }, { 0xD3BE, 0x66C7 }, { 0xD3BF, 0x6DE1 }, { 0xD3C0, 0x6E5B }, { 0xD3C1, 0x6F6D }, { 0xD3C2, 0x6FB9 }, { 0xD3C3, 0x75F0 }, { 0xD3C4, 0x8043 },
	{ 0xD3C5, 0x81BD }, { 0xD3C6, 0x8541 }, { 0xD3C7, 0x8983 }, { 0xD3C8, 0x8AC7 }, { 0xD3C9, 0x8B5A }, { 0xD3CA, 0x931F }, { 0xD3CB, 0x6C93 }, { 0xD3CC, 0x7553 },
	{ 0xD3CD, 0x7B54 }, { 0xD3CE, 0x8E0F }, { 0xD3CF, 0x905D }, { 0xD3D0, 0x5510 }, { 0xD3D1, 0x5802 }, { 0xD3D2, 0x5858 }, { 0xD3D3, 0x5E62 }, { 0xD3D4, 0x6207 },
	{ 0xD3D5, 0x649E }, { 0xD3D6, 0x68E0 }, { 0xD3D7, 0x7576 }, { 0xD3D8, 0x7CD6 }, { 0xD3D9, 0x87B3 }, { 0xD3DA, 0x9EE8 }, { 0xD3DB, 0x4EE3 }, { 0xD3DC, 0x5788 },
	{ 0xD3DD, 0x576E }, { 0xD3DE, 0x5927 }, { 0xD3DF, 0x5C0D }, { 0xD3E0, 0x5CB1 }, { 0xD3E1, 0x5E36 }, { 0xD3E2, 0x5F85 }, { 0xD3E3, 0x6234 }, { 0xD3E4, 0x64E1 },
	{ 0xD3E5, 0x73B3 }, { 0xD3E6, 0x81FA }, { 0xD3E7, 0x888B }, { 0xD3E8, 0x8CB8 }, { 0xD3E9, 0x968A }, { 0xD3EA, 0x9EDB }, { 0xD3EB, 0x5B85 }, { 0xD3EC, 0x5FB7 },
	{ 0xD3ED, 0x60B3 }, { 0xD3EE, 0x5012 }, { 0xD3EF, 0x5200 }, { 0xD3F0, 0x5230 }, { 0xD3F1, 0x5716 }, { 0xD3F2, 0x5835 }, { 0xD3F3, 0x5857 }, { 0xD3F4, 0x5C0E },
	{ 0xD3F5, 0x5C60 }, { 0xD3F6, 0x5CF6 }, { 0xD3F7, 0x5D8B }, { 0xD3F8, 0x5EA6 }, { 0xD3F9, 0x5F92 }, { 0xD3FA, 0x60BC }, { 0xD3FB, 0x6311 }, { 0xD3FC, 0x6389 },
	{ 0xD3FD, 0x6417 }, { 0xD3FE, 0x6843 }, { 0xD4A1, 0x68F9 }, { 0xD4A2, 0x6AC2 }, { 0xD4A3, 0x6DD8 }, { 0xD4A4, 0x6E21 }, { 0xD4A5, 0x6ED4 }, { 0xD4A6, 0x6FE4 },
	{ 0xD4A7, 0x71FE }, { 0xD4A8, 0x76DC }, { 0xD4A9, 0x7779 }, { 0xD4AA, 0x79B1 }, { 0xD4AB, 0x7A3B }, { 0xD4AC, 0x8404 }, { 0xD4AD, 0x89A9 }, { 0xD4AE, 0x8CED },
	{ 0xD4AF, 0x8DF3 }, { 0xD4B0, 0x8E48 }, { 0xD4B1, 0x9003 }, { 0xD4B2, 0x9014 }, { 0xD4B3, 0x9053 }, { 0xD4B4, 0x90FD }, { 0xD4B5, 0x934D }, { 0xD4B6, 0x9676 },
	{ 0xD4B7, 0x97DC }, { 0xD4B8, 0x6BD2 }, { 0xD4B9, 0x7006 }, { 0xD4BA, 0x7258 }, { 0xD4BB, 0x72A2 }, { 0xD4BC, 0x7368 }, { 0xD4BD, 0x7763 }, { 0xD4BE, 0x79BF },
	{ 0xD4BF, 0x7BE4 }, { 0xD4C0, 0x7E9B }, { 0xD4C1, 0x8B80 }, { 0xD4C2, 0x58A9 }, { 0xD4C3, 0x60C7 }, { 0xD4C4, 0x6566 }, { 0xD4C5, 0x65FD }, { 0xD4C6, 0x66BE },
	{ 0xD4C7, 0x6C8C }, { 0xD4C8, 0x711E }, { 0xD4C9, 0x71C9 }, { 0xD4CA, 0x8C5A }, { 0xD4CB, 0x9813 }, { 0xD4CC, 0x4E6D }, { 0xD4CD, 0x7A81 }, { 0xD4CE, 0x4EDD },
	{ 0xD4CF, 0x51AC }, { 0xD4D0, 0x51CD }, { 0xD4D1, 0x52D5 }, { 0xD4D2, 0x540C }, { 0xD4D3, 0x61A7 }, { 0xD4D4, 0x6771 }, { 0xD4D5, 0x6850 }, { 0xD4D6, 0x68DF },
	{ 0xD4D7, 0x6D1E }, { 0xD4D8, 0x6F7C }, { 0xD4D9, 0x75BC }, { 0xD4DA, 0x77B3 }, { 0xD4DB, 0x7AE5 }, { 0xD4DC, 0x80F4 }, { 0xD4DD, 0x8463 }, { 0xD4DE, 0x9285 },
	{ 0xD4DF, 0x515C }, { 0xD4E0, 0x6597 }, { 0xD4E1, 0x675C }, { 0xD4E2, 0x6793 }, { 0xD4E3, 0x75D8 }, { 0xD4E4, 0x7AC7 }, { 0xD4E5, 0x8373 }, { 0xD4E6, 0xF95A },
	{ 0xD4E7, 0x8C46 }, { 0xD4E8, 0x9017 }, { 0xD4E9, 0x982D }, { 0xD4EA, 0x5C6F }, { 0xD4EB, 0x81C0 }, { 0xD4EC, 0x829A }, { 0xD4ED, 0x9041 }, { 0xD4EE, 0x906F },
	{ 0xD4EF, 0x920D }, { 0xD4F0, 0x5F97 }, { 0xD4F1, 0x5D9D }, { 0xD4F2, 0x6A59 }, { 0xD4F3, 0x71C8 }, { 0xD4F4, 0x767B }, { 0xD4F5, 0x7B49 }, { 0xD4F6, 0x85E4 },
	{ 0xD4F7, 0x8B04 }, { 0xD4F8, 0x9127 }, { 0xD4F9, 0x9A30 }, { 0xD4FA, 0x5587 }, { 0xD4FB, 0x61F6 }, { 0xD4FC, 0xF95B }, { 0xD4FD, 0x7669 }, { 0xD4FE, 0x7F85 },
	{ 0xD5A1, 0x863F }, { 0xD5A2, 0x87BA }, { 0xD5A3, 0x88F8 }, { 0xD5A4, 0x908F }, { 0xD5A5, 0xF95C }, { 0xD5A6, 0x6D1B }, { 0xD5A7, 0x70D9 }, { 0xD5A8, 0x73DE },
	{ 0xD5A9, 0x7D61 }, { 0xD5AA, 0x843D }, { 0xD5AB, 0xF95D }, { 0xD5AC, 0x916A }, { 0xD5AD, 0x99F1 }, { 0xD5AE, 0xF95E }, { 0xD5AF, 0x4E82 }, { 0xD5B0, 0x5375 },
	{ 0xD5B1, 0x6B04 }, { 0xD5B2, 0x6B12 }, { 0xD5B3, 0x703E }, { 0xD5B4, 0x721B }, { 0xD5B5, 0x862D }, { 0xD5B6, 0x9E1E }, { 0xD5B7, 0x524C }, { 0xD5B8, 0x8FA3 },
	{ 0xD5B9, 0x5D50 }, { 0xD5BA, 0x64E5 }, { 0xD5BB, 0x652C }, { 0xD5BC, 0x6B16 }, { 0xD5BD, 0x6FEB }, { 0xD5BE, 0x7C43 }, { 0xD5BF, 0x7E9C }, { 0xD5C0, 0x85CD },
	{ 0xD5C1, 0x8964 }, { 0xD5C2, 0x89BD }, { 0xD5C3, 0x62C9 }, { 0xD5C4, 0x81D8 }, { 0xD5C5, 0x881F }, { 0xD5C6, 0x5ECA }, { 0xD5C7, 0x6717 }, { 0xD5C8, 0x6D6A },
	{ 0xD5C9, 0x72FC }, { 0xD5CA, 0x7405 }, { 0xD5CB, 0x746F }, { 0xD5CC, 0x8782 }, { 0xD5CD, 0x90DE }, { 0xD5CE, 0x4F86 }, { 0xD5CF, 0x5D0D }, { 0xD5D0, 0x5FA0 },
	{ 0xD5D1, 0x840A }, { 0xD5D2, 0x51B7 }, { 0xD5D3, 0x63A0 }, { 0xD5D4, 0x7565 }, { 0xD5D5, 0x4EAE }, { 0xD5D6, 0x5006 }, { 0xD5D7, 0x5169 }, { 0xD5D8, 0x51C9 },
	{ 0xD5D9, 0x6881 }, { 0xD5DA, 0x6A11 }, { 0xD5DB, 0x7CAE }, { 0xD5DC, 0x7CB1 }, { 0xD5DD, 0x7CE7 }, { 0xD5DE, 0x826F }, { 0xD5DF, 0x8AD2 }, { 0xD5E0, 0x8F1B },
	{ 0xD5E1, 0x91CF }, { 0xD5E2, 0x4FB6 }, { 0xD5E3, 0x5137 }, { 0xD5E4, 0x52F5 }, { 0xD5E5, 0x5442 }, { 0xD5E6, 0x5EEC }, { 0xD5E7, 0x616E }, { 0xD5E8, 0x623E },
	{ 0xD5E9, 0x65C5 }, { 0xD5EA, 0x6ADA }, { 0xD5EB, 0x6FFE }, { 0xD5EC, 0x792A }, { 0xD5ED, 0x85DC }, { 0xD5EE, 0x8823 }, { 0xD5EF, 0x95AD }, { 0xD5F0, 0x9A62 },
	{ 0xD5F1, 0x9A6A }, { 0xD5F2, 0x9E97 }, { 0xD5F3, 0x9ECE }, { 0xD5F4, 0x529B }, { 0xD5F5, 0x66C6 }, { 0xD5F6, 0x6B77 }, { 0xD5F7, 0x701D }, { 0xD5F8, 0x792B },
	{ 0xD5F9, 0x8F62 }, { 0xD5FA, 0x9742 }, { 0xD5FB, 0x6190 }, { 0xD5FC, 0x6200 }, { 0xD5FD, 0x6523 }, { 0xD5FE, 0x6F23 }, { 0xD6A1, 0x7149 }, { 0xD6A2, 0x7489 },
	{ 0xD6A3, 0x7DF4 }, { 0xD6A4, 0x806F }, { 0xD6A5, 0x84EE }, { 0xD6A6, 0x8F26 }, { 0xD6A7, 0x9023 }, { 0xD6A8, 0x934A }, { 0xD6A9, 0x51BD }, { 0xD6AA, 0x5217 },
	{ 0xD6AB, 0x52A3 }, { 0xD6AC, 0x6D0C }, { 0xD6AD, 0x70C8 }, { 0xD6AE, 0x88C2 }, { 0xD6AF, 0x5EC9 }, { 0xD6B0, 0x6582 }, { 0xD6B1, 0x6BAE }, { 0xD6B2, 0x6FC2 },
	{ 0xD6B3, 0x7C3E }, { 0xD6B4, 0x7375 }, { 0xD6B5, 0x4EE4 }, { 0xD6B6, 0x4F36 }, { 0xD6B7, 0x56F9 }, { 0xD6B8, 0xF95F }, { 0xD6B9, 0x5CBA }, { 0xD6BA, 0x5DBA },
	{ 0xD6BB, 0x601C }, { 0xD6BC, 0x73B2 }, { 0xD6BD, 0x7B2D }, { 0xD6BE, 0x7F9A }, { 0xD6BF, 0x7FCE }, { 0xD6C0, 0x8046 }, { 0xD6C1, 0x901E }, { 0xD6C2, 0x9234 },
	{ 0xD6C3, 0x96F6 }, { 0xD6C4, 0x9748 }, { 0xD6C5, 0x9818 }, { 0xD6C6, 0x9F61 }, { 0xD6C7, 0x4F8B }, { 0xD6C8, 0x6FA7 }, { 0xD6C9, 0x79AE }, { 0xD6CA, 0x91B4 },
	{ 0xD6CB, 0x96B7 }, { 0xD6CC, 0x52DE }, { 0xD6CD, 0xF960 }, { 0xD6CE, 0x6488 }, { 0xD6CF, 0x64C4 }, { 0xD6D0, 0x6AD3 }, { 0xD6D1, 0x6F5E }, { 0xD6D2, 0x7018 },
	{ 0xD6D3, 0x7210 }, { 0xD6D4, 0x76E7 }, { 0xD6D5, 0x8001 }, { 0xD6D6, 0x8606 }, { 0xD6D7, 0x865C }, { 0xD6D8, 0x8DEF }, { 0xD6D9, 0x8F05 }, { 0xD6DA, 0x9732 },
	{ 0xD6DB, 0x9B6F }, { 0xD6DC, 0x9DFA }, { 0xD6DD, 0x9E75 }, { 0xD6DE, 0x788C }, { 0xD6DF, 0x797F }, { 0xD6E0, 0x7DA0 }, { 0xD6E1, 0x83C9 }, { 0xD6E2, 0x9304 },
	{ 0xD6E3, 0x9E7F }, { 0xD6E4, 0x9E93 }, { 0xD6E5, 0x8AD6 }, { 0xD6E6, 0x58DF }, { 0xD6E7, 0x5F04 }, { 0xD6E8, 0x6727 }, { 0xD6E9, 0x7027 }, { 0xD6EA, 0x74CF },
	{ 0xD6EB, 0x7C60 }, { 0xD6EC, 0x807E }, { 0xD6ED, 0x5121 }, { 0xD6EE, 0x7028 }, { 0xD6EF, 0x7262 }, { 0xD6F0, 0x78CA }, { 0xD6F1, 0x8CC2 }, { 0xD6F2, 0x8CDA },
	{ 0xD6F3, 0x8CF4 }, { 0xD6F4, 0x96F7 }, { 0xD6F5, 0x4E86 }, { 0xD6F6, 0x50DA }, { 0xD6F7, 0x5BEE }, { 0xD6F8, 0x5ED6 }, { 0xD6F9, 0x6599 }, { 0xD6FA, 0x71CE },
	{ 0xD6FB, 0x7642 }, { 0xD6FC, 0x77AD }, { 0xD6FD, 0x804A }, { 0xD6FE, 0x84FC }, { 0xD7A1, 0x907C }, { 0xD7A2, 0x9B27 }, { 0xD7A3, 0x9F8D }, { 0xD7A4, 0x58D8 },
	{ 0xD7A5, 0x5A41 }, { 0xD7A6, 0x5C62 }, { 0xD7A7, 0x6A13 }, { 0xD7A8, 0x6DDA }, { 0xD7A9, 0x6F0F }, { 0xD7AA, 0x763B }, { 0xD7AB, 0x7D2F }, { 0xD7AC, 0x7E37 },
	{ 0xD7AD, 0x851E }, { 0xD7AE, 0x8938 }, { 0xD7AF, 0x93E4 }, { 0xD7B0, 0x964B }, { 0xD7B1, 0x5289 }, { 0xD7B2, 0x65D2 }, { 0xD7B3, 0x67F3 }, { 0xD7B4, 0x69B4 },
	{ 0xD7B5, 0x6D41 }, { 0xD7B6, 0x6E9C }, { 0xD7B7, 0x700F }, { 0xD7B8, 0x7409 }, { 0xD7B9, 0x7460 }, { 0xD7BA, 0x7559 }, { 0xD7BB, 0x7624 }, { 0xD7BC, 0x786B },
	{ 0xD7BD, 0x8B2C }, { 0xD7BE, 0x985E }, { 0xD7BF, 0x516D }, { 0xD7C0, 0x622E }, { 0xD7C1, 0x9678 }, { 0xD7C2, 0x4F96 }, { 0xD7C3, 0x502B }, { 0xD7C4, 0x5D19 },
	{ 0xD7C5, 0x6DEA }, { 0xD7C6, 0x7DB8 }, { 0xD7C7, 0x8F2A }, { 0xD7C8, 0x5F8B }, { 0xD7C9, 0x6144 }, { 0xD7CA, 0x6817 }, { 0xD7CB, 0xF961 }, { 0xD7CC, 0x9686 },
	{ 0xD7CD, 0x52D2 }, { 0xD7CE, 0x808B }, { 0xD7CF, 0x51DC }, { 0xD7D0, 0x51CC }, { 0xD7D1, 0x695E }, { 0xD7D2, 0x7A1C }, { 0xD7D3, 0x7DBE }, { 0xD7D4, 0x83F1 },
	{ 0xD7D5, 0x9675 }, { 0xD7D6, 0x4FDA }, { 0xD7D7, 0x5229 }, { 0xD7D8, 0x5398 }, { 0xD7D9, 0x540F }, { 0xD7DA, 0x550E }, { 0xD7DB, 0x5C65 }, { 0xD7DC, 0x60A7 },
	{ 0xD7DD, 0x674E }, { 0xD7DE, 0x68A8 }, { 0xD7DF, 0x6D6C }, { 0xD7E0, 0x7281 }, { 0xD7E1, 0x72F8 }, { 0xD7E2, 0x7406 }, { 0xD7E3, 0x7483 }, { 0xD7E4, 0xF962 },
	{ 0xD7E5, 0x75E2 }, { 0xD7E6, 0x7C6C }, { 0xD7E7, 0x7F79 }, { 0xD7E8, 0x7FB8 }, { 0xD7E9, 0x8389 }, { 0xD7EA, 0x88CF }, { 0xD7EB, 0x88E1 }, { 0xD7EC, 0x91CC },
	{ 0xD7ED, 0x91D0 }, { 0xD7EE, 0x96E2 }, { 0xD7EF, 0x9BC9 }, { 0xD7F0, 0x541D }, { 0xD7F1, 0x6F7E }, { 0xD7F2, 0x71D0 }, { 0xD7F3, 0x7498 }, { 0xD7F4, 0x85FA },
	{ 0xD7F5, 0x8EAA }, { 0xD7F6, 0x96A3 }, { 0xD7F7, 0x9C57 }, { 0xD7F8, 0x9E9F }, { 0xD7F9, 0x6797 }, { 0xD7FA, 0x6DCB }, { 0xD7FB, 0x7433 }, { 0xD7FC, 0x81E8 },
	{ 0xD7FD, 0x9716 }, { 0xD7FE, 0x782C }, { 0xD8A1, 0x7ACB }, { 0xD8A2, 0x7B20 }, { 0xD8A3, 0x7C92 }, { 0xD8A4, 0x6469 }, { 0xD8A5, 0x746A }, { 0xD8A6, 0x75F2 },
	{ 0xD8A7, 0x78BC }, { 0xD8A8, 0x78E8 }, { 0xD8A9, 0x99AC }, { 0xD8AA, 0x9B54 }, { 0xD8AB, 0x9EBB }, { 0xD8AC, 0x5BDE }, { 0xD8AD, 0x5E55 }, { 0xD8AE, 0x6F20 },
	{ 0xD8AF, 0x819C }, { 0xD8B0, 0x83AB }, { 0xD8B1, 0x9088 }, { 0xD8B2, 0x4E07 }, { 0xD8B3, 0x534D }, { 0xD8B4, 0x5A29 }, { 0xD8B5, 0x5DD2 }, { 0xD8B6, 0x5F4E },
	{ 0xD8B7, 0x6162 }, { 0xD8B8, 0x633D }, { 0xD8B9, 0x6669 }, { 0xD8BA, 0x66FC }, { 0xD8BB, 0x6EFF }, { 0xD8BC, 0x6F2B }, { 0xD8BD, 0x7063 }, { 0xD8BE, 0x779E },
	{ 0xD8BF, 0x842C }, { 0xD8C0, 0x8513 }, { 0xD8C1, 0x883B }, { 0xD8C2, 0x8F13 }, { 0xD8C3, 0x9945 }, { 0xD8C4, 0x9C3B }, { 0xD8C5, 0x551C }, { 0xD8C6, 0x62B9 },
	{ 0xD8C7, 0x672B }, { 0xD8C8, 0x6CAB }, { 0xD8C9, 0x8309 }, { 0xD8CA, 0x896A }, { 0xD8CB, 0x977A }, { 0xD8CC, 0x4EA1 }, { 0xD8CD, 0x5984 }, { 0xD8CE, 0x5FD8 },
	{ 0xD8CF, 0x5FD9 }, { 0xD8D0, 0x671B }, { 0xD8D1, 0x7DB2 }, { 0xD8D2, 0x7F54 }, { 0xD8D3, 0x8292 }, { 0xD8D4, 0x832B }, { 0xD8D5, 0x83BD }, { 0xD8D6, 0x8F1E },
	{ 0xD8D7, 0x9099 }, { 0xD8D8, 0x57CB }, { 0xD8D9, 0x59B9 }, { 0xD8DA, 0x5A92 }, { 0xD8DB, 0x5BD0 }, { 0xD8DC, 0x6627 }, { 0xD8DD, 0x679A }, { 0xD8DE, 0x6885 },
	{ 0xD8DF, 0x6BCF }, { 0xD8E0, 0x7164 }, { 0xD8E1, 0x7F75 }, { 0xD8E2, 0x8CB7 }, { 0xD8E3, 0x8CE3 }, { 0xD8E4, 0x9081 }, { 0xD8E5, 0x9B45 }, { 0xD8E6, 0x8108 },
	{ 0xD8E7, 0x8C8A }, { 0xD8E8, 0x964C }, { 0xD8E9, 0x9A40 }, { 0xD8EA, 0x9EA5 }, { 0xD8EB, 0x5B5F }, { 0xD8EC, 0x6C13 }, { 0xD8ED, 0x731B }, { 0xD8EE, 0x76F2 },
	{ 0xD8EF, 0x76DF }, { 0xD8F0, 0x840C }, { 0xD8F1, 0x51AA }, { 0xD8F2, 0x8993 }, { 0xD8F3, 0x514D }, { 0xD8F4, 0x5195 }, { 0xD8F5, 0x52C9 }, { 0xD8F6, 0x68C9 },
	{ 0xD8F7, 0x6C94 }, { 0xD8F8, 0x7704 }, { 0xD8F9, 0x7720 }, { 0xD8FA, 0x7DBF }, { 0xD8FB, 0x7DEC }, { 0xD8FC, 0x9762 }, { 0xD8FD, 0x9EB5 }, { 0xD8FE, 0x6EC5 },
	{ 0xD9A1, 0x8511 }, { 0xD9A2, 0x51A5 }, { 0xD9A3, 0x540D }, { 0xD9A4, 0x547D }, { 0xD9A5, 0x660E }, { 0xD9A6, 0x669D }, { 0xD9A7, 0x6927 }, { 0xD9A8, 0x6E9F },
	{ 0xD9A9, 0x76BF }, { 0xD9AA, 0x7791 }, { 0xD9AB, 0x8317 }, { 0xD9AC, 0x84C2 }, { 0xD9AD, 0x879F }, { 0xD9AE, 0x9169 }, { 0xD9AF, 0x9298 }, { 0xD9B0, 0x9CF4 },
	{ 0xD9B1, 0x8882 }, { 0xD9B2, 0x4FAE }, { 0xD9B3, 0x5192 }, { 0xD9B4, 0x52DF }, { 0xD9B5, 0x59C6 }, { 0xD9B6, 0x5E3D }, { 0xD9B7, 0x6155 }, { 0xD9B8, 0x6478 },
	{ 0xD9B9, 0x6479 }, { 0xD9BA, 0x66AE }, { 0xD9BB, 0x67D0 }, { 0xD9BC, 0x6A21 }, { 0xD9BD, 0x6BCD }, { 0xD9BE, 0x6BDB }, { 0xD9BF, 0x725F }, { 0xD9C0, 0x7261 },
	{ 0xD9C1, 0x7441 }, { 0xD9C2, 0x7738 }, { 0xD9C3, 0x77DB }, { 0xD9C4, 0x8017 }, { 0xD9C5, 0x82BC }, { 0xD9C6, 0x8305 }, { 0xD9C7, 0x8B00 }, { 0xD9C8, 0x8B28 },
	{ 0xD9C9, 0x8C8C }, { 0xD9CA, 0x6728 }, { 0xD9CB, 0x6C90 }, { 0xD9CC, 0x7267 }, { 0xD9CD, 0x76EE }, { 0xD9CE, 0x7766 }, { 0xD9CF, 0x7A46 }, { 0xD9D0, 0x9DA9 },
	{ 0xD9D1, 0x6B7F }, { 0xD9D2, 0x6C92 }, { 0xD9D3, 0x5922 }, { 0xD9D4, 0x6726 }, { 0xD9D5, 0x8499 }, { 0xD9D6, 0x536F }, { 0xD9D7, 0x5893 }, { 0xD9D8, 0x5999 },
	{ 0xD9D9, 0x5EDF }, { 0xD9DA, 0x63CF }, { 0xD9DB, 0x6634 }, { 0xD9DC, 0x6773 }, { 0xD9DD, 0x6E3A }, { 0xD9DE, 0x732B }, { 0xD9DF, 0x7AD7 }, { 0xD9E0, 0x82D7 },
	{ 0xD9E1, 0x9328 }, { 0xD9E2, 0x52D9 }, { 0xD9E3, 0x5DEB }, { 0xD9E4, 0x61AE }, { 0xD9E5, 0x61CB }, { 0xD9E6, 0x620A }, { 0xD9E7, 0x62C7 }, { 0xD9E8, 0x64AB },
	{ 0xD9E9, 0x65E0 }, { 0xD9EA, 0x6959 }, { 0xD9EB, 0x6B66 }, { 0xD9EC, 0x6BCB }, { 0xD9ED, 0x7121 }, { 0xD9EE, 0x73F7 }, { 0xD9EF, 0x755D }, { 0xD9F0, 0x7E46 },
	{ 0xD9F1, 0x821E }, { 0xD9F2, 0x8302 }, { 0xD9F3, 0x856A }, { 0xD9F4, 0x8AA3 }, { 0xD9F5, 0x8CBF }, { 0xD9F6, 0x9727 }, { 0xD9F7, 0x9D61 }, { 0xD9F8, 0x58A8 },
	{ 0xD9F9, 0x9ED8 }, { 0xD9FA, 0x5011 }, { 0xD9FB, 0x520E }, { 0xD9FC, 0x543B }, { 0xD9FD, 0x554F }, { 0xD9FE, 0x6587 }, { 0xDAA1, 0x6C76 }, { 0xDAA2, 0x7D0A },
	{ 0xDAA3, 0x7D0B }, { 0xDAA4, 0x805E }, { 0xDAA5, 0x868A }, { 0xDAA6, 0x9580 }, { 0xDAA7, 0x96EF }, { 0xDAA8, 0x52FF }, { 0xDAA9, 0x6C95 }, { 0xDAAA, 0x7269 },
	{ 0xDAAB, 0x5473 }, { 0xDAAC, 0x5A9A }, { 0xDAAD, 0x5C3E }, { 0xDAAE, 0x5D4B }, { 0xDAAF, 0x5F4C }, { 0xDAB0, 0x5FAE }, { 0xDAB1, 0x672A }, { 0xDAB2, 0x68B6 },
	{ 0xDAB3, 0x6963 }, { 0xDAB4, 0x6E3C }, { 0xDAB5, 0x6E44 }, { 0xDAB6, 0x7709 }, { 0xDAB7, 0x7C73 }, { 0xDAB8, 0x7F8E }, { 0xDAB9, 0x8587 }, { 0xDABA, 0x8B0E },
	{ 0xDABB, 0x8FF7 }, { 0xDABC, 0x9761 }, { 0xDABD, 0x9EF4 }, { 0xDABE, 0x5CB7 }, { 0xDABF, 0x60B6 }, { 0xDAC0, 0x610D }, { 0xDAC1, 0x61AB }, { 0xDAC2, 0x654F },
	{ 0xDAC3, 0x65FB }, { 0xDAC4, 0x65FC }, { 0xDAC5, 0x6C11 }, { 0xDAC6, 0x6CEF }, { 0xDAC7, 0x739F }, { 0xDAC8, 0x73C9 }, { 0xDAC9, 0x7DE1 }, { 0xDACA, 0x9594 },
	{ 0xDACB, 0x5BC6 }, { 0xDACC, 0x871C }, { 0xDACD, 0x8B10 }, { 0xDACE, 0x525D }, { 0xDACF, 0x535A }, { 0xDAD0, 0x62CD }, { 0xDAD1, 0x640F }, { 0xDAD2, 0x64B2 },
	{ 0xDAD3, 0x6734 }, { 0xDAD4, 0x6A38 }, { 0xDAD5, 0x6CCA }, { 0xDAD6, 0x73C0 }, { 0xDAD7, 0x749E }, { 0xDAD8, 0x7B94 }, { 0xDAD9, 0x7C95 }, { 0xDADA, 0x7E1B },
	{ 0xDADB, 0x818A }, { 0xDADC, 0x8236 }, { 0xDADD, 0x8584 }, { 0xDADE, 0x8FEB }, { 0xDADF, 0x96F9 }, { 0xDAE0, 0x99C1 }, { 0xDAE1, 0x4F34 }, { 0xDAE2, 0x534A },
	{ 0xDAE3, 0x53CD }, { 0xDAE4, 0x53DB }, { 0xDAE5, 0x62CC }, { 0xDAE6, 0x642C }, { 0xDAE7, 0x6500 }, { 0xDAE8, 0x6591 }, { 0xDAE9, 0x69C3 }, { 0xDAEA, 0x6CEE },
	{ 0xDAEB, 0x6F58 }, { 0xDAEC, 0x73ED }, { 0xDAED, 0x7554 }, { 0xDAEE, 0x7622 }, { 0xDAEF, 0x76E4 }, { 0xDAF0, 0x76FC }, { 0xDAF1, 0x78D0 }, { 0xDAF2, 0x78FB },
	{ 0xDAF3, 0x792C }, { 0xDAF4, 0x7D46 }, { 0xDAF5, 0x822C }, { 0xDAF6, 0x87E0 }, { 0xDAF7, 0x8FD4 }, { 0xDAF8, 0x9812 }, { 0xDAF9, 0x98EF }, { 0xDAFA, 0x52C3 },
	{ 0xDAFB, 0x62D4 }, { 0xDAFC, 0x64A5 }, { 0xDAFD, 0x6E24 }, { 0xDAFE, 0x6F51 }, { 0xDBA1, 0x767C }, { 0xDBA2, 0x8DCB }, { 0xDBA3, 0x91B1 }, { 0xDBA4, 0x9262 },
	{ 0xDBA5, 0x9AEE }, { 0xDBA6, 0x9B43 }, { 0xDBA7, 0x5023 }, { 0xDBA8, 0x508D }, { 0xDBA9, 0x574A }, { 0xDBAA, 0x59A8 }, { 0xDBAB, 0x5C28 }, { 0xDBAC, 0x5E47 },
	{ 0xDBAD, 0x5F77 }, { 0xDBAE, 0x623F }, { 0xDBAF, 0x653E }, { 0xDBB0, 0x65B9 }, { 0xDBB1, 0x65C1 }, { 0xDBB2, 0x6609 }, { 0xDBB3, 0x678B }, { 0xDBB4, 0x699C },
	{ 0xDBB5, 0x6EC2 }, { 0xDBB6, 0x78C5 }, { 0xDBB7, 0x7D21 }, { 0xDBB8, 0x80AA }, { 0xDBB9, 0x8180 }, { 0xDBBA, 0x822B }, { 0xDBBB, 0x82B3 }, { 0xDBBC, 0x84A1 },
	{ 0xDBBD, 0x868C }, { 0xDBBE, 0x8A2A }, { 0xDBBF, 0x8B17 }, { 0xDBC0, 0x90A6 }, { 0xDBC1, 0x9632 }, { 0xDBC2, 0x9F90 }, { 0xDBC3, 0x500D }, { 0xDBC4, 0x4FF3 },
	{ 0xDBC5, 0xF963 }, { 0xDBC6, 0x57F9 }, { 0xDBC7, 0x5F98 }, { 0xDBC8, 0x62DC }, { 0xDBC9, 0x6392 }, { 0xDBCA, 0x676F }, { 0xDBCB, 0x6E43 }, { 0xDBCC, 0x7119 },
	{ 0xDBCD, 0x76C3 }, { 0xDBCE, 0x80CC }, { 0xDBCF, 0x80DA }, { 0xDBD0, 0x88F4 }, { 0xDBD1, 0x88F5 }, { 0xDBD2, 0x8919 }, { 0xDBD3, 0x8CE0 }, { 0xDBD4, 0x8F29 },
	{ 0xDBD5, 0x914D }, { 0xDBD6, 0x966A }, { 0xDBD7, 0x4F2F }, { 0xDBD8, 0x4F70 }, { 0xDBD9, 0x5E1B }, { 0xDBDA, 0x67CF }, { 0xDBDB, 0x6822 }, { 0xDBDC, 0x767D },
	{ 0xDBDD, 0x767E }, { 0xDBDE, 0x9B44 }, { 0xDBDF, 0x5E61 }, { 0xDBE0, 0x6A0A }, { 0xDBE1, 0x7169 }, { 0xDBE2, 0x71D4 }, { 0xDBE3, 0x756A }, { 0xDBE4, 0xF964 },
	{ 0xDBE5, 0x7E41 }, { 0xDBE6, 0x8543 }, { 0xDBE7, 0x85E9 }, { 0xDBE8, 0x98DC }, { 0xDBE9, 0x4F10 }, { 0xDBEA, 0x7B4F }, { 0xDBEB, 0x7F70 }, { 0xDBEC, 0x95A5 },
	{ 0xDBED, 0x51E1 }, { 0xDBEE, 0x5E06 }, { 0xDBEF, 0x68B5 }, { 0xDBF0, 0x6C3E }, { 0xDBF1, 0x6C4E }, { 0xDBF2, 0x6CDB }, { 0xDBF3, 0x72AF }, { 0xDBF4, 0x7BC4 },
	{ 0xDBF5, 0x8303 }, { 0xDBF6, 0x6CD5 }, { 0xDBF7, 0x743A }, { 0xDBF8, 0x50FB }, { 0xDBF9, 0x5288 }, { 0xDBFA, 0x58C1 }, { 0xDBFB, 0x64D8 }, { 0xDBFC, 0x6A97 },
	{ 0xDBFD, 0x74A7 }, { 0xDBFE, 0x7656 }, { 0xDCA1, 0x78A7 }, { 0xDCA2, 0x8617 }, { 0xDCA3, 0x95E2 }, { 0xDCA4, 0x9739 }, { 0xDCA5, 0xF965 }, { 0xDCA6, 0x535E },
	{ 0xDCA7, 0x5F01 }, { 0xDCA8, 0x8B8A }, { 0xDCA9, 0x8FA8 }, { 0xDCAA, 0x8FAF }, { 0xDCAB, 0x908A }, { 0xDCAC, 0x5225 }, { 0xDCAD, 0x77A5 }, { 0xDCAE, 0x9C49 },
	{ 0xDCAF, 0x9F08 }, { 0xDCB0, 0x4E19 }, { 0xDCB1, 0x5002 }, { 0xDCB2, 0x5175 }, { 0xDCB3, 0x5C5B }, { 0xDCB4, 0x5E77 }, { 0xDCB5, 0x661E }, { 0xDCB6, 0x663A },
	{ 0xDCB7, 0x67C4 }, { 0xDCB8, 0x68C5 }, { 0xDCB9, 0x70B3 }, { 0xDCBA, 0x7501 }, { 0xDCBB, 0x75C5 }, { 0xDCBC, 0x79C9 }, { 0xDCBD, 0x7ADD }, { 0xDCBE, 0x8F27 },
	{ 0xDCBF, 0x9920 }, { 0xDCC0, 0x9A08 }, { 0xDCC1, 0x4FDD }, { 0xDCC2, 0x5821 }, { 0xDCC3, 0x5831 }, { 0xDCC4, 0x5BF6 }, { 0xDCC5, 0x666E }, { 0xDCC6, 0x6B65 },
	{ 0xDCC7, 0x6D11 }, { 0xDCC8, 0x6E7A }, { 0xDCC9, 0x6F7D }, { 0xDCCA, 0x73E4 }, { 0xDCCB, 0x752B }, { 0xDCCC, 0x83E9 }, { 0xDCCD, 0x88DC }, { 0xDCCE, 0x8913 },
	{ 0xDCCF, 0x8B5C }, { 0xDCD0, 0x8F14 }, { 0xDCD1, 0x4F0F }, { 0xDCD2, 0x50D5 }, { 0xDCD3, 0x5310 }, { 0xDCD4, 0x535C }, { 0xDCD5, 0x5B93 }, { 0xDCD6, 0x5FA9 },
	{ 0xDCD7, 0x670D }, { 0xDCD8, 0x798F }, { 0xDCD9, 0x8179 }, { 0xDCDA, 0x832F }, { 0xDCDB, 0x8514 }, { 0xDCDC, 0x8907 }, { 0xDCDD, 0x8986 }, { 0xDCDE, 0x8F39 },
	{ 0xDCDF, 0x8F3B }, { 0xDCE0, 0x99A5 }, { 0xDCE1, 0x9C12 }, { 0xDCE2, 0x672C }, { 0xDCE3, 0x4E76 }, { 0xDCE4, 0x4FF8 }, { 0xDCE5, 0x5949 }, { 0xDCE6, 0x5C01 },
	{ 0xDCE7, 0x5CEF }, { 0xDCE8, 0x5CF0 }, { 0xDCE9, 0x6367 }, { 0xDCEA, 0x68D2 }, { 0xDCEB, 0x70FD }, { 0xDCEC, 0x71A2 }, { 0xDCED, 0x742B }, { 0xDCEE, 0x7E2B },
	{ 0xDCEF, 0x84EC }, { 0xDCF0, 0x8702 }, { 0xDCF1, 0x9022 }, { 0xDCF2, 0x92D2 }, { 0xDCF3, 0x9CF3 }, { 0xDCF4, 0x4E0D }, { 0xDCF5, 0x4ED8 }, { 0xDCF6, 0x4FEF },
	{ 0xDCF7, 0x5085 }, { 0xDCF8, 0x5256 }, { 0xDCF9, 0x526F }, { 0xDCFA, 0x5426 }, { 0xDCFB, 0x5490 }, { 0xDCFC, 0x57E0 }, { 0xDCFD, 0x592B }, { 0xDCFE, 0x5A66 },
	{ 0xDDA1, 0x5B5A }, { 0xDDA2, 0x5B75 }, { 0xDDA3, 0x5BCC }, { 0xDDA4, 0x5E9C }, { 0xDDA5, 0xF966 }, { 0xDDA6, 0x6276 }, { 0xDDA7, 0x6577 }, { 0xDDA8, 0x65A7 },
	{ 0xDDA9, 0x6D6E }, { 0xDDAA, 0x6EA5 }, { 0xDDAB, 0x7236 }, { 0xDDAC, 0x7B26 }, { 0xDDAD, 0x7C3F }, { 0xDDAE, 0x7F36 }, { 0xDDAF, 0x8150 }, { 0xDDB0, 0x8151 },
	{ 0xDDB1, 0x819A }, { 0xDDB2, 0x8240 }, { 0xDDB3, 0x8299 }, { 0xDDB4, 0x83A9 }, { 0xDDB5, 0x8A03 }, { 0xDDB6, 0x8CA0 }, { 0xDDB7, 0x8CE6 }, { 0xDDB8, 0x8CFB },
	{ 0xDDB9, 0x8D74 }, { 0xDDBA, 0x8DBA }, { 0xDDBB, 0x90E8 }, { 0xDDBC, 0x91DC }, { 0xDDBD, 0x961C }, { 0xDDBE, 0x9644 }, { 0xDDBF, 0x99D9 }, { 0xDDC0, 0x9CE7 },
	{ 0xDDC1, 0x5317 }, { 0xDDC2, 0x5206 }, { 0xDDC3, 0x5429 }, { 0xDDC4, 0x5674 }, { 0xDDC5, 0x58B3 }, { 0xDDC6, 0x5954 }, { 0xDDC7, 0x596E }, { 0xDDC8, 0x5FFF },
	{ 0xDDC9, 0x61A4 }, { 0xDDCA, 0x626E }, { 0xDDCB, 0x6610 }, { 0xDDCC, 0x6C7E }, { 0xDDCD, 0x711A }, { 0xDDCE, 0x76C6 }, { 0xDDCF, 0x7C89 }, { 0xDDD0, 0x7CDE },
	{ 0xDDD1, 0x7D1B }, { 0xDDD2, 0x82AC }, { 0xDDD3, 0x8CC1 }, { 0xDDD4, 0x96F0 }, { 0xDDD5, 0xF967 }, { 0xDDD6, 0x4F5B }, { 0xDDD7, 0x5F17 }, { 0xDDD8, 0x5F7F },
	{ 0xDDD9, 0x62C2 }, { 0xDDDA, 0x5D29 }, { 0xDDDB, 0x670B }, { 0xDDDC, 0x68DA }, { 0xDDDD, 0x787C }, { 0xDDDE, 0x7E43 }, { 0xDDDF, 0x9D6C }, { 0xDDE0, 0x4E15 },
	{ 0xDDE1, 0x5099 }, { 0xDDE2, 0x5315 }, { 0xDDE3, 0x532A }, { 0xDDE4, 0x5351 }, { 0xDDE5, 0x5983 }, { 0xDDE6, 0x5A62 }, { 0xDDE7, 0x5E87 }, { 0xDDE8, 0x60B2 },
	{ 0xDDE9, 0x618A }, { 0xDDEA, 0x6249 }, { 0xDDEB, 0x6279 }, { 0xDDEC, 0x6590 }, { 0xDDED, 0x6787 }, { 0xDDEE, 0x69A7 }, { 0xDDEF, 0x6BD4 }, { 0xDDF0, 0x6BD6 },
	{ 0xDDF1, 0x6BD7 }, { 0xDDF2, 0x6BD8 }, { 0xDDF3, 0x6CB8 }, { 0xDDF4, 0xF968 }, { 0xDDF5, 0x7435 }, { 0xDDF6, 0x75FA }, { 0xDDF7, 0x7812 }, { 0xDDF8, 0x7891 },
	{ 0xDDF9, 0x79D5 }, { 0xDDFA, 0x79D8 }, { 0xDDFB, 0x7C83 }, { 0xDDFC, 0x7DCB }, { 0xDDFD, 0x7FE1 }, { 0xDDFE, 0x80A5 }, { 0xDEA1, 0x813E }, { 0xDEA2, 0x81C2 },
	{ 0xDEA3, 0x83F2 }, { 0xDEA4, 0x871A }, { 0xDEA5, 0x88E8 }, { 0xDEA6, 0x8AB9 }, { 0xDEA7, 0x8B6C }, { 0xDEA8, 0x8CBB }, { 0xDEA9, 0x9119 }, { 0xDEAA, 0x975E },
	{ 0xDEAB, 0x98DB }, { 0xDEAC, 0x9F3B }, { 0xDEAD, 0x56AC }, { 0xDEAE, 0x5B2A }, { 0xDEAF, 0x5F6C }, { 0xDEB0, 0x658C }, { 0xDEB1, 0x6AB3 }, { 0xDEB2, 0x6BAF },
	{ 0xDEB3, 0x6D5C }, { 0xDEB4, 0x6FF1 }, { 0xDEB5, 0x7015 }, { 0xDEB6, 0x725D }, { 0xDEB7, 0x73AD }, { 0xDEB8, 0x8CA7 }, { 0xDEB9, 0x8CD3 }, { 0xDEBA, 0x983B },
	{ 0xDEBB, 0x6191 }, { 0xDEBC, 0x6C37 }, { 0xDEBD, 0x8058 }, { 0xDEBE, 0x9A01 }, { 0xDEBF, 0x4E4D }, { 0xDEC0, 0x4E8B }, { 0xDEC1, 0x4E9B }, { 0xDEC2, 0x4ED5 },
	{ 0xDEC3, 0x4F3A }, { 0xDEC4, 0x4F3C }, { 0xDEC5, 0x4F7F }, { 0xDEC6, 0x4FDF }, { 0xDEC7, 0x50FF }, { 0xDEC8, 0x53F2 }, { 0xDEC9, 0x53F8 }, { 0xDECA, 0x5506 },
	{ 0xDECB, 0x55E3 }, { 0xDECC, 0x56DB }, { 0xDECD, 0x58EB }, { 0xDECE, 0x5962 }, { 0xDECF, 0x5A11 }, { 0xDED0, 0x5BEB }, { 0xDED1, 0x5BFA }, { 0xDED2, 0x5C04 },
	{ 0xDED3, 0x5DF3 }, { 0xDED4, 0x5E2B }, { 0xDED5, 0x5F99 }, { 0xDED6, 0x601D }, { 0xDED7, 0x6368 }, { 0xDED8, 0x659C }, { 0xDED9, 0x65AF }, { 0xDEDA, 0x67F6 },
	{ 0xDEDB, 0x67FB }, { 0xDEDC, 0x68AD }, { 0xDEDD, 0x6B7B }, { 0xDEDE, 0x6C99 }, { 0xDEDF, 0x6CD7 }, { 0xDEE0, 0x6E23 }, { 0xDEE1, 0x7009 }, { 0xDEE2, 0x7345 },
	{ 0xDEE3, 0x7802 }, { 0xDEE4, 0x793E }, { 0xDEE5, 0x7940 }, { 0xDEE6, 0x7960 }, { 0xDEE7, 0x79C1 }, { 0xDEE8, 0x7BE9 }, { 0xDEE9, 0x7D17 }, { 0xDEEA, 0x7D72 },
	{ 0xDEEB, 0x8086 }, { 0xDEEC, 0x820D }, { 0xDEED, 0x838E }, { 0xDEEE, 0x84D1 }, { 0xDEEF, 0x86C7 }, { 0xDEF0, 0x88DF }, { 0xDEF1, 0x8A50 }, { 0xDEF2, 0x8A5E },
	{ 0xDEF3, 0x8B1D }, { 0xDEF4, 0x8CDC }, { 0xDEF5, 0x8D66 }, { 0xDEF6, 0x8FAD }, { 0xDEF7, 0x90AA }, { 0xDEF8, 0x98FC }, { 0xDEF9, 0x99DF }, { 0xDEFA, 0x9E9D },
	{ 0xDEFB, 0x524A }, { 0xDEFC, 0xF969 }, { 0xDEFD, 0x6714 }, { 0xDEFE, 0xF96A }, { 0xDFA1, 0x5098 }, { 0xDFA2, 0x522A }, { 0xDFA3, 0x5C71 }, { 0xDFA4, 0x6563 },
	{ 0xDFA5, 0x6C55 }, { 0xDFA6, 0x73CA }, { 0xDFA7, 0x7523 }, { 0xDFA8, 0x759D }, { 0xDFA9, 0x7B97 }, { 0xDFAA, 0x849C }, { 0xDFAB, 0x9178 }, { 0xDFAC, 0x9730 },
	{ 0xDFAD, 0x4E77 }, { 0xDFAE, 0x6492 }, { 0xDFAF, 0x6BBA }, { 0xDFB0, 0x715E }, { 0xDFB1, 0x85A9 }, { 0xDFB2, 0x4E09 }, { 0xDFB3, 0xF96B }, { 0xDFB4, 0x6749 },
	{ 0xDFB5, 0x68EE }, { 0xDFB6, 0x6E17 }, { 0xDFB7, 0x829F }, { 0xDFB8, 0x8518 }, { 0xDFB9, 0x886B }, { 0xDFBA, 0x63F7 }, { 0xDFBB, 0x6F81 }, { 0xDFBC, 0x9212 },
	{ 0xDFBD, 0x98AF }, { 0xDFBE, 0x4E0A }, { 0xDFBF, 0x50B7 }, { 0xDFC0, 0x50CF }, { 0xDFC1, 0x511F }, { 0xDFC2, 0x5546 }, { 0xDFC3, 0x55AA }, { 0xDFC4, 0x5617 },
	{ 0xDFC5, 0x5B40 }, { 0xDFC6, 0x5C19 }, { 0xDFC7, 0x5CE0 }, { 0xDFC8, 0x5E38 }, { 0xDFC9, 0x5E8A }, { 0xDFCA, 0x5EA0 }, { 0xDFCB, 0x5EC2 }, { 0xDFCC, 0x60F3 },
	{ 0xDFCD, 0x6851 }, { 0xDFCE, 0x6A61 }, { 0xDFCF, 0x6E58 }, { 0xDFD0, 0x723D }, { 0xDFD1, 0x7240 }, { 0xDFD2, 0x72C0 }, { 0xDFD3, 0x76F8 }, { 0xDFD4, 0x7965 },
	{ 0xDFD5, 0x7BB1 }, { 0xDFD6, 0x7FD4 }, { 0xDFD7, 0x88F3 }, { 0xDFD8, 0x89F4 }, { 0xDFD9, 0x8A73 }, { 0xDFDA, 0x8C61 }, { 0xDFDB, 0x8CDE }, { 0xDFDC, 0x971C },
	{ 0xDFDD, 0x585E }, { 0xDFDE, 0x74BD }, { 0xDFDF, 0x8CFD }, { 0xDFE0, 0x55C7 }, { 0xDFE1, 0xF96C }, { 0xDFE2, 0x7A61 }, { 0xDFE3, 0x7D22 }, { 0xDFE4, 0x8272 },
	{ 0xDFE5, 0x7272 }, { 0xDFE6, 0x751F }, { 0xDFE7, 0x7525 }, { 0xDFE8, 0xF96D }, { 0xDFE9, 0x7B19 }, { 0xDFEA, 0x5885 }, { 0xDFEB, 0x58FB }, { 0xDFEC, 0x5DBC },
	{ 0xDFED, 0x5E8F }, { 0xDFEE, 0x5EB6 }, { 0xDFEF, 0x5F90 }, { 0xDFF0, 0x6055 }, { 0xDFF1, 0x6292 }, { 0xDFF2, 0x637F }, { 0xDFF3, 0x654D }, { 0xDFF4, 0x6691 },
	{ 0xDFF5, 0x66D9 }, { 0xDFF6, 0x66F8 }, { 0xDFF7, 0x6816 }, { 0xDFF8, 0x68F2 }, { 0xDFF9, 0x7280 }, { 0xDFFA, 0x745E }, { 0xDFFB, 0x7B6E }, { 0xDFFC, 0x7D6E },
	{ 0xDFFD, 0x7DD6 }, { 0xDFFE, 0x7F72 }, { 0xE0A1, 0x80E5 }, { 0xE0A2, 0x8212 }, { 0xE0A3, 0x85AF }, { 0xE0A4, 0x897F }, { 0xE0A5, 0x8A93 }, { 0xE0A6, 0x901D },
	{ 0xE0A7, 0x92E4 }, { 0xE0A8, 0x9ECD }, { 0xE0A9, 0x9F20 }, { 0xE0AA, 0x5915 }, { 0xE0AB, 0x596D }, { 0xE0AC, 0x5E2D }, { 0xE0AD, 0x60DC }, { 0xE0AE, 0x6614 },
	{ 0xE0AF, 0x6673 }, { 0xE0B0, 0x6790 }, { 0xE0B1, 0x6C50 }, { 0xE0B2, 0x6DC5 }, { 0xE0B3, 0x6F5F }, { 0xE0B4, 0x77F3 }, { 0xE0B5, 0x78A9 }, { 0xE0B6, 0x84C6 },
	{ 0xE0B7, 0x91CB }, { 0xE0B8, 0x932B }, { 0xE0B9, 0x4ED9 }, { 0xE0BA, 0x50CA }, { 0xE0BB, 0x5148 }, { 0xE0BC, 0x5584 }, { 0xE0BD, 0x5B0B }, { 0xE0BE, 0x5BA3 },
	{ 0xE0BF, 0x6247 }, { 0xE0C0, 0x657E }, { 0xE0C1, 0x65CB }, { 0xE0C2, 0x6E32 }, { 0xE0C3, 0x717D }, { 0xE0C4, 0x7401 }, { 0xE0C5, 0x7444 }, { 0xE0C6, 0x7487 },
	{ 0xE0C7, 0x74BF }, { 0xE0C8, 0x766C }, { 0xE0C9, 0x79AA }, { 0xE0CA, 0x7DDA }, { 0xE0CB, 0x7E55 }, { 0xE0CC, 0x7FA8 }, { 0xE0CD, 0x817A }, { 0xE0CE, 0x81B3 },
	{ 0xE0CF, 0x8239 }, { 0xE0D0, 0x861A }, { 0xE0D1, 0x87EC }, { 0xE0D2, 0x8A75 }, { 0xE0D3, 0x8DE3 }, { 0xE0D4, 0x9078 }, { 0xE0D5, 0x9291 }, { 0xE0D6, 0x9425 },
	{ 0xE0D7, 0x994D }, { 0xE0D8, 0x9BAE }, { 0xE0D9, 0x5368 }, { 0xE0DA, 0x5C51 }, { 0xE0DB, 0x6954 }, { 0xE0DC, 0x6CC4 }, { 0xE0DD, 0x6D29 }, { 0xE0DE, 0x6E2B },
	{ 0xE0DF, 0x820C }, { 0xE0E0, 0x859B }, { 0xE0E1, 0x893B }, { 0xE0E2, 0x8A2D }, { 0xE0E3, 0x8AAA }, { 0xE0E4, 0x96EA }, { 0xE0E5, 0x9F67 }, { 0xE0E6, 0x5261 },
	{ 0xE0E7, 0x66B9 }, { 0xE0E8, 0x6BB2 }, { 0xE0E9, 0x7E96 }, { 0xE0EA, 0x87FE }, { 0xE0EB, 0x8D0D }, { 0xE0EC, 0x9583 }, { 0xE0ED, 0x965D }, { 0xE0EE, 0x651D },
	{ 0xE0EF, 0x6D89 }, { 0xE0F0, 0x71EE }, { 0xE0F1, 0xF96E }, { 0xE0F2, 0x57CE }, { 0xE0F3, 0x59D3 }, { 0xE0F4, 0x5BAC }, { 0xE0F5, 0x6027 }, { 0xE0F6, 0x60FA },
	{ 0xE0F7, 0x6210 }, { 0xE0F8, 0x661F }, { 0xE0F9, 0x665F }, { 0xE0FA, 0x7329 }, { 0xE0FB, 0x73F9 }, { 0xE0FC, 0x76DB }, { 0xE0FD, 0x7701 }, { 0xE0FE, 0x7B6C },
	{ 0xE1A1, 0x8056 }, { 0xE1A2, 0x8072 }, { 0xE1A3, 0x8165 }, { 0xE1A4, 0x8AA0 }, { 0xE1A5, 0x9192 }, { 0xE1A6, 0x4E16 }, { 0xE1A7, 0x52E2 }, { 0xE1A8, 0x6B72 },
	{ 0xE1A9, 0x6D17 }, { 0xE1AA, 0x7A05 }, { 0xE1AB, 0x7B39 }, { 0xE1AC, 0x7D30 }, { 0xE1AD, 0xF96F }, { 0xE1AE, 0x8CB0 }, { 0xE1AF, 0x53EC }, { 0xE1B0, 0x562F },
	{ 0xE1B1, 0x5851 }, { 0xE1B2, 0x5BB5 }, { 0xE1B3, 0x5C0F }, { 0xE1B4, 0x5C11 }, { 0xE1B5, 0x5DE2 }, { 0xE1B6, 0x6240 }, { 0xE1B7, 0x6383 }, { 0xE1B8, 0x6414 },
	{ 0xE1B9, 0x662D }, { 0xE1BA, 0x68B3 }, { 0xE1BB, 0x6CBC }, { 0xE1BC, 0x6D88 }, { 0xE1BD, 0x6EAF }, { 0xE1BE, 0x701F }, { 0xE1BF, 0x70A4 }, { 0xE1C0, 0x71D2 },
	{ 0xE1C1, 0x7526 }, { 0xE1C2, 0x758F }, { 0xE1C3, 0x758E }, { 0xE1C4, 0x7619 }, { 0xE1C5, 0x7B11 }, { 0xE1C6, 0x7BE0 }, { 0xE1C7, 0x7C2B }, { 0xE1C8, 0x7D20 },
	{ 0xE1C9, 0x7D39 }, { 0xE1CA, 0x852C }, { 0xE1CB, 0x856D }, { 0xE1CC, 0x8607 }, { 0xE1CD, 0x8A34 }, { 0xE1CE, 0x900D }, { 0xE1CF, 0x9061 }, { 0xE1D0, 0x90B5 },
	{ 0xE1D1, 0x92B7 }, { 0xE1D2, 0x97F6 }, { 0xE1D3, 0x9A37 }, { 0xE1D4, 0x4FD7 }, { 0xE1D5, 0x5C6C }, { 0xE1D6, 0x675F }, { 0xE1D7, 0x6D91 }, { 0xE1D8, 0x7C9F },
	{ 0xE1D9, 0x7E8C }, { 0xE1DA, 0x8B16 }, { 0xE1DB, 0x8D16 }, { 0xE1DC, 0x901F }, { 0xE1DD, 0x5B6B }, { 0xE1DE, 0x5DFD }, { 0xE1DF, 0x640D }, { 0xE1E0, 0x84C0 },
	{ 0xE1E1, 0x905C }, { 0xE1E2, 0x98E1 }, { 0xE1E3, 0x7387 }, { 0xE1E4, 0x5B8B }, { 0xE1E5, 0x609A }, { 0xE1E6, 0x677E }, { 0xE1E7, 0x6DDE }, { 0xE1E8, 0x8A1F },
	{ 0xE1E9, 0x8AA6 }, { 0xE1EA, 0x9001 }, { 0xE1EB, 0x980C }, { 0xE1EC, 0x5237 }, { 0xE1ED, 0xF970 }, { 0xE1EE, 0x7051 }, { 0xE1EF, 0x788E }, { 0xE1F0, 0x9396 },
	{ 0xE1F1, 0x8870 }, { 0xE1F2, 0x91D7 }, { 0xE1F3, 0x4FEE }, { 0xE1F4, 0x53D7 }, { 0xE1F5, 0x55FD }, { 0xE1F6, 0x56DA }, { 0xE1F7, 0x5782 }, { 0xE1F8, 0x58FD },
	{ 0xE1F9, 0x5AC2 }, { 0xE1FA, 0x5B88 }, { 0xE1FB, 0x5CAB }, { 0xE1FC, 0x5CC0 }, { 0xE1FD, 0x5E25 }, { 0xE1FE, 0x6101 }, { 0xE2A1, 0x620D }, { 0xE2A2, 0x624B },
	{ 0xE2A3, 0x6388 }, { 0xE2A4, 0x641C }, { 0xE2A5, 0x6536 }, { 0xE2A6, 0x6578 }, { 0xE2A7, 0x6A39 }, { 0xE2A8, 0x6B8A }, { 0xE2A9, 0x6C34 }, { 0xE2AA, 0x6D19 },
	{ 0xE2AB, 0x6F31 }, { 0xE2AC, 0x71E7 }, { 0xE2AD, 0x72E9 }, { 0xE2AE, 0x7378 }, { 0xE2AF, 0x7407 }, { 0xE2B0, 0x74B2 }, { 0xE2B1, 0x7626 }, { 0xE2B2, 0x7761 },
	{ 0xE2B3, 0x79C0 }, { 0xE2B4, 0x7A57 }, { 0xE2B5, 0x7AEA }, { 0xE2B6, 0x7CB9 }, { 0xE2B7, 0x7D8F }, { 0xE2B8, 0x7DAC }, { 0xE2B9, 0x7E61 }, { 0xE2BA, 0x7F9E },
	{ 0xE2BB, 0x8129 }, { 0xE2BC, 0x8331 }, { 0xE2BD, 0x8490 }, { 0xE2BE, 0x84DA }, { 0xE2BF, 0x85EA }, { 0xE2C0, 0x8896 }, { 0xE2C1, 0x8AB0 }, { 0xE2C2, 0x8B90 },
	{ 0xE2C3, 0x8F38 }, { 0xE2C4, 0x9042 }, { 0xE2C5, 0x9083 }, { 0xE2C6, 0x916C }, { 0xE2C7, 0x9296 }, { 0xE2C8, 0x92B9 }, { 0xE2C9, 0x968B }, { 0xE2CA, 0x96A7 },
	{ 0xE2CB, 0x96A8 }, { 0xE2CC, 0x96D6 }, { 0xE2CD, 0x9700 }, { 0xE2CE, 0x9808 }, { 0xE2CF, 0x9996 }, { 0xE2D0, 0x9AD3 }, { 0xE2D1, 0x9B1A }, { 0xE2D2, 0x53D4 },
	{ 0xE2D3, 0x587E }, { 0xE2D4, 0x5919 }, { 0xE2D5, 0x5B70 }, { 0xE2D6, 0x5BBF }, { 0xE2D7, 0x6DD1 }, { 0xE2D8, 0x6F5A }, { 0xE2D9, 0x719F }, { 0xE2DA, 0x7421 },
	{ 0xE2DB, 0x74B9 }, { 0xE2DC, 0x8085 }, { 0xE2DD, 0x83FD }, { 0xE2DE, 0x5DE1 }, { 0xE2DF, 0x5F87 }, { 0xE2E0, 0x5FAA }, { 0xE2E1, 0x6042 }, { 0xE2E2, 0x65EC },
	{ 0xE2E3, 0x6812 }, { 0xE2E4, 0x696F }, { 0xE2E5, 0x6A53 }, { 0xE2E6, 0x6B89 }, { 0xE2E7, 0x6D35 }, { 0xE2E8, 0x6DF3 }, { 0xE2E9, 0x73E3 }, { 0xE2EA, 0x76FE },
	{ 0xE2EB, 0x77AC }, { 0xE2EC, 0x7B4D }, { 0xE2ED, 0x7D14 }, { 0xE2EE, 0x8123 }, { 0xE2EF, 0x821C }, { 0xE2F0, 0x8340 }, { 0xE2F1, 0x84F4 }, { 0xE2F2, 0x8563 },
	{ 0xE2F3, 0x8A62 }, { 0xE2F4, 0x8AC4 }, { 0xE2F5, 0x9187 }, { 0xE2F6, 0x931E }, { 0xE2F7, 0x9806 }, { 0xE2F8, 0x99B4 }, { 0xE2F9, 0x620C }, { 0xE2FA, 0x8853 },
	{ 0xE2FB, 0x8FF0 }, { 0xE2FC, 0x9265 }, { 0xE2FD, 0x5D07 }, { 0xE2FE, 0x5D27 }, { 0xE3A1, 0x5D69 }, { 0xE3A2, 0x745F }, { 0xE3A3, 0x819D }, { 0xE3A4, 0x8768 },
	{ 0xE3A5, 0x6FD5 }, { 0xE3A6, 0x62FE }, { 0xE3A7, 0x7FD2 }, { 0xE3A8, 0x8936 }, { 0xE3A9, 0x8972 }, { 0xE3AA, 0x4E1E }, { 0xE3AB, 0x4E58 }, { 0xE3AC, 0x50E7 },
	{ 0xE3AD, 0x52DD }, { 0xE3AE, 0x5347 }, { 0xE3AF, 0x627F }, { 0xE3B0, 0x6607 }, { 0xE3B1, 0x7E69 }, { 0xE3B2, 0x8805 }, { 0xE3B3, 0x965E }, { 0xE3B4, 0x4F8D },
	{ 0xE3B5, 0x5319 }, { 0xE3B6, 0x5636 }, { 0xE3B7, 0x59CB }, { 0xE3B8, 0x5AA4 }, { 0xE3B9, 0x5C38 }, { 0xE3BA, 0x5C4E }, { 0xE3BB, 0x5C4D }, { 0xE3BC, 0x5E02 },
	{ 0xE3BD, 0x5F11 }, { 0xE3BE, 0x6043 }, { 0xE3BF, 0x65BD }, { 0xE3C0, 0x662F }, { 0xE3C1, 0x6642 }, { 0xE3C2, 0x67BE }, { 0xE3C3, 0x67F4 }, { 0xE3C4, 0x731C },
	{ 0xE3C5, 0x77E2 }, { 0xE3C6, 0x793A }, { 0xE3C7, 0x7FC5 }, { 0xE3C8, 0x8494 }, { 0xE3C9, 0x84CD }, { 0xE3CA, 0x8996 }, { 0xE3CB, 0x8A66 }, { 0xE3CC, 0x8A69 },
	{ 0xE3CD, 0x8AE1 }, { 0xE3CE, 0x8C55 }, { 0xE3CF, 0x8C7A }, { 0xE3D0, 0x57F4 }, { 0xE3D1, 0x5BD4 }, { 0xE3D2, 0x5F0F }, { 0xE3D3, 0x606F }, { 0xE3D4, 0x62ED },
	{ 0xE3D5, 0x690D }, { 0xE3D6, 0x6B96 }, { 0xE3D7, 0x6E5C }, { 0xE3D8, 0x7184 }, { 0xE3D9, 0x7BD2 }, { 0xE3DA, 0x8755 }, { 0xE3DB, 0x8B58 }, { 0xE3DC, 0x8EFE },
	{ 0xE3DD, 0x98DF }, { 0xE3DE, 0x98FE }, { 0xE3DF, 0x4F38 }, { 0xE3E0, 0x4F81 }, { 0xE3E1, 0x4FE1 }, { 0xE3E2, 0x547B }, { 0xE3E3, 0x5A20 }, { 0xE3E4, 0x5BB8 },
	{ 0xE3E5, 0x613C }, { 0xE3E6, 0x65B0 }, { 0xE3E7, 0x6668 }, { 0xE3E8, 0x71FC }, { 0xE3E9, 0x7533 }, { 0xE3EA, 0x795E }, { 0xE3EB, 0x7D33 }, { 0xE3EC, 0x814E },
	{ 0xE3ED, 0x81E3 }, { 0xE3EE, 0x8398 }, { 0xE3EF, 0x85AA }, { 0xE3F0, 0x85CE }, { 0xE3F1, 0x8703 }, { 0xE3F2, 0x8A0A }, { 0xE3F3, 0x8EAB }, { 0xE3F4, 0x8F9B },
	{ 0xE3F5, 0xF971 }, { 0xE3F6, 0x8FC5 }, { 0xE3F7, 0x5931 }, { 0xE3F8, 0x5BA4 }, { 0xE3F9, 0x5BE6 }, { 0xE3FA, 0x6089 }, { 0xE3FB, 0x5BE9 }, { 0xE3FC, 0x5C0B },
	{ 0xE3FD, 0x5FC3 }, { 0xE3FE, 0x6C81 }, { 0xE4A1, 0xF972 }, { 0xE4A2, 0x6DF1 }, { 0xE4A3, 0x700B }, { 0xE4A4, 0x751A }, { 0xE4A5, 0x82AF }, { 0xE4A6, 0x8AF6 },
	{ 0xE4A7, 0x4EC0 }, { 0xE4A8, 0x5341 }, { 0xE4A9, 0xF973 }, { 0xE4AA, 0x96D9 }, { 0xE4AB, 0x6C0F }, { 0xE4AC, 0x4E9E }, { 0xE4AD, 0x4FC4 }, { 0xE4AE, 0x5152 },
	{ 0xE4AF, 0x555E }, { 0xE4B0, 0x5A25 }, { 0xE4B1, 0x5CE8 }, { 0xE4B2, 0x6211 }, { 0xE4B3, 0x7259 }, { 0xE4B4, 0x82BD }, { 0xE4B5, 0x83AA }, { 0xE4B6, 0x86FE },
	{ 0xE4B7, 0x8859 }, { 0xE4B8, 0x8A1D }, { 0xE4B9, 0x963F }, { 0xE4BA, 0x96C5 }, { 0xE4BB, 0x9913 }, { 0xE4BC, 0x9D09 }, { 0xE4BD, 0x9D5D }, { 0xE4BE, 0x580A },
	{ 0xE4BF, 0x5CB3 }, { 0xE4C0, 0x5DBD }, { 0xE4C1, 0x5E44 }, { 0xE4C2, 0x60E1 }, { 0xE4C3, 0x6115 }, { 0xE4C4, 0x63E1 }, { 0xE4C5, 0x6A02 }, { 0xE4C6, 0x6E25 },
	{ 0xE4C7, 0x9102 }, { 0xE4C8, 0x9354 }, { 0xE4C9, 0x984E }, { 0xE4CA, 0x9C10 }, { 0xE4CB, 0x9F77 }, { 0xE4CC, 0x5B89 }, { 0xE4CD, 0x5CB8 }, { 0xE4CE, 0x6309 },
	{ 0xE4CF, 0x664F }, { 0xE4D0, 0x6848 }, { 0xE4D1, 0x773C }, { 0xE4D2, 0x96C1 }, { 0xE4D3, 0x978D }, { 0xE4D4, 0x9854 }, { 0xE4D5, 0x9B9F }, { 0xE4D6, 0x65A1 },
	{ 0xE4D7, 0x8B01 }, { 0xE4D8, 0x8ECB }, { 0xE4D9, 0x95BC }, { 0xE4DA, 0x5535 }, { 0xE4DB, 0x5CA9 }, { 0xE4DC, 0x5DD6 }, { 0xE4DD, 0x5EB5 }, { 0xE4DE, 0x6697 },
	{ 0xE4DF, 0x764C }, { 0xE4E0, 0x83F4 }, { 0xE4E1, 0x95C7 }, { 0xE4E2, 0x58D3 }, { 0xE4E3, 0x62BC }, { 0xE4E4, 0x72CE }, { 0xE4E5, 0x9D28 }, { 0xE4E6, 0x4EF0 },
	{ 0xE4E7, 0x592E }, { 0xE4E8, 0x600F }, { 0xE4E9, 0x663B }, { 0xE4EA, 0x6B83 }, { 0xE4EB, 0x79E7 }, { 0xE4EC, 0x9D26 }, { 0xE4ED, 0x5393 }, { 0xE4EE, 0x54C0 },
	{ 0xE4EF, 0x57C3 }, { 0xE4F0, 0x5D16 }, { 0xE4F1, 0x611B }, { 0xE4F2, 0x66D6 }, { 0xE4F3, 0x6DAF }, { 0xE4F4, 0x788D }, { 0xE4F5, 0x827E }, { 0xE4F6, 0x9698 },
	{ 0xE4F7, 0x9744 }, { 0xE4F8, 0x5384 }, { 0xE4F9, 0x627C }, { 0xE4FA, 0x6396 }, { 0xE4FB, 0x6DB2 }, { 0xE4FC, 0x7E0A }, { 0xE4FD, 0x814B }, { 0xE4FE, 0x984D },
	{ 0xE5A1, 0x6AFB }, { 0xE5A2, 0x7F4C }, { 0xE5A3, 0x9DAF }, { 0xE5A4, 0x9E1A }, { 0xE5A5, 0x4E5F }, { 0xE5A6, 0x503B }, { 0xE5A7, 0x51B6 }, { 0xE5A8, 0x591C },
	{ 0xE5A9, 0x60F9 }, { 0xE5AA, 0x63F6 }, { 0xE5AB, 0x6930 }, { 0xE5AC, 0x723A }, { 0xE5AD, 0x8036 }, { 0xE5AE, 0xF974 }, { 0xE5AF, 0x91CE }, { 0xE5B0, 0x5F31 },
	{ 0xE5B1, 0xF975 }, { 0xE5B2, 0xF976 }, { 0xE5B3, 0x7D04 }, { 0xE5B4, 0x82E5 }, { 0xE5B5, 0x846F }, { 0xE5B6, 0x84BB }, { 0xE5B7, 0x85E5 }, { 0xE5B8, 0x8E8D },
	{ 0xE5B9, 0xF977 }, { 0xE5BA, 0x4F6F }, { 0xE5BB, 0xF978 }, { 0xE5BC, 0xF979 }, { 0xE5BD, 0x58E4 }, { 0xE5BE, 0x5B43 }, { 0xE5BF, 0x6059 }, { 0xE5C0, 0x63DA },
	{ 0xE5C1, 0x6518 }, { 0xE5C2, 0x656D }, { 0xE5C3, 0x6698 }, { 0xE5C4, 0xF97A }, { 0xE5C5, 0x694A }, { 0xE5C6, 0x6A23 }, { 0xE5C7, 0x6D0B }, { 0xE5C8, 0x7001 },
	{ 0xE5C9, 0x716C }, { 0xE5CA, 0x75D2 }, { 0xE5CB, 0x760D }, { 0xE5CC, 0x79B3 }, { 0xE5CD, 0x7A70 }, { 0xE5CE, 0xF97B }, { 0xE5CF, 0x7F8A }, { 0xE5D0, 0xF97C },
	{ 0xE5D1, 0x8944 }, { 0xE5D2, 0xF97D }, { 0xE5D3, 0x8B93 }, { 0xE5D4, 0x91C0 }, { 0xE5D5, 0x967D }, { 0xE5D6, 0xF97E }, { 0xE5D7, 0x990A }, { 0xE5D8, 0x5704 },
	{ 0xE5D9, 0x5FA1 }, { 0xE5DA, 0x65BC }, { 0xE5DB, 0x6F01 }, { 0xE5DC, 0x7600 }, { 0xE5DD, 0x79A6 }, { 0xE5DE, 0x8A9E }, { 0xE5DF, 0x99AD }, { 0xE5E0, 0x9B5A },
	{ 0xE5E1, 0x9F6C }, { 0xE5E2, 0x5104 }, { 0xE5E3, 0x61B6 }, { 0xE5E4, 0x6291 }, { 0xE5E5, 0x6A8D }, { 0xE5E6, 0x81C6 }, { 0xE5E7, 0x5043 }, { 0xE5E8, 0x5830 },
	{ 0xE5E9, 0x5F66 }, { 0xE5EA, 0x7109 }, { 0xE5EB, 0x8A00 }, { 0xE5EC, 0x8AFA }, { 0xE5ED, 0x5B7C }, { 0xE5EE, 0x8616 }, { 0xE5EF, 0x4FFA }, { 0xE5F0, 0x513C },
	{ 0xE5F1, 0x56B4 }, { 0xE5F2, 0x5944 }, { 0xE5F3, 0x63A9 }, { 0xE5F4, 0x6DF9 }, { 0xE5F5, 0x5DAA }, { 0xE5F6, 0x696D }, { 0xE5F7, 0x5186 }, { 0xE5F8, 0x4E88 },
	{ 0xE5F9, 0x4F59 }, { 0xE5FA, 0xF97F }, { 0xE5FB, 0xF980 }, { 0xE5FC, 0xF981 }, { 0xE5FD, 0x5982 }, { 0xE5FE, 0xF982 }, { 0xE6A1, 0xF983 }, { 0xE6A2, 0x6B5F },
	{ 0xE6A3, 0x6C5D }, { 0xE6A4, 0xF984 }, { 0xE6A5, 0x74B5 }, { 0xE6A6, 0x7916 }, { 0xE6A7, 0xF985 }, { 0xE6A8, 0x8207 }, { 0xE6A9, 0x8245 }, { 0xE6AA, 0x8339 },
	{ 0xE6AB, 0x8F3F }, { 0xE6AC, 0x8F5D }, { 0xE6AD, 0xF986 }, { 0xE6AE, 0x9918 }, { 0xE6AF, 0xF987 }, { 0xE6B0, 0xF988 }, { 0xE6B1, 0xF989 }, { 0xE6B2, 0x4EA6 },
	{ 0xE6B3, 0xF98A }, { 0xE6B4, 0x57DF }, { 0xE6B5, 0x5F79 }, { 0xE6B6, 0x6613 }, { 0xE6B7, 0xF98B }, { 0xE6B8, 0xF98C }, { 0xE6B9, 0x75AB }, { 0xE6BA, 0x7E79 },
	{ 0xE6BB, 0x8B6F }, { 0xE6BC, 0xF98D }, { 0xE6BD, 0x9006 }, { 0xE6BE, 0x9A5B }, { 0xE6BF, 0x56A5 }, { 0xE6C0, 0x5827 }, { 0xE6C1, 0x59F8 }, { 0xE6C2, 0x5A1F },
	{ 0xE6C3, 0x5BB4 }, { 0xE6C4, 0xF98E }, { 0xE6C5, 0x5EF6 }, { 0xE6C6, 0xF98F }, { 0xE6C7, 0xF990 }, { 0xE6C8, 0x6350 }, { 0xE6C9, 0x633B }, { 0xE6CA, 0xF991 },
	{ 0xE6CB, 0x693D }, { 0xE6CC, 0x6C87 }, { 0xE6CD, 0x6CBF }, { 0xE6CE, 0x6D8E }, { 0xE6CF, 0x6D93 }, { 0xE6D0, 0x6DF5 }, { 0xE6D1, 0x6F14 }, { 0xE6D2, 0xF992 },
	{ 0xE6D3, 0x70DF }, { 0xE6D4, 0x7136 }, { 0xE6D5, 0x7159 }, { 0xE6D6, 0xF993 }, { 0xE6D7, 0x71C3 }, { 0xE6D8, 0x71D5 }, { 0xE6D9, 0xF994 }, { 0xE6DA, 0x784F },
	{ 0xE6DB, 0x786F }, { 0xE6DC, 0xF995 }, { 0xE6DD, 0x7B75 }, { 0xE6DE, 0x7DE3 }, { 0xE6DF, 0xF996 }, { 0xE6E0, 0x7E2F }, { 0xE6E1, 0xF997 }, { 0xE6E2, 0x884D },
	{ 0xE6E3, 0x8EDF }, { 0xE6E4, 0xF998 }, { 0xE6E5, 0xF999 }, { 0xE6E6, 0xF99A }, { 0xE6E7, 0x925B }, { 0xE6E8, 0xF99B }, { 0xE6E9, 0x9CF6 }, { 0xE6EA, 0xF99C },
	{ 0xE6EB, 0xF99D }, { 0xE6EC, 0xF99E }, { 0xE6ED, 0x6085 }, { 0xE6EE, 0x6D85 }, { 0xE6EF, 0xF99F }, { 0xE6F0, 0x71B1 }, { 0xE6F1, 0xF9A0 }, { 0xE6F2, 0xF9A1 },
	{ 0xE6F3, 0x95B1 }, { 0xE6F4, 0x53AD }, { 0xE6F5, 0xF9A2 }, { 0xE6F6, 0xF9A3 }, { 0xE6F7, 0xF9A4 }, { 0xE6F8, 0x67D3 }, { 0xE6F9, 0xF9A5 }, { 0xE6FA, 0x708E },
	{ 0xE6FB, 0x7130 }, { 0xE6FC, 0x7430 }, { 0xE6FD, 0x8276 }, { 0xE6FE, 0x82D2 }, { 0xE7A1, 0xF9A6 }, { 0xE7A2, 0x95BB }, { 0xE7A3, 0x9AE5 }, { 0xE7A4, 0x9E7D },
	{ 0xE7A5, 0x66C4 }, { 0xE7A6, 0xF9A7 }, { 0xE7A7, 0x71C1 }, { 0xE7A8, 0x8449 }, { 0xE7A9, 0xF9A8 }, { 0xE7AA, 0xF9A9 }, { 0xE7AB, 0x584B }, { 0xE7AC, 0xF9AA },
	{ 0xE7AD, 0xF9AB }, { 0xE7AE, 0x5DB8 }, { 0xE7AF, 0x5F71 }, { 0xE7B0, 0xF9AC }, { 0xE7B1, 0x6620 }, { 0xE7B2, 0x668E }, { 0xE7B3, 0x6979 }, { 0xE7B4, 0x69AE },
	{ 0xE7B5, 0x6C38 }, { 0xE7B6, 0x6CF3 }, { 0xE7B7, 0x6E36 }, { 0xE7B8, 0x6F41 }, { 0xE7B9, 0x6FDA }, { 0xE7BA, 0x701B }, { 0xE7BB, 0x702F }, { 0xE7BC, 0x7150 },
	{ 0xE7BD, 0x71DF }, { 0xE7BE, 0x7370 }, { 0xE7BF, 0xF9AD }, { 0xE7C0, 0x745B }, { 0xE7C1, 0xF9AE }, { 0xE7C2, 0x74D4 }, { 0xE7C3, 0x76C8 }, { 0xE7C4, 0x7A4E },
	{ 0xE7C5, 0x7E93 }, { 0xE7C6, 0xF9AF }, { 0xE7C7, 0xF9B0 }, { 0xE7C8, 0x82F1 }, { 0xE7C9, 0x8A60 }, { 0xE7CA, 0x8FCE }, { 0xE7CB, 0xF9B1 }, { 0xE7CC, 0x9348 },
	{ 0xE7CD, 0xF9B2 }, { 0xE7CE, 0x9719 }, { 0xE7CF, 0xF9B3 }, { 0xE7D0, 0xF9B4 }, { 0xE7D1, 0x4E42 }, { 0xE7D2, 0x502A }, { 0xE7D3, 0xF9B5 }, { 0xE7D4, 0x5208 },
	{ 0xE7D5, 0x53E1 }, { 0xE7D6, 0x66F3 }, { 0xE7D7, 0x6C6D }, { 0xE7D8, 0x6FCA }, { 0xE7D9, 0x730A }, { 0xE7DA, 0x777F }, { 0xE7DB, 0x7A62 }, { 0xE7DC, 0x82AE },
	{ 0xE7DD, 0x85DD }, { 0xE7DE, 0x8602 }, { 0xE7DF, 0xF9B6 }, { 0xE7E0, 0x88D4 }, { 0xE7E1, 0x8A63 }, { 0xE7E2, 0x8B7D }, { 0xE7E3, 0x8C6B }, { 0xE7E4, 0xF9B7 },
	{ 0xE7E5, 0x92B3 }, { 0xE7E6, 0xF9B8 }, { 0xE7E7, 0x9713 }, { 0xE7E8, 0x9810 }, { 0xE7E9, 0x4E94 }, { 0xE7EA, 0x4F0D }, { 0xE7EB, 0x4FC9 }, { 0xE7EC, 0x50B2 },
	{ 0xE7ED, 0x5348 }, { 0xE7EE, 0x543E }, { 0xE7EF, 0x5433 }, { 0xE7F0, 0x55DA }, { 0xE7F1, 0x5862 }, { 0xE7F2, 0x58BA }, { 0xE7F3, 0x5967 }, { 0xE7F4, 0x5A1B },
	{ 0xE7F5, 0x5BE4 }, { 0xE7F6, 0x609F }, { 0xE7F7, 0xF9B9 }, { 0xE7F8, 0x61CA }, { 0xE7F9, 0x6556 }, { 0xE7FA, 0x65FF }, { 0xE7FB, 0x6664 }, { 0xE7FC, 0x68A7 },
	{ 0xE7FD, 0x6C5A }, { 0xE7FE, 0x6FB3 }, { 0xE8A1, 0x70CF }, { 0xE8A2, 0x71AC }, { 0xE8A3, 0x7352 }, { 0xE8A4, 0x7B7D }, { 0xE8A5, 0x8708 }, { 0xE8A6, 0x8AA4 },
	{ 0xE8A7, 0x9C32 }, { 0xE8A8, 0x9F07 }, { 0xE8A9, 0x5C4B }, { 0xE8AA, 0x6C83 }, { 0xE8AB, 0x7344 }, { 0xE8AC, 0x7389 }, { 0xE8AD, 0x923A }, { 0xE8AE, 0x6EAB },
	{ 0xE8AF, 0x7465 }, { 0xE8B0, 0x761F }, { 0xE8B1, 0x7A69 }, { 0xE8B2, 0x7E15 }, { 0xE8B3, 0x860A }, { 0xE8B4, 0x5140 }, { 0xE8B5, 0x58C5 }, { 0xE8B6, 0x64C1 },
	{ 0xE8B7, 0x74EE }, { 0xE8B8, 0x7515 }, { 0xE8B9, 0x7670 }, { 0xE8BA, 0x7FC1 }, { 0xE8BB, 0x9095 }, { 0xE8BC, 0x96CD }, { 0xE8BD, 0x9954 }, { 0xE8BE, 0x6E26 },
	{ 0xE8BF, 0x74E6 }, { 0xE8C0, 0x7AA9 }, { 0xE8C1, 0x7AAA }, { 0xE8C2, 0x81E5 }, { 0xE8C3, 0x86D9 }, { 0xE8C4, 0x8778 }, { 0xE8C5, 0x8A1B }, { 0xE8C6, 0x5A49 },
	{ 0xE8C7, 0x5B8C }, { 0xE8C8, 0x5B9B }, { 0xE8C9, 0x68A1 }, { 0xE8CA, 0x6900 }, { 0xE8CB, 0x6D63 }, { 0xE8CC, 0x73A9 }, { 0xE8CD, 0x7413 }, { 0xE8CE, 0x742C },
	{ 0xE8CF, 0x7897 }, { 0xE8D0, 0x7DE9 }, { 0xE8D1, 0x7FEB }, { 0xE8D2, 0x8118 }, { 0xE8D3, 0x8155 }, { 0xE8D4, 0x839E }, { 0xE8D5, 0x8C4C }, { 0xE8D6, 0x962E },
	{ 0xE8D7, 0x9811 }, { 0xE8D8, 0x66F0 }, { 0xE8D9, 0x5F80 }, { 0xE8DA, 0x65FA }, { 0xE8DB, 0x6789 }, { 0xE8DC, 0x6C6A }, { 0xE8DD, 0x738B }, { 0xE8DE, 0x502D },
	{ 0xE8DF, 0x5A03 }, { 0xE8E0, 0x6B6A }, { 0xE8E1, 0x77EE }, { 0xE8E2, 0x5916 }, { 0xE8E3, 0x5D6C }, { 0xE8E4, 0x5DCD }, { 0xE8E5, 0x7325 }, { 0xE8E6, 0x754F },
	{ 0xE8E7, 0xF9BA }, { 0xE8E8, 0xF9BB }, { 0xE8E9, 0x50E5 }, { 0xE8EA, 0x51F9 }, { 0xE8EB, 0x582F }, { 0xE8EC, 0x592D }, { 0xE8ED, 0x5996 }, { 0xE8EE, 0x59DA },
	{ 0xE8EF, 0x5BE5 }, { 0xE8F0, 0xF9BC }, { 0xE8F1, 0xF9BD }, { 0xE8F2, 0x5DA2 }, { 0xE8F3, 0x62D7 }, { 0xE8F4, 0x6416 }, { 0xE8F5, 0x6493 }, { 0xE8F6, 0x64FE },
	{ 0xE8F7, 0xF9BE }, { 0xE8F8, 0x66DC }, { 0xE8F9, 0xF9BF }, { 0xE8FA, 0x6A48 }, { 0xE8FB, 0xF9C0 }, { 0xE8FC, 0x71FF }, { 0xE8FD, 0x7464 }, { 0xE8FE, 0xF9C1 },
	{ 0xE9A1, 0x7A88 }, { 0xE9A2, 0x7AAF }, { 0xE9A3, 0x7E47 }, { 0xE9A4, 0x7E5E }, { 0xE9A5, 0x8000 }, { 0xE9A6, 0x8170 }, { 0xE9A7, 0xF9C2 }, { 0xE9A8, 0x87EF },
	{ 0xE9A9, 0x8981 }, { 0xE9AA, 0x8B20 }, { 0xE9AB, 0x9059 }, { 0xE9AC, 0xF9C3 }, { 0xE9AD, 0x9080 }, { 0xE9AE, 0x9952 }, { 0xE9AF, 0x617E }, { 0xE9B0, 0x6B32 },
	{ 0xE9B1, 0x6D74 }, { 0xE9B2, 0x7E1F }, { 0xE9B3, 0x8925 }, { 0xE9B4, 0x8FB1 }, { 0xE9B5, 0x4FD1 }, { 0xE9B6, 0x50AD }, { 0xE9B7, 0x5197 }, { 0xE9B8, 0x52C7 },
	{ 0xE9B9, 0x57C7 }, { 0xE9BA, 0x5889 }, { 0xE9BB, 0x5BB9 }, { 0xE9BC, 0x5EB8 }, { 0xE9BD, 0x6142 }, { 0xE9BE, 0x6995 }, { 0xE9BF, 0x6D8C }, { 0xE9C0, 0x6E67 },
	{ 0xE9C1, 0x6EB6 }, { 0xE9C2, 0x7194 }, { 0xE9C3, 0x7462 }, { 0xE9C4, 0x7528 }, { 0xE9C5, 0x752C }, { 0xE9C6, 0x8073 }, { 0xE9C7, 0x8338 }, { 0xE9C8, 0x84C9 },
	{ 0xE9C9, 0x8E0A }, { 0xE9CA, 0x9394 }, { 0xE9CB, 0x93DE }, { 0xE9CC, 0xF9C4 }, { 0xE9CD, 0x4E8E }, { 0xE9CE, 0x4F51 }, { 0xE9CF, 0x5076 }, { 0xE9D0, 0x512A },
	{ 0xE9D1, 0x53C8 }, { 0xE9D2, 0x53CB }, { 0xE9D3, 0x53F3 }, { 0xE9D4, 0x5B87 }, { 0xE9D5, 0x5BD3 }, { 0xE9D6, 0x5C24 }, { 0xE9D7, 0x611A }, { 0xE9D8, 0x6182 },
	{ 0xE9D9, 0x65F4 }, { 0xE9DA, 0x725B }, { 0xE9DB, 0x7397 }, { 0xE9DC, 0x7440 }, { 0xE9DD, 0x76C2 }, { 0xE9DE, 0x7950 }, { 0xE9DF, 0x7991 }, { 0xE9E0, 0x79B9 },
	{ 0xE9E1, 0x7D06 }, { 0xE9E2, 0x7FBD }, { 0xE9E3, 0x828B }, { 0xE9E4, 0x85D5 }, { 0xE9E5, 0x865E }, { 0xE9E6, 0x8FC2 }, { 0xE9E7, 0x9047 }, { 0xE9E8, 0x90F5 },
	{ 0xE9E9, 0x91EA }, { 0xE9EA, 0x9685 }, { 0xE9EB, 0x96E8 }, { 0xE9EC, 0x96E9 }, { 0xE9ED, 0x52D6 }, { 0xE9EE, 0x5F67 }, { 0xE9EF, 0x65ED }, { 0xE9F0, 0x6631 },
	{ 0xE9F1, 0x682F }, { 0xE9F2, 0x715C }, { 0xE9F3, 0x7A36 }, { 0xE9F4, 0x90C1 }, { 0xE9F5, 0x980A }, { 0xE9F6, 0x4E91 }, { 0xE9F7, 0xF9C5 }, { 0xE9F8, 0x6A52 },
	{ 0xE9F9, 0x6B9E }, { 0xE9FA, 0x6F90 }, { 0xE9FB, 0x7189 }, { 0xE9FC, 0x8018 }, { 0xE9FD, 0x82B8 }, { 0xE9FE, 0x8553 }, { 0xEAA1, 0x904B }, { 0xEAA2, 0x9695 },
	{ 0xEAA3, 0x96F2 }, { 0xEAA4, 0x97FB }, { 0xEAA5, 0x851A }, { 0xEAA6, 0x9B31 }, { 0xEAA7, 0x4E90 }, { 0xEAA8, 0x718A }, { 0xEAA9, 0x96C4 }, { 0xEAAA, 0x5143 },
	{ 0xEAAB, 0x539F }, { 0xEAAC, 0x54E1 }, { 0xEAAD, 0x5713 }, { 0xEAAE, 0x5712 }, { 0xEAAF, 0x57A3 }, { 0xEAB0, 0x5A9B }, { 0xEAB1, 0x5AC4 }, { 0xEAB2, 0x5BC3 },
	{ 0xEAB3, 0x6028 }, { 0xEAB4, 0x613F }, { 0xEAB5, 0x63F4 }, { 0xEAB6, 0x6C85 }, { 0xEAB7, 0x6D39 }, { 0xEAB8, 0x6E72 }, { 0xEAB9, 0x6E90 }, { 0xEABA, 0x7230 },
	{ 0xEABB, 0x733F }, { 0xEABC, 0x7457 }, { 0xEABD, 0x82D1 }, { 0xEABE, 0x8881 }, { 0xEABF, 0x8F45 }, { 0xEAC0, 0x9060 }, { 0xEAC1, 0xF9C6 }, { 0xEAC2, 0x9662 },
	{ 0xEAC3, 0x9858 }, { 0xEAC4, 0x9D1B }, { 0xEAC5, 0x6708 }, { 0xEAC6, 0x8D8A }, { 0xEAC7, 0x925E }, { 0xEAC8, 0x4F4D }, { 0xEAC9, 0x5049 }, { 0xEACA, 0x50DE },
	{ 0xEACB, 0x5371 }, { 0xEACC, 0x570D }, { 0xEACD, 0x59D4 }, { 0xEACE, 0x5A01 }, { 0xEACF, 0x5C09 }, { 0xEAD0, 0x6170 }, { 0xEAD1, 0x6690 }, { 0xEAD2, 0x6E2D },
	{ 0xEAD3, 0x7232 }, { 0xEAD4, 0x744B }, { 0xEAD5, 0x7DEF }, { 0xEAD6, 0x80C3 }, { 0xEAD7, 0x840E }, { 0xEAD8, 0x8466 }, { 0xEAD9, 0x853F }, { 0xEADA, 0x875F },
	{ 0xEADB, 0x885B }, { 0xEADC, 0x8918 }, { 0xEADD, 0x8B02 }, { 0xEADE, 0x9055 }, { 0xEADF, 0x97CB }, { 0xEAE0, 0x9B4F }, { 0xEAE1, 0x4E73 }, { 0xEAE2, 0x4F91 },
	{ 0xEAE3, 0x5112 }, { 0xEAE4, 0x516A }, { 0xEAE5, 0xF9C7 }, { 0xEAE6, 0x552F }, { 0xEAE7, 0x55A9 }, { 0xEAE8, 0x5B7A }, { 0xEAE9, 0x5BA5 }, { 0xEAEA, 0x5E7C },
	{ 0xEAEB, 0x5E7D }, { 0xEAEC, 0x5EBE }, { 0xEAED, 0x60A0 }, { 0xEAEE, 0x60DF }, { 0xEAEF, 0x6108 }, { 0xEAF0, 0x6109 }, { 0xEAF1, 0x63C4 }, { 0xEAF2, 0x6538 },
	{ 0xEAF3, 0x6709 }, { 0xEAF4, 0xF9C8 }, { 0xEAF5, 0x67D4 }, { 0xEAF6, 0x67DA }, { 0xEAF7, 0xF9C9 }, { 0xEAF8, 0x6961 }, { 0xEAF9, 0x6962 }, { 0xEAFA, 0x6CB9 },
	{ 0xEAFB, 0x6D27 }, { 0xEAFC, 0xF9CA }, { 0xEAFD, 0x6E38 }, { 0xEAFE, 0xF9CB }, { 0xEBA1, 0x6FE1 }, { 0xEBA2, 0x7336 }, { 0xEBA3, 0x7337 }, { 0xEBA4, 0xF9CC },
	{ 0xEBA5, 0x745C }, { 0xEBA6, 0x7531 }, { 0xEBA7, 0xF9CD }, { 0xEBA8, 0x7652 }, { 0xEBA9, 0xF9CE }, { 0xEBAA, 0xF9CF }, { 0xEBAB, 0x7DAD }, { 0xEBAC, 0x81FE },
	{ 0xEBAD, 0x8438 }, { 0xEBAE, 0x88D5 }, { 0xEBAF, 0x8A98 }, { 0xEBB0, 0x8ADB }, { 0xEBB1, 0x8AED }, { 0xEBB2, 0x8E30 }, { 0xEBB3, 0x8E42 }, { 0xEBB4, 0x904A },
	{ 0xEBB5, 0x903E }, { 0xEBB6, 0x907A }, { 0xEBB7, 0x9149 }, { 0xEBB8, 0x91C9 }, { 0xEBB9, 0x936E }, { 0xEBBA, 0xF9D0 }, { 0xEBBB, 0xF9D1 }, { 0xEBBC, 0x5809 },
	{ 0xEBBD, 0xF9D2 }, { 0xEBBE, 0x6BD3 }, { 0xEBBF, 0x8089 }, { 0xEBC0, 0x80B2 }, { 0xEBC1, 0xF9D3 }, { 0xEBC2, 0xF9D4 }, { 0xEBC3, 0x5141 }, { 0xEBC4, 0x596B },
	{ 0xEBC5, 0x5C39 }, { 0xEBC6, 0xF9D5 }, { 0xEBC7, 0xF9D6 }, { 0xEBC8, 0x6F64 }, { 0xEBC9, 0x73A7 }, { 0xEBCA, 0x80E4 }, { 0xEBCB, 0x8D07 }, { 0xEBCC, 0xF9D7 },
	{ 0xEBCD, 0x9217 }, { 0xEBCE, 0x958F }, { 0xEBCF, 0xF9D8 }, { 0xEBD0, 0xF9D9 }, { 0xEBD1, 0xF9DA }, { 0xEBD2, 0xF9DB }, { 0xEBD3, 0x807F }, { 0xEBD4, 0x620E },
	{ 0xEBD5, 0x701C }, { 0xEBD6, 0x7D68 }, { 0xEBD7, 0x878D }, { 0xEBD8, 0xF9DC }, { 0xEBD9, 0x57A0 }, { 0xEBDA, 0x6069 }, { 0xEBDB, 0x6147 }, { 0xEBDC, 0x6BB7 },
	{ 0xEBDD, 0x8ABE }, { 0xEBDE, 0x9280 }, { 0xEBDF, 0x96B1 }, { 0xEBE0, 0x4E59 }, { 0xEBE1, 0x541F }, { 0xEBE2, 0x6DEB }, { 0xEBE3, 0x852D }, { 0xEBE4, 0x9670 },
	{ 0xEBE5, 0x97F3 }, { 0xEBE6, 0x98EE }, { 0xEBE7, 0x63D6 }, { 0xEBE8, 0x6CE3 }, { 0xEBE9, 0x9091 }, { 0xEBEA, 0x51DD }, { 0xEBEB, 0x61C9 }, { 0xEBEC, 0x81BA },
	{ 0xEBED, 0x9DF9 }, { 0xEBEE, 0x4F9D }, { 0xEBEF, 0x501A }, { 0xEBF0, 0x5100 }, { 0xEBF1, 0x5B9C }, { 0xEBF2, 0x610F }, { 0xEBF3, 0x61FF }, { 0xEBF4, 0x64EC },
	{ 0xEBF5, 0x6905 }, { 0xEBF6, 0x6BC5 }, { 0xEBF7, 0x7591 }, { 0xEBF8, 0x77E3 }, { 0xEBF9, 0x7FA9 }, { 0xEBFA, 0x8264 }, { 0xEBFB, 0x858F }, { 0xEBFC, 0x87FB },
	{ 0xEBFD, 0x8863 }, { 0xEBFE, 0x8ABC }, { 0xECA1, 0x8B70 }, { 0xECA2, 0x91AB }, { 0xECA3, 0x4E8C }, { 0xECA4, 0x4EE5 }, { 0xECA5, 0x4F0A }, { 0xECA6, 0xF9DD },
	{ 0xECA7, 0xF9DE }, { 0xECA8, 0x5937 }, { 0xECA9, 0x59E8 }, { 0xECAA, 0xF9DF }, { 0xECAB, 0x5DF2 }, { 0xECAC, 0x5F1B }, { 0xECAD, 0x5F5B }, { 0xECAE, 0x6021 },
	{ 0xECAF, 0xF9E0 }, { 0xECB0, 0xF9E1 }, { 0xECB1, 0xF9E2 }, { 0xECB2, 0xF9E3 }, { 0xECB3, 0x723E }, { 0xECB4, 0x73E5 }, { 0xECB5, 0xF9E4 }, { 0xECB6, 0x7570 },
	{ 0xECB7, 0x75CD }, { 0xECB8, 0xF9E5 }, { 0xECB9, 0x79FB }, { 0xECBA, 0xF9E6 }, { 0xECBB, 0x800C }, { 0xECBC, 0x8033 }, { 0xECBD, 0x8084 }, { 0xECBE, 0x82E1 },
	{ 0xECBF, 0x8351 }, { 0xECC0, 0xF9E7 }, { 0xECC1, 0xF9E8 }, { 0xECC2, 0x8CBD }, { 0xECC3, 0x8CB3 }, { 0xECC4, 0x9087 }, { 0xECC5, 0xF9E9 }, { 0xECC6, 0xF9EA },
	{ 0xECC7, 0x98F4 }, { 0xECC8, 0x990C }, { 0xECC9, 0xF9EB }, { 0xECCA, 0xF9EC }, { 0xECCB, 0x7037 }, { 0xECCC, 0x76CA }, { 0xECCD, 0x7FCA }, { 0xECCE, 0x7FCC },
	{ 0xECCF, 0x7FFC }, { 0xECD0, 0x8B1A }, { 0xECD1, 0x4EBA }, { 0xECD2, 0x4EC1 }, { 0xECD3, 0x5203 }, { 0xECD4, 0x5370 }, { 0xECD5, 0xF9ED }, { 0xECD6, 0x54BD },
	{ 0xECD7, 0x56E0 }, { 0xECD8, 0x59FB }, { 0xECD9, 0x5BC5 }, { 0xECDA, 0x5F15 }, { 0xECDB, 0x5FCD }, { 0xECDC, 0x6E6E }, { 0xECDD, 0xF9EE }, { 0xECDE, 0xF9EF },
	{ 0xECDF, 0x7D6A }, { 0xECE0, 0x8335 }, { 0xECE1, 0xF9F0 }, { 0xECE2, 0x8693 }, { 0xECE3, 0x8A8D }, { 0xECE4, 0xF9F1 }, { 0xECE5, 0x976D }, { 0xECE6, 0x9777 },
	{ 0xECE7, 0xF9F2 }, { 0xECE8, 0xF9F3 }, { 0xECE9, 0x4E00 }, { 0xECEA, 0x4F5A }, { 0xECEB, 0x4F7E }, { 0xECEC, 0x58F9 }, { 0xECED, 0x65E5 }, { 0xECEE, 0x6EA2 },
	{ 0xECEF, 0x9038 }, { 0xECF0, 0x93B0 }, { 0xECF1, 0x99B9 }, { 0xECF2, 0x4EFB }, { 0xECF3, 0x58EC }, { 0xECF4, 0x598A }, { 0xECF5, 0x59D9 }, { 0xECF6, 0x6041 },
	{ 0xECF7, 0xF9F4 }, { 0xECF8, 0xF9F5 }, { 0xECF9, 0x7A14 }, { 0xECFA, 0xF9F6 }, { 0xECFB, 0x834F }, { 0xECFC, 0x8CC3 }, { 0xECFD, 0x5165 }, { 0xECFE, 0x5344 },
	{ 0xEDA1, 0xF9F7 }, { 0xEDA2, 0xF9F8 }, { 0xEDA3, 0xF9F9 }, { 0xEDA4, 0x4ECD }, { 0xEDA5, 0x5269 }, { 0xEDA6, 0x5B55 }, { 0xEDA7, 0x82BF }, { 0xEDA8, 0x4ED4 },
	{ 0xEDA9, 0x523A }, { 0xEDAA, 0x54A8 }, { 0xEDAB, 0x59C9 }, { 0xEDAC, 0x59FF }, { 0xEDAD, 0x5B50 }, { 0xEDAE, 0x5B57 }, { 0xEDAF, 0x5B5C }, { 0xEDB0, 0x6063 },
	{ 0xEDB1, 0x6148 }, { 0xEDB2, 0x6ECB }, { 0xEDB3, 0x7099 }, { 0xEDB4, 0x716E }, { 0xEDB5, 0x7386 }, { 0xEDB6, 0x74F7 }, { 0xEDB7, 0x75B5 }, { 0xEDB8, 0x78C1 },
	{ 0xEDB9, 0x7D2B }, { 0xEDBA, 0x8005 }, { 0xEDBB, 0x81EA }, { 0xEDBC, 0x8328 }, { 0xEDBD, 0x8517 }, { 0xEDBE, 0x85C9 }, { 0xEDBF, 0x8AEE }, { 0xEDC0, 0x8CC7 },
	{ 0xEDC1, 0x96CC }, { 0xEDC2, 0x4F5C }, { 0xEDC3, 0x52FA }, { 0xEDC4, 0x56BC }, { 0xEDC5, 0x65AB }, { 0xEDC6, 0x6628 }, { 0xEDC7, 0x707C }, { 0xEDC8, 0x70B8 },
	{ 0xEDC9, 0x7235 }, { 0xEDCA, 0x7DBD }, { 0xEDCB, 0x828D }, { 0xEDCC, 0x914C }, { 0xEDCD, 0x96C0 }, { 0xEDCE, 0x9D72 }, { 0xEDCF, 0x5B71 }, { 0xEDD0, 0x68E7 },
	{ 0xEDD1, 0x6B98 }, { 0xEDD2, 0x6F7A }, { 0xEDD3, 0x76DE }, { 0xEDD4, 0x5C91 }, { 0xEDD5, 0x66AB }, { 0xEDD6, 0x6F5B }, { 0xEDD7, 0x7BB4 }, { 0xEDD8, 0x7C2A },
	{ 0xEDD9, 0x8836 }, { 0xEDDA, 0x96DC }, { 0xEDDB, 0x4E08 }, { 0xEDDC, 0x4ED7 }, { 0xEDDD, 0x5320 }, { 0xEDDE, 0x5834 }, { 0xEDDF, 0x58BB }, { 0xEDE0, 0x58EF },
	{ 0xEDE1, 0x596C }, { 0xEDE2, 0x5C07 }, { 0xEDE3, 0x5E33 }, { 0xEDE4, 0x5E84 }, { 0xEDE5, 0x5F35 }, { 0xEDE6, 0x638C }, { 0xEDE7, 0x66B2 }, { 0xEDE8, 0x6756 },
	{ 0xEDE9, 0x6A1F }, { 0xEDEA, 0x6AA3 }, { 0xEDEB, 0x6B0C }, { 0xEDEC, 0x6F3F }, { 0xEDED, 0x7246 }, { 0xEDEE, 0xF9FA }, { 0xEDEF, 0x7350 }, { 0xEDF0, 0x748B },
	{ 0xEDF1, 0x7AE0 }, { 0xEDF2, 0x7CA7 }, { 0xEDF3, 0x8178 }, { 0xEDF4, 0x81DF }, { 0xEDF5, 0x81E7 }, { 0xEDF6, 0x838A }, { 0xEDF7, 0x846C }, { 0xEDF8, 0x8523 },
	{ 0xEDF9, 0x8594 }, { 0xEDFA, 0x85CF }, { 0xEDFB, 0x88DD }, { 0xEDFC, 0x8D13 }, { 0xEDFD, 0x91AC }, { 0xEDFE, 0x9577 }, { 0xEEA1, 0x969C }, { 0xEEA2, 0x518D },
	{ 0xEEA3, 0x54C9 }, { 0xEEA4, 0x5728 }, { 0xEEA5, 0x5BB0 }, { 0xEEA6, 0x624D }, { 0xEEA7, 0x6750 }, { 0xEEA8, 0x683D }, { 0xEEA9, 0x6893 }, { 0xEEAA, 0x6E3D },
	{ 0xEEAB, 0x6ED3 }, { 0xEEAC, 0x707D }, { 0xEEAD, 0x7E21 }, { 0xEEAE, 0x88C1 }, { 0xEEAF, 0x8CA1 }, { 0xEEB0, 0x8F09 }, { 0xEEB1, 0x9F4B }, { 0xEEB2, 0x9F4E },
	{ 0xEEB3, 0x722D }, { 0xEEB4, 0x7B8F }, { 0xEEB5, 0x8ACD }, { 0xEEB6, 0x931A }, { 0xEEB7, 0x4F47 }, { 0xEEB8, 0x4F4E }, { 0xEEB9, 0x5132 }, { 0xEEBA, 0x5480 },
	{ 0xEEBB, 0x59D0 }, { 0xEEBC, 0x5E95 }, { 0xEEBD, 0x62B5 }, { 0xEEBE, 0x6775 }, { 0xEEBF, 0x696E }, { 0xEEC0, 0x6A17 }, { 0xEEC1, 0x6CAE }, { 0xEEC2, 0x6E1A },
	{ 0xEEC3, 0x72D9 }, { 0xEEC4, 0x732A }, { 0xEEC5, 0x75BD }, { 0xEEC6, 0x7BB8 }, { 0xEEC7, 0x7D35 }, { 0xEEC8, 0x82E7 }, { 0xEEC9, 0x83F9 }, { 0xEECA, 0x8457 },
	{ 0xEECB, 0x85F7 }, { 0xEECC, 0x8A5B }, { 0xEECD, 0x8CAF }, { 0xEECE, 0x8E87 }, { 0xEECF, 0x9019 }, { 0xEED0, 0x90B8 }, { 0xEED1, 0x96CE }, { 0xEED2, 0x9F5F },
	{ 0xEED3, 0x52E3 }, { 0xEED4, 0x540A }, { 0xEED5, 0x5AE1 }, { 0xEED6, 0x5BC2 }, { 0xEED7, 0x6458 }, { 0xEED8, 0x6575 }, { 0xEED9, 0x6EF4 }, { 0xEEDA, 0x72C4 },
	{ 0xEEDB, 0xF9FB }, { 0xEEDC, 0x7684 }, { 0xEEDD, 0x7A4D }, { 0xEEDE, 0x7B1B }, { 0xEEDF, 0x7C4D }, { 0xEEE0, 0x7E3E }, { 0xEEE1, 0x7FDF }, { 0xEEE2, 0x837B },
	{ 0xEEE3, 0x8B2B }, { 0xEEE4, 0x8CCA }, { 0xEEE5, 0x8D64 }, { 0xEEE6, 0x8DE1 }, { 0xEEE7, 0x8E5F }, { 0xEEE8, 0x8FEA }, { 0xEEE9, 0x8FF9 }, { 0xEEEA, 0x9069 },
	{ 0xEEEB, 0x93D1 }, { 0xEEEC, 0x4F43 }, { 0xEEED, 0x4F7A }, { 0xEEEE, 0x50B3 }, { 0xEEEF, 0x5168 }, { 0xEEF0, 0x5178 }, { 0xEEF1, 0x524D }, { 0xEEF2, 0x526A },
	{ 0xEEF3, 0x5861 }, { 0xEEF4, 0x587C }, { 0xEEF5, 0x5960 }, { 0xEEF6, 0x5C08 }, { 0xEEF7, 0x5C55 }, { 0xEEF8, 0x5EDB }, { 0xEEF9, 0x609B }, { 0xEEFA, 0x6230 },
	{ 0xEEFB, 0x6813 }, { 0xEEFC, 0x6BBF }, { 0xEEFD, 0x6C08 }, { 0xEEFE, 0x6FB1 }, { 0xEFA1, 0x714E }, { 0xEFA2, 0x7420 }, { 0xEFA3, 0x7530 }, { 0xEFA4, 0x7538 },
	{ 0xEFA5, 0x7551 }, { 0xEFA6, 0x7672 }, { 0xEFA7, 0x7B4C }, { 0xEFA8, 0x7B8B }, { 0xEFA9, 0x7BAD }, { 0xEFAA, 0x7BC6 }, { 0xEFAB, 0x7E8F }, { 0xEFAC, 0x8A6E },
	{ 0xEFAD, 0x8F3E }, { 0xEFAE, 0x8F49 }, { 0xEFAF, 0x923F }, { 0xEFB0, 0x9293 }, { 0xEFB1, 0x9322 }, { 0xEFB2, 0x942B }, { 0xEFB3, 0x96FB }, { 0xEFB4, 0x985A },
	{ 0xEFB5, 0x986B }, { 0xEFB6, 0x991E }, { 0xEFB7, 0x5207 }, { 0xEFB8, 0x622A }, { 0xEFB9, 0x6298 }, { 0xEFBA, 0x6D59 }, { 0xEFBB, 0x7664 }, { 0xEFBC, 0x7ACA },
	{ 0xEFBD, 0x7BC0 }, { 0xEFBE, 0x7D76 }, { 0xEFBF, 0x5360 }, { 0xEFC0, 0x5CBE }, { 0xEFC1, 0x5E97 }, { 0xEFC2, 0x6F38 }, { 0xEFC3, 0x70B9 }, { 0xEFC4, 0x7C98 },
	{ 0xEFC5, 0x9711 }, { 0xEFC6, 0x9B8E }, { 0xEFC7, 0x9EDE }, { 0xEFC8, 0x63A5 }, { 0xEFC9, 0x647A }, { 0xEFCA, 0x8776 }, { 0xEFCB, 0x4E01 }, { 0xEFCC, 0x4E95 },
	{ 0xEFCD, 0x4EAD }, { 0xEFCE, 0x505C }, { 0xEFCF, 0x5075 }, { 0xEFD0, 0x5448 }, { 0xEFD1, 0x59C3 }, { 0xEFD2, 0x5B9A }, { 0xEFD3, 0x5E40 }, { 0xEFD4, 0x5EAD },
	{ 0xEFD5, 0x5EF7 }, { 0xEFD6, 0x5F81 }, { 0xEFD7, 0x60C5 }, { 0xEFD8, 0x633A }, { 0xEFD9, 0x653F }, { 0xEFDA, 0x6574 }, { 0xEFDB, 0x65CC }, { 0xEFDC, 0x6676 },
	{ 0xEFDD, 0x6678 }, { 0xEFDE, 0x67FE }, { 0xEFDF, 0x6968 }, { 0xEFE0, 0x6A89 }, { 0xEFE1, 0x6B63 }, { 0xEFE2, 0x6C40 }, { 0xEFE3, 0x6DC0 }, { 0xEFE4, 0x6DE8 },
	{ 0xEFE5, 0x6E1F }, { 0xEFE6, 0x6E5E }, { 0xEFE7, 0x701E }, { 0xEFE8, 0x70A1 }, { 0xEFE9, 0x738E }, { 0xEFEA, 0x73FD }, { 0xEFEB, 0x753A }, { 0xEFEC, 0x775B },
	{ 0xEFED, 0x7887 }, { 0xEFEE, 0x798E }, { 0xEFEF, 0x7A0B }, { 0xEFF0, 0x7A7D }, { 0xEFF1, 0x7CBE }, { 0xEFF2, 0x7D8E }, { 0xEFF3, 0x8247 }, { 0xEFF4, 0x8A02 },
	{ 0xEFF5, 0x8AEA }, { 0xEFF6, 0x8C9E }, { 0xEFF7, 0x912D }, { 0xEFF8, 0x914A }, { 0xEFF9, 0x91D8 }, { 0xEFFA, 0x9266 }, { 0xEFFB, 0x92CC }, { 0xEFFC, 0x9320 },
	{ 0xEFFD, 0x9706 }, { 0xEFFE, 0x9756 }, { 0xF0A1, 0x975C }, { 0xF0A2, 0x9802 }, { 0xF0A3, 0x9F0E }, { 0xF0A4, 0x5236 }, { 0xF0A5, 0x5291 }, { 0xF0A6, 0x557C },
	{ 0xF0A7, 0x5824 }, { 0xF0A8, 0x5E1D }, { 0xF0A9, 0x5F1F }, { 0xF0AA, 0x608C }, { 0xF0AB, 0x63D0 }, { 0xF0AC, 0x68AF }, { 0xF0AD, 0x6FDF }, { 0xF0AE, 0x796D },
	{ 0xF0AF, 0x7B2C }, { 0xF0B0, 0x81CD }, { 0xF0B1, 0x85BA }, { 0xF0B2, 0x88FD }, { 0xF0B3, 0x8AF8 }, { 0xF0B4, 0x8E44 }, { 0xF0B5, 0x918D }, { 0xF0B6, 0x9664 },
	{ 0xF0B7, 0x969B }, { 0xF0B8, 0x973D }, { 0xF0B9, 0x984C }, { 0xF0BA, 0x9F4A }, { 0xF0BB, 0x4FCE }, { 0xF0BC, 0x5146 }, { 0xF0BD, 0x51CB }, { 0xF0BE, 0x52A9 },
	{ 0xF0BF, 0x5632 }, { 0xF0C0, 0x5F14 }, { 0xF0C1, 0x5F6B }, { 0xF0C2, 0x63AA }, { 0xF0C3, 0x64CD }, { 0xF0C4, 0x65E9 }, { 0xF0C5, 0x6641 }, { 0xF0C6, 0x66FA },
	{ 0xF0C7, 0x66F9 }, { 0xF0C8, 0x671D }, { 0xF0C9, 0x689D }, { 0xF0CA, 0x68D7 }, { 0xF0CB, 0x69FD }, { 0xF0CC, 0x6F15 }, { 0xF0CD, 0x6F6E }, { 0xF0CE, 0x7167 },
	{ 0xF0CF, 0x71E5 }, { 0xF0D0, 0x722A }, { 0xF0D1, 0x74AA }, { 0xF0D2, 0x773A }, { 0xF0D3, 0x7956 }, { 0xF0D4, 0x795A }, { 0xF0D5, 0x79DF }, { 0xF0D6, 0x7A20 },
	{ 0xF0D7, 0x7A95 }, { 0xF0D8, 0x7C97 }, { 0xF0D9, 0x7CDF }, { 0xF0DA, 0x7D44 }, { 0xF0DB, 0x7E70 }, { 0xF0DC, 0x8087 }, { 0xF0DD, 0x85FB }, { 0xF0DE, 0x86A4 },
	{ 0xF0DF, 0x8A54 }, { 0xF0E0, 0x8ABF }, { 0xF0E1, 0x8D99 }, { 0xF0E2, 0x8E81 }, { 0xF0E3, 0x9020 }, { 0xF0E4, 0x906D }, { 0xF0E5, 0x91E3 }, { 0xF0E6, 0x963B },
	{ 0xF0E7, 0x96D5 }, { 0xF0E8, 0x9CE5 }, { 0xF0E9, 0x65CF }, { 0xF0EA, 0x7C07 }, { 0xF0EB, 0x8DB3 }, { 0xF0EC, 0x93C3 }, { 0xF0ED, 0x5B58 }, { 0xF0EE, 0x5C0A },
	{ 0xF0EF, 0x5352 }, { 0xF0F0, 0x62D9 }, { 0xF0F1, 0x731D }, { 0xF0F2, 0x5027 }, { 0xF0F3, 0x5B97 }, { 0xF0F4, 0x5F9E }, { 0xF0F5, 0x60B0 }, { 0xF0F6, 0x616B },
	{ 0xF0F7, 0x68D5 }, { 0xF0F8, 0x6DD9 }, { 0xF0F9, 0x742E }, { 0xF0FA, 0x7A2E }, { 0xF0FB, 0x7D42 }, { 0xF0FC, 0x7D9C }, { 0xF0FD, 0x7E31 }, { 0xF0FE, 0x816B },
	{ 0xF1A1, 0x8E2A }, { 0xF1A2, 0x8E35 }, { 0xF1A3, 0x937E }, { 0xF1A4, 0x9418 }, { 0xF1A5, 0x4F50 }, { 0xF1A6, 0x5750 }, { 0xF1A7, 0x5DE6 }, { 0xF1A8, 0x5EA7 },
	{ 0xF1A9, 0x632B }, { 0xF1AA, 0x7F6A }, { 0xF1AB, 0x4E3B }, { 0xF1AC, 0x4F4F }, { 0xF1AD, 0x4F8F }, { 0xF1AE, 0x505A }, { 0xF1AF, 0x59DD }, { 0xF1B0, 0x80C4 },
	{ 0xF1B1, 0x546A }, { 0xF1B2, 0x5468 }, { 0xF1B3, 0x55FE }, { 0xF1B4, 0x594F }, { 0xF1B5, 0x5B99 }, { 0xF1B6, 0x5DDE }, { 0xF1B7, 0x5EDA }, { 0xF1B8, 0x665D },
	{ 0xF1B9, 0x6731 }, { 0xF1BA, 0x67F1 }, { 0xF1BB, 0x682A }, { 0xF1BC, 0x6CE8 }, { 0xF1BD, 0x6D32 }, { 0xF1BE, 0x6E4A }, { 0xF1BF, 0x6F8D }, { 0xF1C0, 0x70B7 },
	{ 0xF1C1, 0x73E0 }, { 0xF1C2, 0x7587 }, { 0xF1C3, 0x7C4C }, { 0xF1C4, 0x7D02 }, { 0xF1C5, 0x7D2C }, { 0xF1C6, 0x7DA2 }, { 0xF1C7, 0x821F }, { 0xF1C8, 0x86DB },
	{ 0xF1C9, 0x8A3B }, { 0xF1CA, 0x8A85 }, { 0xF1CB, 0x8D70 }, { 0xF1CC, 0x8E8A }, { 0xF1CD, 0x8F33 }, { 0xF1CE, 0x9031 }, { 0xF1CF, 0x914E }, { 0xF1D0, 0x9152 },
	{ 0xF1D1, 0x9444 }, { 0xF1D2, 0x99D0 }, { 0xF1D3, 0x7AF9 }, { 0xF1D4, 0x7CA5 }, { 0xF1D5, 0x4FCA }, { 0xF1D6, 0x5101 }, { 0xF1D7, 0x51C6 }, { 0xF1D8, 0x57C8 },
	{ 0xF1D9, 0x5BEF }, { 0xF1DA, 0x5CFB }, { 0xF1DB, 0x6659 }, { 0xF1DC, 0x6A3D }, { 0xF1DD, 0x6D5A }, { 0xF1DE, 0x6E96 }, { 0xF1DF, 0x6FEC }, { 0xF1E0, 0x710C },
	{ 0xF1E1, 0x756F }, { 0xF1E2, 0x7AE3 }, { 0xF1E3, 0x8822 }, { 0xF1E4, 0x9021 }, { 0xF1E5, 0x9075 }, { 0xF1E6, 0x96CB }, { 0xF1E7, 0x99FF }, { 0xF1E8, 0x8301 },
	{ 0xF1E9, 0x4E2D }, { 0xF1EA, 0x4EF2 }, { 0xF1EB, 0x8846 }, { 0xF1EC, 0x91CD }, { 0xF1ED, 0x537D }, { 0xF1EE, 0x6ADB }, { 0xF1EF, 0x696B }, { 0xF1F0, 0x6C41 },
	{ 0xF1F1, 0x847A }, { 0xF1F2, 0x589E }, { 0xF1F3, 0x618E }, { 0xF1F4, 0x66FE }, { 0xF1F5, 0x62EF }, { 0xF1F6, 0x70DD }, { 0xF1F7, 0x7511 }, { 0xF1F8, 0x75C7 },
	{ 0xF1F9, 0x7E52 }, { 0xF1FA, 0x84B8 }, { 0xF1FB, 0x8B49 }, { 0xF1FC, 0x8D08 }, { 0xF1FD, 0x4E4B }, { 0xF1FE, 0x53EA }, { 0xF2A1, 0x54AB }, { 0xF2A2, 0x5730 },
	{ 0xF2A3, 0x5740 }, { 0xF2A4, 0x5FD7 }, { 0xF2A5, 0x6301 }, { 0xF2A6, 0x6307 }, { 0xF2A7, 0x646F }, { 0xF2A8, 0x652F }, { 0xF2A9, 0x65E8 }, { 0xF2AA, 0x667A },
	{ 0xF2AB, 0x679D }, { 0xF2AC, 0x67B3 }, { 0xF2AD, 0x6B62 }, { 0xF2AE, 0x6C60 }, { 0xF2AF, 0x6C9A }, { 0xF2B0, 0x6F2C }, { 0xF2B1, 0x77E5 }, { 0xF2B2, 0x7825 },
	{ 0xF2B3, 0x7949 }, { 0xF2B4, 0x7957 }, { 0xF2B5, 0x7D19 }, { 0xF2B6, 0x80A2 }, { 0xF2B7, 0x8102 }, { 0xF2B8, 0x81F3 }, { 0xF2B9, 0x829D }, { 0xF2BA, 0x82B7 },
	{ 0xF2BB, 0x8718 }, { 0xF2BC, 0x8A8C }, { 0xF2BD, 0xF9FC }, { 0xF2BE, 0x8D04 }, { 0xF2BF, 0x8DBE }, { 0xF2C0, 0x9072 }, { 0xF2C1, 0x76F4 }, { 0xF2C2, 0x7A19 },
	{ 0xF2C3, 0x7A37 }, { 0xF2C4, 0x7E54 }, { 0xF2C5, 0x8077 }, { 0xF2C6, 0x5507 }, { 0xF2C7, 0x55D4 }, { 0xF2C8, 0x5875 }, { 0xF2C9, 0x632F }, { 0xF2CA, 0x6422 },
	{ 0xF2CB, 0x6649 }, { 0xF2CC, 0x664B }, { 0xF2CD, 0x686D }, { 0xF2CE, 0x699B }, { 0xF2CF, 0x6B84 }, { 0xF2D0, 0x6D25 }, { 0xF2D1, 0x6EB1 }, { 0xF2D2, 0x73CD },
	{ 0xF2D3, 0x7468 }, { 0xF2D4, 0x74A1 }, { 0xF2D5, 0x755B }, { 0xF2D6, 0x75B9 }, { 0xF2D7, 0x76E1 }, { 0xF2D8, 0x771E }, { 0xF2D9, 0x778B }, { 0xF2DA, 0x79E6 },
	{ 0xF2DB, 0x7E09 }, { 0xF2DC, 0x7E1D }, { 0xF2DD, 0x81FB }, { 0xF2DE, 0x852F }, { 0xF2DF, 0x8897 }, { 0xF2E0, 0x8A3A }, { 0xF2E1, 0x8CD1 }, { 0xF2E2, 0x8EEB },
	{ 0xF2E3, 0x8FB0 }, { 0xF2E4, 0x9032 }, { 0xF2E5, 0x93AD }, { 0xF2E6, 0x9663 }, { 0xF2E7, 0x9673 }, { 0xF2E8, 0x9707 }, { 0xF2E9, 0x4F84 }, { 0xF2EA, 0x53F1 },
	{ 0xF2EB, 0x59EA }, { 0xF2EC, 0x5AC9 }, { 0xF2ED, 0x5E19 }, { 0xF2EE, 0x684E }, { 0xF2EF, 0x74C6 }, { 0xF2F0, 0x75BE }, { 0xF2F1, 0x79E9 }, { 0xF2F2, 0x7A92 },
	{ 0xF2F3, 0x81A3 }, { 0xF2F4, 0x86ED }, { 0xF2F5, 0x8CEA }, { 0xF2F6, 0x8DCC }, { 0xF2F7, 0x8FED }, { 0xF2F8, 0x659F }, { 0xF2F9, 0x6715 }, { 0xF2FA, 0xF9FD },
	{ 0xF2FB, 0x57F7 }, { 0xF2FC, 0x6F57 }, { 0xF2FD, 0x7DDD }, { 0xF2FE, 0x8F2F }, { 0xF3A1, 0x93F6 }, { 0xF3A2, 0x96C6 }, { 0xF3A3, 0x5FB5 }, { 0xF3A4, 0x61F2 },
	{ 0xF3A5, 0x6F84 }, { 0xF3A6, 0x4E14 }, { 0xF3A7, 0x4F98 }, { 0xF3A8, 0x501F }, { 0xF3A9, 0x53C9 }, { 0xF3AA, 0x55DF }, { 0xF3AB, 0x5D6F }, { 0xF3AC, 0x5DEE },
	{ 0xF3AD, 0x6B21 }, { 0xF3AE, 0x6B64 }, { 0xF3AF, 0x78CB }, { 0xF3B0, 0x7B9A }, { 0xF3B1, 0xF9FE }, { 0xF3B2, 0x8E49 }, { 0xF3B3, 0x8ECA }, { 0xF3B4, 0x906E },
	{ 0xF3B5, 0x6349 }, { 0xF3B6, 0x643E }, { 0xF3B7, 0x7740 }, { 0xF3B8, 0x7A84 }, { 0xF3B9, 0x932F }, { 0xF3BA, 0x947F }, { 0xF3BB, 0x9F6A }, { 0xF3BC, 0x64B0 },
	{ 0xF3BD, 0x6FAF }, { 0xF3BE, 0x71E6 }, { 0xF3BF, 0x74A8 }, { 0xF3C0, 0x74DA }, { 0xF3C1, 0x7AC4 }, { 0xF3C2, 0x7C12 }, { 0xF3C3, 0x7E82 }, { 0xF3C4, 0x7CB2 },
	{ 0xF3C5, 0x7E98 }, { 0xF3C6, 0x8B9A }, { 0xF3C7, 0x8D0A }, { 0xF3C8, 0x947D }, { 0xF3C9, 0x9910 }, { 0xF3CA, 0x994C }, { 0xF3CB, 0x5239 }, { 0xF3CC, 0x5BDF },
	{ 0xF3CD, 0x64E6 }, { 0xF3CE, 0x672D }, { 0xF3CF, 0x7D2E }, { 0xF3D0, 0x50ED }, { 0xF3D1, 0x53C3 }, { 0xF3D2, 0x5879 }, { 0xF3D3, 0x6158 }, { 0xF3D4, 0x6159 },
	{ 0xF3D5, 0x61FA }, { 0xF3D6, 0x65AC }, { 0xF3D7, 0x7AD9 }, { 0xF3D8, 0x8B92 }, { 0xF3D9, 0x8B96 }, { 0xF3DA, 0x5009 }, { 0xF3DB, 0x5021 }, { 0xF3DC, 0x5275 },
	{ 0xF3DD, 0x5531 }, { 0xF3DE, 0x5A3C }, { 0xF3DF, 0x5EE0 }, { 0xF3E0, 0x5F70 }, { 0xF3E1, 0x6134 }, { 0xF3E2, 0x655E }, { 0xF3E3, 0x660C }, { 0xF3E4, 0x6636 },
	{ 0xF3E5, 0x66A2 }, { 0xF3E6, 0x69CD }, { 0xF3E7, 0x6EC4 }, { 0xF3E8, 0x6F32 }, { 0xF3E9, 0x7316 }, { 0xF3EA, 0x7621 }, { 0xF3EB, 0x7A93 }, { 0xF3EC, 0x8139 },
	{ 0xF3ED, 0x8259 }, { 0xF3EE, 0x83D6 }, { 0xF3EF, 0x84BC }, { 0xF3F0, 0x50B5 }, { 0xF3F1, 0x57F0 }, { 0xF3F2, 0x5BC0 }, { 0xF3F3, 0x5BE8 }, { 0xF3F4, 0x5F69 },
	{ 0xF3F5, 0x63A1 }, { 0xF3F6, 0x7826 }, { 0xF3F7, 0x7DB5 }, { 0xF3F8, 0x83DC }, { 0xF3F9, 0x8521 }, { 0xF3FA, 0x91C7 }, { 0xF3FB, 0x91F5 }, { 0xF3FC, 0x518A },
	{ 0xF3FD, 0x67F5 }, { 0xF3FE, 0x7B56 }, { 0xF4A1, 0x8CAC }, { 0xF4A2, 0x51C4 }, { 0xF4A3, 0x59BB }, { 0xF4A4, 0x60BD }, { 0xF4A5, 0x8655 }, { 0xF4A6, 0x501C },
	{ 0xF4A7, 0xF9FF }, { 0xF4A8, 0x5254 }, { 0xF4A9, 0x5C3A }, { 0xF4AA, 0x617D }, { 0xF4AB, 0x621A }, { 0xF4AC, 0x62D3 }, { 0xF4AD, 0x64F2 }, { 0xF4AE, 0x65A5 },
	{ 0xF4AF, 0x6ECC }, { 0xF4B0, 0x7620 }, { 0xF4B1, 0x810A }, { 0xF4B2, 0x8E60 }, { 0xF4B3, 0x965F }, { 0xF4B4, 0x96BB }, { 0xF4B5, 0x4EDF }, { 0xF4B6, 0x5343 },
	{ 0xF4B7, 0x5598 }, { 0xF4B8, 0x5929 }, { 0xF4B9, 0x5DDD }, { 0xF4BA, 0x64C5 }, { 0xF4BB, 0x6CC9 }, { 0xF4BC, 0x6DFA }, { 0xF4BD, 0x7394 }, { 0xF4BE, 0x7A7F },
	{ 0xF4BF, 0x821B }, { 0xF4C0, 0x85A6 }, { 0xF4C1, 0x8CE4 }, { 0xF4C2, 0x8E10 }, { 0xF4C3, 0x9077 }, { 0xF4C4, 0x91E7 }, { 0xF4C5, 0x95E1 }, { 0xF4C6, 0x9621 },
	{ 0xF4C7, 0x97C6 }, { 0xF4C8, 0x51F8 }, { 0xF4C9, 0x54F2 }, { 0xF4CA, 0x5586 }, { 0xF4CB, 0x5FB9 }, { 0xF4CC, 0x64A4 }, { 0xF4CD, 0x6F88 }, { 0xF4CE, 0x7DB4 },
	{ 0xF4CF, 0x8F1F }, { 0xF4D0, 0x8F4D }, { 0xF4D1, 0x9435 }, { 0xF4D2, 0x50C9 }, { 0xF4D3, 0x5C16 }, { 0xF4D4, 0x6CBE }, { 0xF4D5, 0x6DFB }, { 0xF4D6, 0x751B },
	{ 0xF4D7, 0x77BB }, { 0xF4D8, 0x7C3D }, { 0xF4D9, 0x7C64 }, { 0xF4DA, 0x8A79 }, { 0xF4DB, 0x8AC2 }, { 0xF4DC, 0x581E }, { 0xF4DD, 0x59BE }, { 0xF4DE, 0x5E16 },
	{ 0xF4DF, 0x6377 }, { 0xF4E0, 0x7252 }, { 0xF4E1, 0x758A }, { 0xF4E2, 0x776B }, { 0xF4E3, 0x8ADC }, { 0xF4E4, 0x8CBC }, { 0xF4E5, 0x8F12 }, { 0xF4E6, 0x5EF3 },
	{ 0xF4E7, 0x6674 }, { 0xF4E8, 0x6DF8 }, { 0xF4E9, 0x807D }, { 0xF4EA, 0x83C1 }, { 0xF4EB, 0x8ACB }, { 0xF4EC, 0x9751 }, { 0xF4ED, 0x9BD6 }, { 0xF4EE, 0xFA00 },
	{ 0xF4EF, 0x5243 }, { 0xF4F0, 0x66FF }, { 0xF4F1, 0x6D95 }, { 0xF4F2, 0x6EEF }, { 0xF4F3, 0x7DE0 }, { 0xF4F4, 0x8AE6 }, { 0xF4F5, 0x902E }, { 0xF4F6, 0x905E },
	{ 0xF4F7, 0x9AD4 }, { 0xF4F8, 0x521D }, { 0xF4F9, 0x527F }, { 0xF4FA, 0x54E8 }, { 0xF4FB, 0x6194 }, { 0xF4FC, 0x6284 }, { 0xF4FD, 0x62DB }, { 0xF4FE, 0x68A2 },
	{ 0xF5A1, 0x6912 }, { 0xF5A2, 0x695A }, { 0xF5A3, 0x6A35 }, { 0xF5A4, 0x7092 }, { 0xF5A5, 0x7126 }, { 0xF5A6, 0x785D }, { 0xF5A7, 0x7901 }, { 0xF5A8, 0x790E },
	{ 0xF5A9, 0x79D2 }, { 0xF5AA, 0x7A0D }, { 0xF5AB, 0x8096 }, { 0xF5AC, 0x8278 }, { 0xF5AD, 0x82D5 }, { 0xF5AE, 0x8349 }, { 0xF5AF, 0x8549 }, { 0xF5B0, 0x8C82 },
	{ 0xF5B1, 0x8D85 }, { 0xF5B2, 0x9162 }, { 0xF5B3, 0x918B }, { 0xF5B4, 0x91AE }, { 0xF5B5, 0x4FC3 }, { 0xF5B6, 0x56D1 }, { 0xF5B7, 0x71ED }, { 0xF5B8, 0x77D7 },
	{ 0xF5B9, 0x8700 }, { 0xF5BA, 0x89F8 }, { 0xF5BB, 0x5BF8 }, { 0xF5BC, 0x5FD6 }, { 0xF5BD, 0x6751 }, { 0xF5BE, 0x90A8 }, { 0xF5BF, 0x53E2 }, { 0xF5C0, 0x585A },
	{ 0xF5C1, 0x5BF5 }, { 0xF5C2, 0x60A4 }, { 0xF5C3, 0x6181 }, { 0xF5C4, 0x6460 }, { 0xF5C5, 0x7E3D }, { 0xF5C6, 0x8070 }, { 0xF5C7, 0x8525 }, { 0xF5C8, 0x9283 },
	{ 0xF5C9, 0x64AE }, { 0xF5CA, 0x50AC }, { 0xF5CB, 0x5D14 }, { 0xF5CC, 0x6700 }, { 0xF5CD, 0x589C }, { 0xF5CE, 0x62BD }, { 0xF5CF, 0x63A8 }, { 0xF5D0, 0x690E },
	{ 0xF5D1, 0x6978 }, { 0xF5D2, 0x6A1E }, { 0xF5D3, 0x6E6B }, { 0xF5D4, 0x76BA }, { 0xF5D5, 0x79CB }, { 0xF5D6, 0x82BB }, { 0xF5D7, 0x8429 }, { 0xF5D8, 0x8ACF },
	{ 0xF5D9, 0x8DA8 }, { 0xF5DA, 0x8FFD }, { 0xF5DB, 0x9112 }, { 0xF5DC, 0x914B }, { 0xF5DD, 0x919C }, { 0xF5DE, 0x9310 }, { 0xF5DF, 0x9318 }, { 0xF5E0, 0x939A },
	{ 0xF5E1, 0x96DB }, { 0xF5E2, 0x9A36 }, { 0xF5E3, 0x9C0D }, { 0xF5E4, 0x4E11 }, { 0xF5E5, 0x755C }, { 0xF5E6, 0x795D }, { 0xF5E7, 0x7AFA }, { 0xF5E8, 0x7B51 },
	{ 0xF5E9, 0x7BC9 }, { 0xF5EA, 0x7E2E }, { 0xF5EB, 0x84C4 }, { 0xF5EC, 0x8E59 }, { 0xF5ED, 0x8E74 }, { 0xF5EE, 0x8EF8 }, { 0xF5EF, 0x9010 }, { 0xF5F0, 0x6625 },
	{ 0xF5F1, 0x693F }, { 0xF5F2, 0x7443 }, { 0xF5F3, 0x51FA }, { 0xF5F4, 0x672E }, { 0xF5F5, 0x9EDC }, { 0xF5F6, 0x5145 }, { 0xF5F7, 0x5FE0 }, { 0xF5F8, 0x6C96 },
	{ 0xF5F9, 0x87F2 }, { 0xF5FA, 0x885D }, { 0xF5FB, 0x8877 }, { 0xF5FC, 0x60B4 }, { 0xF5FD, 0x81B5 }, { 0xF5FE, 0x8403 }, { 0xF6A1, 0x8D05 }, { 0xF6A2, 0x53D6 },
	{ 0xF6A3, 0x5439 }, { 0xF6A4, 0x5634 }, { 0xF6A5, 0x5A36 }, { 0xF6A6, 0x5C31 }, { 0xF6A7, 0x708A }, { 0xF6A8, 0x7FE0 }, { 0xF6A9, 0x805A }, { 0xF6AA, 0x8106 },
	{ 0xF6AB, 0x81ED }, { 0xF6AC, 0x8DA3 }, { 0xF6AD, 0x9189 }, { 0xF6AE, 0x9A5F }, { 0xF6AF, 0x9DF2 }, { 0xF6B0, 0x5074 }, { 0xF6B1, 0x4EC4 }, { 0xF6B2, 0x53A0 },
	{ 0xF6B3, 0x60FB }, { 0xF6B4, 0x6E2C }, { 0xF6B5, 0x5C64 }, { 0xF6B6, 0x4F88 }, { 0xF6B7, 0x5024 }, { 0xF6B8, 0x55E4 }, { 0xF6B9, 0x5CD9 }, { 0xF6BA, 0x5E5F },
	{ 0xF6BB, 0x6065 }, { 0xF6BC, 0x6894 }, { 0xF6BD, 0x6CBB }, { 0xF6BE, 0x6DC4 }, { 0xF6BF, 0x71BE }, { 0xF6C0, 0x75D4 }, { 0xF6C1, 0x75F4 }, { 0xF6C2, 0x7661 },
	{ 0xF6C3, 0x7A1A }, { 0xF6C4, 0x7A49 }, { 0xF6C5, 0x7DC7 }, { 0xF6C6, 0x7DFB }, { 0xF6C7, 0x7F6E }, { 0xF6C8, 0x81F4 }, { 0xF6C9, 0x86A9 }, { 0xF6CA, 0x8F1C },
	{ 0xF6CB, 0x96C9 }, { 0xF6CC, 0x99B3 }, { 0xF6CD, 0x9F52 }, { 0xF6CE, 0x5247 }, { 0xF6CF, 0x52C5 }, { 0xF6D0, 0x98ED }, { 0xF6D1, 0x89AA }, { 0xF6D2, 0x4E03 },
	{ 0xF6D3, 0x67D2 }, { 0xF6D4, 0x6F06 }, { 0xF6D5, 0x4FB5 }, { 0xF6D6, 0x5BE2 }, { 0xF6D7, 0x6795 }, { 0xF6D8, 0x6C88 }, { 0xF6D9, 0x6D78 }, { 0xF6DA, 0x741B },
	{ 0xF6DB, 0x7827 }, { 0xF6DC, 0x91DD }, { 0xF6DD, 0x937C }, { 0xF6DE, 0x87C4 }, { 0xF6DF, 0x79E4 }, { 0xF6E0, 0x7A31 }, { 0xF6E1, 0x5FEB }, { 0xF6E2, 0x4ED6 },
	{ 0xF6E3, 0x54A4 }, { 0xF6E4, 0x553E }, { 0xF6E5, 0x58AE }, { 0xF6E6, 0x59A5 }, { 0xF6E7, 0x60F0 }, { 0xF6E8, 0x6253 }, { 0xF6E9, 0x62D6 }, { 0xF6EA, 0x6736 },
	{ 0xF6EB, 0x6955 }, { 0xF6EC, 0x8235 }, { 0xF6ED, 0x9640 }, { 0xF6EE, 0x99B1 }, { 0xF6EF, 0x99DD }, { 0xF6F0, 0x502C }, { 0xF6F1, 0x5353 }, { 0xF6F2, 0x5544 },
	{ 0xF6F3, 0x577C }, { 0xF6F4, 0xFA01 }, { 0xF6F5, 0x6258 }, { 0xF6F6, 0xFA02 }, { 0xF6F7, 0x64E2 }, { 0xF6F8, 0x666B }, { 0xF6F9, 0x67DD }, { 0xF6FA, 0x6FC1 },
	{ 0xF6FB, 0x6FEF }, { 0xF6FC, 0x7422 }, { 0xF6FD, 0x7438 }, { 0xF6FE, 0x8A17 }, { 0xF7A1, 0x9438 }, { 0xF7A2, 0x5451 }, { 0xF7A3, 0x5606 }, { 0xF7A4, 0x5766 },
	{ 0xF7A5, 0x5F48 }, { 0xF7A6, 0x619A }, { 0xF7A7, 0x6B4E }, { 0xF7A8, 0x7058 }, { 0xF7A9, 0x70AD }, { 0xF7AA, 0x7DBB }, { 0xF7AB, 0x8A95 }, { 0xF7AC, 0x596A },
	{ 0xF7AD, 0x812B }, { 0xF7AE, 0x63A2 }, { 0xF7AF, 0x7708 }, { 0xF7B0, 0x803D }, { 0xF7B1, 0x8CAA }, { 0xF7B2, 0x5854 }, { 0xF7B3, 0x642D }, { 0xF7B4, 0x69BB },
	{ 0xF7B5, 0x5B95 }, { 0xF7B6, 0x5E11 }, { 0xF7B7, 0x6E6F }, { 0xF7B8, 0xFA03 }, { 0xF7B9, 0x8569 }, { 0xF7BA, 0x514C }, { 0xF7BB, 0x53F0 }, { 0xF7BC, 0x592A },
	{ 0xF7BD, 0x6020 }, { 0xF7BE, 0x614B }, { 0xF7BF, 0x6B86 }, { 0xF7C0, 0x6C70 }, { 0xF7C1, 0x6CF0 }, { 0xF7C2, 0x7B1E }, { 0xF7C3, 0x80CE }, { 0xF7C4, 0x82D4 },
	{ 0xF7C5, 0x8DC6 }, { 0xF7C6, 0x90B0 }, { 0xF7C7, 0x98B1 }, { 0xF7C8, 0xFA04 }, { 0xF7C9, 0x64C7 }, { 0xF7CA, 0x6FA4 }, { 0xF7CB, 0x6491 }, { 0xF7CC, 0x6504 },
	{ 0xF7CD, 0x514E }, { 0xF7CE, 0x5410 }, { 0xF7CF, 0x571F }, { 0xF7D0, 0x8A0E }, { 0xF7D1, 0x615F }, { 0xF7D2, 0x6876 }, { 0xF7D3, 0xFA05 }, { 0xF7D4, 0x75DB },
	{ 0xF7D5, 0x7B52 }, { 0xF7D6, 0x7D71 }, { 0xF7D7, 0x901A }, { 0xF7D8, 0x5806 }, { 0xF7D9, 0x69CC }, { 0xF7DA, 0x817F }, { 0xF7DB, 0x892A }, { 0xF7DC, 0x9000 },
	{ 0xF7DD, 0x9839 }, { 0xF7DE, 0x5078 }, { 0xF7DF, 0x5957 }, { 0xF7E0, 0x59AC }, { 0xF7E1, 0x6295 }, { 0xF7E2, 0x900F }, { 0xF7E3, 0x9B2A }, { 0xF7E4, 0x615D },
	{ 0xF7E5, 0x7279 }, { 0xF7E6, 0x95D6 }, { 0xF7E7, 0x5761 }, { 0xF7E8, 0x5A46 }, { 0xF7E9, 0x5DF4 }, { 0xF7EA, 0x628A }, { 0xF7EB, 0x64AD }, { 0xF7EC, 0x64FA },
	{ 0xF7ED, 0x6777 }, { 0xF7EE, 0x6CE2 }, { 0xF7EF, 0x6D3E }, { 0xF7F0, 0x722C }, { 0xF7F1, 0x7436 }, { 0xF7F2, 0x7834 }, { 0xF7F3, 0x7F77 }, { 0xF7F4, 0x82AD },
	{ 0xF7F5, 0x8DDB }, { 0xF7F6, 0x9817 }, { 0xF7F7, 0x5224 }, { 0xF7F8, 0x5742 }, { 0xF7F9, 0x677F }, { 0xF7FA, 0x7248 }, { 0xF7FB, 0x74E3 }, { 0xF7FC, 0x8CA9 },
	{ 0xF7FD, 0x8FA6 }, { 0xF7FE, 0x9211 }, { 0xF8A1, 0x962A }, { 0xF8A2, 0x516B }, { 0xF8A3, 0x53ED }, { 0xF8A4, 0x634C }, { 0xF8A5, 0x4F69 }, { 0xF8A6, 0x5504 },
	{ 0xF8A7, 0x6096 }, { 0xF8A8, 0x6557 }, { 0xF8A9, 0x6C9B }, { 0xF8AA, 0x6D7F }, { 0xF8AB, 0x724C }, { 0xF8AC, 0x72FD }, { 0xF8AD, 0x7A17 }, { 0xF8AE, 0x8987 },
	{ 0xF8AF, 0x8C9D }, { 0xF8B0, 0x5F6D }, { 0xF8B1, 0x6F8E }, { 0xF8B2, 0x70F9 }, { 0xF8B3, 0x81A8 }, { 0xF8B4, 0x610E }, { 0xF8B5, 0x4FBF }, { 0xF8B6, 0x504F },
	{ 0xF8B7, 0x6241 }, { 0xF8B8, 0x7247 }, { 0xF8B9, 0x7BC7 }, { 0xF8BA, 0x7DE8 }, { 0xF8BB, 0x7FE9 }, { 0xF8BC, 0x904D }, { 0xF8BD, 0x97AD }, { 0xF8BE, 0x9A19 },
	{ 0xF8BF, 0x8CB6 }, { 0xF8C0, 0x576A }, { 0xF8C1, 0x5E73 }, { 0xF8C2, 0x67B0 }, { 0xF8C3, 0x840D }, { 0xF8C4, 0x8A55 }, { 0xF8C5, 0x5420 }, { 0xF8C6, 0x5B16 },
	{ 0xF8C7, 0x5E63 }, { 0xF8C8, 0x5EE2 }, { 0xF8C9, 0x5F0A }, { 0xF8CA, 0x6583 }, { 0xF8CB, 0x80BA }, { 0xF8CC, 0x853D }, { 0xF8CD, 0x9589 }, { 0xF8CE, 0x965B },
	{ 0xF8CF, 0x4F48 }, { 0xF8D0, 0x5305 }, { 0xF8D1, 0x530D }, { 0xF8D2, 0x530F }, { 0xF8D3, 0x5486 }, { 0xF8D4, 0x54FA }, { 0xF8D5, 0x5703 }, { 0xF8D6, 0x5E03 },
	{ 0xF8D7, 0x6016 }, { 0xF8D8, 0x629B }, { 0xF8D9, 0x62B1 }, { 0xF8DA, 0x6355 }, { 0xF8DB, 0xFA06 }, { 0xF8DC, 0x6CE1 }, { 0xF8DD, 0x6D66 }, { 0xF8DE, 0x75B1 },
	{ 0xF8DF, 0x7832 }, { 0xF8E0, 0x80DE }, { 0xF8E1, 0x812F }, { 0xF8E2, 0x82DE }, { 0xF8E3, 0x8461 }, { 0xF8E4, 0x84B2 }, { 0xF8E5, 0x888D }, { 0xF8E6, 0x8912 },
	{ 0xF8E7, 0x900B }, { 0xF8E8, 0x92EA }, { 0xF8E9, 0x98FD }, { 0xF8EA, 0x9B91 }, { 0xF8EB, 0x5E45 }, { 0xF8EC, 0x66B4 }, { 0xF8ED, 0x66DD }, { 0xF8EE, 0x7011 },
	{ 0xF8EF, 0x7206 }, { 0xF8F0, 0xFA07 }, { 0xF8F1, 0x4FF5 }, { 0xF8F2, 0x527D }, { 0xF8F3, 0x5F6A }, { 0xF8F4, 0x6153 }, { 0xF8F5, 0x6753 }, { 0xF8F6, 0x6A19 },
	{ 0xF8F7, 0x6F02 }, { 0xF8F8, 0x74E2 }, { 0xF8F9, 0x7968 }, { 0xF8FA, 0x8868 }, { 0xF8FB, 0x8C79 }, { 0xF8FC, 0x98C7 }, { 0xF8FD, 0x98C4 }, { 0xF8FE, 0x9A43 },
	{ 0xF9A1, 0x54C1 }, { 0xF9A2, 0x7A1F }, { 0xF9A3, 0x6953 }, { 0xF9A4, 0x8AF7 }, { 0xF9A5, 0x8C4A }, { 0xF9A6, 0x98A8 }, { 0xF9A7, 0x99AE }, { 0xF9A8, 0x5F7C },
	{ 0xF9A9, 0x62AB }, { 0xF9AA, 0x75B2 }, { 0xF9AB, 0x76AE }, { 0xF9AC, 0x88AB }, { 0xF9AD, 0x907F }, { 0xF9AE, 0x9642 }, { 0xF9AF, 0x5339 }, { 0xF9B0, 0x5F3C },
	{ 0xF9B1, 0x5FC5 }, { 0xF9B2, 0x6CCC }, { 0xF9B3, 0x73CC }, { 0xF9B4, 0x7562 }, { 0xF9B5, 0x758B }, { 0xF9B6, 0x7B46 }, { 0xF9B7, 0x82FE }, { 0xF9B8, 0x999D },
	{ 0xF9B9, 0x4E4F }, { 0xF9BA, 0x903C }, { 0xF9BB, 0x4E0B }, { 0xF9BC, 0x4F55 }, { 0xF9BD, 0x53A6 }, { 0xF9BE, 0x590F }, { 0xF9BF, 0x5EC8 }, { 0xF9C0, 0x6630 },
	{ 0xF9C1, 0x6CB3 }, { 0xF9C2, 0x7455 }, { 0xF9C3, 0x8377 }, { 0xF9C4, 0x8766 }, { 0xF9C5, 0x8CC0 }, { 0xF9C6, 0x9050 }, { 0xF9C7, 0x971E }, { 0xF9C8, 0x9C15 },
	{ 0xF9C9, 0x58D1 }, { 0xF9CA, 0x5B78 }, { 0xF9CB, 0x8650 }, { 0xF9CC, 0x8B14 }, { 0xF9CD, 0x9DB4 }, { 0xF9CE, 0x5BD2 }, { 0xF9CF, 0x6068 }, { 0xF9D0, 0x608D },
	{ 0xF9D1, 0x65F1 }, { 0xF9D2, 0x6C57 }, { 0xF9D3, 0x6F22 }, { 0xF9D4, 0x6FA3 }, { 0xF9D5, 0x701A }, { 0xF9D6, 0x7F55 }, { 0xF9D7, 0x7FF0 }, { 0xF9D8, 0x9591 },
	{ 0xF9D9, 0x9592 }, { 0xF9DA, 0x9650 }, { 0xF9DB, 0x97D3 }, { 0xF9DC, 0x5272 }, { 0xF9DD, 0x8F44 }, { 0xF9DE, 0x51FD }, { 0xF9DF, 0x542B }, { 0xF9E0, 0x54B8 },
	{ 0xF9E1, 0x5563 }, { 0xF9E2, 0x558A }, { 0xF9E3, 0x6ABB }, { 0xF9E4, 0x6DB5 }, { 0xF9E5, 0x7DD8 }, { 0xF9E6, 0x8266 }, { 0xF9E7, 0x929C }, { 0xF9E8, 0x9677 },
	{ 0xF9E9, 0x9E79 }, { 0xF9EA, 0x5408 }, { 0xF9EB, 0x54C8 }, { 0xF9EC, 0x76D2 }, { 0xF9ED, 0x86E4 }, { 0xF9EE, 0x95A4 }, { 0xF9EF, 0x95D4 }, { 0xF9F0, 0x965C },
	{ 0xF9F1, 0x4EA2 }, { 0xF9F2, 0x4F09 }, { 0xF9F3, 0x59EE }, { 0xF9F4, 0x5AE6 }, { 0xF9F5, 0x5DF7 }, { 0xF9F6, 0x6052 }, { 0xF9F7, 0x6297 }, { 0xF9F8, 0x676D },
	{ 0xF9F9, 0x6841 }, { 0xF9FA, 0x6C86 }, { 0xF9FB, 0x6E2F }, { 0xF9FC, 0x7F38 }, { 0xF9FD, 0x809B }, { 0xF9FE, 0x822A }, { 0xFAA1, 0xFA08 }, { 0xFAA2, 0xFA09 },
	{ 0xFAA3, 0x9805 }, { 0xFAA4, 0x4EA5 }, { 0xFAA5, 0x5055 }, { 0xFAA6, 0x54B3 }, { 0xFAA7, 0x5793 }, { 0xFAA8, 0x595A }, { 0xFAA9, 0x5B69 }, { 0xFAAA, 0x5BB3 },
	{ 0xFAAB, 0x61C8 }, { 0xFAAC, 0x6977 }, { 0xFAAD, 0x6D77 }, { 0xFAAE, 0x7023 }, { 0xFAAF, 0x87F9 }, { 0xFAB0, 0x89E3 }, { 0xFAB1, 0x8A72 }, { 0xFAB2, 0x8AE7 },
	{ 0xFAB3, 0x9082 }, { 0xFAB4, 0x99ED }, { 0xFAB5, 0x9AB8 }, { 0xFAB6, 0x52BE }, { 0xFAB7, 0x6838 }, { 0xFAB8, 0x5016 }, { 0xFAB9, 0x5E78 }, { 0xFABA, 0x674F },
	{ 0xFABB, 0x8347 }, { 0xFABC, 0x884C }, { 0xFABD, 0x4EAB }, { 0xFABE, 0x5411 }, { 0xFABF, 0x56AE }, { 0xFAC0, 0x73E6 }, { 0xFAC1, 0x9115 }, { 0xFAC2, 0x97FF },
	{ 0xFAC3, 0x9909 }, { 0xFAC4, 0x9957 }, { 0xFAC5, 0x9999 }, { 0xFAC6, 0x5653 }, { 0xFAC7, 0x589F }, { 0xFAC8, 0x865B }, { 0xFAC9, 0x8A31 }, { 0xFACA, 0x61B2 },
	{ 0xFACB, 0x6AF6 }, { 0xFACC, 0x737B }, { 0xFACD, 0x8ED2 }, { 0xFACE, 0x6B47 }, { 0xFACF, 0x96AA }, { 0xFAD0, 0x9A57 }, { 0xFAD1, 0x5955 }, { 0xFAD2, 0x7200 },
	{ 0xFAD3, 0x8D6B }, { 0xFAD4, 0x9769 }, { 0xFAD5, 0x4FD4 }, { 0xFAD6, 0x5CF4 }, { 0xFAD7, 0x5F26 }, { 0xFAD8, 0x61F8 }, { 0xFAD9, 0x665B }, { 0xFADA, 0x6CEB },
	{ 0xFADB, 0x70AB }, { 0xFADC, 0x7384 }, { 0xFADD, 0x73B9 }, { 0xFADE, 0x73FE }, { 0xFADF, 0x7729 }, { 0xFAE0, 0x774D }, { 0xFAE1, 0x7D43 }, { 0xFAE2, 0x7D62 },
	{ 0xFAE3, 0x7E23 }, { 0xFAE4, 0x8237 }, { 0xFAE5, 0x8852 }, { 0xFAE6, 0xFA0A }, { 0xFAE7, 0x8CE2 }, { 0xFAE8, 0x9249 }, { 0xFAE9, 0x986F }, { 0xFAEA, 0x5B51 },
	{ 0xFAEB, 0x7A74 }, { 0xFAEC, 0x8840 }, { 0xFAED, 0x9801 }, { 0xFAEE, 0x5ACC }, { 0xFAEF, 0x4FE0 }, { 0xFAF0, 0x5354 }, { 0xFAF1, 0x593E }, { 0xFAF2, 0x5CFD },
	{ 0xFAF3, 0x633E }, { 0xFAF4, 0x6D79 }, { 0xFAF5, 0x72F9 }, { 0xFAF6, 0x8105 }, { 0xFAF7, 0x8107 }, { 0xFAF8, 0x83A2 }, { 0xFAF9, 0x92CF }, { 0xFAFA, 0x9830 },
	{ 0xFAFB, 0x4EA8 }, { 0xFAFC, 0x5144 }, { 0xFAFD, 0x5211 }, { 0xFAFE, 0x578B }, { 0xFBA1, 0x5F62 }, { 0xFBA2, 0x6CC2 }, { 0xFBA3, 0x6ECE }, { 0xFBA4, 0x7005 },
	{ 0xFBA5, 0x7050 }, { 0xFBA6, 0x70AF }, { 0xFBA7, 0x7192 }, { 0xFBA8, 0x73E9 }, { 0xFBA9, 0x7469 }, { 0xFBAA, 0x834A }, { 0xFBAB, 0x87A2 }, { 0xFBAC, 0x8861 },
	{ 0xFBAD, 0x9008 }, { 0xFBAE, 0x90A2 }, { 0xFBAF, 0x93A3 }, { 0xFBB0, 0x99A8 }, { 0xFBB1, 0x516E }, { 0xFBB2, 0x5F57 }, { 0xFBB3, 0x60E0 }, { 0xFBB4, 0x6167 },
	{ 0xFBB5, 0x66B3 }, { 0xFBB6, 0x8559 }, { 0xFBB7, 0x8E4A }, { 0xFBB8, 0x91AF }, { 0xFBB9, 0x978B }, { 0xFBBA, 0x4E4E }, { 0xFBBB, 0x4E92 }, { 0xFBBC, 0x547C },
	{ 0xFBBD, 0x58D5 }, { 0xFBBE, 0x58FA }, { 0xFBBF, 0x597D }, { 0xFBC0, 0x5CB5 }, { 0xFBC1, 0x5F27 }, { 0xFBC2, 0x6236 }, { 0xFBC3, 0x6248 }, { 0xFBC4, 0x660A },
	{ 0xFBC5, 0x6667 }, { 0xFBC6, 0x6BEB }, { 0xFBC7, 0x6D69 }, { 0xFBC8, 0x6DCF }, { 0xFBC9, 0x6E56 }, { 0xFBCA, 0x6EF8 }, { 0xFBCB, 0x6F94 }, { 0xFBCC, 0x6FE0 },
	{ 0xFBCD, 0x6FE9 }, { 0xFBCE, 0x705D }, { 0xFBCF, 0x72D0 }, { 0xFBD0, 0x7425 }, { 0xFBD1, 0x745A }, { 0xFBD2, 0x74E0 }, { 0xFBD3, 0x7693 }, { 0xFBD4, 0x795C },
	{ 0xFBD5, 0x7CCA }, { 0xFBD6, 0x7E1E }, { 0xFBD7, 0x80E1 }, { 0xFBD8, 0x82A6 }, { 0xFBD9, 0x846B }, { 0xFBDA, 0x84BF }, { 0xFBDB, 0x864E }, { 0xFBDC, 0x865F },
	{ 0xFBDD, 0x8774 }, { 0xFBDE, 0x8B77 }, { 0xFBDF, 0x8C6A }, { 0xFBE0, 0x93AC }, { 0xFBE1, 0x9800 }, { 0xFBE2, 0x9865 }, { 0xFBE3, 0x60D1 }, { 0xFBE4, 0x6216 },
	{ 0xFBE5, 0x9177 }, { 0xFBE6, 0x5A5A }, { 0xFBE7, 0x660F }, { 0xFBE8, 0x6DF7 }, { 0xFBE9, 0x6E3E }, { 0xFBEA, 0x743F }, { 0xFBEB, 0x9B42 }, { 0xFBEC, 0x5FFD },
	{ 0xFBED, 0x60DA }, { 0xFBEE, 0x7B0F }, { 0xFBEF, 0x54C4 }, { 0xFBF0, 0x5F18 }, { 0xFBF1, 0x6C5E }, { 0xFBF2, 0x6CD3 }, { 0xFBF3, 0x6D2A }, { 0xFBF4, 0x70D8 },
	{ 0xFBF5, 0x7D05 }, { 0xFBF6, 0x8679 }, { 0xFBF7, 0x8A0C }, { 0xFBF8, 0x9D3B }, { 0xFBF9, 0x5316 }, { 0xFBFA, 0x548C }, { 0xFBFB, 0x5B05 }, { 0xFBFC, 0x6A3A },
	{ 0xFBFD, 0x706B }, { 0xFBFE, 0x7575 }, { 0xFCA1, 0x798D }, { 0xFCA2, 0x79BE }, { 0xFCA3, 0x82B1 }, { 0xFCA4, 0x83EF }, { 0xFCA5, 0x8A71 }, { 0xFCA6, 0x8B41 },
	{ 0xFCA7, 0x8CA8 }, { 0xFCA8, 0x9774 }, { 0xFCA9, 0xFA0B }, { 0xFCAA, 0x64F4 }, { 0xFCAB, 0x652B }, { 0xFCAC, 0x78BA }, { 0xFCAD, 0x78BB }, { 0xFCAE, 0x7A6B },
	{ 0xFCAF, 0x4E38 }, { 0xFCB0, 0x559A }, { 0xFCB1, 0x5950 }, { 0xFCB2, 0x5BA6 }, { 0xFCB3, 0x5E7B }, { 0xFCB4, 0x60A3 }, { 0xFCB5, 0x63DB }, { 0xFCB6, 0x6B61 },
	{ 0xFCB7, 0x6665 }, { 0xFCB8, 0x6853 }, { 0xFCB9, 0x6E19 }, { 0xFCBA, 0x7165 }, { 0xFCBB, 0x74B0 }, { 0xFCBC, 0x7D08 }, { 0xFCBD, 0x9084 }, { 0xFCBE, 0x9A69 },
	{ 0xFCBF, 0x9C25 }, { 0xFCC0, 0x6D3B }, { 0xFCC1, 0x6ED1 }, { 0xFCC2, 0x733E }, { 0xFCC3, 0x8C41 }, { 0xFCC4, 0x95CA }, { 0xFCC5, 0x51F0 }, { 0xFCC6, 0x5E4C },
	{ 0xFCC7, 0x5FA8 }, { 0xFCC8, 0x604D }, { 0xFCC9, 0x60F6 }, { 0xFCCA, 0x6130 }, { 0xFCCB, 0x614C }, { 0xFCCC, 0x6643 }, { 0xFCCD, 0x6644 }, { 0xFCCE, 0x69A5 },
	{ 0xFCCF, 0x6CC1 }, { 0xFCD0, 0x6E5F }, { 0xFCD1, 0x6EC9 }, { 0xFCD2, 0x6F62 }, { 0xFCD3, 0x714C }, { 0xFCD4, 0x749C }, { 0xFCD5, 0x7687 }, { 0xFCD6, 0x7BC1 },
	{ 0xFCD7, 0x7C27 }, { 0xFCD8, 0x8352 }, { 0xFCD9, 0x8757 }, { 0xFCDA, 0x9051 }, { 0xFCDB, 0x968D }, { 0xFCDC, 0x9EC3 }, { 0xFCDD, 0x532F }, { 0xFCDE, 0x56DE },
	{ 0xFCDF, 0x5EFB }, { 0xFCE0, 0x5F8A }, { 0xFCE1, 0x6062 }, { 0xFCE2, 0x6094 }, { 0xFCE3, 0x61F7 }, { 0xFCE4, 0x6666 }, { 0xFCE5, 0x6703 }, { 0xFCE6, 0x6A9C },
	{ 0xFCE7, 0x6DEE }, { 0xFCE8, 0x6FAE }, { 0xFCE9, 0x7070 }, { 0xFCEA, 0x736A }, { 0xFCEB, 0x7E6A }, { 0xFCEC, 0x81BE }, { 0xFCED, 0x8334 }, { 0xFCEE, 0x86D4 },
	{ 0xFCEF, 0x8AA8 }, { 0xFCF0, 0x8CC4 }, { 0xFCF1, 0x5283 }, { 0xFCF2, 0x7372 }, { 0xFCF3, 0x5B96 }, { 0xFCF4, 0x6A6B }, { 0xFCF5, 0x9404 }, { 0xFCF6, 0x54EE },
	{ 0xFCF7, 0x5686 }, { 0xFCF8, 0x5B5D }, { 0xFCF9, 0x6548 }, { 0xFCFA, 0x6585 }, { 0xFCFB, 0x66C9 }, { 0xFCFC, 0x689F }, { 0xFCFD, 0x6D8D }, { 0xFCFE, 0x6DC6 },
	{ 0xFDA1, 0x723B }, { 0xFDA2, 0x80B4 }, { 0xFDA3, 0x9175 }, { 0xFDA4, 0x9A4D }, { 0xFDA5, 0x4FAF }, { 0xFDA6, 0x5019 }, { 0xFDA7, 0x539A }, { 0xFDA8, 0x540E },
	{ 0xFDA9, 0x543C }, { 0xFDAA, 0x5589 }, { 0xFDAB, 0x55C5 }, { 0xFDAC, 0x5E3F }, { 0xFDAD, 0x5F8C }, { 0xFDAE, 0x673D }, { 0xFDAF, 0x7166 }, { 0xFDB0, 0x73DD },
	{ 0xFDB1, 0x9005 }, { 0xFDB2, 0x52DB }, { 0xFDB3, 0x52F3 }, { 0xFDB4, 0x5864 }, { 0xFDB5, 0x58CE }, { 0xFDB6, 0x7104 }, { 0xFDB7, 0x718F }, { 0xFDB8, 0x71FB },
	{ 0xFDB9, 0x85B0 }, { 0xFDBA, 0x8A13 }, { 0xFDBB, 0x6688 }, { 0xFDBC, 0x85A8 }, { 0xFDBD, 0x55A7 }, { 0xFDBE, 0x6684 }, { 0xFDBF, 0x714A }, { 0xFDC0, 0x8431 },
	{ 0xFDC1, 0x5349 }, { 0xFDC2, 0x5599 }, { 0xFDC3, 0x6BC1 }, { 0xFDC4, 0x5F59 }, { 0xFDC5, 0x5FBD }, { 0xFDC6, 0x63EE }, { 0xFDC7, 0x6689 }, { 0xFDC8, 0x7147 },
	{ 0xFDC9, 0x8AF1 }, { 0xFDCA, 0x8F1D }, { 0xFDCB, 0x9EBE }, { 0xFDCC, 0x4F11 }, { 0xFDCD, 0x643A }, { 0xFDCE, 0x70CB }, { 0xFDCF, 0x7566 }, { 0xFDD0, 0x8667 },
	{ 0xFDD1, 0x6064 }, { 0xFDD2, 0x8B4E }, { 0xFDD3, 0x9DF8 }, { 0xFDD4, 0x5147 }, { 0xFDD5, 0x51F6 }, { 0xFDD6, 0x5308 }, { 0xFDD7, 0x6D36 }, { 0xFDD8, 0x80F8 },
	{ 0xFDD9, 0x9ED1 }, { 0xFDDA, 0x6615 }, { 0xFDDB, 0x6B23 }, { 0xFDDC, 0x7098 }, { 0xFDDD, 0x75D5 }, { 0xFDDE, 0x5403 }, { 0xFDDF, 0x5C79 }, { 0xFDE0, 0x7D07 },
	{ 0xFDE1, 0x8A16 }, { 0xFDE2, 0x6B20 }, { 0xFDE3, 0x6B3D }, { 0xFDE4, 0x6B46 }, { 0xFDE5, 0x5438 }, { 0xFDE6, 0x6070 }, { 0xFDE7, 0x6D3D }, { 0xFDE8, 0x7FD5 },
	{ 0xFDE9, 0x8208 }, { 0xFDEA, 0x50D6 }, { 0xFDEB, 0x51DE }, { 0xFDEC, 0x559C }, { 0xFDED, 0x566B }, { 0xFDEE, 0x56CD }, { 0xFDEF, 0x59EC }, { 0xFDF0, 0x5B09 },
	{ 0xFDF1, 0x5E0C }, { 0xFDF2, 0x6199 }, { 0xFDF3, 0x6198 }, { 0xFDF4, 0x6231 }, { 0xFDF5, 0x665E }, { 0xFDF6, 0x66E6 }, { 0xFDF7, 0x7199 }, { 0xFDF8, 0x71B9 },
	{ 0xFDF9, 0x71BA }, { 0xFDFA, 0x72A7 }, { 0xFDFB, 0x79A7 }, { 0xFDFC, 0x7A00 }, { 0xFDFD, 0x7FB2 }, { 0xFDFE, 0x8A70 },
};


const DoubleByteEncoding::Mapping MacKoreanEncoding::_reverseMappingTable[] = {
	{ 0x0020, 0x0020 }, { 0x0021, 0x0021 }, { 0x0022, 0x0022 }, { 0x0023, 0x0023 }, { 0x0024, 0x0024 }, { 0x0025, 0x0025 }, { 0x0026, 0x0026 }, { 0x0027, 0x0027 },
	{ 0x0028, 0x0028 }, { 0x0029, 0x0029 }, { 0x002A, 0x002A }, { 0x002B, 0x002B }, { 0x002C, 0x002C }, { 0x002D, 0x002D }, { 0x002E, 0x002E }, { 0x002F, 0x002F },
	{ 0x0030, 0x0030 }, { 0x0031, 0x0031 }, { 0x0032, 0x0032 }, { 0x0033, 0x0033 }, { 0x0034, 0x0034 }, { 0x0035, 0x0035 }, { 0x0036, 0x0036 }, { 0x0037, 0x0037 },
	{ 0x0038, 0x0038 }, { 0x0039, 0x0039 }, { 0x003A, 0x003A }, { 0x003B, 0x003B }, { 0x003C, 0x003C }, { 0x003D, 0x003D }, { 0x003E, 0x003E }, { 0x003F, 0x003F },
	{ 0x0040, 0x0040 }, { 0x0041, 0x0041 }, { 0x0042, 0x0042 }, { 0x0043, 0x0043 }, { 0x0044, 0x0044 }, { 0x0045, 0x0045 }, { 0x0046, 0x0046 }, { 0x0047, 0x0047 },
	{ 0x0048, 0x0048 }, { 0x0049, 0x0049 }, { 0x004A, 0x004A }, { 0x004B, 0x004B }, { 0x004C, 0x004C }, { 0x004D, 0x004D }, { 0x004E, 0x004E }, { 0x004F, 0x004F },
	{ 0x0050, 0x0050 }, { 0x0051, 0x0051 }, { 0x0052, 0x0052 }, { 0x0053, 0x0053 }, { 0x0054, 0x0054 }, { 0x0055, 0x0055 }, { 0x0056, 0x0056 }, { 0x0057, 0x0057 },
	{ 0x0058, 0x0058 }, { 0x0059, 0x0059 }, { 0x005A, 0x005A }, { 0x005B, 0x005B }, { 0x005C, 0x005C }, { 0x005D, 0x005D }, { 0x005E, 0x005E }, { 0x005F, 0x005F },
	{ 0x0060, 0x0060 }, { 0x0061, 0x0061 }, { 0x0062, 0x0062 }, { 0x0063, 0x0063 }, { 0x0064, 0x0064 }, { 0x0065, 0x0065 }, { 0x0066, 0x0066 }, { 0x0067, 0x0067 },
	{ 0x0068, 0x0068 }, { 0x0069, 0x0069 }, { 0x006A, 0x006A }, { 0x006B, 0x006B }, { 0x006C, 0x006C }, { 0x006D, 0x006D }, { 0x006E, 0x006E }, { 0x006F, 0x006F },
	{ 0x0070, 0x0070 }, { 0x0071, 0x0071 }, { 0x0072, 0x0072 }, { 0x0073, 0x0073 }, { 0x0074, 0x0074 }, { 0x0075, 0x0075 }, { 0x0076, 0x0076 }, { 0x0077, 0x0077 },
	{ 0x0078, 0x0078 }, { 0x0079, 0x0079 }, { 0x007A, 0x007A }, { 0x007B, 0x007B }, { 0x007C, 0x007C }, { 0x007D, 0x007D }, { 0x007E, 0x007E }, { 0x0085, 0x0085 },
	{ 0x0086, 0x0086 }, { 0x0087, 0x0087 }, { 0x0088, 0x0088 }, { 0x0089, 0x0089 }, { 0x008A, 0x008A }, { 0x008B, 0x008B }, { 0x008C, 0x008C }, { 0x008D, 0x008D },
	{ 0x008E, 0x008E }, { 0x008F, 0x008F }, { 0x0090, 0x0090 }, { 0x0091, 0x0091 }, { 0x0092, 0x0092 }, { 0x0093, 0x0093 }, { 0x0094, 0x0094 }, { 0x0095, 0x0095 },
	{ 0x0096, 0x0096 }, { 0x0097, 0x0097 }, { 0x0098, 0x0098 }, { 0x0099, 0x0099 }, { 0x009A, 0x009A }, { 0x009B, 0x009B }, { 0x009C, 0x009C }, { 0x009D, 0x009D },
	{ 0x009E, 0x009E }, { 0x009F, 0x009F }, { 0x00A0, 0x0080 }, { 0x00A1, 0xA2AE }, { 0x00A2, 0xA1CB }, { 0x00A3, 0xA1CC }, { 0x00A4, 0xA2B4 }, { 0x00A5, 0xA1CD },
	{ 0x00A7, 0xA1D7 }, { 0x00A8, 0xA1A7 }, { 0x00A9, 0x0083 }, { 0x00AA, 0xA8A3 }, { 0x00AB, 0xA65C }, { 0x00AC, 0xA1FE }, { 0x00B0, 0xA1C6 }, { 0x00B1, 0xA1BE },
	{ 0x00B2, 0xA9F7 }, { 0x00B3, 0xA9F8 }, { 0x00B4, 0xA2A5 }, { 0x00B6, 0xA2D2 }, { 0x00B7, 0xA1A4 }, { 0x00B8, 0xA2AC }, { 0x00B9, 0xA9F6 }, { 0x00BA, 0xA8AC },
	{ 0x00BB, 0xA65D }, { 0x00BC, 0xA8F9 }, { 0x00BD, 0xA8F6 }, { 0x00BE, 0xA8FA }, { 0x00BF, 0xA2AF }, { 0x00C6, 0xA8A1 }, { 0x00D0, 0xA8A2 }, { 0x00D7, 0xA1BF },
	{ 0x00D8, 0xA8AA }, { 0x00DE, 0xA8AD }, { 0x00DF, 0xA9AC }, { 0x00E6, 0xA9A1 }, { 0x00F0, 0xA9A3 }, { 0x00F7, 0xA1C0 }, { 0x00F8, 0xA9AA }, { 0x00FE, 0xA9AD },
	{ 0x0111, 0xA9A2 }, { 0x0126, 0xA8A4 }, { 0x0127, 0xA9A4 }, { 0x0131, 0xA9A5 }, { 0x0132, 0xA8A6 }, { 0x0133, 0xA9A6 }, { 0x0138, 0xA9A7 }, { 0x013F, 0xA8A8 },
	{ 0x0140, 0xA9A8 }, { 0x0141, 0xA8A9 }, { 0x0142, 0xA9A9 }, { 0x0149, 0xA9B0 }, { 0x014A, 0xA8AF }, { 0x014B, 0xA9AF }, { 0x0152, 0xA8AB }, { 0x0153, 0xA9AB },
	{ 0x0166, 0xA8AE }, { 0x0167, 0xA9AE }, { 0x02BC, 0xA198 }, { 0x02C7, 0xA2A7 }, { 0x02D0, 0xA2B0 }, { 0x02D8, 0xA2A8 }, { 0x02D9, 0xA2AB }, { 0x02DA, 0xA2AA },
	{ 0x02DB, 0xA2AD }, { 0x02DC, 0xA2A6 }, { 0x02DD, 0xA2A9 }, { 0x0391, 0xA5C1 }, { 0x0392, 0xA5C2 }, { 0x0393, 0xA5C3 }, { 0x0394, 0xA5C4 }, { 0x0395, 0xA5C5 },
	{ 0x0396, 0xA5C6 }, { 0x0397, 0xA5C7 }, { 0x0398, 0xA5C8 }, { 0x0399, 0xA5C9 }, { 0x039A, 0xA5CA }, { 0x039B, 0xA5CB }, { 0x039C, 0xA5CC }, { 0x039D, 0xA5CD },
	{ 0x039E, 0xA5CE }, { 0x039F, 0xA5CF }, { 0x03A0, 0xA5D0 }, { 0x03A1, 0xA5D1 }, { 0x03A3, 0xA5D2 }, { 0x03A4, 0xA5D3 }, { 0x03A5, 0xA5D4 }, { 0x03A6, 0xA5D5 },
	{ 0x03A7, 0xA5D6 }, { 0x03A8, 0xA5D7 }, { 0x03A9, 0xA5D8 }, { 0x03B1, 0xA5E1 }, { 0x03B2, 0xA5E2 }, { 0x03B3, 0xA5E3 }, { 0x03B4, 0xA5E4 }, { 0x03B5, 0xA5E5 },
	{ 0x03B6, 0xA5E6 }, { 0x03B7, 0xA5E7 }, { 0x03B8, 0xA5E8 }, { 0x03B9, 0xA5E9 }, { 0x03BA, 0xA5EA }, { 0x03BB, 0xA5EB }, { 0x03BC, 0xA5EC }, { 0x03BD, 0xA5ED },
	{ 0x03BE, 0xA5EE }, { 0x03BF, 0xA5EF }, { 0x03C0, 0xA5F0 }, { 0x03C1, 0xA5F1 }, { 0x03C3, 0xA5F2 }, { 0x03C4, 0xA5F3 }, { 0x03C5, 0xA5F4 }, { 0x03C6, 0xA5F5 },
	{ 0x03C7, 0xA5F6 }, { 0x03C8, 0xA5F7 }, { 0x03C9, 0xA5F8 }, { 0x03D5, 0xA76A }, { 0x0401, 0xACA7 }, { 0x0410, 0xACA1 }, { 0x0411, 0xACA2 }, { 0x0412, 0xACA3 },
	{ 0x0413, 0xACA4 }, { 0x0414, 0xACA5 }, { 0x0415, 0xACA6 }, { 0x0416, 0xACA8 }, { 0x0417, 0xACA9 }, { 0x0418, 0xACAA }, { 0x0419, 0xACAB }, { 0x041A, 0xACAC },
	{ 0x041B, 0xACAD }, { 0x041C, 0xACAE }, { 0x041D, 0xACAF }, { 0x041E, 0xACB0 }, { 0x041F, 0xACB1 }, { 0x0420, 0xACB2 }, { 0x0421, 0xACB3 }, { 0x0422, 0xACB4 },
	{ 0x0423, 0xACB5 }, { 0x0424, 0xACB6 }, { 0x0425, 0xACB7 }, { 0x0426, 0xACB8 }, { 0x0427, 0xACB9 }, { 0x0428, 0xACBA }, { 0x0429, 0xACBB }, { 0x042A, 0xACBC },
	{ 0x042B, 0xACBD }, { 0x042C, 0xACBE }, { 0x042D, 0xACBF }, { 0x042E, 0xACC0 }, { 0x042F, 0xACC1 }, { 0x0430, 0xACD1 }, { 0x0431, 0xACD2 }, { 0x0432, 0xACD3 },
	{ 0x0433, 0xACD4 }, { 0x0434, 0xACD5 }, { 0x0435, 0xACD6 }, { 0x0436, 0xACD8 }, { 0x0437, 0xACD9 }, { 0x0438, 0xACDA }, { 0x0439, 0xACDB }, { 0x043A, 0xACDC },
	{ 0x043B, 0xACDD }, { 0x043C, 0xACDE }, { 0x043D, 0xACDF }, { 0x043E, 0xACE0 }, { 0x043F, 0xACE1 }, { 0x0440, 0xACE2 }, { 0x0441, 0xACE3 }, { 0x0442, 0xACE4 },
	{ 0x0443, 0xACE5 }, { 0x0444, 0xACE6 }, { 0x0445, 0xACE7 }, { 0x0446, 0xACE8 }, { 0x0447, 0xACE9 }, { 0x0448, 0xACEA }, { 0x0449, 0xACEB }, { 0x044A, 0xACEC },
	{ 0x044B, 0xACED }, { 0x044C, 0xACEE }, { 0x044D, 0xACEF }, { 0x044E, 0xACF0 }, { 0x044F, 0xACF1 }, { 0x0451, 0xACD7 }, { 0x2013, 0xA1A9 }, { 0x2014, 0xA1AA },
	{ 0x2016, 0xA1AB }, { 0x2018, 0xA1AE }, { 0x2019, 0xA1AF }, { 0x201B, 0xA170 }, { 0x201C, 0xA1B0 }, { 0x201D, 0xA1B1 }, { 0x201F, 0xA16F }, { 0x2020, 0xA2D3 },
	{ 0x2021, 0xA2D4 }, { 0x2022, 0xA655 }, { 0x2025, 0xA1A5 }, { 0x2026, 0xA1A6 }, { 0x2030, 0xA2B6 }, { 0x2032, 0xA1C7 }, { 0x2033, 0xA1C8 }, { 0x2034, 0xA5DE },
	{ 0x2035, 0xADAD }, { 0x2036, 0xADAB }, { 0x2039, 0xA65A }, { 0x203A, 0xA65B }, { 0x203B, 0xA1D8 }, { 0x203C, 0xA784 }, { 0x203E, 0xA3FE }, { 0x2042, 0xA64D },
	{ 0x2047, 0xA787 }, { 0x2049, 0xA785 }, { 0x204C, 0xA196 }, { 0x204D, 0xA197 }, { 0x204E, 0xA64E }, { 0x2051, 0xA16D }, { 0x2074, 0xA9F9 }, { 0x207A, 0xA171 },
	{ 0x207B, 0xA172 }, { 0x207C, 0xA17A }, { 0x207D, 0xA17C }, { 0x207E, 0xA17D }, { 0x207F, 0xA9FA }, { 0x2081, 0xA9FB }, { 0x2082, 0xA9FC }, { 0x2083, 0xA9FD },
	{ 0x2084, 0xA9FE }, { 0x20A9, 0x0081 }, { 0x2103, 0xA1C9 }, { 0x2109, 0xA2B5 }, { 0x2113, 0xA7A4 }, { 0x2116, 0xA2E0 }, { 0x2121, 0xA2E5 }, { 0x2122, 0xA2E2 },
	{ 0x2126, 0xA7D9 }, { 0x212B, 0xA1CA }, { 0x2153, 0xA8F7 }, { 0x2154, 0xA8F8 }, { 0x215B, 0xA8FB }, { 0x215C, 0xA8FC }, { 0x215D, 0xA8FD }, { 0x215E, 0xA8FE },
	{ 0x2160, 0xA5B0 }, { 0x2161, 0xA5B1 }, { 0x2162, 0xA5B2 }, { 0x2163, 0xA5B3 }, { 0x2164, 0xA5B4 }, { 0x2165, 0xA5B5 }, { 0x2166, 0xA5B6 }, { 0x2167, 0xA5B7 },
	{ 0x2168, 0xA5B8 }, { 0x2169, 0xA5B9 }, { 0x2170, 0xA5A1 }, { 0x2171, 0xA5A2 }, { 0x2172, 0xA5A3 }, { 0x2173, 0xA5A4 }, { 0x2174, 0xA5A5 }, { 0x2175, 0xA5A6 },
	{ 0x2176, 0xA5A7 }, { 0x2177, 0xA5A8 }, { 0x2178, 0xA5A9 }, { 0x2179, 0xA5AA }, { 0x2190, 0xA1E7 }, { 0x2191, 0xA1E8 }, { 0x2192, 0xA1E6 }, { 0x2193, 0xA1E9 },
	{ 0x2194, 0xA1EA }, { 0x2195, 0xA2D5 }, { 0x2196, 0xA2D8 }, { 0x2197, 0xA2D6 }, { 0x2198, 0xA2D9 }, { 0x2199, 0xA2D7 }, { 0x219C, 0xAC53 }, { 0x219D, 0xAC52 },
	{ 0x21B0, 0xA882 }, { 0x21B1, 0xA87C }, { 0x21B2, 0xA87B }, { 0x21B3, 0xA883 }, { 0x21B4, 0xA881 }, { 0x21B6, 0xAC50 }, { 0x21B7, 0xAC51 }, { 0x21BC, 0xA892 },
	{ 0x21C0, 0xA893 }, { 0x21C4, 0xA89E }, { 0x21C5, 0xA89F }, { 0x21CD, 0xA84B }, { 0x21CF, 0xA84A }, { 0x21D0, 0xA849 }, { 0x21D2, 0xA2A1 }, { 0x21D4, 0xA2A2 },
	{ 0x21E0, 0xAC6A }, { 0x21E1, 0xAC6C }, { 0x21E2, 0xAC6B }, { 0x21E3, 0xAC6D }, { 0x21E6, 0xAC72 }, { 0x21E7, 0xAC74 }, { 0x21E8, 0xAC73 }, { 0x21E9, 0xAC75 },
	{ 0x21F0, 0xAC41 }, { 0x2200, 0xA2A3 }, { 0x2202, 0xA1D3 }, { 0x2203, 0xA2A4 }, { 0x2206, 0xA751 }, { 0x2207, 0xA1D4 }, { 0x2208, 0xA1F4 }, { 0x2209, 0xA773 },
	{ 0x220B, 0xA1F5 }, { 0x220C, 0xA774 }, { 0x220F, 0xA2B3 }, { 0x2211, 0xA2B2 }, { 0x2213, 0xA75C }, { 0x221A, 0xA1EE }, { 0x221D, 0xA1F0 }, { 0x221E, 0xA1C4 },
	{ 0x221F, 0xA753 }, { 0x2220, 0xA1D0 }, { 0x2222, 0xA768 }, { 0x2225, 0xA755 }, { 0x2226, 0xA756 }, { 0x2227, 0xA1FC }, { 0x2228, 0xA1FD }, { 0x2229, 0xA1FB },
	{ 0x222A, 0xA1FA }, { 0x222B, 0xA1F2 }, { 0x222C, 0xA1F3 }, { 0x222E, 0xA2B1 }, { 0x2234, 0xA1C5 }, { 0x2235, 0xA1F1 }, { 0x2237, 0xA2FE }, { 0x223D, 0xA1EF },
	{ 0x2243, 0xA49A }, { 0x2245, 0xA499 }, { 0x2248, 0xA49B }, { 0x2250, 0xA769 }, { 0x2251, 0xA759 }, { 0x2252, 0xA1D6 }, { 0x2253, 0xA758 }, { 0x225A, 0xA777 },
	{ 0x2260, 0xA1C1 }, { 0x2261, 0xA1D5 }, { 0x2262, 0xA764 }, { 0x2264, 0xA1C2 }, { 0x2265, 0xA1C3 }, { 0x2266, 0xA75A }, { 0x2267, 0xA75B }, { 0x226A, 0xA1EC },
	{ 0x226B, 0xA1ED }, { 0x226E, 0xA76F }, { 0x226F, 0xA770 }, { 0x2270, 0xA487 }, { 0x2271, 0xA488 }, { 0x2272, 0xA489 }, { 0x2273, 0xA48A }, { 0x2276, 0xA48F },
	{ 0x2277, 0xA490 }, { 0x2279, 0xA491 }, { 0x227A, 0xA481 }, { 0x227B, 0xA482 }, { 0x2280, 0xA485 }, { 0x2281, 0xA486 }, { 0x2282, 0xA1F8 }, { 0x2283, 0xA1F9 },
	{ 0x2284, 0xA772 }, { 0x2285, 0xA771 }, { 0x2286, 0xA1F6 }, { 0x2287, 0xA1F7 }, { 0x2295, 0xA75D }, { 0x2296, 0xA75E }, { 0x2297, 0xA75F }, { 0x22A3, 0xA76C },
	{ 0x22A4, 0xA49D }, { 0x22A5, 0xA1D1 }, { 0x22BB, 0xA775 }, { 0x22BC, 0xA776 }, { 0x22CE, 0xA483 }, { 0x22CF, 0xA484 }, { 0x22DA, 0xA492 }, { 0x22DB, 0xA493 },
	{ 0x22EE, 0xA2FD }, { 0x2306, 0xA778 }, { 0x2312, 0xA1D2 }, { 0x2314, 0xA761 }, { 0x2460, 0xA8E7 }, { 0x2461, 0xA8E8 }, { 0x2462, 0xA8E9 }, { 0x2463, 0xA8EA },
	{ 0x2464, 0xA8EB }, { 0x2465, 0xA8EC }, { 0x2466, 0xA8ED }, { 0x2467, 0xA8EE }, { 0x2468, 0xA8EF }, { 0x2469, 0xA8F0 }, { 0x246A, 0xA8F1 }, { 0x246B, 0xA8F2 },
	{ 0x246C, 0xA8F3 }, { 0x246D, 0xA8F4 }, { 0x246E, 0xA8F5 }, { 0x246F, 0xA7F0 }, { 0x2470, 0xA7F1 }, { 0x2471, 0xA7F2 }, { 0x2472, 0xA7F3 }, { 0x2473, 0xA7F4 },
	{ 0x2474, 0xA9E7 }, { 0x2475, 0xA9E8 }, { 0x2476, 0xA9E9 }, { 0x2477, 0xA9EA }, { 0x2478, 0xA9EB }, { 0x2479, 0xA9EC }, { 0x247A, 0xA9ED }, { 0x247B, 0xA9EE },
	{ 0x247C, 0xA9EF }, { 0x247D, 0xA9F0 }, { 0x247E, 0xA9F1 }, { 0x247F, 0xA9F2 }, { 0x2480, 0xA9F3 }, { 0x2481, 0xA9F4 }, { 0x2482, 0xA9F5 }, { 0x2483, 0xAAF4 },
	{ 0x2484, 0xAAF5 }, { 0x2485, 0xAAF6 }, { 0x2486, 0xAAF7 }, { 0x2487, 0xAAF8 }, { 0x249C, 0xA9CD }, { 0x249D, 0xA9CE }, { 0x249E, 0xA9CF }, { 0x249F, 0xA9D0 },
	{ 0x24A0, 0xA9D1 }, { 0x24A1, 0xA9D2 }, { 0x24A2, 0xA9D3 }, { 0x24A3, 0xA9D4 }, { 0x24A4, 0xA9D5 }, { 0x24A5, 0xA9D6 }, { 0x24A6, 0xA9D7 }, { 0x24A7, 0xA9D8 },
	{ 0x24A8, 0xA9D9 }, { 0x24A9, 0xA9DA }, { 0x24AA, 0xA9DB }, { 0x24AB, 0xA9DC }, { 0x24AC, 0xA9DD }, { 0x24AD, 0xA9DE }, { 0x24AE, 0xA9DF }, { 0x24AF, 0xA9E0 },
	{ 0x24B0, 0xA9E1 }, { 0x24B1, 0xA9E2 }, { 0x24B2, 0xA9E3 }, { 0x24B3, 0xA9E4 }, { 0x24B4, 0xA9E5 }, { 0x24B5, 0xA9E6 }, { 0x24B6, 0xA386 }, { 0x24B7, 0xA387 },
	{ 0x24B8, 0xA388 }, { 0x24B9, 0xA389 }, { 0x24BA, 0xA38A }, { 0x24BB, 0xA38B }, { 0x24BC, 0xA38C }, { 0x24BD, 0xA38D }, { 0x24BE, 0xA38E }, { 0x24BF, 0xA38F },
	{ 0x24C0, 0xA390 }, { 0x24C1, 0xA391 }, { 0x24C2, 0xA392 }, { 0x24C3, 0xA393 }, { 0x24C4, 0xA394 }, { 0x24C5, 0xA395 }, { 0x24C6, 0xA396 }, { 0x24C7, 0xA397 },
	{ 0x24C8, 0xA398 }, { 0x24C9, 0xA399 }, { 0x24CA, 0xA39A }, { 0x24CB, 0xA39B }, { 0x24CC, 0xA39C }, { 0x24CD, 0xA39D }, { 0x24CE, 0xA39E }, { 0x24CF, 0xA39F },
	{ 0x24D0, 0xA8CD }, { 0x24D1, 0xA8CE }, { 0x24D2, 0xA8CF }, { 0x24D3, 0xA8D0 }, { 0x24D4, 0xA8D1 }, { 0x24D5, 0xA8D2 }, { 0x24D6, 0xA8D3 }, { 0x24D7, 0xA8D4 },
	{ 0x24D8, 0xA8D5 }, { 0x24D9, 0xA8D6 }, { 0x24DA, 0xA8D7 }, { 0x24DB, 0xA8D8 }, { 0x24DC, 0xA8D9 }, { 0x24DD, 0xA8DA }, { 0x24DE, 0xA8DB }, { 0x24DF, 0xA8DC },
	{ 0x24E0, 0xA8DD }, { 0x24E1, 0xA8DE }, { 0x24E2, 0xA8DF }, { 0x24E3, 0xA8E0 }, { 0x24E4, 0xA8E1 }, { 0x24E5, 0xA8E2 }, { 0x24E6, 0xA8E3 }, { 0x24E7, 0xA8E4 },
	{ 0x24E8, 0xA8E5 }, { 0x24E9, 0xA8E6 }, { 0x24EB, 0xA6EF }, { 0x24EC, 0xA6F0 }, { 0x24ED, 0xA6F1 }, { 0x24EE, 0xA6F2 }, { 0x24EF, 0xA6F3 }, { 0x24F0, 0xA6F4 },
	{ 0x24F1, 0xA6F5 }, { 0x24F2, 0xA6F6 }, { 0x24F3, 0xA6F7 }, { 0x24F4, 0xA6F8 }, { 0x2500, 0xA6A1 }, { 0x2501, 0xA6AC }, { 0x2502, 0xA6A2 }, { 0x2503, 0xA6AD },
	{ 0x250C, 0xA6A3 }, { 0x250D, 0xA6C8 }, { 0x250E, 0xA6C7 }, { 0x250F, 0xA6AE }, { 0x2510, 0xA6A4 }, { 0x2511, 0xA6C2 }, { 0x2512, 0xA6C1 }, { 0x2513, 0xA6AF },
	{ 0x2514, 0xA6A6 }, { 0x2515, 0xA6C6 }, { 0x2516, 0xA6C5 }, { 0x2517, 0xA6B1 }, { 0x2518, 0xA6A5 }, { 0x2519, 0xA6C4 }, { 0x251A, 0xA6C3 }, { 0x251B, 0xA6B0 },
	{ 0x251C, 0xA6A7 }, { 0x251D, 0xA6BC }, { 0x251E, 0xA6C9 }, { 0x251F, 0xA6CA }, { 0x2520, 0xA6B7 }, { 0x2521, 0xA6CB }, { 0x2522, 0xA6CC }, { 0x2523, 0xA6B2 },
	{ 0x2524, 0xA6A9 }, { 0x2525, 0xA6BE }, { 0x2526, 0xA6CD }, { 0x2527, 0xA6CE }, { 0x2528, 0xA6B9 }, { 0x2529, 0xA6CF }, { 0x252A, 0xA6D0 }, { 0x252B, 0xA6B4 },
	{ 0x252C, 0xA6A8 }, { 0x252D, 0xA6D1 }, { 0x252E, 0xA6D2 }, { 0x252F, 0xA6B8 }, { 0x2530, 0xA6BD }, { 0x2531, 0xA6D3 }, { 0x2532, 0xA6D4 }, { 0x2533, 0xA6B3 },
	{ 0x2534, 0xA6AA }, { 0x2535, 0xA6D5 }, { 0x2536, 0xA6D6 }, { 0x2537, 0xA6BA }, { 0x2538, 0xA6BF }, { 0x2539, 0xA6D7 }, { 0x253A, 0xA6D8 }, { 0x253B, 0xA6B5 },
	{ 0x253C, 0xA6AB }, { 0x253D, 0xA6D9 }, { 0x253E, 0xA6DA }, { 0x253F, 0xA6BB }, { 0x2540, 0xA6DB }, { 0x2541, 0xA6DC }, { 0x2542, 0xA6C0 }, { 0x2543, 0xA6DD },
	{ 0x2544, 0xA6DE }, { 0x2545, 0xA6DF }, { 0x2546, 0xA6E0 }, { 0x2547, 0xA6E1 }, { 0x2548, 0xA6E2 }, { 0x2549, 0xA6E3 }, { 0x254A, 0xA6E4 }, { 0x254B, 0xA6B6 },
	{ 0x2588, 0xA78F }, { 0x2592, 0xA2C6 }, { 0x25A0, 0xA1E1 }, { 0x25A1, 0xA1E0 }, { 0x25A2, 0xA678 }, { 0x25A3, 0xA2C3 }, { 0x25A4, 0xA2C7 }, { 0x25A5, 0xA2C8 },
	{ 0x25A6, 0xA2CB }, { 0x25A7, 0xA2CA }, { 0x25A8, 0xA2C9 }, { 0x25A9, 0xA2CC }, { 0x25AD, 0xA74A }, { 0x25B1, 0xA766 }, { 0x25B2, 0xA1E3 }, { 0x25B3, 0xA1E2 },
	{ 0x25B5, 0xA795 }, { 0x25B6, 0xA2BA }, { 0x25B7, 0xA2B9 }, { 0x25B9, 0xA796 }, { 0x25BC, 0xA1E5 }, { 0x25BD, 0xA1E4 }, { 0x25BF, 0xA794 }, { 0x25C0, 0xA2B8 },
	{ 0x25C1, 0xA2B7 }, { 0x25C3, 0xA797 }, { 0x25C6, 0xA1DF }, { 0x25C7, 0xA1DE }, { 0x25C8, 0xA2C2 }, { 0x25C9, 0xA2C1 }, { 0x25CA, 0xA79C }, { 0x25CB, 0xA1DB },
	{ 0x25CC, 0xA675 }, { 0x25CD, 0xA684 }, { 0x25CE, 0xA1DD }, { 0x25CF, 0xA1DC }, { 0x25D0, 0xA2C4 }, { 0x25D1, 0xA2C5 }, { 0x25E6, 0xA790 }, { 0x25EF, 0xA66F },
	{ 0x25FB, 0xA746 }, { 0x25FC, 0xA79A }, { 0x2605, 0xA1DA }, { 0x2606, 0xA1D9 }, { 0x260E, 0xA2CF }, { 0x260F, 0xA2CE }, { 0x2610, 0xA677 }, { 0x261C, 0xA2D0 },
	{ 0x261D, 0xAC8D }, { 0x261E, 0xA2D1 }, { 0x261F, 0xAC8E }, { 0x262F, 0xA693 }, { 0x2640, 0xA1CF }, { 0x2642, 0xA1CE }, { 0x2660, 0xA2BC }, { 0x2661, 0xA2BD },
	{ 0x2663, 0xA2C0 }, { 0x2664, 0xA2BB }, { 0x2665, 0xA2BE }, { 0x2666, 0xA798 }, { 0x2667, 0xA2BF }, { 0x2668, 0xA2CD }, { 0x2669, 0xA2DB }, { 0x266A, 0xA2DC },
	{ 0x266C, 0xA2DD }, { 0x266D, 0xA2DA }, { 0x266F, 0xA648 }, { 0x2716, 0xA66D }, { 0x271A, 0xA66C }, { 0x2720, 0xA688 }, { 0x2723, 0xA672 }, { 0x2731, 0xA653 },
	{ 0x273D, 0xA652 }, { 0x273F, 0xA67C }, { 0x2740, 0xA699 }, { 0x2741, 0xA68D }, { 0x2747, 0xA654 }, { 0x274D, 0xA683 }, { 0x2756, 0xA673 }, { 0x2776, 0xA6E5 },
	{ 0x2777, 0xA6E6 }, { 0x2778, 0xA6E7 }, { 0x2779, 0xA6E8 }, { 0x277A, 0xA6E9 }, { 0x277B, 0xA6EA }, { 0x277C, 0xA6EB }, { 0x277D, 0xA6EC }, { 0x277E, 0xA6ED },
	{ 0x277F, 0xA6EE }, { 0x278A, 0xA355 }, { 0x278B, 0xA356 }, { 0x278C, 0xA357 }, { 0x278D, 0xA358 }, { 0x278E, 0xA359 }, { 0x278F, 0xA35A }, { 0x2790, 0xA35B },
	{ 0x2791, 0xA35C }, { 0x2792, 0xA35D }, { 0x2793, 0xA35E }, { 0x2794, 0xAC5E }, { 0x279B, 0xA86A }, { 0x279C, 0xA860 }, { 0x279E, 0xA878 }, { 0x27A1, 0xA874 },
	{ 0x27A4, 0xAC48 }, { 0x27B2, 0xA85C }, { 0x27B5, 0xAC43 }, { 0x2934, 0xA88D }, { 0x2935, 0xA889 }, { 0x2936, 0xA886 }, { 0x2937, 0xA88B }, { 0x2939, 0xA88C },
	{ 0x2962, 0xA86F }, { 0x2963, 0xA871 }, { 0x2964, 0xA870 }, { 0x2965, 0xA872 }, { 0x2981, 0xA799 }, { 0x2985, 0xA159 }, { 0x2986, 0xA15A }, { 0x2997, 0xA199 },
	{ 0x2998, 0xA19A }, { 0x29A3, 0xA49C }, { 0x29BE, 0xA668 }, { 0x29BF, 0xA66E }, { 0x29C8, 0xA664 }, { 0x2A26, 0xA47D }, { 0x2A38, 0xA760 }, { 0x2A72, 0xA77B },
	{ 0x2A8B, 0xA494 }, { 0x2A8C, 0xA495 }, { 0x2A91, 0xA496 }, { 0x2A92, 0xA497 }, { 0x2AC5, 0xA48B }, { 0x2AC6, 0xA48D }, { 0x2ACB, 0xA48C }, { 0x2ACC, 0xA48E },
	{ 0x2AE8, 0xA76B }, { 0x3000, 0xA1A1 }, { 0x3001, 0xA1A2 }, { 0x3002, 0xA1A3 }, { 0x3003, 0xA1A8 }, { 0x3008, 0xA1B4 }, { 0x3009, 0xA1B5 }, { 0x300A, 0xA1B6 },
	{ 0x300B, 0xA1B7 }, { 0x300C, 0xA1B8 }, { 0x300D, 0xA1B9 }, { 0x300E, 0xA1BA }, { 0x300F, 0xA1BB }, { 0x3010, 0xA1BC }, { 0x3011, 0xA1BD }, { 0x3012, 0xA742 },
	{ 0x3013, 0xA1EB }, { 0x3014, 0xA1B2 }, { 0x3015, 0xA1B3 }, { 0x3016, 0xA15D }, { 0x3017, 0xA15E }, { 0x3018, 0xA15F }, { 0x3019, 0xA160 }, { 0x301C, 0xA1AD },
	{ 0x301E, 0xADA9 }, { 0x301F, 0xADAA }, { 0x3020, 0xA69E }, { 0x3036, 0xA743 }, { 0x3041, 0xAAA1 }, { 0x3042, 0xAAA2 }, { 0x3043, 0xAAA3 }, { 0x3044, 0xAAA4 },
	{ 0x3045, 0xAAA5 }, { 0x3046, 0xAAA6 }, { 0x3047, 0xAAA7 }, { 0x3048, 0xAAA8 }, { 0x3049, 0xAAA9 }, { 0x304A, 0xAAAA }, { 0x304B, 0xAAAB }, { 0x304C, 0xAAAC },
	{ 0x304D, 0xAAAD }, { 0x304E, 0xAAAE }, { 0x304F, 0xAAAF }, { 0x3050, 0xAAB0 }, { 0x3051, 0xAAB1 }, { 0x3052, 0xAAB2 }, { 0x3053, 0xAAB3 }, { 0x3054, 0xAAB4 },
	{ 0x3055, 0xAAB5 }, { 0x3056, 0xAAB6 }, { 0x3057, 0xAAB7 }, { 0x3058, 0xAAB8 }, { 0x3059, 0xAAB9 }, { 0x305A, 0xAABA }, { 0x305B, 0xAABB }, { 0x305C, 0xAABC },
	{ 0x305D, 0xAABD }, { 0x305E, 0xAABE }, { 0x305F, 0xAABF }, { 0x3060, 0xAAC0 }, { 0x3061, 0xAAC1 }, { 0x3062, 0xAAC2 }, { 0x3063, 0xAAC3 }, { 0x3064, 0xAAC4 },
	{ 0x3065, 0xAAC5 }, { 0x3066, 0xAAC6 }, { 0x3067, 0xAAC7 }, { 0x3068, 0xAAC8 }, { 0x3069, 0xAAC9 }, { 0x306A, 0xAACA }, { 0x306B, 0xAACB }, { 0x306C, 0xAACC },
	{ 0x306D, 0xAACD }, { 0x306E, 0xAACE }, { 0x306F, 0xAACF }, { 0x3070, 0xAAD0 }, { 0x3071, 0xAAD1 }, { 0x3072, 0xAAD2 }, { 0x3073, 0xAAD3 }, { 0x3074, 0xAAD4 },
	{ 0x3075, 0xAAD5 }, { 0x3076, 0xAAD6 }, { 0x3077, 0xAAD7 }, { 0x3078, 0xAAD8 }, { 0x3079, 0xAAD9 }, { 0x307A, 0xAADA }, { 0x307B, 0xAADB }, { 0x307C, 0xAADC },
	{ 0x307D, 0xAADD }, { 0x307E, 0xAADE }, { 0x307F, 0xAADF }, { 0x3080, 0xAAE0 }, { 0x3081, 0xAAE1 }, { 0x3082, 0xAAE2 }, { 0x3083, 0xAAE3 }, { 0x3084, 0xAAE4 },
	{ 0x3085, 0xAAE5 }, { 0x3086, 0xAAE6 }, { 0x3087, 0xAAE7 }, { 0x3088, 0xAAE8 }, { 0x3089, 0xAAE9 }, { 0x308A, 0xAAEA }, { 0x308B, 0xAAEB }, { 0x308C, 0xAAEC },
	{ 0x308D, 0xAAED }, { 0x308E, 0xAAEE }, { 0x308F, 0xAAEF }, { 0x3090, 0xAAF0 }, { 0x3091, 0xAAF1 }, { 0x3092, 0xAAF2 }, { 0x3093, 0xAAF3 }, { 0x30A1, 0xABA1 },
	{ 0x30A2, 0xABA2 }, { 0x30A3, 0xABA3 }, { 0x30A4, 0xABA4 }, { 0x30A5, 0xABA5 }, { 0x30A6, 0xABA6 }, { 0x30A7, 0xABA7 }, { 0x30A8, 0xABA8 }, { 0x30A9, 0xABA9 },
	{ 0x30AA, 0xABAA }, { 0x30AB, 0xABAB }, { 0x30AC, 0xABAC }, { 0x30AD, 0xABAD }, { 0x30AE, 0xABAE }, { 0x30AF, 0xABAF }, { 0x30B0, 0xABB0 }, { 0x30B1, 0xABB1 },
	{ 0x30B2, 0xABB2 }, { 0x30B3, 0xABB3 }, { 0x30B4, 0xABB4 }, { 0x30B5, 0xABB5 }, { 0x30B6, 0xABB6 }, { 0x30B7, 0xABB7 }, { 0x30B8, 0xABB8 }, { 0x30B9, 0xABB9 },
	{ 0x30BA, 0xABBA }, { 0x30BB, 0xABBB }, { 0x30BC, 0xABBC }, { 0x30BD, 0xABBD }, { 0x30BE, 0xABBE }, { 0x30BF, 0xABBF }, { 0x30C0, 0xABC0 }, { 0x30C1, 0xABC1 },
	{ 0x30C2, 0xABC2 }, { 0x30C3, 0xABC3 }, { 0x30C4, 0xABC4 }, { 0x30C5, 0xABC5 }, { 0x30C6, 0xABC6 }, { 0x30C7, 0xABC7 }, { 0x30C8, 0xABC8 }, { 0x30C9, 0xABC9 },
	{ 0x30CA, 0xABCA }, { 0x30CB, 0xABCB }, { 0x30CC, 0xABCC }, { 0x30CD, 0xABCD }, { 0x30CE, 0xABCE }, { 0x30CF, 0xABCF }, { 0x30D0, 0xABD0 }, { 0x30D1, 0xABD1 },
	{ 0x30D2, 0xABD2 }, { 0x30D3, 0xABD3 }, { 0x30D4, 0xABD4 }, { 0x30D5, 0xABD5 }, { 0x30D6, 0xABD6 }, { 0x30D7, 0xABD7 }, { 0x30D8, 0xABD8 }, { 0x30D9, 0xABD9 },
	{ 0x30DA, 0xABDA }, { 0x30DB, 0xABDB }, { 0x30DC, 0xABDC }, { 0x30DD, 0xABDD }, { 0x30DE, 0xABDE }, { 0x30DF, 0xABDF }, { 0x30E0, 0xABE0 }, { 0x30E1, 0xABE1 },
	{ 0x30E2, 0xABE2 }, { 0x30E3, 0xABE3 }, { 0x30E4, 0xABE4 }, { 0x30E5, 0xABE5 }, { 0x30E6, 0xABE6 }, { 0x30E7, 0xABE7 }, { 0x30E8, 0xABE8 }, { 0x30E9, 0xABE9 },
	{ 0x30EA, 0xABEA }, { 0x30EB, 0xABEB }, { 0x30EC, 0xABEC }, { 0x30ED, 0xABED }, { 0x30EE, 0xABEE }, { 0x30EF, 0xABEF }, { 0x30F0, 0xABF0 }, { 0x30F1, 0xABF1 },
	{ 0x30F2, 0xABF2 }, { 0x30F3, 0xABF3 }, { 0x30F4, 0xABF4 }, { 0x30F5, 0xABF5 }, { 0x30F6, 0xABF6 }, { 0x3131, 0xA4A1 }, { 0x3132, 0xA4A2 }, { 0x3133, 0xA4A3 },
	{ 0x3134, 0xA4A4 }, { 0x3135, 0xA4A5 }, { 0x3136, 0xA4A6 }, { 0x3137, 0xA4A7 }, { 0x3138, 0xA4A8 }, { 0x3139, 0xA4A9 }, { 0x313A, 0xA4AA }, { 0x313B, 0xA4AB },
	{ 0x313C, 0xA4AC }, { 0x313D, 0xA4AD }, { 0x313E, 0xA4AE }, { 0x313F, 0xA4AF }, { 0x3140, 0xA4B0 }, { 0x3141, 0xA4B1 }, { 0x3142, 0xA4B2 }, { 0x3143, 0xA4B3 },
	{ 0x3144, 0xA4B4 }, { 0x3145, 0xA4B5 }, { 0x3146, 0xA4B6 }, { 0x3147, 0xA4B7 }, { 0x3148, 0xA4B8 }, { 0x3149, 0xA4B9 }, { 0x314A, 0xA4BA }, { 0x314B, 0xA4BB },
	{ 0x314C, 0xA4BC }, { 0x314D, 0xA4BD }, { 0x314E, 0xA4BE }, { 0x314F, 0xA4BF }, { 0x3150, 0xA4C0 }, { 0x3151, 0xA4C1 }, { 0x3152, 0xA4C2 }, { 0x3153, 0xA4C3 },
	{ 0x3154, 0xA4C4 }, { 0x3155, 0xA4C5 }, { 0x3156, 0xA4C6 }, { 0x3157, 0xA4C7 }, { 0x3158, 0xA4C8 }, { 0x3159, 0xA4C9 }, { 0x315A, 0xA4CA }, { 0x315B, 0xA4CB },
	{ 0x315C, 0xA4CC }, { 0x315D, 0xA4CD }, { 0x315E, 0xA4CE }, { 0x315F, 0xA4CF }, { 0x3160, 0xA4D0 }, { 0x3161, 0xA4D1 }, { 0x3162, 0xA4D2 }, { 0x3163, 0xA4D3 },
	{ 0x3164, 0xA4D4 }, { 0x3165, 0xA4D5 }, { 0x3166, 0xA4D6 }, { 0x3167, 0xA4D7 }, { 0x3168, 0xA4D8 }, { 0x3169, 0xA4D9 }, { 0x316A, 0xA4DA }, { 0x316B, 0xA4DB },
	{ 0x316C, 0xA4DC }, { 0x316D, 0xA4DD }, { 0x316E, 0xA4DE }, { 0x316F, 0xA4DF }, { 0x3170, 0xA4E0 }, { 0x3171, 0xA4E1 }, { 0x3172, 0xA4E2 }, { 0x3173, 0xA4E3 },
	{ 0x3174, 0xA4E4 }, { 0x3175, 0xA4E5 }, { 0x3176, 0xA4E6 }, { 0x3177, 0xA4E7 }, { 0x3178, 0xA4E8 }, { 0x3179, 0xA4E9 }, { 0x317A, 0xA4EA }, { 0x317B, 0xA4EB },
	{ 0x317C, 0xA4EC }, { 0x317D, 0xA4ED }, { 0x317E, 0xA4EE }, { 0x317F, 0xA4EF }, { 0x3180, 0xA4F0 }, { 0x3181, 0xA4F1 }, { 0x3182, 0xA4F2 }, { 0x3183, 0xA4F3 },
	{ 0x3184, 0xA4F4 }, { 0x3185, 0xA4F5 }, { 0x3186, 0xA4F6 }, { 0x3187, 0xA4F7 }, { 0x3188, 0xA4F8 }, { 0x3189, 0xA4F9 }, { 0x318A, 0xA4FA }, { 0x318B, 0xA4FB },
	{ 0x318C, 0xA4FC }, { 0x318D, 0xA4FD }, { 0x318E, 0xA4FE }, { 0x3200, 0xA9B1 }, { 0x3201, 0xA9B2 }, { 0x3202, 0xA9B3 }, { 0x3203, 0xA9B4 }, { 0x3204, 0xA9B5 },
	{ 0x3205, 0xA9B6 }, { 0x3206, 0xA9B7 }, { 0x3207, 0xA9B8 }, { 0x3208, 0xA9B9 }, { 0x3209, 0xA9BA }, { 0x320A, 0xA9BB }, { 0x320B, 0xA9BC }, { 0x320C, 0xA9BD },
	{ 0x320D, 0xA9BE }, { 0x320E, 0xA9BF }, { 0x320F, 0xA9C0 }, { 0x3210, 0xA9C1 }, { 0x3211, 0xA9C2 }, { 0x3212, 0xA9C3 }, { 0x3213, 0xA9C4 }, { 0x3214, 0xA9C5 },
	{ 0x3215, 0xA9C6 }, { 0x3216, 0xA9C7 }, { 0x3217, 0xA9C8 }, { 0x3218, 0xA9C9 }, { 0x3219, 0xA9CA }, { 0x321A, 0xA9CB }, { 0x321B, 0xA9CC }, { 0x321C, 0xA2DF },
	{ 0x3231, 0xA79D }, { 0x3239, 0xA79E }, { 0x3251, 0xA7F5 }, { 0x3252, 0xA7F6 }, { 0x3253, 0xA7F7 }, { 0x3254, 0xA7F8 }, { 0x3255, 0xA7F9 }, { 0x3256, 0xA7FA },
	{ 0x3257, 0xA7FB }, { 0x3258, 0xA7FC }, { 0x3259, 0xA7FD }, { 0x325A, 0xA7FE }, { 0x3260, 0xA8B1 }, { 0x3261, 0xA8B2 }, { 0x3262, 0xA8B3 }, { 0x3263, 0xA8B4 },
	{ 0x3264, 0xA8B5 }, { 0x3265, 0xA8B6 }, { 0x3266, 0xA8B7 }, { 0x3267, 0xA8B8 }, { 0x3268, 0xA8B9 }, { 0x3269, 0xA8BA }, { 0x326A, 0xA8BB }, { 0x326B, 0xA8BC },
	{ 0x326C, 0xA8BD }, { 0x326D, 0xA8BE }, { 0x326E, 0xA8BF }, { 0x326F, 0xA8C0 }, { 0x3270, 0xA8C1 }, { 0x3271, 0xA8C2 }, { 0x3272, 0xA8C3 }, { 0x3273, 0xA8C4 },
	{ 0x3274, 0xA8C5 }, { 0x3275, 0xA8C6 }, { 0x3276, 0xA8C7 }, { 0x3277, 0xA8C8 }, { 0x3278, 0xA8C9 }, { 0x3279, 0xA8CA }, { 0x327A, 0xA8CB }, { 0x327B, 0xA8CC },
	{ 0x327F, 0xA2DE }, { 0x328A, 0xAD71 }, { 0x328B, 0xAD72 }, { 0x328C, 0xAD73 }, { 0x328D, 0xAD74 }, { 0x328E, 0xAD75 }, { 0x328F, 0xAD76 }, { 0x3290, 0xAD70 },
	{ 0x3294, 0xAB5C }, { 0x329E, 0xA782 }, { 0x32A5, 0xAB6C }, { 0x3380, 0xA7C9 }, { 0x3381, 0xA7CA }, { 0x3382, 0xA7CB }, { 0x3383, 0xA7CC }, { 0x3384, 0xA7CD },
	{ 0x3388, 0xA7BA }, { 0x3389, 0xA7BB }, { 0x338A, 0xA7DC }, { 0x338B, 0xA7DD }, { 0x338C, 0xA7DE }, { 0x338D, 0xA7B6 }, { 0x338E, 0xA7B7 }, { 0x338F, 0xA7B8 },
	{ 0x3390, 0xA7D4 }, { 0x3391, 0xA7D5 }, { 0x3392, 0xA7D6 }, { 0x3393, 0xA7D7 }, { 0x3394, 0xA7D8 }, { 0x3395, 0xA7A1 }, { 0x3396, 0xA7A2 }, { 0x3397, 0xA7A3 },
	{ 0x3398, 0xA7A5 }, { 0x3399, 0xA7AB }, { 0x339A, 0xA7AC }, { 0x339B, 0xA7AD }, { 0x339C, 0xA7AE }, { 0x339D, 0xA7AF }, { 0x339E, 0xA7B0 }, { 0x339F, 0xA7B1 },
	{ 0x33A0, 0xA7B2 }, { 0x33A1, 0xA7B3 }, { 0x33A2, 0xA7B4 }, { 0x33A3, 0xA7A7 }, { 0x33A4, 0xA7A8 }, { 0x33A5, 0xA7A9 }, { 0x33A6, 0xA7AA }, { 0x33A7, 0xA7BD },
	{ 0x33A8, 0xA7BE }, { 0x33A9, 0xA7E5 }, { 0x33AA, 0xA7E6 }, { 0x33AB, 0xA7E7 }, { 0x33AC, 0xA7E8 }, { 0x33AD, 0xA7E1 }, { 0x33AE, 0xA7E2 }, { 0x33AF, 0xA7E3 },
	{ 0x33B0, 0xA7BF }, { 0x33B1, 0xA7C0 }, { 0x33B2, 0xA7C1 }, { 0x33B3, 0xA7C2 }, { 0x33B4, 0xA7C3 }, { 0x33B5, 0xA7C4 }, { 0x33B6, 0xA7C5 }, { 0x33B7, 0xA7C6 },
	{ 0x33B8, 0xA7C7 }, { 0x33B9, 0xA7C8 }, { 0x33BA, 0xA7CE }, { 0x33BB, 0xA7CF }, { 0x33BC, 0xA7D0 }, { 0x33BD, 0xA7D1 }, { 0x33BE, 0xA7D2 }, { 0x33BF, 0xA7D3 },
	{ 0x33C0, 0xA7DA }, { 0x33C1, 0xA7DB }, { 0x33C2, 0xA2E3 }, { 0x33C3, 0xA7EC }, { 0x33C4, 0xA7A6 }, { 0x33C5, 0xA7E0 }, { 0x33C6, 0xA7EF }, { 0x33C7, 0xA2E1 },
	{ 0x33C8, 0xA7BC }, { 0x33C9, 0xA7ED }, { 0x33CA, 0xA7B5 }, { 0x33CB, 0xA79F }, { 0x33CF, 0xA7B9 }, { 0x33D0, 0xA7EA }, { 0x33D3, 0xA7EB }, { 0x33D6, 0xA7DF },
	{ 0x33D8, 0xA2E4 }, { 0x33DB, 0xA7E4 }, { 0x33DC, 0xA7EE }, { 0x33DD, 0xA7E9 }, { 0x4E00, 0xECE9 }, { 0x4E01, 0xEFCB }, { 0x4E03, 0xF6D2 }, { 0x4E07, 0xD8B2 },
	{ 0x4E08, 0xEDDB }, { 0x4E09, 0xDFB2 }, { 0x4E0A, 0xDFBE }, { 0x4E0B, 0xF9BB }, { 0x4E0D, 0xDCF4 }, { 0x4E11, 0xF5E4 }, { 0x4E14, 0xF3A6 }, { 0x4E15, 0xDDE0 },
	{ 0x4E16, 0xE1A6 }, { 0x4E18, 0xCEF8 }, { 0x4E19, 0xDCB0 }, { 0x4E1E, 0xE3AA }, { 0x4E2D, 0xF1E9 }, { 0x4E32, 0xCDFA }, { 0x4E38, 0xFCAF }, { 0x4E39, 0xD3A1 },
	{ 0x4E3B, 0xF1AB }, { 0x4E42, 0xE7D1 }, { 0x4E43, 0xD2AC }, { 0x4E45, 0xCEF9 }, { 0x4E4B, 0xF1FD }, { 0x4E4D, 0xDEBF }, { 0x4E4E, 0xFBBA }, { 0x4E4F, 0xF9B9 },
	{ 0x4E56, 0xCED2 }, { 0x4E58, 0xE3AB }, { 0x4E59, 0xEBE0 }, { 0x4E5D, 0xCEFA }, { 0x4E5E, 0xCBF7 }, { 0x4E5F, 0xE5A5 }, { 0x4E6B, 0xCAE1 }, { 0x4E6D, 0xD4CC },
	{ 0x4E73, 0xEAE1 }, { 0x4E76, 0xDCE3 }, { 0x4E77, 0xDFAD }, { 0x4E7E, 0xCBEB }, { 0x4E82, 0xD5AF }, { 0x4E86, 0xD6F5 }, { 0x4E88, 0xE5F8 }, { 0x4E8B, 0xDEC0 },
	{ 0x4E8C, 0xECA3 }, { 0x4E8E, 0xE9CD }, { 0x4E90, 0xEAA7 }, { 0x4E91, 0xE9F6 }, { 0x4E92, 0xFBBB }, { 0x4E94, 0xE7E9 }, { 0x4E95, 0xEFCC }, { 0x4E98, 0xD0E6 },
	{ 0x4E9B, 0xDEC1 }, { 0x4E9E, 0xE4AC }, { 0x4EA1, 0xD8CC }, { 0x4EA2, 0xF9F1 }, { 0x4EA4, 0xCEDF }, { 0x4EA5, 0xFAA4 }, { 0x4EA6, 0xE6B2 }, { 0x4EA8, 0xFAFB },
	{ 0x4EAB, 0xFABD }, { 0x4EAC, 0xCCC8 }, { 0x4EAD, 0xEFCD }, { 0x4EAE, 0xD5D5 }, { 0x4EB6, 0xD3A2 }, { 0x4EBA, 0xECD1 }, { 0x4EC0, 0xE4A7 }, { 0x4EC1, 0xECD2 },
	{ 0x4EC4, 0xF6B1 }, { 0x4EC7, 0xCEFB }, { 0x4ECA, 0xD0D1 }, { 0x4ECB, 0xCBBF }, { 0x4ECD, 0xEDA4 }, { 0x4ED4, 0xEDA8 }, { 0x4ED5, 0xDEC2 }, { 0x4ED6, 0xF6E2 },
	{ 0x4ED7, 0xEDDC }, { 0x4ED8, 0xDCF5 }, { 0x4ED9, 0xE0B9 }, { 0x4EDD, 0xD4CE }, { 0x4EDF, 0xF4B5 }, { 0x4EE3, 0xD3DB }, { 0x4EE4, 0xD6B5 }, { 0x4EE5, 0xECA4 },
	{ 0x4EF0, 0xE4E6 }, { 0x4EF2, 0xF1EA }, { 0x4EF6, 0xCBEC }, { 0x4EF7, 0xCBC0 }, { 0x4EFB, 0xECF2 }, { 0x4F01, 0xD0EA }, { 0x4F09, 0xF9F2 }, { 0x4F0A, 0xECA5 },
	{ 0x4F0B, 0xD0DF }, { 0x4F0D, 0xE7EA }, { 0x4F0E, 0xD0EB }, { 0x4F0F, 0xDCD1 }, { 0x4F10, 0xDBE9 }, { 0x4F11, 0xFDCC }, { 0x4F2F, 0xDBD7 }, { 0x4F34, 0xDAE1 },
	{ 0x4F36, 0xD6B6 }, { 0x4F38, 0xE3DF }, { 0x4F3A, 0xDEC3 }, { 0x4F3C, 0xDEC4 }, { 0x4F3D, 0xCAA1 }, { 0x4F43, 0xEEEC }, { 0x4F46, 0xD3A3 }, { 0x4F47, 0xEEB7 },
	{ 0x4F48, 0xF8CF }, { 0x4F4D, 0xEAC8 }, { 0x4F4E, 0xEEB8 }, { 0x4F4F, 0xF1AC }, { 0x4F50, 0xF1A5 }, { 0x4F51, 0xE9CE }, { 0x4F55, 0xF9BC }, { 0x4F59, 0xE5F9 },
	{ 0x4F5A, 0xECEA }, { 0x4F5B, 0xDDD6 }, { 0x4F5C, 0xEDC2 }, { 0x4F69, 0xF8A5 }, { 0x4F6F, 0xE5BA }, { 0x4F70, 0xDBD8 }, { 0x4F73, 0xCAA2 }, { 0x4F76, 0xD1CD },
	{ 0x4F7A, 0xEEED }, { 0x4F7E, 0xECEB }, { 0x4F7F, 0xDEC5 }, { 0x4F81, 0xE3E0 }, { 0x4F83, 0xCAC9 }, { 0x4F84, 0xF2E9 }, { 0x4F86, 0xD5CE }, { 0x4F88, 0xF6B6 },
	{ 0x4F8A, 0xCEC2 }, { 0x4F8B, 0xD6C7 }, { 0x4F8D, 0xE3B4 }, { 0x4F8F, 0xF1AD }, { 0x4F91, 0xEAE2 }, { 0x4F96, 0xD7C2 }, { 0x4F98, 0xF3A7 }, { 0x4F9B, 0xCDEA },
	{ 0x4F9D, 0xEBEE }, { 0x4FAE, 0xD9B2 }, { 0x4FAF, 0xFDA5 }, { 0x4FB5, 0xF6D5 }, { 0x4FB6, 0xD5E2 }, { 0x4FBF, 0xF8B5 }, { 0x4FC2, 0xCCF5 }, { 0x4FC3, 0xF5B5 },
	{ 0x4FC4, 0xE4AD }, { 0x4FC9, 0xE7EB }, { 0x4FCA, 0xF1D5 }, { 0x4FCE, 0xF0BB }, { 0x4FD1, 0xE9B5 }, { 0x4FD3, 0xCCC9 }, { 0x4FD4, 0xFAD5 }, { 0x4FD7, 0xE1D4 },
	{ 0x4FDA, 0xD7D6 }, { 0x4FDD, 0xDCC1 }, { 0x4FDF, 0xDEC6 }, { 0x4FE0, 0xFAEF }, { 0x4FE1, 0xE3E1 }, { 0x4FEE, 0xE1F3 }, { 0x4FEF, 0xDCF6 }, { 0x4FF1, 0xCEFC },
	{ 0x4FF3, 0xDBC4 }, { 0x4FF5, 0xF8F1 }, { 0x4FF8, 0xDCE4 }, { 0x4FFA, 0xE5EF }, { 0x5002, 0xDCB1 }, { 0x5006, 0xD5D6 }, { 0x5009, 0xF3DA }, { 0x500B, 0xCBC1 },
	{ 0x500D, 0xDBC3 }, { 0x5011, 0xD9FA }, { 0x5012, 0xD3EE }, { 0x5016, 0xFAB8 }, { 0x5019, 0xFDA6 }, { 0x501A, 0xEBEF }, { 0x501C, 0xF4A6 }, { 0x501E, 0xCCCA },
	{ 0x501F, 0xF3A8 }, { 0x5021, 0xF3DB }, { 0x5023, 0xDBA7 }, { 0x5024, 0xF6B7 }, { 0x5026, 0xCFE6 }, { 0x5027, 0xF0F2 }, { 0x5028, 0xCBDA }, { 0x502A, 0xE7D2 },
	{ 0x502B, 0xD7C3 }, { 0x502C, 0xF6F0 }, { 0x502D, 0xE8DE }, { 0x503B, 0xE5A6 }, { 0x5043, 0xE5E7 }, { 0x5047, 0xCAA3 }, { 0x5048, 0xCCA7 }, { 0x5049, 0xEAC9 },
	{ 0x504F, 0xF8B6 }, { 0x5055, 0xFAA5 }, { 0x505A, 0xF1AE }, { 0x505C, 0xEFCE }, { 0x5065, 0xCBED }, { 0x5074, 0xF6B0 }, { 0x5075, 0xEFCF }, { 0x5076, 0xE9CF },
	{ 0x5078, 0xF7DE }, { 0x5080, 0xCED3 }, { 0x5085, 0xDCF7 }, { 0x508D, 0xDBA8 }, { 0x5091, 0xCBF8 }, { 0x5098, 0xDFA1 }, { 0x5099, 0xDDE1 }, { 0x50AC, 0xF5CA },
	{ 0x50AD, 0xE9B6 }, { 0x50B2, 0xE7EC }, { 0x50B3, 0xEEEE }, { 0x50B5, 0xF3F0 }, { 0x50B7, 0xDFBF }, { 0x50BE, 0xCCCB }, { 0x50C5, 0xD0C1 }, { 0x50C9, 0xF4D2 },
	{ 0x50CA, 0xE0BA }, { 0x50CF, 0xDFC0 }, { 0x50D1, 0xCEE0 }, { 0x50D5, 0xDCD2 }, { 0x50D6, 0xFDEA }, { 0x50DA, 0xD6F6 }, { 0x50DE, 0xEACA }, { 0x50E5, 0xE8E9 },
	{ 0x50E7, 0xE3AC }, { 0x50ED, 0xF3D0 }, { 0x50F9, 0xCAA4 }, { 0x50FB, 0xDBF8 }, { 0x50FF, 0xDEC7 }, { 0x5100, 0xEBF0 }, { 0x5101, 0xF1D6 }, { 0x5104, 0xE5E2 },
	{ 0x5106, 0xCCCC }, { 0x5109, 0xCBFB }, { 0x5112, 0xEAE3 }, { 0x511F, 0xDFC1 }, { 0x5121, 0xD6ED }, { 0x512A, 0xE9D0 }, { 0x5132, 0xEEB9 }, { 0x5137, 0xD5E3 },
	{ 0x513A, 0xD1D3 }, { 0x513C, 0xE5F0 }, { 0x5140, 0xE8B4 }, { 0x5141, 0xEBC3 }, { 0x5143, 0xEAAA }, { 0x5144, 0xFAFC }, { 0x5145, 0xF5F6 }, { 0x5146, 0xF0BC },
	{ 0x5147, 0xFDD4 }, { 0x5148, 0xE0BB }, { 0x5149, 0xCEC3 }, { 0x514B, 0xD0BA }, { 0x514C, 0xF7BA }, { 0x514D, 0xD8F3 }, { 0x514E, 0xF7CD }, { 0x5152, 0xE4AE },
	{ 0x515C, 0xD4DF }, { 0x5162, 0xD0E7 }, { 0x5165, 0xECFD }, { 0x5167, 0xD2AE }, { 0x5168, 0xEEEF }, { 0x5169, 0xD5D7 }, { 0x516A, 0xEAE4 }, { 0x516B, 0xF8A2 },
	{ 0x516C, 0xCDEB }, { 0x516D, 0xD7BF }, { 0x516E, 0xFBB1 }, { 0x5171, 0xCDEC }, { 0x5175, 0xDCB2 }, { 0x5176, 0xD0EC }, { 0x5177, 0xCEFD }, { 0x5178, 0xEEF0 },
	{ 0x517C, 0xCCC2 }, { 0x5180, 0xD0ED }, { 0x5186, 0xE5F7 }, { 0x518A, 0xF3FC }, { 0x518D, 0xEEA2 }, { 0x5192, 0xD9B3 }, { 0x5195, 0xD8F4 }, { 0x5197, 0xE9B7 },
	{ 0x51A0, 0xCEAE }, { 0x51A5, 0xD9A2 }, { 0x51AA, 0xD8F1 }, { 0x51AC, 0xD4CF }, { 0x51B6, 0xE5A7 }, { 0x51B7, 0xD5D2 }, { 0x51BD, 0xD6A9 }, { 0x51C4, 0xF4A2 },
	{ 0x51C6, 0xF1D7 }, { 0x51C9, 0xD5D8 }, { 0x51CB, 0xF0BD }, { 0x51CC, 0xD7D0 }, { 0x51CD, 0xD4D0 }, { 0x51DC, 0xD7CF }, { 0x51DD, 0xEBEA }, { 0x51DE, 0xFDEB },
	{ 0x51E1, 0xDBED }, { 0x51F0, 0xFCC5 }, { 0x51F1, 0xCBC2 }, { 0x51F6, 0xFDD5 }, { 0x51F8, 0xF4C8 }, { 0x51F9, 0xE8EA }, { 0x51FA, 0xF5F3 }, { 0x51FD, 0xF9DE },
	{ 0x5200, 0xD3EF }, { 0x5203, 0xECD3 }, { 0x5206, 0xDDC2 }, { 0x5207, 0xEFB7 }, { 0x5208, 0xE7D4 }, { 0x520A, 0xCACA }, { 0x520E, 0xD9FB }, { 0x5211, 0xFAFD },
	{ 0x5217, 0xD6AA }, { 0x521D, 0xF4F8 }, { 0x5224, 0xF7F7 }, { 0x5225, 0xDCAC }, { 0x5229, 0xD7D7 }, { 0x522A, 0xDFA2 }, { 0x522E, 0xCEBE }, { 0x5230, 0xD3F0 },
	{ 0x5236, 0xF0A4 }, { 0x5237, 0xE1EC }, { 0x5238, 0xCFE7 }, { 0x5239, 0xF3CB }, { 0x523A, 0xEDA9 }, { 0x523B, 0xCABE }, { 0x5243, 0xF4EF }, { 0x5247, 0xF6CE },
	{ 0x524A, 0xDEFB }, { 0x524B, 0xD0BB }, { 0x524C, 0xD5B7 }, { 0x524D, 0xEEF1 }, { 0x5254, 0xF4A8 }, { 0x5256, 0xDCF8 }, { 0x525B, 0xCBA7 }, { 0x525D, 0xDACE },
	{ 0x5261, 0xE0E6 }, { 0x5269, 0xEDA5 }, { 0x526A, 0xEEF2 }, { 0x526F, 0xDCF9 }, { 0x5272, 0xF9DC }, { 0x5275, 0xF3DC }, { 0x527D, 0xF8F2 }, { 0x527F, 0xF4F9 },
	{ 0x5283, 0xFCF1 }, { 0x5287, 0xD0BC }, { 0x5288, 0xDBF9 }, { 0x5289, 0xD7B1 }, { 0x528D, 0xCBFC }, { 0x5291, 0xF0A5 }, { 0x5292, 0xCBFD }, { 0x529B, 0xD5F4 },
	{ 0x529F, 0xCDED }, { 0x52A0, 0xCAA5 }, { 0x52A3, 0xD6AB }, { 0x52A4, 0xD0C2 }, { 0x52A9, 0xF0BE }, { 0x52AA, 0xD2BD }, { 0x52AB, 0xCCA4 }, { 0x52BE, 0xFAB6 },
	{ 0x52C1, 0xCCCD }, { 0x52C3, 0xDAFA }, { 0x52C5, 0xF6CF }, { 0x52C7, 0xE9B8 }, { 0x52C9, 0xD8F5 }, { 0x52CD, 0xCCCE }, { 0x52D2, 0xD7CD }, { 0x52D5, 0xD4D1 },
	{ 0x52D6, 0xE9ED }, { 0x52D8, 0xCAEB }, { 0x52D9, 0xD9E2 }, { 0x52DB, 0xFDB2 }, { 0x52DD, 0xE3AD }, { 0x52DE, 0xD6CC }, { 0x52DF, 0xD9B4 }, { 0x52E2, 0xE1A7 },
	{ 0x52E3, 0xEED3 }, { 0x52E4, 0xD0C3 }, { 0x52F3, 0xFDB3 }, { 0x52F5, 0xD5E4 }, { 0x52F8, 0xCFE8 }, { 0x52FA, 0xEDC3 }, { 0x52FB, 0xD0B2 }, { 0x52FE, 0xCEFE },
	{ 0x52FF, 0xDAA8 }, { 0x5305, 0xF8D0 }, { 0x5308, 0xFDD6 }, { 0x530D, 0xF8D1 }, { 0x530F, 0xF8D2 }, { 0x5310, 0xDCD3 }, { 0x5315, 0xDDE2 }, { 0x5316, 0xFBF9 },
	{ 0x5317, 0xDDC1 }, { 0x5319, 0xE3B5 }, { 0x5320, 0xEDDD }, { 0x5321, 0xCEC4 }, { 0x5323, 0xCBA1 }, { 0x532A, 0xDDE3 }, { 0x532F, 0xFCDD }, { 0x5339, 0xF9AF },
	{ 0x533F, 0xD2FB }, { 0x5340, 0xCFA1 }, { 0x5341, 0xE4A8 }, { 0x5343, 0xF4B6 }, { 0x5344, 0xECFE }, { 0x5347, 0xE3AE }, { 0x5348, 0xE7ED }, { 0x5349, 0xFDC1 },
	{ 0x534A, 0xDAE2 }, { 0x534D, 0xD8B3 }, { 0x5351, 0xDDE4 }, { 0x5352, 0xF0EF }, { 0x5353, 0xF6F1 }, { 0x5354, 0xFAF0 }, { 0x5357, 0xD1F5 }, { 0x535A, 0xDACF },
	{ 0x535C, 0xDCD4 }, { 0x535E, 0xDCA6 }, { 0x5360, 0xEFBF }, { 0x5366, 0xCECF }, { 0x5368, 0xE0D9 }, { 0x536F, 0xD9D6 }, { 0x5370, 0xECD4 }, { 0x5371, 0xEACB },
	{ 0x5374, 0xCABF }, { 0x5375, 0xD5B0 }, { 0x5377, 0xCFE9 }, { 0x537D, 0xF1ED }, { 0x537F, 0xCCCF }, { 0x5384, 0xE4F8 }, { 0x5393, 0xE4ED }, { 0x5398, 0xD7D8 },
	{ 0x539A, 0xFDA7 }, { 0x539F, 0xEAAB }, { 0x53A0, 0xF6B2 }, { 0x53A5, 0xCFF0 }, { 0x53A6, 0xF9BD }, { 0x53AD, 0xE6F4 }, { 0x53BB, 0xCBDB }, { 0x53C3, 0xF3D1 },
	{ 0x53C8, 0xE9D1 }, { 0x53C9, 0xF3A9 }, { 0x53CA, 0xD0E0 }, { 0x53CB, 0xE9D2 }, { 0x53CD, 0xDAE3 }, { 0x53D4, 0xE2D2 }, { 0x53D6, 0xF6A2 }, { 0x53D7, 0xE1F4 },
	{ 0x53DB, 0xDAE4 }, { 0x53E1, 0xE7D5 }, { 0x53E2, 0xF5BF }, { 0x53E3, 0xCFA2 }, { 0x53E4, 0xCDAF }, { 0x53E5, 0xCFA3 }, { 0x53E9, 0xCDB0 }, { 0x53EA, 0xF1FE },
	{ 0x53EB, 0xD0A3 }, { 0x53EC, 0xE1AF }, { 0x53ED, 0xF8A3 }, { 0x53EF, 0xCAA6 }, { 0x53F0, 0xF7BB }, { 0x53F1, 0xF2EA }, { 0x53F2, 0xDEC8 }, { 0x53F3, 0xE9D3 },
	{ 0x53F8, 0xDEC9 }, { 0x5403, 0xFDDE }, { 0x5404, 0xCAC0 }, { 0x5408, 0xF9EA }, { 0x5409, 0xD1CE }, { 0x540A, 0xEED4 }, { 0x540C, 0xD4D2 }, { 0x540D, 0xD9A3 },
	{ 0x540E, 0xFDA8 }, { 0x540F, 0xD7D9 }, { 0x5410, 0xF7CE }, { 0x5411, 0xFABE }, { 0x541B, 0xCFD6 }, { 0x541D, 0xD7F0 }, { 0x541F, 0xEBE1 }, { 0x5420, 0xF8C5 },
	{ 0x5426, 0xDCFA }, { 0x5429, 0xDDC3 }, { 0x542B, 0xF9DF }, { 0x5433, 0xE7EF }, { 0x5438, 0xFDE5 }, { 0x5439, 0xF6A3 }, { 0x543B, 0xD9FC }, { 0x543C, 0xFDA9 },
	{ 0x543E, 0xE7EE }, { 0x5442, 0xD5E5 }, { 0x5448, 0xEFD0 }, { 0x544A, 0xCDB1 }, { 0x5451, 0xF7A2 }, { 0x5468, 0xF1B2 }, { 0x546A, 0xF1B1 }, { 0x5471, 0xCDB2 },
	{ 0x5473, 0xDAAB }, { 0x5475, 0xCAA7 }, { 0x547B, 0xE3E2 }, { 0x547C, 0xFBBC }, { 0x547D, 0xD9A4 }, { 0x5480, 0xEEBA }, { 0x5486, 0xF8D3 }, { 0x548C, 0xFBFA },
	{ 0x548E, 0xCFA4 }, { 0x5490, 0xDCFB }, { 0x54A4, 0xF6E3 }, { 0x54A8, 0xEDAA }, { 0x54AB, 0xF2A1 }, { 0x54AC, 0xCEE1 }, { 0x54B3, 0xFAA6 }, { 0x54B8, 0xF9E0 },
	{ 0x54BD, 0xECD6 }, { 0x54C0, 0xE4EE }, { 0x54C1, 0xF9A1 }, { 0x54C4, 0xFBEF }, { 0x54C8, 0xF9EB }, { 0x54C9, 0xEEA3 }, { 0x54E1, 0xEAAC }, { 0x54E5, 0xCAA8 },
	{ 0x54E8, 0xF4FA }, { 0x54ED, 0xCDD6 }, { 0x54EE, 0xFCF6 }, { 0x54F2, 0xF4C9 }, { 0x54FA, 0xF8D4 }, { 0x5504, 0xF8A6 }, { 0x5506, 0xDECA }, { 0x5507, 0xF2C6 },
	{ 0x550E, 0xD7DA }, { 0x5510, 0xD3D0 }, { 0x551C, 0xD8C5 }, { 0x552F, 0xEAE6 }, { 0x5531, 0xF3DD }, { 0x5535, 0xE4DA }, { 0x553E, 0xF6E4 }, { 0x5544, 0xF6F2 },
	{ 0x5546, 0xDFC2 }, { 0x554F, 0xD9FD }, { 0x5553, 0xCCF6 }, { 0x5556, 0xD3BA }, { 0x555E, 0xE4AF }, { 0x5563, 0xF9E1 }, { 0x557C, 0xF0A6 }, { 0x5580, 0xCBD3 },
	{ 0x5584, 0xE0BC }, { 0x5586, 0xF4CA }, { 0x5587, 0xD4FA }, { 0x5589, 0xFDAA }, { 0x558A, 0xF9E2 }, { 0x5598, 0xF4B7 }, { 0x5599, 0xFDC2 }, { 0x559A, 0xFCB0 },
	{ 0x559C, 0xFDEC }, { 0x559D, 0xCAE2 }, { 0x55A7, 0xFDBD }, { 0x55A9, 0xEAE7 }, { 0x55AA, 0xDFC3 }, { 0x55AB, 0xD1D2 }, { 0x55AC, 0xCEE2 }, { 0x55AE, 0xD3A4 },
	{ 0x55C5, 0xFDAB }, { 0x55C7, 0xDFE0 }, { 0x55D4, 0xF2C7 }, { 0x55DA, 0xE7F0 }, { 0x55DC, 0xD0EE }, { 0x55DF, 0xF3AA }, { 0x55E3, 0xDECB }, { 0x55E4, 0xF6B8 },
	{ 0x55FD, 0xE1F5 }, { 0x55FE, 0xF1B3 }, { 0x5606, 0xF7A3 }, { 0x5609, 0xCAA9 }, { 0x5614, 0xCFA5 }, { 0x5617, 0xDFC4 }, { 0x562F, 0xE1B0 }, { 0x5632, 0xF0BF },
	{ 0x5634, 0xF6A4 }, { 0x5636, 0xE3B6 }, { 0x5653, 0xFAC6 }, { 0x5668, 0xD0EF }, { 0x566B, 0xFDED }, { 0x5674, 0xDDC4 }, { 0x5686, 0xFCF7 }, { 0x56A5, 0xE6BF },
	{ 0x56AC, 0xDEAD }, { 0x56AE, 0xFABF }, { 0x56B4, 0xE5F1 }, { 0x56BC, 0xEDC4 }, { 0x56CA, 0xD2A5 }, { 0x56CD, 0xFDEE }, { 0x56D1, 0xF5B6 }, { 0x56DA, 0xE1F6 },
	{ 0x56DB, 0xDECC }, { 0x56DE, 0xFCDE }, { 0x56E0, 0xECD7 }, { 0x56F0, 0xCDDD }, { 0x56F9, 0xD6B7 }, { 0x56FA, 0xCDB3 }, { 0x5703, 0xF8D5 }, { 0x5704, 0xE5D8 },
	{ 0x5708, 0xCFEA }, { 0x570B, 0xCFD0 }, { 0x570D, 0xEACC }, { 0x5712, 0xEAAE }, { 0x5713, 0xEAAD }, { 0x5716, 0xD3F1 }, { 0x5718, 0xD3A5 }, { 0x571F, 0xF7CF },
	{ 0x5728, 0xEEA4 }, { 0x572D, 0xD0A4 }, { 0x5730, 0xF2A2 }, { 0x573B, 0xD0F0 }, { 0x5740, 0xF2A3 }, { 0x5742, 0xF7F8 }, { 0x5747, 0xD0B3 }, { 0x574A, 0xDBA9 },
	{ 0x574D, 0xD3BB }, { 0x574E, 0xCAEC }, { 0x5750, 0xF1A6 }, { 0x5751, 0xCBD5 }, { 0x5761, 0xF7E7 }, { 0x5764, 0xCDDE }, { 0x5766, 0xF7A4 }, { 0x576A, 0xF8C0 },
	{ 0x576E, 0xD3DD }, { 0x5770, 0xCCD0 }, { 0x5775, 0xCFA6 }, { 0x577C, 0xF6F3 }, { 0x5782, 0xE1F7 }, { 0x5788, 0xD3DC }, { 0x578B, 0xFAFE }, { 0x5793, 0xFAA7 },
	{ 0x57A0, 0xEBD9 }, { 0x57A2, 0xCFA7 }, { 0x57A3, 0xEAAF }, { 0x57C3, 0xE4EF }, { 0x57C7, 0xE9B9 }, { 0x57C8, 0xF1D8 }, { 0x57CB, 0xD8D8 }, { 0x57CE, 0xE0F2 },
	{ 0x57DF, 0xE6B4 }, { 0x57E0, 0xDCFC }, { 0x57F0, 0xF3F1 }, { 0x57F4, 0xE3D0 }, { 0x57F7, 0xF2FB }, { 0x57F9, 0xDBC6 }, { 0x57FA, 0xD0F1 }, { 0x57FC, 0xD0F2 },
	{ 0x5800, 0xCFDC }, { 0x5802, 0xD3D1 }, { 0x5805, 0xCCB1 }, { 0x5806, 0xF7D8 }, { 0x5808, 0xCBA8 }, { 0x5809, 0xEBBC }, { 0x580A, 0xE4BE }, { 0x581E, 0xF4DC },
	{ 0x5821, 0xDCC2 }, { 0x5824, 0xF0A7 }, { 0x5827, 0xE6C0 }, { 0x582A, 0xCAED }, { 0x582F, 0xE8EB }, { 0x5830, 0xE5E8 }, { 0x5831, 0xDCC3 }, { 0x5834, 0xEDDE },
	{ 0x5835, 0xD3F2 }, { 0x583A, 0xCCF7 }, { 0x584A, 0xCED4 }, { 0x584B, 0xE7AB }, { 0x584F, 0xCBC3 }, { 0x5851, 0xE1B1 }, { 0x5854, 0xF7B2 }, { 0x5857, 0xD3F3 },
	{ 0x5858, 0xD3D2 }, { 0x585A, 0xF5C0 }, { 0x585E, 0xDFDD }, { 0x5861, 0xEEF3 }, { 0x5862, 0xE7F1 }, { 0x5864, 0xFDB4 }, { 0x5875, 0xF2C8 }, { 0x5879, 0xF3D2 },
	{ 0x587C, 0xEEF4 }, { 0x587E, 0xE2D3 }, { 0x5883, 0xCCD1 }, { 0x5885, 0xDFEA }, { 0x5889, 0xE9BA }, { 0x5893, 0xD9D7 }, { 0x589C, 0xF5CD }, { 0x589E, 0xF1F2 },
	{ 0x589F, 0xFAC7 }, { 0x58A8, 0xD9F8 }, { 0x58A9, 0xD4C2 }, { 0x58AE, 0xF6E5 }, { 0x58B3, 0xDDC5 }, { 0x58BA, 0xE7F2 }, { 0x58BB, 0xEDDF }, { 0x58BE, 0xCACB },
	{ 0x58C1, 0xDBFA }, { 0x58C5, 0xE8B5 }, { 0x58C7, 0xD3A6 }, { 0x58CE, 0xFDB5 }, { 0x58D1, 0xF9C9 }, { 0x58D3, 0xE4E2 }, { 0x58D5, 0xFBBD }, { 0x58D8, 0xD7A4 },
	{ 0x58D9, 0xCEC5 }, { 0x58DE, 0xCED5 }, { 0x58DF, 0xD6E6 }, { 0x58E4, 0xE5BD }, { 0x58EB, 0xDECD }, { 0x58EC, 0xECF3 }, { 0x58EF, 0xEDE0 }, { 0x58F9, 0xECEC },
	{ 0x58FA, 0xFBBE }, { 0x58FB, 0xDFEB }, { 0x58FD, 0xE1F8 }, { 0x590F, 0xF9BE }, { 0x5914, 0xD0F3 }, { 0x5915, 0xE0AA }, { 0x5916, 0xE8E2 }, { 0x5919, 0xE2D4 },
	{ 0x591A, 0xD2FD }, { 0x591C, 0xE5A8 }, { 0x5922, 0xD9D3 }, { 0x5927, 0xD3DE }, { 0x5929, 0xF4B8 }, { 0x592A, 0xF7BC }, { 0x592B, 0xDCFD }, { 0x592D, 0xE8EC },
	{ 0x592E, 0xE4E7 }, { 0x5931, 0xE3F7 }, { 0x5937, 0xECA8 }, { 0x593E, 0xFAF1 }, { 0x5944, 0xE5F2 }, { 0x5947, 0xD0F4 }, { 0x5948, 0xD2AF }, { 0x5949, 0xDCE5 },
	{ 0x594E, 0xD0A5 }, { 0x594F, 0xF1B4 }, { 0x5950, 0xFCB1 }, { 0x5951, 0xCCF8 }, { 0x5954, 0xDDC6 }, { 0x5955, 0xFAD1 }, { 0x5957, 0xF7DF }, { 0x595A, 0xFAA8 },
	{ 0x5960, 0xEEF5 }, { 0x5962, 0xDECE }, { 0x5967, 0xE7F3 }, { 0x596A, 0xF7AC }, { 0x596B, 0xEBC4 }, { 0x596C, 0xEDE1 }, { 0x596D, 0xE0AB }, { 0x596E, 0xDDC7 },
	{ 0x5973, 0xD2B3 }, { 0x5974, 0xD2BF }, { 0x5978, 0xCACC }, { 0x597D, 0xFBBF }, { 0x5982, 0xE5FD }, { 0x5983, 0xDDE5 }, { 0x5984, 0xD8CD }, { 0x598A, 0xECF4 },
	{ 0x5993, 0xD0F5 }, { 0x5996, 0xE8ED }, { 0x5997, 0xD0D2 }, { 0x5999, 0xD9D8 }, { 0x59A5, 0xF6E6 }, { 0x59A8, 0xDBAA }, { 0x59AC, 0xF7E0 }, { 0x59B9, 0xD8D9 },
	{ 0x59BB, 0xF4A3 }, { 0x59BE, 0xF4DD }, { 0x59C3, 0xEFD1 }, { 0x59C6, 0xD9B5 }, { 0x59C9, 0xEDAB }, { 0x59CB, 0xE3B7 }, { 0x59D0, 0xEEBB }, { 0x59D1, 0xCDB4 },
	{ 0x59D3, 0xE0F3 }, { 0x59D4, 0xEACD }, { 0x59D9, 0xECF5 }, { 0x59DA, 0xE8EE }, { 0x59DC, 0xCBA9 }, { 0x59DD, 0xF1AF }, { 0x59E6, 0xCACD }, { 0x59E8, 0xECA9 },
	{ 0x59EA, 0xF2EB }, { 0x59EC, 0xFDEF }, { 0x59EE, 0xF9F3 }, { 0x59F8, 0xE6C1 }, { 0x59FB, 0xECD8 }, { 0x59FF, 0xEDAC }, { 0x5A01, 0xEACE }, { 0x5A03, 0xE8DF },
	{ 0x5A11, 0xDECF }, { 0x5A18, 0xD2A6 }, { 0x5A1B, 0xE7F4 }, { 0x5A1C, 0xD1D6 }, { 0x5A1F, 0xE6C2 }, { 0x5A20, 0xE3E3 }, { 0x5A25, 0xE4B0 }, { 0x5A29, 0xD8B4 },
	{ 0x5A36, 0xF6A5 }, { 0x5A3C, 0xF3DE }, { 0x5A41, 0xD7A5 }, { 0x5A46, 0xF7E8 }, { 0x5A49, 0xE8C6 }, { 0x5A5A, 0xFBE6 }, { 0x5A62, 0xDDE6 }, { 0x5A66, 0xDCFE },
	{ 0x5A92, 0xD8DA }, { 0x5A9A, 0xDAAC }, { 0x5A9B, 0xEAB0 }, { 0x5AA4, 0xE3B8 }, { 0x5AC1, 0xCAAA }, { 0x5AC2, 0xE1F9 }, { 0x5AC4, 0xEAB1 }, { 0x5AC9, 0xF2EC },
	{ 0x5ACC, 0xFAEE }, { 0x5AE1, 0xEED5 }, { 0x5AE6, 0xF9F4 }, { 0x5AE9, 0xD2EC }, { 0x5B05, 0xFBFB }, { 0x5B09, 0xFDF0 }, { 0x5B0B, 0xE0BD }, { 0x5B0C, 0xCEE3 },
	{ 0x5B16, 0xF8C6 }, { 0x5B2A, 0xDEAE }, { 0x5B40, 0xDFC5 }, { 0x5B43, 0xE5BE }, { 0x5B50, 0xEDAD }, { 0x5B51, 0xFAEA }, { 0x5B54, 0xCDEE }, { 0x5B55, 0xEDA6 },
	{ 0x5B57, 0xEDAE }, { 0x5B58, 0xF0ED }, { 0x5B5A, 0xDDA1 }, { 0x5B5C, 0xEDAF }, { 0x5B5D, 0xFCF8 }, { 0x5B5F, 0xD8EB }, { 0x5B63, 0xCCF9 }, { 0x5B64, 0xCDB5 },
	{ 0x5B69, 0xFAA9 }, { 0x5B6B, 0xE1DD }, { 0x5B70, 0xE2D5 }, { 0x5B71, 0xEDCF }, { 0x5B75, 0xDDA2 }, { 0x5B78, 0xF9CA }, { 0x5B7A, 0xEAE8 }, { 0x5B7C, 0xE5ED },
	{ 0x5B85, 0xD3EB }, { 0x5B87, 0xE9D4 }, { 0x5B88, 0xE1FA }, { 0x5B89, 0xE4CC }, { 0x5B8B, 0xE1E4 }, { 0x5B8C, 0xE8C7 }, { 0x5B8F, 0xCEDB }, { 0x5B93, 0xDCD5 },
	{ 0x5B95, 0xF7B5 }, { 0x5B96, 0xFCF3 }, { 0x5B97, 0xF0F3 }, { 0x5B98, 0xCEAF }, { 0x5B99, 0xF1B5 }, { 0x5B9A, 0xEFD2 }, { 0x5B9B, 0xE8C8 }, { 0x5B9C, 0xEBF1 },
	{ 0x5BA2, 0xCBD4 }, { 0x5BA3, 0xE0BE }, { 0x5BA4, 0xE3F8 }, { 0x5BA5, 0xEAE9 }, { 0x5BA6, 0xFCB2 }, { 0x5BAC, 0xE0F4 }, { 0x5BAE, 0xCFE0 }, { 0x5BB0, 0xEEA5 },
	{ 0x5BB3, 0xFAAA }, { 0x5BB4, 0xE6C3 }, { 0x5BB5, 0xE1B2 }, { 0x5BB6, 0xCAAB }, { 0x5BB8, 0xE3E4 }, { 0x5BB9, 0xE9BB }, { 0x5BBF, 0xE2D6 }, { 0x5BC0, 0xF3F2 },
	{ 0x5BC2, 0xEED6 }, { 0x5BC3, 0xEAB2 }, { 0x5BC4, 0xD0F6 }, { 0x5BC5, 0xECD9 }, { 0x5BC6, 0xDACB }, { 0x5BC7, 0xCFA8 }, { 0x5BCC, 0xDDA3 }, { 0x5BD0, 0xD8DB },
	{ 0x5BD2, 0xF9CE }, { 0x5BD3, 0xE9D5 }, { 0x5BD4, 0xE3D1 }, { 0x5BD7, 0xD2BC }, { 0x5BDE, 0xD8AC }, { 0x5BDF, 0xF3CC }, { 0x5BE1, 0xCDFB }, { 0x5BE2, 0xF6D6 },
	{ 0x5BE4, 0xE7F5 }, { 0x5BE5, 0xE8EF }, { 0x5BE6, 0xE3F9 }, { 0x5BE7, 0xD2BB }, { 0x5BE8, 0xF3F3 }, { 0x5BE9, 0xE3FB }, { 0x5BEB, 0xDED0 }, { 0x5BEC, 0xCEB0 },
	{ 0x5BEE, 0xD6F7 }, { 0x5BEF, 0xF1D9 }, { 0x5BF5, 0xF5C1 }, { 0x5BF6, 0xDCC4 }, { 0x5BF8, 0xF5BB }, { 0x5BFA, 0xDED1 }, { 0x5C01, 0xDCE6 }, { 0x5C04, 0xDED2 },
	{ 0x5C07, 0xEDE2 }, { 0x5C08, 0xEEF6 }, { 0x5C09, 0xEACF }, { 0x5C0A, 0xF0EE }, { 0x5C0B, 0xE3FC }, { 0x5C0D, 0xD3DF }, { 0x5C0E, 0xD3F4 }, { 0x5C0F, 0xE1B3 },
	{ 0x5C11, 0xE1B4 }, { 0x5C16, 0xF4D3 }, { 0x5C19, 0xDFC6 }, { 0x5C24, 0xE9D6 }, { 0x5C28, 0xDBAB }, { 0x5C31, 0xF6A6 }, { 0x5C38, 0xE3B9 }, { 0x5C39, 0xEBC5 },
	{ 0x5C3A, 0xF4A9 }, { 0x5C3B, 0xCDB6 }, { 0x5C3C, 0xD2F9 }, { 0x5C3E, 0xDAAD }, { 0x5C3F, 0xD2E3 }, { 0x5C40, 0xCFD1 }, { 0x5C45, 0xCBDC }, { 0x5C46, 0xCCFA },
	{ 0x5C48, 0xCFDD }, { 0x5C4B, 0xE8A9 }, { 0x5C4D, 0xE3BB }, { 0x5C4E, 0xE3BA }, { 0x5C51, 0xE0DA }, { 0x5C55, 0xEEF7 }, { 0x5C5B, 0xDCB3 }, { 0x5C60, 0xD3F5 },
	{ 0x5C62, 0xD7A6 }, { 0x5C64, 0xF6B5 }, { 0x5C65, 0xD7DB }, { 0x5C6C, 0xE1D5 }, { 0x5C6F, 0xD4EA }, { 0x5C71, 0xDFA3 }, { 0x5C79, 0xFDDF }, { 0x5C90, 0xD0F7 },
	{ 0x5C91, 0xEDD4 }, { 0x5CA1, 0xCBAA }, { 0x5CA9, 0xE4DB }, { 0x5CAB, 0xE1FB }, { 0x5CAC, 0xCBA2 }, { 0x5CB1, 0xD3E0 }, { 0x5CB3, 0xE4BF }, { 0x5CB5, 0xFBC0 },
	{ 0x5CB7, 0xDABE }, { 0x5CB8, 0xE4CD }, { 0x5CBA, 0xD6B9 }, { 0x5CBE, 0xEFC0 }, { 0x5CC0, 0xE1FC }, { 0x5CD9, 0xF6B9 }, { 0x5CE0, 0xDFC7 }, { 0x5CE8, 0xE4B1 },
	{ 0x5CEF, 0xDCE7 }, { 0x5CF0, 0xDCE8 }, { 0x5CF4, 0xFAD6 }, { 0x5CF6, 0xD3F6 }, { 0x5CFB, 0xF1DA }, { 0x5CFD, 0xFAF2 }, { 0x5D07, 0xE2FD }, { 0x5D0D, 0xD5CF },
	{ 0x5D0E, 0xD0F8 }, { 0x5D11, 0xCDDF }, { 0x5D14, 0xF5CB }, { 0x5D16, 0xE4F0 }, { 0x5D17, 0xCBAB }, { 0x5D19, 0xD7C4 }, { 0x5D27, 0xE2FE }, { 0x5D29, 0xDDDA },
	{ 0x5D4B, 0xDAAE }, { 0x5D4C, 0xCAEE }, { 0x5D50, 0xD5B9 }, { 0x5D69, 0xE3A1 }, { 0x5D6C, 0xE8E3 }, { 0x5D6F, 0xF3AB }, { 0x5D87, 0xCFA9 }, { 0x5D8B, 0xD3F7 },
	{ 0x5D9D, 0xD4F1 }, { 0x5DA0, 0xCEE4 }, { 0x5DA2, 0xE8F2 }, { 0x5DAA, 0xE5F5 }, { 0x5DB8, 0xE7AE }, { 0x5DBA, 0xD6BA }, { 0x5DBC, 0xDFEC }, { 0x5DBD, 0xE4C0 },
	{ 0x5DCD, 0xE8E4 }, { 0x5DD2, 0xD8B5 }, { 0x5DD6, 0xE4DC }, { 0x5DDD, 0xF4B9 }, { 0x5DDE, 0xF1B6 }, { 0x5DE1, 0xE2DE }, { 0x5DE2, 0xE1B5 }, { 0x5DE5, 0xCDEF },
	{ 0x5DE6, 0xF1A7 }, { 0x5DE7, 0xCEE5 }, { 0x5DE8, 0xCBDD }, { 0x5DEB, 0xD9E3 }, { 0x5DEE, 0xF3AC }, { 0x5DF1, 0xD0F9 }, { 0x5DF2, 0xECAB }, { 0x5DF3, 0xDED3 },
	{ 0x5DF4, 0xF7E9 }, { 0x5DF7, 0xF9F5 }, { 0x5DFD, 0xE1DE }, { 0x5DFE, 0xCBEE }, { 0x5E02, 0xE3BC }, { 0x5E03, 0xF8D6 }, { 0x5E06, 0xDBEE }, { 0x5E0C, 0xFDF1 },
	{ 0x5E11, 0xF7B6 }, { 0x5E16, 0xF4DE }, { 0x5E19, 0xF2ED }, { 0x5E1B, 0xDBD9 }, { 0x5E1D, 0xF0A8 }, { 0x5E25, 0xE1FD }, { 0x5E2B, 0xDED4 }, { 0x5E2D, 0xE0AC },
	{ 0x5E33, 0xEDE3 }, { 0x5E36, 0xD3E1 }, { 0x5E38, 0xDFC8 }, { 0x5E3D, 0xD9B6 }, { 0x5E3F, 0xFDAC }, { 0x5E40, 0xEFD3 }, { 0x5E44, 0xE4C1 }, { 0x5E45, 0xF8EB },
	{ 0x5E47, 0xDBAC }, { 0x5E4C, 0xFCC6 }, { 0x5E55, 0xD8AD }, { 0x5E5F, 0xF6BA }, { 0x5E61, 0xDBDF }, { 0x5E62, 0xD3D3 }, { 0x5E63, 0xF8C7 }, { 0x5E72, 0xCACE },
	{ 0x5E73, 0xF8C1 }, { 0x5E74, 0xD2B4 }, { 0x5E77, 0xDCB4 }, { 0x5E78, 0xFAB9 }, { 0x5E79, 0xCACF }, { 0x5E7B, 0xFCB3 }, { 0x5E7C, 0xEAEA }, { 0x5E7D, 0xEAEB },
	{ 0x5E7E, 0xD0FA }, { 0x5E84, 0xEDE4 }, { 0x5E87, 0xDDE7 }, { 0x5E8A, 0xDFC9 }, { 0x5E8F, 0xDFED }, { 0x5E95, 0xEEBC }, { 0x5E97, 0xEFC1 }, { 0x5E9A, 0xCCD2 },
	{ 0x5E9C, 0xDDA4 }, { 0x5EA0, 0xDFCA }, { 0x5EA6, 0xD3F8 }, { 0x5EA7, 0xF1A8 }, { 0x5EAB, 0xCDB7 }, { 0x5EAD, 0xEFD4 }, { 0x5EB5, 0xE4DD }, { 0x5EB6, 0xDFEE },
	{ 0x5EB7, 0xCBAC }, { 0x5EB8, 0xE9BC }, { 0x5EBE, 0xEAEC }, { 0x5EC2, 0xDFCB }, { 0x5EC8, 0xF9BF }, { 0x5EC9, 0xD6AF }, { 0x5ECA, 0xD5C6 }, { 0x5ED0, 0xCFAA },
	{ 0x5ED3, 0xCEA9 }, { 0x5ED6, 0xD6F8 }, { 0x5EDA, 0xF1B7 }, { 0x5EDB, 0xEEF8 }, { 0x5EDF, 0xD9D9 }, { 0x5EE0, 0xF3DF }, { 0x5EE2, 0xF8C8 }, { 0x5EE3, 0xCEC6 },
	{ 0x5EEC, 0xD5E6 }, { 0x5EF3, 0xF4E6 }, { 0x5EF6, 0xE6C5 }, { 0x5EF7, 0xEFD5 }, { 0x5EFA, 0xCBEF }, { 0x5EFB, 0xFCDF }, { 0x5F01, 0xDCA7 }, { 0x5F04, 0xD6E7 },
	{ 0x5F0A, 0xF8C9 }, { 0x5F0F, 0xE3D2 }, { 0x5F11, 0xE3BD }, { 0x5F13, 0xCFE1 }, { 0x5F14, 0xF0C0 }, { 0x5F15, 0xECDA }, { 0x5F17, 0xDDD7 }, { 0x5F18, 0xFBF0 },
	{ 0x5F1B, 0xECAC }, { 0x5F1F, 0xF0A9 }, { 0x5F26, 0xFAD7 }, { 0x5F27, 0xFBC1 }, { 0x5F29, 0xD2C0 }, { 0x5F31, 0xE5B0 }, { 0x5F35, 0xEDE5 }, { 0x5F3A, 0xCBAD },
	{ 0x5F3C, 0xF9B0 }, { 0x5F48, 0xF7A5 }, { 0x5F4A, 0xCBAE }, { 0x5F4C, 0xDAAF }, { 0x5F4E, 0xD8B6 }, { 0x5F56, 0xD3A7 }, { 0x5F57, 0xFBB2 }, { 0x5F59, 0xFDC4 },
	{ 0x5F5B, 0xECAD }, { 0x5F62, 0xFBA1 }, { 0x5F66, 0xE5E9 }, { 0x5F67, 0xE9EE }, { 0x5F69, 0xF3F4 }, { 0x5F6A, 0xF8F3 }, { 0x5F6B, 0xF0C1 }, { 0x5F6C, 0xDEAF },
	{ 0x5F6D, 0xF8B0 }, { 0x5F70, 0xF3E0 }, { 0x5F71, 0xE7AF }, { 0x5F77, 0xDBAD }, { 0x5F79, 0xE6B5 }, { 0x5F7C, 0xF9A8 }, { 0x5F7F, 0xDDD8 }, { 0x5F80, 0xE8D9 },
	{ 0x5F81, 0xEFD6 }, { 0x5F85, 0xD3E2 }, { 0x5F87, 0xE2DF }, { 0x5F8A, 0xFCE0 }, { 0x5F8B, 0xD7C8 }, { 0x5F8C, 0xFDAD }, { 0x5F90, 0xDFEF }, { 0x5F91, 0xCCD3 },
	{ 0x5F92, 0xD3F9 }, { 0x5F97, 0xD4F0 }, { 0x5F98, 0xDBC7 }, { 0x5F99, 0xDED5 }, { 0x5F9E, 0xF0F4 }, { 0x5FA0, 0xD5D0 }, { 0x5FA1, 0xE5D9 }, { 0x5FA8, 0xFCC7 },
	{ 0x5FA9, 0xDCD6 }, { 0x5FAA, 0xE2E0 }, { 0x5FAE, 0xDAB0 }, { 0x5FB5, 0xF3A3 }, { 0x5FB7, 0xD3EC }, { 0x5FB9, 0xF4CB }, { 0x5FBD, 0xFDC5 }, { 0x5FC3, 0xE3FD },
	{ 0x5FC5, 0xF9B1 }, { 0x5FCC, 0xD0FB }, { 0x5FCD, 0xECDB }, { 0x5FD6, 0xF5BC }, { 0x5FD7, 0xF2A4 }, { 0x5FD8, 0xD8CE }, { 0x5FD9, 0xD8CF }, { 0x5FE0, 0xF5F7 },
	{ 0x5FEB, 0xF6E1 }, { 0x5FF5, 0xD2B7 }, { 0x5FFD, 0xFBEC }, { 0x5FFF, 0xDDC8 }, { 0x600F, 0xE4E8 }, { 0x6012, 0xD2C1 }, { 0x6016, 0xF8D7 }, { 0x601C, 0xD6BB },
	{ 0x601D, 0xDED6 }, { 0x6020, 0xF7BD }, { 0x6021, 0xECAE }, { 0x6025, 0xD0E1 }, { 0x6027, 0xE0F5 }, { 0x6028, 0xEAB3 }, { 0x602A, 0xCED6 }, { 0x602F, 0xCCA5 },
	{ 0x6041, 0xECF6 }, { 0x6042, 0xE2E1 }, { 0x6043, 0xE3BE }, { 0x604D, 0xFCC8 }, { 0x6050, 0xCDF0 }, { 0x6052, 0xF9F6 }, { 0x6055, 0xDFF0 }, { 0x6059, 0xE5BF },
	{ 0x605D, 0xCEBF }, { 0x6062, 0xFCE1 }, { 0x6063, 0xEDB0 }, { 0x6064, 0xFDD1 }, { 0x6065, 0xF6BB }, { 0x6068, 0xF9CF }, { 0x6069, 0xEBDA }, { 0x606A, 0xCAC1 },
	{ 0x606C, 0xD2B8 }, { 0x606D, 0xCDF1 }, { 0x606F, 0xE3D3 }, { 0x6070, 0xFDE6 }, { 0x6085, 0xE6ED }, { 0x6089, 0xE3FA }, { 0x608C, 0xF0AA }, { 0x608D, 0xF9D0 },
	{ 0x6094, 0xFCE2 }, { 0x6096, 0xF8A7 }, { 0x609A, 0xE1E5 }, { 0x609B, 0xEEF9 }, { 0x609F, 0xE7F6 }, { 0x60A0, 0xEAED }, { 0x60A3, 0xFCB4 }, { 0x60A4, 0xF5C2 },
	{ 0x60A7, 0xD7DC }, { 0x60B0, 0xF0F5 }, { 0x60B2, 0xDDE8 }, { 0x60B3, 0xD3ED }, { 0x60B4, 0xF5FC }, { 0x60B6, 0xDABF }, { 0x60B8, 0xCCFB }, { 0x60BC, 0xD3FA },
	{ 0x60BD, 0xF4A4 }, { 0x60C5, 0xEFD7 }, { 0x60C7, 0xD4C3 }, { 0x60D1, 0xFBE3 }, { 0x60DA, 0xFBED }, { 0x60DC, 0xE0AD }, { 0x60DF, 0xEAEE }, { 0x60E0, 0xFBB3 },
	{ 0x60E1, 0xE4C2 }, { 0x60F0, 0xF6E7 }, { 0x60F1, 0xD2DD }, { 0x60F3, 0xDFCC }, { 0x60F6, 0xFCC9 }, { 0x60F9, 0xE5A9 }, { 0x60FA, 0xE0F6 }, { 0x60FB, 0xF6B3 },
	{ 0x6101, 0xE1FE }, { 0x6106, 0xCBF0 }, { 0x6108, 0xEAEF }, { 0x6109, 0xEAF0 }, { 0x610D, 0xDAC0 }, { 0x610E, 0xF8B4 }, { 0x610F, 0xEBF2 }, { 0x6115, 0xE4C3 },
	{ 0x611A, 0xE9D7 }, { 0x611B, 0xE4F1 }, { 0x611F, 0xCAEF }, { 0x6127, 0xCED7 }, { 0x6130, 0xFCCA }, { 0x6134, 0xF3E1 }, { 0x6137, 0xCBC4 }, { 0x613C, 0xE3E5 },
	{ 0x613E, 0xCBC5 }, { 0x613F, 0xEAB4 }, { 0x6142, 0xE9BD }, { 0x6144, 0xD7C9 }, { 0x6147, 0xEBDB }, { 0x6148, 0xEDB1 }, { 0x614A, 0xCCC3 }, { 0x614B, 0xF7BE },
	{ 0x614C, 0xFCCB }, { 0x6153, 0xF8F4 }, { 0x6155, 0xD9B7 }, { 0x6158, 0xF3D3 }, { 0x6159, 0xF3D4 }, { 0x615D, 0xF7E4 }, { 0x615F, 0xF7D1 }, { 0x6162, 0xD8B7 },
	{ 0x6163, 0xCEB1 }, { 0x6164, 0xCAC2 }, { 0x6167, 0xFBB4 }, { 0x6168, 0xCBC6 }, { 0x616B, 0xF0F6 }, { 0x616E, 0xD5E7 }, { 0x6170, 0xEAD0 }, { 0x6176, 0xCCD4 },
	{ 0x6177, 0xCBAF }, { 0x617D, 0xF4AA }, { 0x617E, 0xE9AF }, { 0x6181, 0xF5C3 }, { 0x6182, 0xE9D8 }, { 0x618A, 0xDDE9 }, { 0x618E, 0xF1F3 }, { 0x6190, 0xD5FB },
	{ 0x6191, 0xDEBB }, { 0x6194, 0xF4FB }, { 0x6198, 0xFDF3 }, { 0x6199, 0xFDF2 }, { 0x619A, 0xF7A6 }, { 0x61A4, 0xDDC9 }, { 0x61A7, 0xD4D3 }, { 0x61A9, 0xCCA8 },
	{ 0x61AB, 0xDAC1 }, { 0x61AC, 0xCCD5 }, { 0x61AE, 0xD9E4 }, { 0x61B2, 0xFACA }, { 0x61B6, 0xE5E3 }, { 0x61BA, 0xD3BC }, { 0x61BE, 0xCAF0 }, { 0x61C3, 0xD0C4 },
	{ 0x61C7, 0xCAD0 }, { 0x61C8, 0xFAAB }, { 0x61C9, 0xEBEB }, { 0x61CA, 0xE7F8 }, { 0x61CB, 0xD9E5 }, { 0x61E6, 0xD1D7 }, { 0x61F2, 0xF3A4 }, { 0x61F6, 0xD4FB },
	{ 0x61F7, 0xFCE3 }, { 0x61F8, 0xFAD8 }, { 0x61FA, 0xF3D5 }, { 0x61FC, 0xCFAB }, { 0x61FF, 0xEBF3 }, { 0x6200, 0xD5FC }, { 0x6207, 0xD3D4 }, { 0x6208, 0xCDFC },
	{ 0x620A, 0xD9E6 }, { 0x620C, 0xE2F9 }, { 0x620D, 0xE2A1 }, { 0x620E, 0xEBD4 }, { 0x6210, 0xE0F7 }, { 0x6211, 0xE4B2 }, { 0x6212, 0xCCFC }, { 0x6216, 0xFBE4 },
	{ 0x621A, 0xF4AB }, { 0x621F, 0xD0BD }, { 0x6221, 0xCAF1 }, { 0x622A, 0xEFB8 }, { 0x622E, 0xD7C0 }, { 0x6230, 0xEEFA }, { 0x6231, 0xFDF4 }, { 0x6234, 0xD3E3 },
	{ 0x6236, 0xFBC2 }, { 0x623E, 0xD5E8 }, { 0x623F, 0xDBAE }, { 0x6240, 0xE1B6 }, { 0x6241, 0xF8B7 }, { 0x6247, 0xE0BF }, { 0x6248, 0xFBC3 }, { 0x6249, 0xDDEA },
	{ 0x624B, 0xE2A2 }, { 0x624D, 0xEEA6 }, { 0x6253, 0xF6E8 }, { 0x6258, 0xF6F5 }, { 0x626E, 0xDDCA }, { 0x6271, 0xD0E2 }, { 0x6276, 0xDDA6 }, { 0x6279, 0xDDEB },
	{ 0x627C, 0xE4F9 }, { 0x627F, 0xE3AF }, { 0x6280, 0xD0FC }, { 0x6284, 0xF4FC }, { 0x6289, 0xCCBC }, { 0x628A, 0xF7EA }, { 0x6291, 0xE5E4 }, { 0x6292, 0xDFF1 },
	{ 0x6295, 0xF7E1 }, { 0x6297, 0xF9F7 }, { 0x6298, 0xEFB9 }, { 0x629B, 0xF8D8 }, { 0x62AB, 0xF9A9 }, { 0x62B1, 0xF8D9 }, { 0x62B5, 0xEEBD }, { 0x62B9, 0xD8C6 },
	{ 0x62BC, 0xE4E3 }, { 0x62BD, 0xF5CE }, { 0x62C2, 0xDDD9 }, { 0x62C7, 0xD9E7 }, { 0x62C8, 0xD2B9 }, { 0x62C9, 0xD5C3 }, { 0x62CC, 0xDAE5 }, { 0x62CD, 0xDAD0 },
	{ 0x62CF, 0xD1D9 }, { 0x62D0, 0xCED8 }, { 0x62D2, 0xCBDE }, { 0x62D3, 0xF4AC }, { 0x62D4, 0xDAFB }, { 0x62D6, 0xF6E9 }, { 0x62D7, 0xE8F3 }, { 0x62D8, 0xCFAC },
	{ 0x62D9, 0xF0F0 }, { 0x62DB, 0xF4FD }, { 0x62DC, 0xDBC8 }, { 0x62EC, 0xCEC0 }, { 0x62ED, 0xE3D4 }, { 0x62EE, 0xD1CF }, { 0x62EF, 0xF1F5 }, { 0x62F1, 0xCDF2 },
	{ 0x62F3, 0xCFEB }, { 0x62F7, 0xCDB8 }, { 0x62FE, 0xE3A6 }, { 0x62FF, 0xD1DA }, { 0x6301, 0xF2A5 }, { 0x6307, 0xF2A6 }, { 0x6309, 0xE4CE }, { 0x6311, 0xD3FB },
	{ 0x632B, 0xF1A9 }, { 0x632F, 0xF2C9 }, { 0x633A, 0xEFD8 }, { 0x633B, 0xE6C9 }, { 0x633D, 0xD8B8 }, { 0x633E, 0xFAF3 }, { 0x6349, 0xF3B5 }, { 0x634C, 0xF8A4 },
	{ 0x634F, 0xD1F3 }, { 0x6350, 0xE6C8 }, { 0x6355, 0xF8DA }, { 0x6367, 0xDCE9 }, { 0x6368, 0xDED7 }, { 0x636E, 0xCBDF }, { 0x6372, 0xCFEC }, { 0x6377, 0xF4DF },
	{ 0x637A, 0xD1F4 }, { 0x637B, 0xD2BA }, { 0x637F, 0xDFF2 }, { 0x6383, 0xE1B7 }, { 0x6388, 0xE2A3 }, { 0x6389, 0xD3FC }, { 0x638C, 0xEDE6 }, { 0x6392, 0xDBC9 },
	{ 0x6396, 0xE4FA }, { 0x6398, 0xCFDE }, { 0x639B, 0xCED0 }, { 0x63A0, 0xD5D3 }, { 0x63A1, 0xF3F5 }, { 0x63A2, 0xF7AE }, { 0x63A5, 0xEFC8 }, { 0x63A7, 0xCDF3 },
	{ 0x63A8, 0xF5CF }, { 0x63A9, 0xE5F3 }, { 0x63AA, 0xF0C2 }, { 0x63C0, 0xCAD1 }, { 0x63C4, 0xEAF1 }, { 0x63C6, 0xD0A6 }, { 0x63CF, 0xD9DA }, { 0x63D0, 0xF0AB },
	{ 0x63D6, 0xEBE7 }, { 0x63DA, 0xE5C0 }, { 0x63DB, 0xFCB5 }, { 0x63E1, 0xE4C4 }, { 0x63ED, 0xCCA9 }, { 0x63EE, 0xFDC6 }, { 0x63F4, 0xEAB5 }, { 0x63F6, 0xE5AA },
	{ 0x63F7, 0xDFBA }, { 0x640D, 0xE1DF }, { 0x640F, 0xDAD1 }, { 0x6414, 0xE1B8 }, { 0x6416, 0xE8F4 }, { 0x6417, 0xD3FD }, { 0x641C, 0xE2A4 }, { 0x6422, 0xF2CA },
	{ 0x642C, 0xDAE6 }, { 0x642D, 0xF7B3 }, { 0x643A, 0xFDCD }, { 0x643E, 0xF3B6 }, { 0x6458, 0xEED7 }, { 0x6460, 0xF5C4 }, { 0x6469, 0xD8A4 }, { 0x646F, 0xF2A7 },
	{ 0x6478, 0xD9B8 }, { 0x6479, 0xD9B9 }, { 0x647A, 0xEFC9 }, { 0x6488, 0xD6CE }, { 0x6491, 0xF7CB }, { 0x6492, 0xDFAE }, { 0x6493, 0xE8F5 }, { 0x649A, 0xD2B5 },
	{ 0x649E, 0xD3D5 }, { 0x64A4, 0xF4CC }, { 0x64A5, 0xDAFC }, { 0x64AB, 0xD9E8 }, { 0x64AD, 0xF7EB }, { 0x64AE, 0xF5C9 }, { 0x64B0, 0xF3BC }, { 0x64B2, 0xDAD2 },
	{ 0x64BB, 0xD3B5 }, { 0x64C1, 0xE8B6 }, { 0x64C4, 0xD6CF }, { 0x64C5, 0xF4BA }, { 0x64C7, 0xF7C9 }, { 0x64CA, 0xCCAA }, { 0x64CD, 0xF0C3 }, { 0x64CE, 0xCCD6 },
	{ 0x64D2, 0xD0D3 }, { 0x64D4, 0xD3BD }, { 0x64D8, 0xDBFB }, { 0x64DA, 0xCBE0 }, { 0x64E1, 0xD3E4 }, { 0x64E2, 0xF6F7 }, { 0x64E5, 0xD5BA }, { 0x64E6, 0xF3CD },
	{ 0x64E7, 0xCBE1 }, { 0x64EC, 0xEBF4 }, { 0x64F2, 0xF4AD }, { 0x64F4, 0xFCAA }, { 0x64FA, 0xF7EC }, { 0x64FE, 0xE8F6 }, { 0x6500, 0xDAE7 }, { 0x6504, 0xF7CC },
	{ 0x6518, 0xE5C1 }, { 0x651D, 0xE0EE }, { 0x6523, 0xD5FD }, { 0x652A, 0xCEE6 }, { 0x652B, 0xFCAB }, { 0x652C, 0xD5BB }, { 0x652F, 0xF2A8 }, { 0x6536, 0xE2A5 },
	{ 0x6537, 0xCDB9 }, { 0x6538, 0xEAF2 }, { 0x6539, 0xCBC7 }, { 0x653B, 0xCDF4 }, { 0x653E, 0xDBAF }, { 0x653F, 0xEFD9 }, { 0x6545, 0xCDBA }, { 0x6548, 0xFCF9 },
	{ 0x654D, 0xDFF3 }, { 0x654E, 0xCEE7 }, { 0x654F, 0xDAC2 }, { 0x6551, 0xCFAD }, { 0x6556, 0xE7F9 }, { 0x6557, 0xF8A8 }, { 0x655E, 0xF3E2 }, { 0x6562, 0xCAF2 },
	{ 0x6563, 0xDFA4 }, { 0x6566, 0xD4C4 }, { 0x656C, 0xCCD7 }, { 0x656D, 0xE5C2 }, { 0x6572, 0xCDBB }, { 0x6574, 0xEFDA }, { 0x6575, 0xEED8 }, { 0x6577, 0xDDA7 },
	{ 0x6578, 0xE2A6 }, { 0x657E, 0xE0C0 }, { 0x6582, 0xD6B0 }, { 0x6583, 0xF8CA }, { 0x6585, 0xFCFA }, { 0x6587, 0xD9FE }, { 0x658C, 0xDEB0 }, { 0x6590, 0xDDEC },
	{ 0x6591, 0xDAE8 }, { 0x6597, 0xD4E0 }, { 0x6599, 0xD6F9 }, { 0x659B, 0xCDD7 }, { 0x659C, 0xDED8 }, { 0x659F, 0xF2F8 }, { 0x65A1, 0xE4D6 }, { 0x65A4, 0xD0C5 },
	{ 0x65A5, 0xF4AE }, { 0x65A7, 0xDDA8 }, { 0x65AB, 0xEDC5 }, { 0x65AC, 0xF3D6 }, { 0x65AF, 0xDED9 }, { 0x65B0, 0xE3E6 }, { 0x65B7, 0xD3A8 }, { 0x65B9, 0xDBB0 },
	{ 0x65BC, 0xE5DA }, { 0x65BD, 0xE3BF }, { 0x65C1, 0xDBB1 }, { 0x65C5, 0xD5E9 }, { 0x65CB, 0xE0C1 }, { 0x65CC, 0xEFDB }, { 0x65CF, 0xF0E9 }, { 0x65D2, 0xD7B2 },
	{ 0x65D7, 0xD0FD }, { 0x65E0, 0xD9E9 }, { 0x65E3, 0xD0FE }, { 0x65E5, 0xECED }, { 0x65E6, 0xD3A9 }, { 0x65E8, 0xF2A9 }, { 0x65E9, 0xF0C4 }, { 0x65EC, 0xE2E2 },
	{ 0x65ED, 0xE9EF }, { 0x65F1, 0xF9D1 }, { 0x65F4, 0xE9D9 }, { 0x65FA, 0xE8DA }, { 0x65FB, 0xDAC3 }, { 0x65FC, 0xDAC4 }, { 0x65FD, 0xD4C5 }, { 0x65FF, 0xE7FA },
	{ 0x6606, 0xCDE0 }, { 0x6607, 0xE3B0 }, { 0x6609, 0xDBB2 }, { 0x660A, 0xFBC4 }, { 0x660C, 0xF3E3 }, { 0x660E, 0xD9A5 }, { 0x660F, 0xFBE7 }, { 0x6610, 0xDDCB },
	{ 0x6611, 0xD0D4 }, { 0x6613, 0xE6B6 }, { 0x6614, 0xE0AE }, { 0x6615, 0xFDDA }, { 0x661E, 0xDCB5 }, { 0x661F, 0xE0F8 }, { 0x6620, 0xE7B1 }, { 0x6625, 0xF5F0 },
	{ 0x6627, 0xD8DC }, { 0x6628, 0xEDC6 }, { 0x662D, 0xE1B9 }, { 0x662F, 0xE3C0 }, { 0x6630, 0xF9C0 }, { 0x6631, 0xE9F0 }, { 0x6634, 0xD9DB }, { 0x6636, 0xF3E4 },
	{ 0x663A, 0xDCB6 }, { 0x663B, 0xE4E9 }, { 0x6641, 0xF0C5 }, { 0x6642, 0xE3C1 }, { 0x6643, 0xFCCC }, { 0x6644, 0xFCCD }, { 0x6649, 0xF2CB }, { 0x664B, 0xF2CC },
	{ 0x664F, 0xE4CF }, { 0x6659, 0xF1DB }, { 0x665B, 0xFAD9 }, { 0x665D, 0xF1B8 }, { 0x665E, 0xFDF5 }, { 0x665F, 0xE0F9 }, { 0x6664, 0xE7FB }, { 0x6665, 0xFCB7 },
	{ 0x6666, 0xFCE4 }, { 0x6667, 0xFBC5 }, { 0x6668, 0xE3E7 }, { 0x6669, 0xD8B9 }, { 0x666B, 0xF6F8 }, { 0x666E, 0xDCC5 }, { 0x666F, 0xCCD8 }, { 0x6673, 0xE0AF },
	{ 0x6674, 0xF4E7 }, { 0x6676, 0xEFDC }, { 0x6677, 0xCFFC }, { 0x6678, 0xEFDD }, { 0x667A, 0xF2AA }, { 0x6684, 0xFDBE }, { 0x6687, 0xCAAC }, { 0x6688, 0xFDBB },
	{ 0x6689, 0xFDC7 }, { 0x668E, 0xE7B2 }, { 0x6690, 0xEAD1 }, { 0x6691, 0xDFF4 }, { 0x6696, 0xD1EC }, { 0x6697, 0xE4DE }, { 0x6698, 0xE5C3 }, { 0x669D, 0xD9A6 },
	{ 0x66A0, 0xCDBC }, { 0x66A2, 0xF3E5 }, { 0x66AB, 0xEDD5 }, { 0x66AE, 0xD9BA }, { 0x66B2, 0xEDE7 }, { 0x66B3, 0xFBB5 }, { 0x66B4, 0xF8EC }, { 0x66B9, 0xE0E7 },
	{ 0x66BB, 0xCCD9 }, { 0x66BE, 0xD4C6 }, { 0x66C4, 0xE7A5 }, { 0x66C6, 0xD5F5 }, { 0x66C7, 0xD3BE }, { 0x66C9, 0xFCFB }, { 0x66D6, 0xE4F2 }, { 0x66D9, 0xDFF5 },
	{ 0x66DC, 0xE8F8 }, { 0x66DD, 0xF8ED }, { 0x66E0, 0xCEC7 }, { 0x66E6, 0xFDF6 }, { 0x66F0, 0xE8D8 }, { 0x66F2, 0xCDD8 }, { 0x66F3, 0xE7D6 }, { 0x66F4, 0xCCDA },
	{ 0x66F7, 0xCAE3 }, { 0x66F8, 0xDFF6 }, { 0x66F9, 0xF0C7 }, { 0x66FA, 0xF0C6 }, { 0x66FC, 0xD8BA }, { 0x66FE, 0xF1F4 }, { 0x66FF, 0xF4F0 }, { 0x6700, 0xF5CC },
	{ 0x6703, 0xFCE5 }, { 0x6708, 0xEAC5 }, { 0x6709, 0xEAF3 }, { 0x670B, 0xDDDB }, { 0x670D, 0xDCD7 }, { 0x6714, 0xDEFD }, { 0x6715, 0xF2F9 }, { 0x6717, 0xD5C7 },
	{ 0x671B, 0xD8D0 }, { 0x671D, 0xF0C8 }, { 0x671E, 0xD1A1 }, { 0x671F, 0xD1A2 }, { 0x6726, 0xD9D4 }, { 0x6727, 0xD6E8 }, { 0x6728, 0xD9CA }, { 0x672A, 0xDAB1 },
	{ 0x672B, 0xD8C7 }, { 0x672C, 0xDCE2 }, { 0x672D, 0xF3CE }, { 0x672E, 0xF5F4 }, { 0x6731, 0xF1B9 }, { 0x6734, 0xDAD3 }, { 0x6736, 0xF6EA }, { 0x673A, 0xCFF5 },
	{ 0x673D, 0xFDAE }, { 0x6746, 0xCAD2 }, { 0x6749, 0xDFB4 }, { 0x674E, 0xD7DD }, { 0x674F, 0xFABA }, { 0x6750, 0xEEA7 }, { 0x6751, 0xF5BD }, { 0x6753, 0xF8F5 },
	{ 0x6756, 0xEDE8 }, { 0x675C, 0xD4E1 }, { 0x675E, 0xD1A3 }, { 0x675F, 0xE1D6 }, { 0x676D, 0xF9F8 }, { 0x676F, 0xDBCA }, { 0x6770, 0xCBF9 }, { 0x6771, 0xD4D4 },
	{ 0x6773, 0xD9DC }, { 0x6775, 0xEEBE }, { 0x6777, 0xF7ED }, { 0x677B, 0xD2EE }, { 0x677E, 0xE1E6 }, { 0x677F, 0xF7F9 }, { 0x6787, 0xDDED }, { 0x6789, 0xE8DB },
	{ 0x678B, 0xDBB3 }, { 0x678F, 0xD1F7 }, { 0x6790, 0xE0B0 }, { 0x6793, 0xD4E2 }, { 0x6795, 0xF6D7 }, { 0x6797, 0xD7F9 }, { 0x679A, 0xD8DD }, { 0x679C, 0xCDFD },
	{ 0x679D, 0xF2AB }, { 0x67AF, 0xCDBD }, { 0x67B0, 0xF8C2 }, { 0x67B3, 0xF2AC }, { 0x67B6, 0xCAAD }, { 0x67B7, 0xCAAE }, { 0x67B8, 0xCFAE }, { 0x67BE, 0xE3C2 },
	{ 0x67C4, 0xDCB7 }, { 0x67CF, 0xDBDA }, { 0x67D0, 0xD9BB }, { 0x67D1, 0xCAF3 }, { 0x67D2, 0xF6D3 }, { 0x67D3, 0xE6F8 }, { 0x67D4, 0xEAF5 }, { 0x67DA, 0xEAF6 },
	{ 0x67DD, 0xF6F9 }, { 0x67E9, 0xCFAF }, { 0x67EC, 0xCAD3 }, { 0x67EF, 0xCAAF }, { 0x67F0, 0xD2B0 }, { 0x67F1, 0xF1BA }, { 0x67F3, 0xD7B3 }, { 0x67F4, 0xE3C3 },
	{ 0x67F5, 0xF3FD }, { 0x67F6, 0xDEDA }, { 0x67FB, 0xDEDB }, { 0x67FE, 0xEFDE }, { 0x6812, 0xE2E3 }, { 0x6813, 0xEEFB }, { 0x6816, 0xDFF7 }, { 0x6817, 0xD7CA },
	{ 0x6821, 0xCEE8 }, { 0x6822, 0xDBDB }, { 0x682A, 0xF1BB }, { 0x682F, 0xE9F1 }, { 0x6838, 0xFAB7 }, { 0x6839, 0xD0C6 }, { 0x683C, 0xCCAB }, { 0x683D, 0xEEA8 },
	{ 0x6840, 0xCBFA }, { 0x6841, 0xF9F9 }, { 0x6842, 0xCCFD }, { 0x6843, 0xD3FE }, { 0x6848, 0xE4D0 }, { 0x684E, 0xF2EE }, { 0x6850, 0xD4D5 }, { 0x6851, 0xDFCD },
	{ 0x6853, 0xFCB8 }, { 0x6854, 0xD1D0 }, { 0x686D, 0xF2CD }, { 0x6876, 0xF7D2 }, { 0x687F, 0xCAD4 }, { 0x6881, 0xD5D9 }, { 0x6885, 0xD8DE }, { 0x688F, 0xCDD9 },
	{ 0x6893, 0xEEA9 }, { 0x6894, 0xF6BC }, { 0x6897, 0xCCDB }, { 0x689D, 0xF0C9 }, { 0x689F, 0xFCFC }, { 0x68A1, 0xE8C9 }, { 0x68A2, 0xF4FE }, { 0x68A7, 0xE7FC },
	{ 0x68A8, 0xD7DE }, { 0x68AD, 0xDEDC }, { 0x68AF, 0xF0AC }, { 0x68B0, 0xCCFE }, { 0x68B1, 0xCDE1 }, { 0x68B3, 0xE1BA }, { 0x68B5, 0xDBEF }, { 0x68B6, 0xDAB2 },
	{ 0x68C4, 0xD1A5 }, { 0x68C5, 0xDCB8 }, { 0x68C9, 0xD8F6 }, { 0x68CB, 0xD1A4 }, { 0x68CD, 0xCDE2 }, { 0x68D2, 0xDCEA }, { 0x68D5, 0xF0F7 }, { 0x68D7, 0xF0CA },
	{ 0x68D8, 0xD0BE }, { 0x68DA, 0xDDDC }, { 0x68DF, 0xD4D6 }, { 0x68E0, 0xD3D6 }, { 0x68E7, 0xEDD0 }, { 0x68E8, 0xCDA1 }, { 0x68EE, 0xDFB5 }, { 0x68F2, 0xDFF8 },
	{ 0x68F9, 0xD4A1 }, { 0x68FA, 0xCEB2 }, { 0x6900, 0xE8CA }, { 0x6905, 0xEBF5 }, { 0x690D, 0xE3D5 }, { 0x690E, 0xF5D0 }, { 0x6912, 0xF5A1 }, { 0x6927, 0xD9A7 },
	{ 0x6930, 0xE5AB }, { 0x693D, 0xE6CB }, { 0x693F, 0xF5F1 }, { 0x694A, 0xE5C5 }, { 0x6953, 0xF9A3 }, { 0x6954, 0xE0DB }, { 0x6955, 0xF6EB }, { 0x6957, 0xCBF1 },
	{ 0x6959, 0xD9EA }, { 0x695A, 0xF5A2 }, { 0x695E, 0xD7D1 }, { 0x6960, 0xD1F8 }, { 0x6961, 0xEAF8 }, { 0x6962, 0xEAF9 }, { 0x6963, 0xDAB3 }, { 0x6968, 0xEFDF },
	{ 0x696B, 0xF1EF }, { 0x696D, 0xE5F6 }, { 0x696E, 0xEEBF }, { 0x696F, 0xE2E4 }, { 0x6975, 0xD0BF }, { 0x6977, 0xFAAC }, { 0x6978, 0xF5D1 }, { 0x6979, 0xE7B3 },
	{ 0x6995, 0xE9BE }, { 0x699B, 0xF2CE }, { 0x699C, 0xDBB4 }, { 0x69A5, 0xFCCE }, { 0x69A7, 0xDDEE }, { 0x69AE, 0xE7B4 }, { 0x69B4, 0xD7B4 }, { 0x69BB, 0xF7B4 },
	{ 0x69C1, 0xCDBE }, { 0x69C3, 0xDAE9 }, { 0x69CB, 0xCFB0 }, { 0x69CC, 0xF7D9 }, { 0x69CD, 0xF3E6 }, { 0x69D0, 0xCED9 }, { 0x69E8, 0xCEAA }, { 0x69EA, 0xCBC8 },
	{ 0x69FB, 0xD0A7 }, { 0x69FD, 0xF0CB }, { 0x69FF, 0xD0C7 }, { 0x6A02, 0xE4C5 }, { 0x6A0A, 0xDBE0 }, { 0x6A11, 0xD5DA }, { 0x6A13, 0xD7A7 }, { 0x6A17, 0xEEC0 },
	{ 0x6A19, 0xF8F6 }, { 0x6A1E, 0xF5D2 }, { 0x6A1F, 0xEDE9 }, { 0x6A21, 0xD9BC }, { 0x6A23, 0xE5C6 }, { 0x6A35, 0xF5A3 }, { 0x6A38, 0xDAD4 }, { 0x6A39, 0xE2A7 },
	{ 0x6A3A, 0xFBFC }, { 0x6A3D, 0xF1DC }, { 0x6A44, 0xCAF4 }, { 0x6A48, 0xE8FA }, { 0x6A4B, 0xCEE9 }, { 0x6A52, 0xE9F8 }, { 0x6A53, 0xE2E5 }, { 0x6A58, 0xD0B9 },
	{ 0x6A59, 0xD4F2 }, { 0x6A5F, 0xD1A6 }, { 0x6A61, 0xDFCE }, { 0x6A6B, 0xFCF4 }, { 0x6A80, 0xD3AA }, { 0x6A84, 0xCCAC }, { 0x6A89, 0xEFE0 }, { 0x6A8D, 0xE5E5 },
	{ 0x6A8E, 0xD0D5 }, { 0x6A97, 0xDBFC }, { 0x6A9C, 0xFCE6 }, { 0x6AA2, 0xCBFE }, { 0x6AA3, 0xEDEA }, { 0x6AB3, 0xDEB1 }, { 0x6ABB, 0xF9E3 }, { 0x6AC2, 0xD4A2 },
	{ 0x6AC3, 0xCFF6 }, { 0x6AD3, 0xD6D0 }, { 0x6ADA, 0xD5EA }, { 0x6ADB, 0xF1EE }, { 0x6AF6, 0xFACB }, { 0x6AFB, 0xE5A1 }, { 0x6B04, 0xD5B1 }, { 0x6B0A, 0xCFED },
	{ 0x6B0C, 0xEDEB }, { 0x6B12, 0xD5B2 }, { 0x6B16, 0xD5BC }, { 0x6B20, 0xFDE2 }, { 0x6B21, 0xF3AD }, { 0x6B23, 0xFDDB }, { 0x6B32, 0xE9B0 }, { 0x6B3A, 0xD1A7 },
	{ 0x6B3D, 0xFDE3 }, { 0x6B3E, 0xCEB3 }, { 0x6B46, 0xFDE4 }, { 0x6B47, 0xFACE }, { 0x6B4C, 0xCAB0 }, { 0x6B4E, 0xF7A7 }, { 0x6B50, 0xCFB1 }, { 0x6B5F, 0xE6A2 },
	{ 0x6B61, 0xFCB6 }, { 0x6B62, 0xF2AD }, { 0x6B63, 0xEFE1 }, { 0x6B64, 0xF3AE }, { 0x6B65, 0xDCC6 }, { 0x6B66, 0xD9EB }, { 0x6B6A, 0xE8E0 }, { 0x6B72, 0xE1A8 },
	{ 0x6B77, 0xD5F6 }, { 0x6B78, 0xCFFD }, { 0x6B7B, 0xDEDD }, { 0x6B7F, 0xD9D1 }, { 0x6B83, 0xE4EA }, { 0x6B84, 0xF2CF }, { 0x6B86, 0xF7BF }, { 0x6B89, 0xE2E6 },
	{ 0x6B8A, 0xE2A8 }, { 0x6B96, 0xE3D6 }, { 0x6B98, 0xEDD1 }, { 0x6B9E, 0xE9F9 }, { 0x6BAE, 0xD6B1 }, { 0x6BAF, 0xDEB2 }, { 0x6BB2, 0xE0E8 }, { 0x6BB5, 0xD3AB },
	{ 0x6BB7, 0xEBDC }, { 0x6BBA, 0xDFAF }, { 0x6BBC, 0xCAC3 }, { 0x6BBF, 0xEEFC }, { 0x6BC1, 0xFDC3 }, { 0x6BC5, 0xEBF6 }, { 0x6BC6, 0xCFB2 }, { 0x6BCB, 0xD9EC },
	{ 0x6BCD, 0xD9BD }, { 0x6BCF, 0xD8DF }, { 0x6BD2, 0xD4B8 }, { 0x6BD3, 0xEBBE }, { 0x6BD4, 0xDDEF }, { 0x6BD6, 0xDDF0 }, { 0x6BD7, 0xDDF1 }, { 0x6BD8, 0xDDF2 },
	{ 0x6BDB, 0xD9BE }, { 0x6BEB, 0xFBC6 }, { 0x6BEC, 0xCFB3 }, { 0x6C08, 0xEEFD }, { 0x6C0F, 0xE4AB }, { 0x6C11, 0xDAC5 }, { 0x6C13, 0xD8EC }, { 0x6C23, 0xD1A8 },
	{ 0x6C34, 0xE2A9 }, { 0x6C37, 0xDEBC }, { 0x6C38, 0xE7B5 }, { 0x6C3E, 0xDBF0 }, { 0x6C40, 0xEFE2 }, { 0x6C41, 0xF1F0 }, { 0x6C42, 0xCFB4 }, { 0x6C4E, 0xDBF1 },
	{ 0x6C50, 0xE0B1 }, { 0x6C55, 0xDFA5 }, { 0x6C57, 0xF9D2 }, { 0x6C5A, 0xE7FD }, { 0x6C5D, 0xE6A3 }, { 0x6C5E, 0xFBF1 }, { 0x6C5F, 0xCBB0 }, { 0x6C60, 0xF2AE },
	{ 0x6C68, 0xCDE7 }, { 0x6C6A, 0xE8DC }, { 0x6C6D, 0xE7D7 }, { 0x6C70, 0xF7C0 }, { 0x6C72, 0xD0E3 }, { 0x6C76, 0xDAA1 }, { 0x6C7A, 0xCCBD }, { 0x6C7D, 0xD1A9 },
	{ 0x6C7E, 0xDDCC }, { 0x6C81, 0xE3FE }, { 0x6C82, 0xD1AA }, { 0x6C83, 0xE8AA }, { 0x6C85, 0xEAB6 }, { 0x6C86, 0xF9FA }, { 0x6C87, 0xE6CC }, { 0x6C88, 0xF6D8 },
	{ 0x6C8C, 0xD4C7 }, { 0x6C90, 0xD9CB }, { 0x6C92, 0xD9D2 }, { 0x6C93, 0xD3CB }, { 0x6C94, 0xD8F7 }, { 0x6C95, 0xDAA9 }, { 0x6C96, 0xF5F8 }, { 0x6C99, 0xDEDE },
	{ 0x6C9A, 0xF2AF }, { 0x6C9B, 0xF8A9 }, { 0x6CAB, 0xD8C8 }, { 0x6CAE, 0xEEC1 }, { 0x6CB3, 0xF9C1 }, { 0x6CB8, 0xDDF3 }, { 0x6CB9, 0xEAFA }, { 0x6CBB, 0xF6BD },
	{ 0x6CBC, 0xE1BB }, { 0x6CBD, 0xCDBF }, { 0x6CBE, 0xF4D4 }, { 0x6CBF, 0xE6CD }, { 0x6CC1, 0xFCCF }, { 0x6CC2, 0xFBA2 }, { 0x6CC4, 0xE0DC }, { 0x6CC9, 0xF4BB },
	{ 0x6CCA, 0xDAD5 }, { 0x6CCC, 0xF9B2 }, { 0x6CD3, 0xFBF2 }, { 0x6CD5, 0xDBF6 }, { 0x6CD7, 0xDEDF }, { 0x6CDB, 0xDBF2 }, { 0x6CE1, 0xF8DC }, { 0x6CE2, 0xF7EE },
	{ 0x6CE3, 0xEBE8 }, { 0x6CE5, 0xD2FA }, { 0x6CE8, 0xF1BC }, { 0x6CEB, 0xFADA }, { 0x6CEE, 0xDAEA }, { 0x6CEF, 0xDAC6 }, { 0x6CF0, 0xF7C1 }, { 0x6CF3, 0xE7B6 },
	{ 0x6D0B, 0xE5C7 }, { 0x6D0C, 0xD6AC }, { 0x6D11, 0xDCC7 }, { 0x6D17, 0xE1A9 }, { 0x6D19, 0xE2AA }, { 0x6D1B, 0xD5A6 }, { 0x6D1E, 0xD4D7 }, { 0x6D25, 0xF2D0 },
	{ 0x6D27, 0xEAFB }, { 0x6D29, 0xE0DD }, { 0x6D2A, 0xFBF3 }, { 0x6D32, 0xF1BD }, { 0x6D35, 0xE2E7 }, { 0x6D36, 0xFDD7 }, { 0x6D38, 0xCEC8 }, { 0x6D39, 0xEAB7 },
	{ 0x6D3B, 0xFCC0 }, { 0x6D3D, 0xFDE7 }, { 0x6D3E, 0xF7EF }, { 0x6D41, 0xD7B5 }, { 0x6D59, 0xEFBA }, { 0x6D5A, 0xF1DD }, { 0x6D5C, 0xDEB3 }, { 0x6D63, 0xE8CB },
	{ 0x6D66, 0xF8DD }, { 0x6D69, 0xFBC7 }, { 0x6D6A, 0xD5C8 }, { 0x6D6C, 0xD7DF }, { 0x6D6E, 0xDDA9 }, { 0x6D74, 0xE9B1 }, { 0x6D77, 0xFAAD }, { 0x6D78, 0xF6D9 },
	{ 0x6D79, 0xFAF4 }, { 0x6D7F, 0xF8AA }, { 0x6D85, 0xE6EE }, { 0x6D87, 0xCCDC }, { 0x6D88, 0xE1BC }, { 0x6D89, 0xE0EF }, { 0x6D8C, 0xE9BF }, { 0x6D8D, 0xFCFD },
	{ 0x6D8E, 0xE6CE }, { 0x6D91, 0xE1D7 }, { 0x6D93, 0xE6CF }, { 0x6D95, 0xF4F1 }, { 0x6DAF, 0xE4F3 }, { 0x6DB2, 0xE4FB }, { 0x6DB5, 0xF9E4 }, { 0x6DC0, 0xEFE3 },
	{ 0x6DC3, 0xCFEE }, { 0x6DC4, 0xF6BE }, { 0x6DC5, 0xE0B2 }, { 0x6DC6, 0xFCFE }, { 0x6DC7, 0xD1AB }, { 0x6DCB, 0xD7FA }, { 0x6DCF, 0xFBC8 }, { 0x6DD1, 0xE2D7 },
	{ 0x6DD8, 0xD4A3 }, { 0x6DD9, 0xF0F8 }, { 0x6DDA, 0xD7A8 }, { 0x6DDE, 0xE1E7 }, { 0x6DE1, 0xD3BF }, { 0x6DE8, 0xEFE4 }, { 0x6DEA, 0xD7C5 }, { 0x6DEB, 0xEBE2 },
	{ 0x6DEE, 0xFCE7 }, { 0x6DF1, 0xE4A2 }, { 0x6DF3, 0xE2E8 }, { 0x6DF5, 0xE6D0 }, { 0x6DF7, 0xFBE8 }, { 0x6DF8, 0xF4E8 }, { 0x6DF9, 0xE5F4 }, { 0x6DFA, 0xF4BC },
	{ 0x6DFB, 0xF4D5 }, { 0x6E17, 0xDFB6 }, { 0x6E19, 0xFCB9 }, { 0x6E1A, 0xEEC2 }, { 0x6E1B, 0xCAF5 }, { 0x6E1F, 0xEFE5 }, { 0x6E20, 0xCBE2 }, { 0x6E21, 0xD4A4 },
	{ 0x6E23, 0xDEE0 }, { 0x6E24, 0xDAFD }, { 0x6E25, 0xE4C6 }, { 0x6E26, 0xE8BE }, { 0x6E2B, 0xE0DE }, { 0x6E2C, 0xF6B4 }, { 0x6E2D, 0xEAD2 }, { 0x6E2F, 0xF9FB },
	{ 0x6E32, 0xE0C2 }, { 0x6E34, 0xCAE4 }, { 0x6E36, 0xE7B7 }, { 0x6E38, 0xEAFD }, { 0x6E3A, 0xD9DD }, { 0x6E3C, 0xDAB4 }, { 0x6E3D, 0xEEAA }, { 0x6E3E, 0xFBE9 },
	{ 0x6E43, 0xDBCB }, { 0x6E44, 0xDAB5 }, { 0x6E4A, 0xF1BE }, { 0x6E4D, 0xD3AC }, { 0x6E56, 0xFBC9 }, { 0x6E58, 0xDFCF }, { 0x6E5B, 0xD3C0 }, { 0x6E5C, 0xE3D7 },
	{ 0x6E5E, 0xEFE6 }, { 0x6E5F, 0xFCD0 }, { 0x6E67, 0xE9C0 }, { 0x6E6B, 0xF5D3 }, { 0x6E6E, 0xECDC }, { 0x6E6F, 0xF7B7 }, { 0x6E72, 0xEAB8 }, { 0x6E73, 0xD1F9 },
	{ 0x6E7A, 0xDCC8 }, { 0x6E90, 0xEAB9 }, { 0x6E96, 0xF1DE }, { 0x6E9C, 0xD7B6 }, { 0x6E9D, 0xCFB5 }, { 0x6E9F, 0xD9A8 }, { 0x6EA2, 0xECEE }, { 0x6EA5, 0xDDAA },
	{ 0x6EAA, 0xCDA2 }, { 0x6EAB, 0xE8AE }, { 0x6EAF, 0xE1BD }, { 0x6EB1, 0xF2D1 }, { 0x6EB6, 0xE9C1 }, { 0x6EBA, 0xD2FC }, { 0x6EC2, 0xDBB5 }, { 0x6EC4, 0xF3E7 },
	{ 0x6EC5, 0xD8FE }, { 0x6EC9, 0xFCD1 }, { 0x6ECB, 0xEDB2 }, { 0x6ECC, 0xF4AF }, { 0x6ECE, 0xFBA3 }, { 0x6ED1, 0xFCC1 }, { 0x6ED3, 0xEEAB }, { 0x6ED4, 0xD4A5 },
	{ 0x6EEF, 0xF4F2 }, { 0x6EF4, 0xEED9 }, { 0x6EF8, 0xFBCA }, { 0x6EFE, 0xCDE3 }, { 0x6EFF, 0xD8BB }, { 0x6F01, 0xE5DB }, { 0x6F02, 0xF8F7 }, { 0x6F06, 0xF6D4 },
	{ 0x6F0F, 0xD7A9 }, { 0x6F11, 0xCBC9 }, { 0x6F14, 0xE6D1 }, { 0x6F15, 0xF0CC }, { 0x6F20, 0xD8AE }, { 0x6F22, 0xF9D3 }, { 0x6F23, 0xD5FE }, { 0x6F2B, 0xD8BC },
	{ 0x6F2C, 0xF2B0 }, { 0x6F31, 0xE2AB }, { 0x6F32, 0xF3E8 }, { 0x6F38, 0xEFC2 }, { 0x6F3F, 0xEDEC }, { 0x6F41, 0xE7B8 }, { 0x6F51, 0xDAFE }, { 0x6F54, 0xCCBE },
	{ 0x6F57, 0xF2FC }, { 0x6F58, 0xDAEB }, { 0x6F5A, 0xE2D8 }, { 0x6F5B, 0xEDD6 }, { 0x6F5E, 0xD6D1 }, { 0x6F5F, 0xE0B3 }, { 0x6F62, 0xFCD2 }, { 0x6F64, 0xEBC8 },
	{ 0x6F6D, 0xD3C1 }, { 0x6F6E, 0xF0CD }, { 0x6F70, 0xCFF7 }, { 0x6F7A, 0xEDD2 }, { 0x6F7C, 0xD4D8 }, { 0x6F7D, 0xDCC9 }, { 0x6F7E, 0xD7F1 }, { 0x6F81, 0xDFBB },
	{ 0x6F84, 0xF3A5 }, { 0x6F88, 0xF4CD }, { 0x6F8D, 0xF1BF }, { 0x6F8E, 0xF8B1 }, { 0x6F90, 0xE9FA }, { 0x6F94, 0xFBCB }, { 0x6F97, 0xCAD5 }, { 0x6FA3, 0xF9D4 },
	{ 0x6FA4, 0xF7CA }, { 0x6FA7, 0xD6C8 }, { 0x6FAE, 0xFCE8 }, { 0x6FAF, 0xF3BD }, { 0x6FB1, 0xEEFE }, { 0x6FB3, 0xE7FE }, { 0x6FB9, 0xD3C2 }, { 0x6FBE, 0xD3B6 },
	{ 0x6FC0, 0xCCAD }, { 0x6FC1, 0xF6FA }, { 0x6FC2, 0xD6B2 }, { 0x6FC3, 0xD2D8 }, { 0x6FCA, 0xE7D8 }, { 0x6FD5, 0xE3A5 }, { 0x6FDA, 0xE7B9 }, { 0x6FDF, 0xF0AD },
	{ 0x6FE0, 0xFBCC }, { 0x6FE1, 0xEBA1 }, { 0x6FE4, 0xD4A6 }, { 0x6FE9, 0xFBCD }, { 0x6FEB, 0xD5BD }, { 0x6FEC, 0xF1DF }, { 0x6FEF, 0xF6FB }, { 0x6FF1, 0xDEB4 },
	{ 0x6FFE, 0xD5EB }, { 0x7001, 0xE5C8 }, { 0x7005, 0xFBA4 }, { 0x7006, 0xD4B9 }, { 0x7009, 0xDEE1 }, { 0x700B, 0xE4A3 }, { 0x700F, 0xD7B7 }, { 0x7011, 0xF8EE },
	{ 0x7015, 0xDEB5 }, { 0x7018, 0xD6D2 }, { 0x701A, 0xF9D5 }, { 0x701B, 0xE7BA }, { 0x701C, 0xEBD5 }, { 0x701D, 0xD5F7 }, { 0x701E, 0xEFE7 }, { 0x701F, 0xE1BE },
	{ 0x7023, 0xFAAE }, { 0x7027, 0xD6E9 }, { 0x7028, 0xD6EE }, { 0x702F, 0xE7BB }, { 0x7037, 0xECCB }, { 0x703E, 0xD5B3 }, { 0x704C, 0xCEB4 }, { 0x7050, 0xFBA5 },
	{ 0x7051, 0xE1EE }, { 0x7058, 0xF7A8 }, { 0x705D, 0xFBCE }, { 0x7063, 0xD8BD }, { 0x706B, 0xFBFD }, { 0x7070, 0xFCE9 }, { 0x7078, 0xCFB6 }, { 0x707C, 0xEDC7 },
	{ 0x707D, 0xEEAC }, { 0x7085, 0xCCDD }, { 0x708A, 0xF6A7 }, { 0x708E, 0xE6FA }, { 0x7092, 0xF5A4 }, { 0x7098, 0xFDDC }, { 0x7099, 0xEDB3 }, { 0x709A, 0xCEC9 },
	{ 0x70A1, 0xEFE8 }, { 0x70A4, 0xE1BF }, { 0x70AB, 0xFADB }, { 0x70AC, 0xCBE3 }, { 0x70AD, 0xF7A9 }, { 0x70AF, 0xFBA6 }, { 0x70B3, 0xDCB9 }, { 0x70B7, 0xF1C0 },
	{ 0x70B8, 0xEDC8 }, { 0x70B9, 0xEFC3 }, { 0x70C8, 0xD6AD }, { 0x70CB, 0xFDCE }, { 0x70CF, 0xE8A1 }, { 0x70D8, 0xFBF4 }, { 0x70D9, 0xD5A7 }, { 0x70DD, 0xF1F6 },
	{ 0x70DF, 0xE6D3 }, { 0x70F1, 0xCCDE }, { 0x70F9, 0xF8B2 }, { 0x70FD, 0xDCEB }, { 0x7104, 0xFDB6 }, { 0x7109, 0xE5EA }, { 0x710C, 0xF1E0 }, { 0x7119, 0xDBCC },
	{ 0x711A, 0xDDCD }, { 0x711E, 0xD4C8 }, { 0x7121, 0xD9ED }, { 0x7126, 0xF5A5 }, { 0x7130, 0xE6FB }, { 0x7136, 0xE6D4 }, { 0x7147, 0xFDC8 }, { 0x7149, 0xD6A1 },
	{ 0x714A, 0xFDBF }, { 0x714C, 0xFCD3 }, { 0x714E, 0xEFA1 }, { 0x7150, 0xE7BC }, { 0x7156, 0xD1EE }, { 0x7159, 0xE6D5 }, { 0x715C, 0xE9F2 }, { 0x715E, 0xDFB0 },
	{ 0x7164, 0xD8E0 }, { 0x7165, 0xFCBA }, { 0x7166, 0xFDAF }, { 0x7167, 0xF0CE }, { 0x7169, 0xDBE1 }, { 0x716C, 0xE5C9 }, { 0x716E, 0xEDB4 }, { 0x717D, 0xE0C3 },
	{ 0x7184, 0xE3D8 }, { 0x7189, 0xE9FB }, { 0x718A, 0xEAA8 }, { 0x718F, 0xFDB7 }, { 0x7192, 0xFBA7 }, { 0x7194, 0xE9C2 }, { 0x7199, 0xFDF7 }, { 0x719F, 0xE2D9 },
	{ 0x71A2, 0xDCEC }, { 0x71AC, 0xE8A2 }, { 0x71B1, 0xE6F0 }, { 0x71B9, 0xFDF8 }, { 0x71BA, 0xFDF9 }, { 0x71BE, 0xF6BF }, { 0x71C1, 0xE7A7 }, { 0x71C3, 0xE6D7 },
	{ 0x71C8, 0xD4F3 }, { 0x71C9, 0xD4C9 }, { 0x71CE, 0xD6FA }, { 0x71D0, 0xD7F2 }, { 0x71D2, 0xE1C0 }, { 0x71D4, 0xDBE2 }, { 0x71D5, 0xE6D8 }, { 0x71DF, 0xE7BD },
	{ 0x71E5, 0xF0CF }, { 0x71E6, 0xF3BE }, { 0x71E7, 0xE2AC }, { 0x71ED, 0xF5B7 }, { 0x71EE, 0xE0F0 }, { 0x71FB, 0xFDB8 }, { 0x71FC, 0xE3E8 }, { 0x71FE, 0xD4A7 },
	{ 0x71FF, 0xE8FC }, { 0x7200, 0xFAD2 }, { 0x7206, 0xF8EF }, { 0x7210, 0xD6D3 }, { 0x721B, 0xD5B4 }, { 0x722A, 0xF0D0 }, { 0x722C, 0xF7F0 }, { 0x722D, 0xEEB3 },
	{ 0x7230, 0xEABA }, { 0x7232, 0xEAD3 }, { 0x7235, 0xEDC9 }, { 0x7236, 0xDDAB }, { 0x723A, 0xE5AC }, { 0x723B, 0xFDA1 }, { 0x723D, 0xDFD0 }, { 0x723E, 0xECB3 },
	{ 0x7240, 0xDFD1 }, { 0x7246, 0xEDED }, { 0x7247, 0xF8B8 }, { 0x7248, 0xF7FA }, { 0x724C, 0xF8AB }, { 0x7252, 0xF4E0 }, { 0x7258, 0xD4BA }, { 0x7259, 0xE4B3 },
	{ 0x725B, 0xE9DA }, { 0x725D, 0xDEB6 }, { 0x725F, 0xD9BF }, { 0x7261, 0xD9C0 }, { 0x7262, 0xD6EF }, { 0x7267, 0xD9CC }, { 0x7269, 0xDAAA }, { 0x7272, 0xDFE5 },
	{ 0x7279, 0xF7E5 }, { 0x727D, 0xCCB2 }, { 0x7280, 0xDFF9 }, { 0x7281, 0xD7E0 }, { 0x72A2, 0xD4BB }, { 0x72A7, 0xFDFA }, { 0x72AC, 0xCCB3 }, { 0x72AF, 0xDBF3 },
	{ 0x72C0, 0xDFD2 }, { 0x72C2, 0xCECA }, { 0x72C4, 0xEEDA }, { 0x72CE, 0xE4E4 }, { 0x72D0, 0xFBCF }, { 0x72D7, 0xCFB7 }, { 0x72D9, 0xEEC3 }, { 0x72E1, 0xCEEA },
	{ 0x72E9, 0xE2AD }, { 0x72F8, 0xD7E1 }, { 0x72F9, 0xFAF5 }, { 0x72FC, 0xD5C9 }, { 0x72FD, 0xF8AC }, { 0x730A, 0xE7D9 }, { 0x7316, 0xF3E9 }, { 0x731B, 0xD8ED },
	{ 0x731C, 0xE3C4 }, { 0x731D, 0xF0F1 }, { 0x7325, 0xE8E5 }, { 0x7329, 0xE0FA }, { 0x732A, 0xEEC4 }, { 0x732B, 0xD9DE }, { 0x7336, 0xEBA2 }, { 0x7337, 0xEBA3 },
	{ 0x733E, 0xFCC2 }, { 0x733F, 0xEABB }, { 0x7344, 0xE8AB }, { 0x7345, 0xDEE2 }, { 0x7350, 0xEDEF }, { 0x7352, 0xE8A3 }, { 0x7357, 0xCFF1 }, { 0x7368, 0xD4BC },
	{ 0x736A, 0xFCEA }, { 0x7370, 0xE7BE }, { 0x7372, 0xFCF2 }, { 0x7375, 0xD6B4 }, { 0x7378, 0xE2AE }, { 0x737A, 0xD3B7 }, { 0x737B, 0xFACC }, { 0x7384, 0xFADC },
	{ 0x7386, 0xEDB5 }, { 0x7387, 0xE1E3 }, { 0x7389, 0xE8AC }, { 0x738B, 0xE8DD }, { 0x738E, 0xEFE9 }, { 0x7394, 0xF4BD }, { 0x7396, 0xCFB8 }, { 0x7397, 0xE9DB },
	{ 0x7398, 0xD1AC }, { 0x739F, 0xDAC7 }, { 0x73A7, 0xEBC9 }, { 0x73A9, 0xE8CC }, { 0x73AD, 0xDEB7 }, { 0x73B2, 0xD6BC }, { 0x73B3, 0xD3E5 }, { 0x73B9, 0xFADD },
	{ 0x73C0, 0xDAD6 }, { 0x73C2, 0xCAB1 }, { 0x73C9, 0xDAC8 }, { 0x73CA, 0xDFA6 }, { 0x73CC, 0xF9B3 }, { 0x73CD, 0xF2D2 }, { 0x73CF, 0xCAC4 }, { 0x73D6, 0xCECB },
	{ 0x73D9, 0xCDF5 }, { 0x73DD, 0xFDB0 }, { 0x73DE, 0xD5A8 }, { 0x73E0, 0xF1C1 }, { 0x73E3, 0xE2E9 }, { 0x73E4, 0xDCCA }, { 0x73E5, 0xECB4 }, { 0x73E6, 0xFAC0 },
	{ 0x73E9, 0xFBA8 }, { 0x73EA, 0xD0A8 }, { 0x73ED, 0xDAEC }, { 0x73F7, 0xD9EE }, { 0x73F9, 0xE0FB }, { 0x73FD, 0xEFEA }, { 0x73FE, 0xFADE }, { 0x7401, 0xE0C4 },
	{ 0x7403, 0xCFB9 }, { 0x7405, 0xD5CA }, { 0x7406, 0xD7E2 }, { 0x7407, 0xE2AF }, { 0x7409, 0xD7B8 }, { 0x7413, 0xE8CD }, { 0x741B, 0xF6DA }, { 0x7420, 0xEFA2 },
	{ 0x7421, 0xE2DA }, { 0x7422, 0xF6FC }, { 0x7425, 0xFBD0 }, { 0x7426, 0xD1AD }, { 0x7428, 0xCDE4 }, { 0x742A, 0xD1AE }, { 0x742B, 0xDCED }, { 0x742C, 0xE8CE },
	{ 0x742E, 0xF0F9 }, { 0x742F, 0xCEB5 }, { 0x7430, 0xE6FC }, { 0x7433, 0xD7FB }, { 0x7434, 0xD0D6 }, { 0x7435, 0xDDF5 }, { 0x7436, 0xF7F1 }, { 0x7438, 0xF6FD },
	{ 0x743A, 0xDBF7 }, { 0x743F, 0xFBEA }, { 0x7440, 0xE9DC }, { 0x7441, 0xD9C1 }, { 0x7443, 0xF5F2 }, { 0x7444, 0xE0C5 }, { 0x744B, 0xEAD4 }, { 0x7455, 0xF9C2 },
	{ 0x7457, 0xEABC }, { 0x7459, 0xD2C5 }, { 0x745A, 0xFBD1 }, { 0x745B, 0xE7C0 }, { 0x745C, 0xEBA5 }, { 0x745E, 0xDFFA }, { 0x745F, 0xE3A2 }, { 0x7460, 0xD7B9 },
	{ 0x7462, 0xE9C3 }, { 0x7464, 0xE8FD }, { 0x7465, 0xE8AF }, { 0x7468, 0xF2D3 }, { 0x7469, 0xFBA9 }, { 0x746A, 0xD8A5 }, { 0x746F, 0xD5CB }, { 0x747E, 0xD0C8 },
	{ 0x7482, 0xD1AF }, { 0x7483, 0xD7E3 }, { 0x7487, 0xE0C6 }, { 0x7489, 0xD6A2 }, { 0x748B, 0xEDF0 }, { 0x7498, 0xD7F3 }, { 0x749C, 0xFCD4 }, { 0x749E, 0xDAD7 },
	{ 0x749F, 0xCCDF }, { 0x74A1, 0xF2D4 }, { 0x74A3, 0xD1B0 }, { 0x74A5, 0xCCE0 }, { 0x74A7, 0xDBFD }, { 0x74A8, 0xF3BF }, { 0x74AA, 0xF0D1 }, { 0x74B0, 0xFCBB },
	{ 0x74B2, 0xE2B0 }, { 0x74B5, 0xE6A5 }, { 0x74B9, 0xE2DB }, { 0x74BD, 0xDFDE }, { 0x74BF, 0xE0C7 }, { 0x74C6, 0xF2EF }, { 0x74CA, 0xCCE1 }, { 0x74CF, 0xD6EA },
	{ 0x74D4, 0xE7C2 }, { 0x74D8, 0xCEB6 }, { 0x74DA, 0xF3C0 }, { 0x74DC, 0xCDFE }, { 0x74E0, 0xFBD2 }, { 0x74E2, 0xF8F8 }, { 0x74E3, 0xF7FB }, { 0x74E6, 0xE8BF },
	{ 0x74EE, 0xE8B7 }, { 0x74F7, 0xEDB6 }, { 0x7501, 0xDCBA }, { 0x7504, 0xCCB4 }, { 0x7511, 0xF1F7 }, { 0x7515, 0xE8B8 }, { 0x7518, 0xCAF6 }, { 0x751A, 0xE4A4 },
	{ 0x751B, 0xF4D6 }, { 0x751F, 0xDFE6 }, { 0x7523, 0xDFA7 }, { 0x7525, 0xDFE7 }, { 0x7526, 0xE1C1 }, { 0x7528, 0xE9C4 }, { 0x752B, 0xDCCB }, { 0x752C, 0xE9C5 },
	{ 0x7530, 0xEFA3 }, { 0x7531, 0xEBA6 }, { 0x7532, 0xCBA3 }, { 0x7533, 0xE3E9 }, { 0x7537, 0xD1FB }, { 0x7538, 0xEFA4 }, { 0x753A, 0xEFEB }, { 0x7547, 0xD0B4 },
	{ 0x754C, 0xCDA3 }, { 0x754F, 0xE8E6 }, { 0x7551, 0xEFA5 }, { 0x7553, 0xD3CC }, { 0x7554, 0xDAED }, { 0x7559, 0xD7BA }, { 0x755B, 0xF2D5 }, { 0x755C, 0xF5E5 },
	{ 0x755D, 0xD9EF }, { 0x7562, 0xF9B4 }, { 0x7565, 0xD5D4 }, { 0x7566, 0xFDCF }, { 0x756A, 0xDBE3 }, { 0x756F, 0xF1E1 }, { 0x7570, 0xECB6 }, { 0x7575, 0xFBFE },
	{ 0x7576, 0xD3D7 }, { 0x7578, 0xD1B1 }, { 0x757A, 0xCBB1 }, { 0x757F, 0xD1B2 }, { 0x7586, 0xCBB2 }, { 0x7587, 0xF1C2 }, { 0x758A, 0xF4E1 }, { 0x758B, 0xF9B5 },
	{ 0x758E, 0xE1C3 }, { 0x758F, 0xE1C2 }, { 0x7591, 0xEBF7 }, { 0x759D, 0xDFA8 }, { 0x75A5, 0xCBCA }, { 0x75AB, 0xE6B9 }, { 0x75B1, 0xF8DE }, { 0x75B2, 0xF9AA },
	{ 0x75B3, 0xCAF7 }, { 0x75B5, 0xEDB7 }, { 0x75B8, 0xD3B8 }, { 0x75B9, 0xF2D6 }, { 0x75BC, 0xD4D9 }, { 0x75BD, 0xEEC5 }, { 0x75BE, 0xF2F0 }, { 0x75C2, 0xCAB2 },
	{ 0x75C5, 0xDCBB }, { 0x75C7, 0xF1F8 }, { 0x75CD, 0xECB7 }, { 0x75D2, 0xE5CA }, { 0x75D4, 0xF6C0 }, { 0x75D5, 0xFDDD }, { 0x75D8, 0xD4E3 }, { 0x75D9, 0xCCE2 },
	{ 0x75DB, 0xF7D4 }, { 0x75E2, 0xD7E5 }, { 0x75F0, 0xD3C3 }, { 0x75F2, 0xD8A6 }, { 0x75F4, 0xF6C1 }, { 0x75FA, 0xDDF6 }, { 0x75FC, 0xCDC0 }, { 0x7600, 0xE5DC },
	{ 0x760D, 0xE5CB }, { 0x7619, 0xE1C4 }, { 0x761F, 0xE8B0 }, { 0x7620, 0xF4B0 }, { 0x7621, 0xF3EA }, { 0x7622, 0xDAEE }, { 0x7624, 0xD7BB }, { 0x7626, 0xE2B1 },
	{ 0x763B, 0xD7AA }, { 0x7642, 0xD6FB }, { 0x764C, 0xE4DF }, { 0x764E, 0xCAD6 }, { 0x7652, 0xEBA8 }, { 0x7656, 0xDBFE }, { 0x7661, 0xF6C2 }, { 0x7664, 0xEFBB },
	{ 0x7669, 0xD4FD }, { 0x766C, 0xE0C8 }, { 0x7670, 0xE8B9 }, { 0x7672, 0xEFA6 }, { 0x7678, 0xCDA4 }, { 0x767B, 0xD4F4 }, { 0x767C, 0xDBA1 }, { 0x767D, 0xDBDC },
	{ 0x767E, 0xDBDD }, { 0x7684, 0xEEDC }, { 0x7686, 0xCBCB }, { 0x7687, 0xFCD5 }, { 0x768E, 0xCEEB }, { 0x7690, 0xCDC1 }, { 0x7693, 0xFBD3 }, { 0x76AE, 0xF9AB },
	{ 0x76BA, 0xF5D4 }, { 0x76BF, 0xD9A9 }, { 0x76C2, 0xE9DD }, { 0x76C3, 0xDBCD }, { 0x76C6, 0xDDCE }, { 0x76C8, 0xE7C3 }, { 0x76CA, 0xECCC }, { 0x76D2, 0xF9EC },
	{ 0x76D6, 0xCBCC }, { 0x76DB, 0xE0FC }, { 0x76DC, 0xD4A8 }, { 0x76DE, 0xEDD3 }, { 0x76DF, 0xD8EF }, { 0x76E1, 0xF2D7 }, { 0x76E3, 0xCAF8 }, { 0x76E4, 0xDAEF },
	{ 0x76E7, 0xD6D4 }, { 0x76EE, 0xD9CD }, { 0x76F2, 0xD8EE }, { 0x76F4, 0xF2C1 }, { 0x76F8, 0xDFD3 }, { 0x76FC, 0xDAF0 }, { 0x76FE, 0xE2EA }, { 0x7701, 0xE0FD },
	{ 0x7704, 0xD8F8 }, { 0x7708, 0xF7AF }, { 0x7709, 0xDAB6 }, { 0x770B, 0xCAD7 }, { 0x771E, 0xF2D8 }, { 0x7720, 0xD8F9 }, { 0x7729, 0xFADF }, { 0x7737, 0xCFEF },
	{ 0x7738, 0xD9C2 }, { 0x773A, 0xF0D2 }, { 0x773C, 0xE4D1 }, { 0x7740, 0xF3B7 }, { 0x774D, 0xFAE0 }, { 0x775B, 0xEFEC }, { 0x7761, 0xE2B2 }, { 0x7763, 0xD4BD },
	{ 0x7766, 0xD9CE }, { 0x776B, 0xF4E2 }, { 0x7779, 0xD4A9 }, { 0x777E, 0xCDC2 }, { 0x777F, 0xE7DA }, { 0x778B, 0xF2D9 }, { 0x7791, 0xD9AA }, { 0x779E, 0xD8BE },
	{ 0x77A5, 0xDCAD }, { 0x77AC, 0xE2EB }, { 0x77AD, 0xD6FC }, { 0x77B0, 0xCAF9 }, { 0x77B3, 0xD4DA }, { 0x77BB, 0xF4D7 }, { 0x77BC, 0xCCA1 }, { 0x77BF, 0xCFBA },
	{ 0x77D7, 0xF5B8 }, { 0x77DB, 0xD9C3 }, { 0x77DC, 0xD0E8 }, { 0x77E2, 0xE3C5 }, { 0x77E3, 0xEBF8 }, { 0x77E5, 0xF2B1 }, { 0x77E9, 0xCFBB }, { 0x77ED, 0xD3AD },
	{ 0x77EE, 0xE8E1 }, { 0x77EF, 0xCEEC }, { 0x77F3, 0xE0B4 }, { 0x7802, 0xDEE3 }, { 0x7812, 0xDDF7 }, { 0x7825, 0xF2B2 }, { 0x7826, 0xF3F6 }, { 0x7827, 0xF6DB },
	{ 0x782C, 0xD7FE }, { 0x7832, 0xF8DF }, { 0x7834, 0xF7F2 }, { 0x7845, 0xD0A9 }, { 0x784F, 0xE6DA }, { 0x785D, 0xF5A6 }, { 0x786B, 0xD7BC }, { 0x786C, 0xCCE3 },
	{ 0x786F, 0xE6DB }, { 0x787C, 0xDDDD }, { 0x7881, 0xD1B3 }, { 0x7887, 0xEFED }, { 0x788C, 0xD6DE }, { 0x788D, 0xE4F4 }, { 0x788E, 0xE1EF }, { 0x7891, 0xDDF8 },
	{ 0x7897, 0xE8CF }, { 0x78A3, 0xCAE5 }, { 0x78A7, 0xDCA1 }, { 0x78A9, 0xE0B5 }, { 0x78BA, 0xFCAC }, { 0x78BB, 0xFCAD }, { 0x78BC, 0xD8A7 }, { 0x78C1, 0xEDB8 },
	{ 0x78C5, 0xDBB6 }, { 0x78CA, 0xD6F0 }, { 0x78CB, 0xF3AF }, { 0x78CE, 0xCDA5 }, { 0x78D0, 0xDAF1 }, { 0x78E8, 0xD8A8 }, { 0x78EC, 0xCCE4 }, { 0x78EF, 0xD1B4 },
	{ 0x78F5, 0xCAD8 }, { 0x78FB, 0xDAF2 }, { 0x7901, 0xF5A7 }, { 0x790E, 0xF5A8 }, { 0x7916, 0xE6A6 }, { 0x792A, 0xD5EC }, { 0x792B, 0xD5F8 }, { 0x792C, 0xDAF3 },
	{ 0x793A, 0xE3C6 }, { 0x793E, 0xDEE4 }, { 0x7940, 0xDEE5 }, { 0x7941, 0xD1B5 }, { 0x7947, 0xD1B6 }, { 0x7948, 0xD1B7 }, { 0x7949, 0xF2B3 }, { 0x7950, 0xE9DE },
	{ 0x7956, 0xF0D3 }, { 0x7957, 0xF2B4 }, { 0x795A, 0xF0D4 }, { 0x795B, 0xCBE4 }, { 0x795C, 0xFBD4 }, { 0x795D, 0xF5E6 }, { 0x795E, 0xE3EA }, { 0x7960, 0xDEE6 },
	{ 0x7965, 0xDFD4 }, { 0x7968, 0xF8F9 }, { 0x796D, 0xF0AE }, { 0x797A, 0xD1B8 }, { 0x797F, 0xD6DF }, { 0x7981, 0xD0D7 }, { 0x798D, 0xFCA1 }, { 0x798E, 0xEFEE },
	{ 0x798F, 0xDCD8 }, { 0x7991, 0xE9DF }, { 0x79A6, 0xE5DD }, { 0x79A7, 0xFDFB }, { 0x79AA, 0xE0C9 }, { 0x79AE, 0xD6C9 }, { 0x79B1, 0xD4AA }, { 0x79B3, 0xE5CC },
	{ 0x79B9, 0xE9E0 }, { 0x79BD, 0xD0D8 }, { 0x79BE, 0xFCA2 }, { 0x79BF, 0xD4BE }, { 0x79C0, 0xE2B3 }, { 0x79C1, 0xDEE7 }, { 0x79C9, 0xDCBC }, { 0x79CA, 0xD2B6 },
	{ 0x79CB, 0xF5D5 }, { 0x79D1, 0xCEA1 }, { 0x79D2, 0xF5A9 }, { 0x79D5, 0xDDF9 }, { 0x79D8, 0xDDFA }, { 0x79DF, 0xF0D5 }, { 0x79E4, 0xF6DF }, { 0x79E6, 0xF2DA },
	{ 0x79E7, 0xE4EB }, { 0x79E9, 0xF2F1 }, { 0x79FB, 0xECB9 }, { 0x7A00, 0xFDFC }, { 0x7A05, 0xE1AA }, { 0x7A08, 0xCAD9 }, { 0x7A0B, 0xEFEF }, { 0x7A0D, 0xF5AA },
	{ 0x7A14, 0xECF9 }, { 0x7A17, 0xF8AD }, { 0x7A19, 0xF2C2 }, { 0x7A1A, 0xF6C3 }, { 0x7A1C, 0xD7D2 }, { 0x7A1F, 0xF9A2 }, { 0x7A20, 0xF0D6 }, { 0x7A2E, 0xF0FA },
	{ 0x7A31, 0xF6E0 }, { 0x7A36, 0xE9F3 }, { 0x7A37, 0xF2C3 }, { 0x7A3B, 0xD4AB }, { 0x7A3C, 0xCAB3 }, { 0x7A3D, 0xCDA6 }, { 0x7A3F, 0xCDC3 }, { 0x7A40, 0xCDDA },
	{ 0x7A46, 0xD9CF }, { 0x7A49, 0xF6C4 }, { 0x7A4D, 0xEEDD }, { 0x7A4E, 0xE7C4 }, { 0x7A57, 0xE2B4 }, { 0x7A61, 0xDFE2 }, { 0x7A62, 0xE7DB }, { 0x7A69, 0xE8B1 },
	{ 0x7A6B, 0xFCAE }, { 0x7A70, 0xE5CD }, { 0x7A74, 0xFAEB }, { 0x7A76, 0xCFBC }, { 0x7A79, 0xCFE2 }, { 0x7A7A, 0xCDF6 }, { 0x7A7D, 0xEFF0 }, { 0x7A7F, 0xF4BE },
	{ 0x7A81, 0xD4CD }, { 0x7A84, 0xF3B8 }, { 0x7A88, 0xE9A1 }, { 0x7A92, 0xF2F2 }, { 0x7A93, 0xF3EB }, { 0x7A95, 0xF0D7 }, { 0x7A98, 0xCFD7 }, { 0x7A9F, 0xCFDF },
	{ 0x7AA9, 0xE8C0 }, { 0x7AAA, 0xE8C1 }, { 0x7AAE, 0xCFE3 }, { 0x7AAF, 0xE9A2 }, { 0x7ABA, 0xD0AA }, { 0x7AC4, 0xF3C1 }, { 0x7AC5, 0xD0AB }, { 0x7AC7, 0xD4E4 },
	{ 0x7ACA, 0xEFBC }, { 0x7ACB, 0xD8A1 }, { 0x7AD7, 0xD9DF }, { 0x7AD9, 0xF3D7 }, { 0x7ADD, 0xDCBD }, { 0x7ADF, 0xCCE5 }, { 0x7AE0, 0xEDF1 }, { 0x7AE3, 0xF1E2 },
	{ 0x7AE5, 0xD4DB }, { 0x7AEA, 0xE2B5 }, { 0x7AED, 0xCAE6 }, { 0x7AEF, 0xD3AE }, { 0x7AF6, 0xCCE6 }, { 0x7AF9, 0xF1D3 }, { 0x7AFA, 0xF5E7 }, { 0x7AFF, 0xCADA },
	{ 0x7B0F, 0xFBEE }, { 0x7B11, 0xE1C5 }, { 0x7B19, 0xDFE9 }, { 0x7B1B, 0xEEDE }, { 0x7B1E, 0xF7C2 }, { 0x7B20, 0xD8A2 }, { 0x7B26, 0xDDAC }, { 0x7B2C, 0xF0AF },
	{ 0x7B2D, 0xD6BD }, { 0x7B39, 0xE1AB }, { 0x7B46, 0xF9B6 }, { 0x7B49, 0xD4F5 }, { 0x7B4B, 0xD0C9 }, { 0x7B4C, 0xEFA7 }, { 0x7B4D, 0xE2EC }, { 0x7B4F, 0xDBEA },
	{ 0x7B50, 0xCECC }, { 0x7B51, 0xF5E8 }, { 0x7B52, 0xF7D5 }, { 0x7B54, 0xD3CD }, { 0x7B56, 0xF3FE }, { 0x7B60, 0xD0B5 }, { 0x7B6C, 0xE0FE }, { 0x7B6E, 0xDFFB },
	{ 0x7B75, 0xE6DD }, { 0x7B7D, 0xE8A4 }, { 0x7B87, 0xCBCD }, { 0x7B8B, 0xEFA8 }, { 0x7B8F, 0xEEB4 }, { 0x7B94, 0xDAD8 }, { 0x7B95, 0xD1B9 }, { 0x7B97, 0xDFA9 },
	{ 0x7B9A, 0xF3B0 }, { 0x7B9D, 0xCCC4 }, { 0x7BA1, 0xCEB7 }, { 0x7BAD, 0xEFA9 }, { 0x7BB1, 0xDFD5 }, { 0x7BB4, 0xEDD7 }, { 0x7BB8, 0xEEC6 }, { 0x7BC0, 0xEFBD },
	{ 0x7BC1, 0xFCD6 }, { 0x7BC4, 0xDBF4 }, { 0x7BC6, 0xEFAA }, { 0x7BC7, 0xF8B9 }, { 0x7BC9, 0xF5E9 }, { 0x7BD2, 0xE3D9 }, { 0x7BE0, 0xE1C6 }, { 0x7BE4, 0xD4BF },
	{ 0x7BE9, 0xDEE8 }, { 0x7C07, 0xF0EA }, { 0x7C12, 0xF3C2 }, { 0x7C1E, 0xD3AF }, { 0x7C21, 0xCADB }, { 0x7C27, 0xFCD7 }, { 0x7C2A, 0xEDD8 }, { 0x7C2B, 0xE1C7 },
	{ 0x7C3D, 0xF4D8 }, { 0x7C3E, 0xD6B3 }, { 0x7C3F, 0xDDAD }, { 0x7C43, 0xD5BE }, { 0x7C4C, 0xF1C3 }, { 0x7C4D, 0xEEDF }, { 0x7C60, 0xD6EB }, { 0x7C64, 0xF4D9 },
	{ 0x7C6C, 0xD7E6 }, { 0x7C73, 0xDAB7 }, { 0x7C83, 0xDDFB }, { 0x7C89, 0xDDCF }, { 0x7C92, 0xD8A3 }, { 0x7C95, 0xDAD9 }, { 0x7C97, 0xF0D8 }, { 0x7C98, 0xEFC4 },
	{ 0x7C9F, 0xE1D8 }, { 0x7CA5, 0xF1D4 }, { 0x7CA7, 0xEDF2 }, { 0x7CAE, 0xD5DB }, { 0x7CB1, 0xD5DC }, { 0x7CB2, 0xF3C4 }, { 0x7CB3, 0xCBD7 }, { 0x7CB9, 0xE2B6 },
	{ 0x7CBE, 0xEFF1 }, { 0x7CCA, 0xFBD5 }, { 0x7CD6, 0xD3D8 }, { 0x7CDE, 0xDDD0 }, { 0x7CDF, 0xF0D9 }, { 0x7CE0, 0xCBB3 }, { 0x7CE7, 0xD5DD }, { 0x7CFB, 0xCDA7 },
	{ 0x7CFE, 0xD0AC }, { 0x7D00, 0xD1BA }, { 0x7D02, 0xF1C4 }, { 0x7D04, 0xE5B3 }, { 0x7D05, 0xFBF5 }, { 0x7D06, 0xE9E1 }, { 0x7D07, 0xFDE0 }, { 0x7D08, 0xFCBC },
	{ 0x7D0A, 0xDAA2 }, { 0x7D0B, 0xDAA3 }, { 0x7D0D, 0xD2A1 }, { 0x7D10, 0xD2EF }, { 0x7D14, 0xE2ED }, { 0x7D17, 0xDEE9 }, { 0x7D18, 0xCEDC }, { 0x7D19, 0xF2B5 },
	{ 0x7D1A, 0xD0E4 }, { 0x7D1B, 0xDDD1 }, { 0x7D20, 0xE1C8 }, { 0x7D21, 0xDBB7 }, { 0x7D22, 0xDFE3 }, { 0x7D2B, 0xEDB9 }, { 0x7D2C, 0xF1C5 }, { 0x7D2E, 0xF3CF },
	{ 0x7D2F, 0xD7AB }, { 0x7D30, 0xE1AC }, { 0x7D33, 0xE3EB }, { 0x7D35, 0xEEC7 }, { 0x7D39, 0xE1C9 }, { 0x7D3A, 0xCAFA }, { 0x7D42, 0xF0FB }, { 0x7D43, 0xFAE1 },
	{ 0x7D44, 0xF0DA }, { 0x7D45, 0xCCE7 }, { 0x7D46, 0xDAF4 }, { 0x7D50, 0xCCBF }, { 0x7D5E, 0xCEED }, { 0x7D61, 0xD5A9 }, { 0x7D62, 0xFAE2 }, { 0x7D66, 0xD0E5 },
	{ 0x7D68, 0xEBD6 }, { 0x7D6A, 0xECDF }, { 0x7D6E, 0xDFFC }, { 0x7D71, 0xF7D6 }, { 0x7D72, 0xDEEA }, { 0x7D73, 0xCBB4 }, { 0x7D76, 0xEFBE }, { 0x7D79, 0xCCB5 },
	{ 0x7D7F, 0xCFBD }, { 0x7D8E, 0xEFF2 }, { 0x7D8F, 0xE2B7 }, { 0x7D93, 0xCCE8 }, { 0x7D9C, 0xF0FC }, { 0x7DA0, 0xD6E0 }, { 0x7DA2, 0xF1C6 }, { 0x7DAC, 0xE2B8 },
	{ 0x7DAD, 0xEBAB }, { 0x7DB1, 0xCBB5 }, { 0x7DB2, 0xD8D1 }, { 0x7DB4, 0xF4CE }, { 0x7DB5, 0xF3F7 }, { 0x7DB8, 0xD7C6 }, { 0x7DBA, 0xD1BB }, { 0x7DBB, 0xF7AA },
	{ 0x7DBD, 0xEDCA }, { 0x7DBE, 0xD7D3 }, { 0x7DBF, 0xD8FA }, { 0x7DC7, 0xF6C5 }, { 0x7DCA, 0xD1CC }, { 0x7DCB, 0xDDFC }, { 0x7DD6, 0xDFFD }, { 0x7DD8, 0xF9E5 },
	{ 0x7DDA, 0xE0CA }, { 0x7DDD, 0xF2FD }, { 0x7DDE, 0xD3B0 }, { 0x7DE0, 0xF4F3 }, { 0x7DE1, 0xDAC9 }, { 0x7DE3, 0xE6DE }, { 0x7DE8, 0xF8BA }, { 0x7DE9, 0xE8D0 },
	{ 0x7DEC, 0xD8FB }, { 0x7DEF, 0xEAD5 }, { 0x7DF4, 0xD6A3 }, { 0x7DFB, 0xF6C6 }, { 0x7E09, 0xF2DB }, { 0x7E0A, 0xE4FC }, { 0x7E15, 0xE8B2 }, { 0x7E1B, 0xDADA },
	{ 0x7E1D, 0xF2DC }, { 0x7E1E, 0xFBD6 }, { 0x7E1F, 0xE9B2 }, { 0x7E21, 0xEEAD }, { 0x7E23, 0xFAE3 }, { 0x7E2B, 0xDCEE }, { 0x7E2E, 0xF5EA }, { 0x7E2F, 0xE6E0 },
	{ 0x7E31, 0xF0FD }, { 0x7E37, 0xD7AC }, { 0x7E3D, 0xF5C5 }, { 0x7E3E, 0xEEE0 }, { 0x7E41, 0xDBE5 }, { 0x7E43, 0xDDDE }, { 0x7E46, 0xD9F0 }, { 0x7E47, 0xE9A3 },
	{ 0x7E52, 0xF1F9 }, { 0x7E54, 0xF2C4 }, { 0x7E55, 0xE0CB }, { 0x7E5E, 0xE9A4 }, { 0x7E61, 0xE2B9 }, { 0x7E69, 0xE3B1 }, { 0x7E6A, 0xFCEB }, { 0x7E6B, 0xCDA8 },
	{ 0x7E6D, 0xCCB6 }, { 0x7E70, 0xF0DB }, { 0x7E79, 0xE6BA }, { 0x7E7C, 0xCDA9 }, { 0x7E82, 0xF3C3 }, { 0x7E8C, 0xE1D9 }, { 0x7E8F, 0xEFAB }, { 0x7E93, 0xE7C5 },
	{ 0x7E96, 0xE0E9 }, { 0x7E98, 0xF3C5 }, { 0x7E9B, 0xD4C0 }, { 0x7E9C, 0xD5BF }, { 0x7F36, 0xDDAE }, { 0x7F38, 0xF9FC }, { 0x7F3A, 0xCCC0 }, { 0x7F4C, 0xE5A2 },
	{ 0x7F50, 0xCEB8 }, { 0x7F54, 0xD8D2 }, { 0x7F55, 0xF9D6 }, { 0x7F6A, 0xF1AA }, { 0x7F6B, 0xCED1 }, { 0x7F6E, 0xF6C7 }, { 0x7F70, 0xDBEB }, { 0x7F72, 0xDFFE },
	{ 0x7F75, 0xD8E1 }, { 0x7F77, 0xF7F3 }, { 0x7F79, 0xD7E7 }, { 0x7F85, 0xD4FE }, { 0x7F88, 0xD1BC }, { 0x7F8A, 0xE5CF }, { 0x7F8C, 0xCBB6 }, { 0x7F8E, 0xDAB8 },
	{ 0x7F94, 0xCDC4 }, { 0x7F9A, 0xD6BE }, { 0x7F9E, 0xE2BA }, { 0x7FA4, 0xCFD8 }, { 0x7FA8, 0xE0CC }, { 0x7FA9, 0xEBF9 }, { 0x7FB2, 0xFDFD }, { 0x7FB8, 0xD7E8 },
	{ 0x7FB9, 0xCBD8 }, { 0x7FBD, 0xE9E2 }, { 0x7FC1, 0xE8BA }, { 0x7FC5, 0xE3C7 }, { 0x7FCA, 0xECCD }, { 0x7FCC, 0xECCE }, { 0x7FCE, 0xD6BF }, { 0x7FD2, 0xE3A7 },
	{ 0x7FD4, 0xDFD6 }, { 0x7FD5, 0xFDE8 }, { 0x7FDF, 0xEEE1 }, { 0x7FE0, 0xF6A8 }, { 0x7FE1, 0xDDFD }, { 0x7FE9, 0xF8BB }, { 0x7FEB, 0xE8D1 }, { 0x7FF0, 0xF9D7 },
	{ 0x7FF9, 0xCEEE }, { 0x7FFC, 0xECCF }, { 0x8000, 0xE9A5 }, { 0x8001, 0xD6D5 }, { 0x8003, 0xCDC5 }, { 0x8005, 0xEDBA }, { 0x8006, 0xD1BD }, { 0x8009, 0xCFBE },
	{ 0x800C, 0xECBB }, { 0x8010, 0xD2B1 }, { 0x8015, 0xCCE9 }, { 0x8017, 0xD9C4 }, { 0x8018, 0xE9FC }, { 0x802D, 0xD1BE }, { 0x8033, 0xECBC }, { 0x8036, 0xE5AD },
	{ 0x803D, 0xF7B0 }, { 0x803F, 0xCCEA }, { 0x8043, 0xD3C4 }, { 0x8046, 0xD6C0 }, { 0x804A, 0xD6FD }, { 0x8056, 0xE1A1 }, { 0x8058, 0xDEBD }, { 0x805A, 0xF6A9 },
	{ 0x805E, 0xDAA4 }, { 0x806F, 0xD6A4 }, { 0x8070, 0xF5C6 }, { 0x8072, 0xE1A2 }, { 0x8073, 0xE9C6 }, { 0x8077, 0xF2C5 }, { 0x807D, 0xF4E9 }, { 0x807E, 0xD6EC },
	{ 0x807F, 0xEBD3 }, { 0x8084, 0xECBD }, { 0x8085, 0xE2DC }, { 0x8086, 0xDEEB }, { 0x8087, 0xF0DC }, { 0x8089, 0xEBBF }, { 0x808B, 0xD7CE }, { 0x808C, 0xD1BF },
	{ 0x8096, 0xF5AB }, { 0x809B, 0xF9FD }, { 0x809D, 0xCADC }, { 0x80A1, 0xCDC6 }, { 0x80A2, 0xF2B6 }, { 0x80A5, 0xDDFE }, { 0x80A9, 0xCCB7 }, { 0x80AA, 0xDBB8 },
	{ 0x80AF, 0xD0E9 }, { 0x80B1, 0xCEDD }, { 0x80B2, 0xEBC0 }, { 0x80B4, 0xFDA2 }, { 0x80BA, 0xF8CB }, { 0x80C3, 0xEAD6 }, { 0x80C4, 0xF1B0 }, { 0x80CC, 0xDBCE },
	{ 0x80CE, 0xF7C3 }, { 0x80DA, 0xDBCF }, { 0x80DB, 0xCBA4 }, { 0x80DE, 0xF8E0 }, { 0x80E1, 0xFBD7 }, { 0x80E4, 0xEBCA }, { 0x80E5, 0xE0A1 }, { 0x80F1, 0xCECD },
	{ 0x80F4, 0xD4DC }, { 0x80F8, 0xFDD8 }, { 0x80FD, 0xD2F6 }, { 0x8102, 0xF2B7 }, { 0x8105, 0xFAF6 }, { 0x8106, 0xF6AA }, { 0x8107, 0xFAF7 }, { 0x8108, 0xD8E6 },
	{ 0x810A, 0xF4B1 }, { 0x8118, 0xE8D2 }, { 0x811A, 0xCAC5 }, { 0x811B, 0xCCEB }, { 0x8123, 0xE2EE }, { 0x8129, 0xE2BB }, { 0x812B, 0xF7AD }, { 0x812F, 0xF8E1 },
	{ 0x8139, 0xF3EC }, { 0x813E, 0xDEA1 }, { 0x814B, 0xE4FD }, { 0x814E, 0xE3EC }, { 0x8150, 0xDDAF }, { 0x8151, 0xDDB0 }, { 0x8154, 0xCBB7 }, { 0x8155, 0xE8D3 },
	{ 0x8165, 0xE1A3 }, { 0x8166, 0xD2E0 }, { 0x816B, 0xF0FE }, { 0x8170, 0xE9A6 }, { 0x8171, 0xCBF2 }, { 0x8178, 0xEDF3 }, { 0x8179, 0xDCD9 }, { 0x817A, 0xE0CD },
	{ 0x817F, 0xF7DA }, { 0x8180, 0xDBB9 }, { 0x8188, 0xCCAE }, { 0x818A, 0xDADB }, { 0x818F, 0xCDC7 }, { 0x819A, 0xDDB1 }, { 0x819C, 0xD8AF }, { 0x819D, 0xE3A3 },
	{ 0x81A0, 0xCEEF }, { 0x81A3, 0xF2F3 }, { 0x81A8, 0xF8B3 }, { 0x81B3, 0xE0CE }, { 0x81B5, 0xF5FD }, { 0x81BA, 0xEBEC }, { 0x81BD, 0xD3C5 }, { 0x81BE, 0xFCEC },
	{ 0x81BF, 0xD2DB }, { 0x81C0, 0xD4EB }, { 0x81C2, 0xDEA2 }, { 0x81C6, 0xE5E6 }, { 0x81CD, 0xF0B0 }, { 0x81D8, 0xD5C4 }, { 0x81DF, 0xEDF4 }, { 0x81E3, 0xE3ED },
	{ 0x81E5, 0xE8C2 }, { 0x81E7, 0xEDF5 }, { 0x81E8, 0xD7FC }, { 0x81EA, 0xEDBB }, { 0x81ED, 0xF6AB }, { 0x81F3, 0xF2B8 }, { 0x81F4, 0xF6C8 }, { 0x81FA, 0xD3E6 },
	{ 0x81FB, 0xF2DD }, { 0x81FC, 0xCFBF }, { 0x81FE, 0xEBAC }, { 0x8205, 0xCFC0 }, { 0x8207, 0xE6A8 }, { 0x8208, 0xFDE9 }, { 0x820A, 0xCFC1 }, { 0x820C, 0xE0DF },
	{ 0x820D, 0xDEEC }, { 0x8212, 0xE0A2 }, { 0x821B, 0xF4BF }, { 0x821C, 0xE2EF }, { 0x821E, 0xD9F1 }, { 0x821F, 0xF1C7 }, { 0x8221, 0xCBB8 }, { 0x822A, 0xF9FE },
	{ 0x822B, 0xDBBA }, { 0x822C, 0xDAF5 }, { 0x8235, 0xF6EC }, { 0x8236, 0xDADC }, { 0x8237, 0xFAE4 }, { 0x8239, 0xE0CF }, { 0x8240, 0xDDB2 }, { 0x8245, 0xE6A9 },
	{ 0x8247, 0xEFF3 }, { 0x8259, 0xF3ED }, { 0x8264, 0xEBFA }, { 0x8266, 0xF9E6 }, { 0x826E, 0xCADD }, { 0x826F, 0xD5DE }, { 0x8271, 0xCADE }, { 0x8272, 0xDFE4 },
	{ 0x8276, 0xE6FD }, { 0x8278, 0xF5AC }, { 0x827E, 0xE4F5 }, { 0x828B, 0xE9E3 }, { 0x828D, 0xEDCB }, { 0x828E, 0xCFE4 }, { 0x8292, 0xD8D3 }, { 0x8299, 0xDDB3 },
	{ 0x829A, 0xD4EC }, { 0x829D, 0xF2B9 }, { 0x829F, 0xDFB7 }, { 0x82A5, 0xCBCE }, { 0x82A6, 0xFBD8 }, { 0x82A9, 0xD0D9 }, { 0x82AC, 0xDDD2 }, { 0x82AD, 0xF7F4 },
	{ 0x82AE, 0xE7DC }, { 0x82AF, 0xE4A5 }, { 0x82B1, 0xFCA3 }, { 0x82B3, 0xDBBB }, { 0x82B7, 0xF2BA }, { 0x82B8, 0xE9FD }, { 0x82B9, 0xD0CA }, { 0x82BB, 0xF5D6 },
	{ 0x82BC, 0xD9C5 }, { 0x82BD, 0xE4B4 }, { 0x82BF, 0xEDA7 }, { 0x82D1, 0xEABD }, { 0x82D2, 0xE6FE }, { 0x82D4, 0xF7C4 }, { 0x82D5, 0xF5AD }, { 0x82D7, 0xD9E0 },
	{ 0x82DB, 0xCAB4 }, { 0x82DE, 0xF8E2 }, { 0x82DF, 0xCFC2 }, { 0x82E1, 0xECBE }, { 0x82E5, 0xE5B4 }, { 0x82E6, 0xCDC8 }, { 0x82E7, 0xEEC8 }, { 0x82F1, 0xE7C8 },
	{ 0x82FD, 0xCDC9 }, { 0x82FE, 0xF9B7 }, { 0x8301, 0xF1E8 }, { 0x8302, 0xD9F2 }, { 0x8303, 0xDBF5 }, { 0x8304, 0xCAB5 }, { 0x8305, 0xD9C6 }, { 0x8309, 0xD8C9 },
	{ 0x8317, 0xD9AB }, { 0x8328, 0xEDBC }, { 0x832B, 0xD8D4 }, { 0x832F, 0xDCDA }, { 0x8331, 0xE2BC }, { 0x8334, 0xFCED }, { 0x8335, 0xECE0 }, { 0x8336, 0xD2FE },
	{ 0x8338, 0xE9C7 }, { 0x8339, 0xE6AA }, { 0x8340, 0xE2F0 }, { 0x8347, 0xFABB }, { 0x8349, 0xF5AE }, { 0x834A, 0xFBAA }, { 0x834F, 0xECFB }, { 0x8351, 0xECBF },
	{ 0x8352, 0xFCD8 }, { 0x8373, 0xD4E5 }, { 0x8377, 0xF9C3 }, { 0x837B, 0xEEE2 }, { 0x8389, 0xD7E9 }, { 0x838A, 0xEDF6 }, { 0x838E, 0xDEED }, { 0x8396, 0xCCEC },
	{ 0x8398, 0xE3EE }, { 0x839E, 0xE8D4 }, { 0x83A2, 0xFAF8 }, { 0x83A9, 0xDDB4 }, { 0x83AA, 0xE4B5 }, { 0x83AB, 0xD8B0 }, { 0x83BD, 0xD8D5 }, { 0x83C1, 0xF4EA },
	{ 0x83C5, 0xCEB9 }, { 0x83C9, 0xD6E1 }, { 0x83CA, 0xCFD2 }, { 0x83CC, 0xD0B6 }, { 0x83D3, 0xCEA2 }, { 0x83D6, 0xF3EE }, { 0x83DC, 0xF3F8 }, { 0x83E9, 0xDCCC },
	{ 0x83EB, 0xD0CB }, { 0x83EF, 0xFCA4 }, { 0x83F0, 0xCDCA }, { 0x83F1, 0xD7D4 }, { 0x83F2, 0xDEA3 }, { 0x83F4, 0xE4E0 }, { 0x83F9, 0xEEC9 }, { 0x83FD, 0xE2DD },
	{ 0x8403, 0xF5FE }, { 0x8404, 0xD4AC }, { 0x840A, 0xD5D1 }, { 0x840C, 0xD8F0 }, { 0x840D, 0xF8C3 }, { 0x840E, 0xEAD7 }, { 0x8429, 0xF5D7 }, { 0x842C, 0xD8BF },
	{ 0x8431, 0xFDC0 }, { 0x8438, 0xEBAD }, { 0x843D, 0xD5AA }, { 0x8449, 0xE7A8 }, { 0x8457, 0xEECA }, { 0x845B, 0xCAE7 }, { 0x8461, 0xF8E3 }, { 0x8463, 0xD4DD },
	{ 0x8466, 0xEAD8 }, { 0x846B, 0xFBD9 }, { 0x846C, 0xEDF7 }, { 0x846F, 0xE5B5 }, { 0x8475, 0xD0AD }, { 0x847A, 0xF1F1 }, { 0x8490, 0xE2BD }, { 0x8494, 0xE3C8 },
	{ 0x8499, 0xD9D5 }, { 0x849C, 0xDFAA }, { 0x84A1, 0xDBBC }, { 0x84B2, 0xF8E4 }, { 0x84B8, 0xF1FA }, { 0x84BB, 0xE5B6 }, { 0x84BC, 0xF3EF }, { 0x84BF, 0xFBDA },
	{ 0x84C0, 0xE1E0 }, { 0x84C2, 0xD9AC }, { 0x84C4, 0xF5EB }, { 0x84C6, 0xE0B6 }, { 0x84C9, 0xE9C8 }, { 0x84CB, 0xCBCF }, { 0x84CD, 0xE3C9 }, { 0x84D1, 0xDEEE },
	{ 0x84DA, 0xE2BE }, { 0x84EC, 0xDCEF }, { 0x84EE, 0xD6A5 }, { 0x84F4, 0xE2F1 }, { 0x84FC, 0xD6FE }, { 0x8511, 0xD9A1 }, { 0x8513, 0xD8C0 }, { 0x8514, 0xDCDB },
	{ 0x8517, 0xEDBD }, { 0x8518, 0xDFB8 }, { 0x851A, 0xEAA5 }, { 0x851E, 0xD7AD }, { 0x8521, 0xF3F9 }, { 0x8523, 0xEDF8 }, { 0x8525, 0xF5C7 }, { 0x852C, 0xE1CA },
	{ 0x852D, 0xEBE3 }, { 0x852F, 0xF2DE }, { 0x853D, 0xF8CC }, { 0x853F, 0xEAD9 }, { 0x8541, 0xD3C6 }, { 0x8543, 0xDBE6 }, { 0x8549, 0xF5AF }, { 0x854E, 0xCEF0 },
	{ 0x8553, 0xE9FE }, { 0x8559, 0xFBB6 }, { 0x8563, 0xE2F2 }, { 0x8568, 0xCFF2 }, { 0x8569, 0xF7B9 }, { 0x856A, 0xD9F3 }, { 0x856D, 0xE1CB }, { 0x8584, 0xDADD },
	{ 0x8587, 0xDAB9 }, { 0x858F, 0xEBFB }, { 0x8591, 0xCBB9 }, { 0x8594, 0xEDF9 }, { 0x859B, 0xE0E0 }, { 0x85A6, 0xF4C0 }, { 0x85A8, 0xFDBC }, { 0x85A9, 0xDFB1 },
	{ 0x85AA, 0xE3EF }, { 0x85AF, 0xE0A3 }, { 0x85B0, 0xFDB9 }, { 0x85BA, 0xF0B1 }, { 0x85C1, 0xCDCB }, { 0x85C9, 0xEDBE }, { 0x85CD, 0xD5C0 }, { 0x85CE, 0xE3F0 },
	{ 0x85CF, 0xEDFA }, { 0x85D5, 0xE9E4 }, { 0x85DC, 0xD5ED }, { 0x85DD, 0xE7DD }, { 0x85E4, 0xD4F6 }, { 0x85E5, 0xE5B7 }, { 0x85E9, 0xDBE7 }, { 0x85EA, 0xE2BF },
	{ 0x85F7, 0xEECB }, { 0x85FA, 0xD7F4 }, { 0x85FB, 0xF0DD }, { 0x85FF, 0xCEAB }, { 0x8602, 0xE7DE }, { 0x8606, 0xD6D6 }, { 0x8607, 0xE1CC }, { 0x860A, 0xE8B3 },
	{ 0x8616, 0xE5EE }, { 0x8617, 0xDCA2 }, { 0x861A, 0xE0D0 }, { 0x862D, 0xD5B5 }, { 0x863F, 0xD5A1 }, { 0x864E, 0xFBDB }, { 0x8650, 0xF9CB }, { 0x8654, 0xCBF3 },
	{ 0x8655, 0xF4A5 }, { 0x865B, 0xFAC8 }, { 0x865C, 0xD6D7 }, { 0x865E, 0xE9E5 }, { 0x865F, 0xFBDC }, { 0x8667, 0xFDD0 }, { 0x8679, 0xFBF6 }, { 0x868A, 0xDAA5 },
	{ 0x868C, 0xDBBD }, { 0x8693, 0xECE2 }, { 0x86A3, 0xCDF7 }, { 0x86A4, 0xF0DE }, { 0x86A9, 0xF6C9 }, { 0x86C7, 0xDEEF }, { 0x86CB, 0xD3B1 }, { 0x86D4, 0xFCEE },
	{ 0x86D9, 0xE8C3 }, { 0x86DB, 0xF1C8 }, { 0x86DF, 0xCEF1 }, { 0x86E4, 0xF9ED }, { 0x86ED, 0xF2F4 }, { 0x86FE, 0xE4B6 }, { 0x8700, 0xF5B9 }, { 0x8702, 0xDCF0 },
	{ 0x8703, 0xE3F1 }, { 0x8708, 0xE8A5 }, { 0x8718, 0xF2BB }, { 0x871A, 0xDEA4 }, { 0x871C, 0xDACC }, { 0x874E, 0xCAE9 }, { 0x8755, 0xE3DA }, { 0x8757, 0xFCD9 },
	{ 0x875F, 0xEADA }, { 0x8766, 0xF9C4 }, { 0x8768, 0xE3A4 }, { 0x8774, 0xFBDD }, { 0x8776, 0xEFCA }, { 0x8778, 0xE8C4 }, { 0x8782, 0xD5CC }, { 0x878D, 0xEBD7 },
	{ 0x879F, 0xD9AD }, { 0x87A2, 0xFBAB }, { 0x87B3, 0xD3D9 }, { 0x87BA, 0xD5A2 }, { 0x87C4, 0xF6DE }, { 0x87E0, 0xDAF6 }, { 0x87EC, 0xE0D1 }, { 0x87EF, 0xE9A8 },
	{ 0x87F2, 0xF5F9 }, { 0x87F9, 0xFAAF }, { 0x87FB, 0xEBFC }, { 0x87FE, 0xE0EA }, { 0x8805, 0xE3B2 }, { 0x881F, 0xD5C5 }, { 0x8822, 0xF1E3 }, { 0x8823, 0xD5EE },
	{ 0x8831, 0xCDCC }, { 0x8836, 0xEDD9 }, { 0x883B, 0xD8C1 }, { 0x8840, 0xFAEC }, { 0x8846, 0xF1EB }, { 0x884C, 0xFABC }, { 0x884D, 0xE6E2 }, { 0x8852, 0xFAE5 },
	{ 0x8853, 0xE2FA }, { 0x8857, 0xCAB6 }, { 0x8859, 0xE4B7 }, { 0x885B, 0xEADB }, { 0x885D, 0xF5FA }, { 0x8861, 0xFBAC }, { 0x8862, 0xCFC3 }, { 0x8863, 0xEBFD },
	{ 0x8868, 0xF8FA }, { 0x886B, 0xDFB9 }, { 0x8870, 0xE1F1 }, { 0x8872, 0xD2A4 }, { 0x8877, 0xF5FB }, { 0x887E, 0xD0DA }, { 0x887F, 0xD0DB }, { 0x8881, 0xEABE },
	{ 0x8882, 0xD9B1 }, { 0x8888, 0xCAB7 }, { 0x888B, 0xD3E7 }, { 0x888D, 0xF8E5 }, { 0x8892, 0xD3B2 }, { 0x8896, 0xE2C0 }, { 0x8897, 0xF2DF }, { 0x889E, 0xCDE5 },
	{ 0x88AB, 0xF9AC }, { 0x88B4, 0xCDCD }, { 0x88C1, 0xEEAE }, { 0x88C2, 0xD6AE }, { 0x88CF, 0xD7EA }, { 0x88D4, 0xE7E0 }, { 0x88D5, 0xEBAE }, { 0x88D9, 0xCFD9 },
	{ 0x88DC, 0xDCCD }, { 0x88DD, 0xEDFB }, { 0x88DF, 0xDEF0 }, { 0x88E1, 0xD7EB }, { 0x88E8, 0xDEA5 }, { 0x88F3, 0xDFD7 }, { 0x88F4, 0xDBD0 }, { 0x88F5, 0xDBD1 },
	{ 0x88F8, 0xD5A3 }, { 0x88FD, 0xF0B2 }, { 0x8907, 0xDCDC }, { 0x8910, 0xCAE8 }, { 0x8912, 0xF8E6 }, { 0x8913, 0xDCCE }, { 0x8918, 0xEADC }, { 0x8919, 0xDBD2 },
	{ 0x8925, 0xE9B3 }, { 0x892A, 0xF7DB }, { 0x8936, 0xE3A8 }, { 0x8938, 0xD7AE }, { 0x893B, 0xE0E1 }, { 0x8941, 0xCBBA }, { 0x8944, 0xE5D1 }, { 0x895F, 0xD0DC },
	{ 0x8964, 0xD5C1 }, { 0x896A, 0xD8CA }, { 0x8972, 0xE3A9 }, { 0x897F, 0xE0A4 }, { 0x8981, 0xE9A9 }, { 0x8983, 0xD3C7 }, { 0x8986, 0xDCDD }, { 0x8987, 0xF8AE },
	{ 0x898B, 0xCCB8 }, { 0x898F, 0xD0AE }, { 0x8993, 0xD8F2 }, { 0x8996, 0xE3CA }, { 0x89A1, 0xCCAF }, { 0x89A9, 0xD4AD }, { 0x89AA, 0xF6D1 }, { 0x89B2, 0xD0CC },
	{ 0x89BA, 0xCAC6 }, { 0x89BD, 0xD5C2 }, { 0x89C0, 0xCEBA }, { 0x89D2, 0xCAC7 }, { 0x89E3, 0xFAB0 }, { 0x89F4, 0xDFD8 }, { 0x89F8, 0xF5BA }, { 0x8A00, 0xE5EB },
	{ 0x8A02, 0xEFF4 }, { 0x8A03, 0xDDB5 }, { 0x8A08, 0xCDAA }, { 0x8A0A, 0xE3F2 }, { 0x8A0C, 0xFBF7 }, { 0x8A0E, 0xF7D0 }, { 0x8A13, 0xFDBA }, { 0x8A16, 0xFDE1 },
	{ 0x8A17, 0xF6FE }, { 0x8A18, 0xD1C0 }, { 0x8A1B, 0xE8C5 }, { 0x8A1D, 0xE4B8 }, { 0x8A1F, 0xE1E8 }, { 0x8A23, 0xCCC1 }, { 0x8A25, 0xD2ED }, { 0x8A2A, 0xDBBE },
	{ 0x8A2D, 0xE0E2 }, { 0x8A31, 0xFAC9 }, { 0x8A34, 0xE1CD }, { 0x8A36, 0xCAB8 }, { 0x8A3A, 0xF2E0 }, { 0x8A3B, 0xF1C9 }, { 0x8A50, 0xDEF1 }, { 0x8A54, 0xF0DF },
	{ 0x8A55, 0xF8C4 }, { 0x8A5B, 0xEECC }, { 0x8A5E, 0xDEF2 }, { 0x8A60, 0xE7C9 }, { 0x8A62, 0xE2F3 }, { 0x8A63, 0xE7E1 }, { 0x8A66, 0xE3CB }, { 0x8A69, 0xE3CC },
	{ 0x8A6D, 0xCFF8 }, { 0x8A6E, 0xEFAC }, { 0x8A70, 0xFDFE }, { 0x8A71, 0xFCA5 }, { 0x8A72, 0xFAB1 }, { 0x8A73, 0xDFD9 }, { 0x8A75, 0xE0D2 }, { 0x8A79, 0xF4DA },
	{ 0x8A85, 0xF1CA }, { 0x8A87, 0xCEA3 }, { 0x8A8C, 0xF2BC }, { 0x8A8D, 0xECE3 }, { 0x8A93, 0xE0A5 }, { 0x8A95, 0xF7AB }, { 0x8A98, 0xEBAF }, { 0x8A9E, 0xE5DE },
	{ 0x8AA0, 0xE1A4 }, { 0x8AA1, 0xCDAB }, { 0x8AA3, 0xD9F4 }, { 0x8AA4, 0xE8A6 }, { 0x8AA5, 0xCDCE }, { 0x8AA6, 0xE1E9 }, { 0x8AA8, 0xFCEF }, { 0x8AAA, 0xE0E3 },
	{ 0x8AB0, 0xE2C1 }, { 0x8AB2, 0xCEA4 }, { 0x8AB9, 0xDEA6 }, { 0x8ABC, 0xEBFE }, { 0x8ABE, 0xEBDD }, { 0x8ABF, 0xF0E0 }, { 0x8AC2, 0xF4DB }, { 0x8AC4, 0xE2F4 },
	{ 0x8AC7, 0xD3C8 }, { 0x8ACB, 0xF4EB }, { 0x8ACD, 0xEEB5 }, { 0x8ACF, 0xF5D8 }, { 0x8AD2, 0xD5DF }, { 0x8AD6, 0xD6E5 }, { 0x8ADB, 0xEBB0 }, { 0x8ADC, 0xF4E3 },
	{ 0x8AE1, 0xE3CD }, { 0x8AE6, 0xF4F4 }, { 0x8AE7, 0xFAB2 }, { 0x8AEA, 0xEFF5 }, { 0x8AEB, 0xCADF }, { 0x8AED, 0xEBB1 }, { 0x8AEE, 0xEDBF }, { 0x8AF1, 0xFDC9 },
	{ 0x8AF6, 0xE4A6 }, { 0x8AF7, 0xF9A4 }, { 0x8AF8, 0xF0B3 }, { 0x8AFA, 0xE5EC }, { 0x8AFE, 0xD1E7 }, { 0x8B00, 0xD9C7 }, { 0x8B01, 0xE4D7 }, { 0x8B02, 0xEADD },
	{ 0x8B04, 0xD4F7 }, { 0x8B0E, 0xDABA }, { 0x8B10, 0xDACD }, { 0x8B14, 0xF9CC }, { 0x8B16, 0xE1DA }, { 0x8B17, 0xDBBF }, { 0x8B19, 0xCCC5 }, { 0x8B1A, 0xECD0 },
	{ 0x8B1B, 0xCBBB }, { 0x8B1D, 0xDEF3 }, { 0x8B20, 0xE9AA }, { 0x8B28, 0xD9C8 }, { 0x8B2B, 0xEEE3 }, { 0x8B2C, 0xD7BD }, { 0x8B33, 0xCFC4 }, { 0x8B39, 0xD0CD },
	{ 0x8B41, 0xFCA6 }, { 0x8B49, 0xF1FB }, { 0x8B4E, 0xFDD2 }, { 0x8B4F, 0xD1C1 }, { 0x8B58, 0xE3DB }, { 0x8B5A, 0xD3C9 }, { 0x8B5C, 0xDCCF }, { 0x8B66, 0xCCED },
	{ 0x8B6C, 0xDEA7 }, { 0x8B6F, 0xE6BB }, { 0x8B70, 0xECA1 }, { 0x8B74, 0xCCB9 }, { 0x8B77, 0xFBDE }, { 0x8B7D, 0xE7E2 }, { 0x8B80, 0xD4C1 }, { 0x8B8A, 0xDCA8 },
	{ 0x8B90, 0xE2C2 }, { 0x8B92, 0xF3D8 }, { 0x8B93, 0xE5D3 }, { 0x8B96, 0xF3D9 }, { 0x8B9A, 0xF3C6 }, { 0x8C37, 0xCDDB }, { 0x8C3F, 0xCDAC }, { 0x8C41, 0xFCC3 },
	{ 0x8C46, 0xD4E7 }, { 0x8C48, 0xD1C2 }, { 0x8C4A, 0xF9A5 }, { 0x8C4C, 0xE8D5 }, { 0x8C55, 0xE3CE }, { 0x8C5A, 0xD4CA }, { 0x8C61, 0xDFDA }, { 0x8C6A, 0xFBDF },
	{ 0x8C6B, 0xE7E3 }, { 0x8C79, 0xF8FB }, { 0x8C7A, 0xE3CF }, { 0x8C82, 0xF5B0 }, { 0x8C8A, 0xD8E7 }, { 0x8C8C, 0xD9C9 }, { 0x8C9D, 0xF8AF }, { 0x8C9E, 0xEFF6 },
	{ 0x8CA0, 0xDDB6 }, { 0x8CA1, 0xEEAF }, { 0x8CA2, 0xCDF8 }, { 0x8CA7, 0xDEB8 }, { 0x8CA8, 0xFCA7 }, { 0x8CA9, 0xF7FC }, { 0x8CAA, 0xF7B1 }, { 0x8CAB, 0xCEBB },
	{ 0x8CAC, 0xF4A1 }, { 0x8CAF, 0xEECD }, { 0x8CB0, 0xE1AE }, { 0x8CB3, 0xECC3 }, { 0x8CB4, 0xCFFE }, { 0x8CB6, 0xF8BF }, { 0x8CB7, 0xD8E2 }, { 0x8CB8, 0xD3E8 },
	{ 0x8CBB, 0xDEA8 }, { 0x8CBC, 0xF4E4 }, { 0x8CBD, 0xECC2 }, { 0x8CBF, 0xD9F5 }, { 0x8CC0, 0xF9C5 }, { 0x8CC1, 0xDDD3 }, { 0x8CC2, 0xD6F1 }, { 0x8CC3, 0xECFC },
	{ 0x8CC4, 0xFCF0 }, { 0x8CC7, 0xEDC0 }, { 0x8CC8, 0xCAB9 }, { 0x8CCA, 0xEEE4 }, { 0x8CD1, 0xF2E1 }, { 0x8CD3, 0xDEB9 }, { 0x8CDA, 0xD6F2 }, { 0x8CDC, 0xDEF4 },
	{ 0x8CDE, 0xDFDB }, { 0x8CE0, 0xDBD3 }, { 0x8CE2, 0xFAE7 }, { 0x8CE3, 0xD8E3 }, { 0x8CE4, 0xF4C1 }, { 0x8CE6, 0xDDB7 }, { 0x8CEA, 0xF2F5 }, { 0x8CED, 0xD4AE },
	{ 0x8CF4, 0xD6F3 }, { 0x8CFB, 0xDDB8 }, { 0x8CFC, 0xCFC5 }, { 0x8CFD, 0xDFDF }, { 0x8D04, 0xF2BE }, { 0x8D05, 0xF6A1 }, { 0x8D07, 0xEBCB }, { 0x8D08, 0xF1FC },
	{ 0x8D0A, 0xF3C7 }, { 0x8D0D, 0xE0EB }, { 0x8D13, 0xEDFC }, { 0x8D16, 0xE1DB }, { 0x8D64, 0xEEE5 }, { 0x8D66, 0xDEF5 }, { 0x8D6B, 0xFAD3 }, { 0x8D70, 0xF1CB },
	{ 0x8D73, 0xD0AF }, { 0x8D74, 0xDDB9 }, { 0x8D77, 0xD1C3 }, { 0x8D85, 0xF5B1 }, { 0x8D8A, 0xEAC6 }, { 0x8D99, 0xF0E1 }, { 0x8DA3, 0xF6AC }, { 0x8DA8, 0xF5D9 },
	{ 0x8DB3, 0xF0EB }, { 0x8DBA, 0xDDBA }, { 0x8DBE, 0xF2BF }, { 0x8DC6, 0xF7C5 }, { 0x8DCB, 0xDBA2 }, { 0x8DCC, 0xF2F6 }, { 0x8DCF, 0xCABA }, { 0x8DDB, 0xF7F5 },
	{ 0x8DDD, 0xCBE5 }, { 0x8DE1, 0xEEE6 }, { 0x8DE3, 0xE0D3 }, { 0x8DE8, 0xCEA5 }, { 0x8DEF, 0xD6D8 }, { 0x8DF3, 0xD4AF }, { 0x8E0A, 0xE9C9 }, { 0x8E0F, 0xD3CE },
	{ 0x8E10, 0xF4C2 }, { 0x8E1E, 0xCBE6 }, { 0x8E2A, 0xF1A1 }, { 0x8E30, 0xEBB2 }, { 0x8E35, 0xF1A2 }, { 0x8E42, 0xEBB3 }, { 0x8E44, 0xF0B4 }, { 0x8E47, 0xCBF4 },
	{ 0x8E48, 0xD4B0 }, { 0x8E49, 0xF3B2 }, { 0x8E4A, 0xFBB7 }, { 0x8E59, 0xF5EC }, { 0x8E5F, 0xEEE7 }, { 0x8E60, 0xF4B2 }, { 0x8E74, 0xF5ED }, { 0x8E76, 0xCFF3 },
	{ 0x8E81, 0xF0E2 }, { 0x8E87, 0xEECE }, { 0x8E8A, 0xF1CC }, { 0x8E8D, 0xE5B8 }, { 0x8EAA, 0xD7F5 }, { 0x8EAB, 0xE3F3 }, { 0x8EAC, 0xCFE5 }, { 0x8EC0, 0xCFC6 },
	{ 0x8ECA, 0xF3B3 }, { 0x8ECB, 0xE4D8 }, { 0x8ECC, 0xCFF9 }, { 0x8ECD, 0xCFDA }, { 0x8ED2, 0xFACD }, { 0x8EDF, 0xE6E3 }, { 0x8EEB, 0xF2E2 }, { 0x8EF8, 0xF5EE },
	{ 0x8EFB, 0xCABB }, { 0x8EFE, 0xE3DC }, { 0x8F03, 0xCEF2 }, { 0x8F05, 0xD6D9 }, { 0x8F09, 0xEEB0 }, { 0x8F12, 0xF4E5 }, { 0x8F13, 0xD8C2 }, { 0x8F14, 0xDCD0 },
	{ 0x8F15, 0xCCEE }, { 0x8F1B, 0xD5E0 }, { 0x8F1C, 0xF6CA }, { 0x8F1D, 0xFDCA }, { 0x8F1E, 0xD8D6 }, { 0x8F1F, 0xF4CF }, { 0x8F26, 0xD6A6 }, { 0x8F27, 0xDCBE },
	{ 0x8F29, 0xDBD4 }, { 0x8F2A, 0xD7C7 }, { 0x8F2F, 0xF2FE }, { 0x8F33, 0xF1CD }, { 0x8F38, 0xE2C3 }, { 0x8F39, 0xDCDE }, { 0x8F3B, 0xDCDF }, { 0x8F3E, 0xEFAD },
	{ 0x8F3F, 0xE6AB }, { 0x8F44, 0xF9DD }, { 0x8F45, 0xEABF }, { 0x8F49, 0xEFAE }, { 0x8F4D, 0xF4D0 }, { 0x8F4E, 0xCEF3 }, { 0x8F5D, 0xE6AC }, { 0x8F5F, 0xCEDE },
	{ 0x8F62, 0xD5F9 }, { 0x8F9B, 0xE3F4 }, { 0x8F9C, 0xCDD0 }, { 0x8FA3, 0xD5B8 }, { 0x8FA6, 0xF7FD }, { 0x8FA8, 0xDCA9 }, { 0x8FAD, 0xDEF6 }, { 0x8FAF, 0xDCAA },
	{ 0x8FB0, 0xF2E3 }, { 0x8FB1, 0xE9B4 }, { 0x8FB2, 0xD2DC }, { 0x8FC2, 0xE9E6 }, { 0x8FC5, 0xE3F6 }, { 0x8FCE, 0xE7CA }, { 0x8FD1, 0xD0CE }, { 0x8FD4, 0xDAF7 },
	{ 0x8FE6, 0xCABC }, { 0x8FEA, 0xEEE8 }, { 0x8FEB, 0xDADE }, { 0x8FED, 0xF2F7 }, { 0x8FF0, 0xE2FB }, { 0x8FF2, 0xCCA6 }, { 0x8FF7, 0xDABB }, { 0x8FF9, 0xEEE9 },
	{ 0x8FFD, 0xF5DA }, { 0x9000, 0xF7DC }, { 0x9001, 0xE1EA }, { 0x9002, 0xCEC1 }, { 0x9003, 0xD4B1 }, { 0x9005, 0xFDB1 }, { 0x9006, 0xE6BD }, { 0x9008, 0xFBAD },
	{ 0x900B, 0xF8E7 }, { 0x900D, 0xE1CE }, { 0x900F, 0xF7E2 }, { 0x9010, 0xF5EF }, { 0x9011, 0xCFC7 }, { 0x9014, 0xD4B2 }, { 0x9015, 0xCCEF }, { 0x9017, 0xD4E8 },
	{ 0x9019, 0xEECF }, { 0x901A, 0xF7D7 }, { 0x901D, 0xE0A6 }, { 0x901E, 0xD6C1 }, { 0x901F, 0xE1DC }, { 0x9020, 0xF0E3 }, { 0x9021, 0xF1E4 }, { 0x9022, 0xDCF1 },
	{ 0x9023, 0xD6A7 }, { 0x902E, 0xF4F5 }, { 0x9031, 0xF1CE }, { 0x9032, 0xF2E4 }, { 0x9035, 0xD0B0 }, { 0x9038, 0xECEF }, { 0x903C, 0xF9BA }, { 0x903E, 0xEBB5 },
	{ 0x9041, 0xD4ED }, { 0x9042, 0xE2C4 }, { 0x9047, 0xE9E7 }, { 0x904A, 0xEBB4 }, { 0x904B, 0xEAA1 }, { 0x904D, 0xF8BC }, { 0x904E, 0xCEA6 }, { 0x9050, 0xF9C6 },
	{ 0x9051, 0xFCDA }, { 0x9053, 0xD4B3 }, { 0x9054, 0xD3B9 }, { 0x9055, 0xEADE }, { 0x9059, 0xE9AB }, { 0x905C, 0xE1E1 }, { 0x905D, 0xD3CF }, { 0x905E, 0xF4F6 },
	{ 0x9060, 0xEAC0 }, { 0x9061, 0xE1CF }, { 0x9063, 0xCCBA }, { 0x9069, 0xEEEA }, { 0x906D, 0xF0E4 }, { 0x906E, 0xF3B4 }, { 0x906F, 0xD4EE }, { 0x9072, 0xF2C0 },
	{ 0x9075, 0xF1E5 }, { 0x9077, 0xF4C3 }, { 0x9078, 0xE0D4 }, { 0x907A, 0xEBB6 }, { 0x907C, 0xD7A1 }, { 0x907D, 0xCBE8 }, { 0x907F, 0xF9AD }, { 0x9080, 0xE9AD },
	{ 0x9081, 0xD8E4 }, { 0x9082, 0xFAB3 }, { 0x9083, 0xE2C5 }, { 0x9084, 0xFCBD }, { 0x9087, 0xECC4 }, { 0x9088, 0xD8B1 }, { 0x908A, 0xDCAB }, { 0x908F, 0xD5A4 },
	{ 0x9091, 0xEBE9 }, { 0x9095, 0xE8BB }, { 0x9099, 0xD8D7 }, { 0x90A2, 0xFBAE }, { 0x90A3, 0xD1E1 }, { 0x90A6, 0xDBC0 }, { 0x90A8, 0xF5BE }, { 0x90AA, 0xDEF7 },
	{ 0x90AF, 0xCAFB }, { 0x90B0, 0xF7C6 }, { 0x90B1, 0xCFC8 }, { 0x90B5, 0xE1D0 }, { 0x90B8, 0xEED0 }, { 0x90C1, 0xE9F4 }, { 0x90CA, 0xCEF4 }, { 0x90DE, 0xD5CD },
	{ 0x90E1, 0xCFDB }, { 0x90E8, 0xDDBB }, { 0x90ED, 0xCEAC }, { 0x90F5, 0xE9E8 }, { 0x90FD, 0xD4B4 }, { 0x9102, 0xE4C7 }, { 0x9112, 0xF5DB }, { 0x9115, 0xFAC1 },
	{ 0x9119, 0xDEA9 }, { 0x9127, 0xD4F8 }, { 0x912D, 0xEFF7 }, { 0x9132, 0xD3B3 }, { 0x9149, 0xEBB7 }, { 0x914A, 0xEFF8 }, { 0x914B, 0xF5DC }, { 0x914C, 0xEDCC },
	{ 0x914D, 0xDBD5 }, { 0x914E, 0xF1CF }, { 0x9152, 0xF1D0 }, { 0x9162, 0xF5B2 }, { 0x9169, 0xD9AE }, { 0x916A, 0xD5AC }, { 0x916C, 0xE2C6 }, { 0x9175, 0xFDA3 },
	{ 0x9177, 0xFBE5 }, { 0x9178, 0xDFAB }, { 0x9187, 0xE2F5 }, { 0x9189, 0xF6AD }, { 0x918B, 0xF5B3 }, { 0x918D, 0xF0B5 }, { 0x9192, 0xE1A5 }, { 0x919C, 0xF5DD },
	{ 0x91AB, 0xECA2 }, { 0x91AC, 0xEDFD }, { 0x91AE, 0xF5B4 }, { 0x91AF, 0xFBB8 }, { 0x91B1, 0xDBA3 }, { 0x91B4, 0xD6CA }, { 0x91B5, 0xCBD9 }, { 0x91C0, 0xE5D4 },
	{ 0x91C7, 0xF3FA }, { 0x91C9, 0xEBB8 }, { 0x91CB, 0xE0B7 }, { 0x91CC, 0xD7EC }, { 0x91CD, 0xF1EC }, { 0x91CE, 0xE5AF }, { 0x91CF, 0xD5E1 }, { 0x91D0, 0xD7ED },
	{ 0x91D1, 0xD1D1 }, { 0x91D7, 0xE1F2 }, { 0x91D8, 0xEFF9 }, { 0x91DC, 0xDDBC }, { 0x91DD, 0xF6DC }, { 0x91E3, 0xF0E5 }, { 0x91E7, 0xF4C4 }, { 0x91EA, 0xE9E9 },
	{ 0x91F5, 0xF3FB }, { 0x920D, 0xD4EF }, { 0x9210, 0xCCA2 }, { 0x9211, 0xF7FE }, { 0x9212, 0xDFBC }, { 0x9217, 0xEBCD }, { 0x921E, 0xD0B7 }, { 0x9234, 0xD6C2 },
	{ 0x923A, 0xE8AD }, { 0x923F, 0xEFAF }, { 0x9240, 0xCBA5 }, { 0x9245, 0xCBE9 }, { 0x9249, 0xFAE8 }, { 0x9257, 0xCCC6 }, { 0x925B, 0xE6E7 }, { 0x925E, 0xEAC7 },
	{ 0x9262, 0xDBA4 }, { 0x9264, 0xCFC9 }, { 0x9265, 0xE2FC }, { 0x9266, 0xEFFA }, { 0x9280, 0xEBDE }, { 0x9283, 0xF5C8 }, { 0x9285, 0xD4DE }, { 0x9291, 0xE0D5 },
	{ 0x9293, 0xEFB0 }, { 0x9296, 0xE2C7 }, { 0x9298, 0xD9AF }, { 0x929C, 0xF9E7 }, { 0x92B3, 0xE7E5 }, { 0x92B6, 0xCFCA }, { 0x92B7, 0xE1D1 }, { 0x92B9, 0xE2C8 },
	{ 0x92CC, 0xEFFB }, { 0x92CF, 0xFAF9 }, { 0x92D2, 0xDCF2 }, { 0x92E4, 0xE0A7 }, { 0x92EA, 0xF8E8 }, { 0x92F8, 0xCBEA }, { 0x92FC, 0xCBBC }, { 0x9304, 0xD6E2 },
	{ 0x9310, 0xF5DE }, { 0x9318, 0xF5DF }, { 0x931A, 0xEEB6 }, { 0x931E, 0xE2F6 }, { 0x931F, 0xD3CA }, { 0x9320, 0xEFFC }, { 0x9321, 0xD1C4 }, { 0x9322, 0xEFB1 },
	{ 0x9324, 0xD1C5 }, { 0x9326, 0xD0DE }, { 0x9328, 0xD9E1 }, { 0x932B, 0xE0B8 }, { 0x932E, 0xCDD1 }, { 0x932F, 0xF3B9 }, { 0x9348, 0xE7CC }, { 0x934A, 0xD6A8 },
	{ 0x934B, 0xCEA7 }, { 0x934D, 0xD4B5 }, { 0x9354, 0xE4C8 }, { 0x935B, 0xD3B4 }, { 0x936E, 0xEBB9 }, { 0x9375, 0xCBF5 }, { 0x937C, 0xF6DD }, { 0x937E, 0xF1A3 },
	{ 0x938C, 0xCCC7 }, { 0x9394, 0xE9CA }, { 0x9396, 0xE1F0 }, { 0x939A, 0xF5E0 }, { 0x93A3, 0xFBAF }, { 0x93A7, 0xCBD1 }, { 0x93AC, 0xFBE0 }, { 0x93AD, 0xF2E5 },
	{ 0x93B0, 0xECF0 }, { 0x93C3, 0xF0EC }, { 0x93D1, 0xEEEB }, { 0x93DE, 0xE9CB }, { 0x93E1, 0xCCF0 }, { 0x93E4, 0xD7AF }, { 0x93F6, 0xF3A1 }, { 0x9404, 0xFCF5 },
	{ 0x9418, 0xF1A4 }, { 0x9425, 0xE0D6 }, { 0x942B, 0xEFB2 }, { 0x9435, 0xF4D1 }, { 0x9438, 0xF7A1 }, { 0x9444, 0xF1D1 }, { 0x9451, 0xCAFC }, { 0x9452, 0xCAFD },
	{ 0x945B, 0xCECE }, { 0x947D, 0xF3C8 }, { 0x947F, 0xF3BA }, { 0x9577, 0xEDFE }, { 0x9580, 0xDAA6 }, { 0x9583, 0xE0EC }, { 0x9589, 0xF8CD }, { 0x958B, 0xCBD2 },
	{ 0x958F, 0xEBCE }, { 0x9591, 0xF9D8 }, { 0x9592, 0xF9D9 }, { 0x9593, 0xCAE0 }, { 0x9594, 0xDACA }, { 0x9598, 0xCBA6 }, { 0x95A3, 0xCAC8 }, { 0x95A4, 0xF9EE },
	{ 0x95A5, 0xDBEC }, { 0x95A8, 0xD0B1 }, { 0x95AD, 0xD5EF }, { 0x95B1, 0xE6F3 }, { 0x95BB, 0xE7A2 }, { 0x95BC, 0xE4D9 }, { 0x95C7, 0xE4E1 }, { 0x95CA, 0xFCC4 },
	{ 0x95D4, 0xF9EF }, { 0x95D5, 0xCFF4 }, { 0x95D6, 0xF7E6 }, { 0x95DC, 0xCEBC }, { 0x95E1, 0xF4C5 }, { 0x95E2, 0xDCA3 }, { 0x961C, 0xDDBD }, { 0x9621, 0xF4C6 },
	{ 0x962A, 0xF8A1 }, { 0x962E, 0xE8D6 }, { 0x9632, 0xDBC1 }, { 0x963B, 0xF0E6 }, { 0x963F, 0xE4B9 }, { 0x9640, 0xF6ED }, { 0x9642, 0xF9AE }, { 0x9644, 0xDDBE },
	{ 0x964B, 0xD7B0 }, { 0x964C, 0xD8E8 }, { 0x964D, 0xCBBD }, { 0x9650, 0xF9DA }, { 0x965B, 0xF8CE }, { 0x965C, 0xF9F0 }, { 0x965D, 0xE0ED }, { 0x965E, 0xE3B3 },
	{ 0x965F, 0xF4B3 }, { 0x9662, 0xEAC2 }, { 0x9663, 0xF2E6 }, { 0x9664, 0xF0B6 }, { 0x966A, 0xDBD6 }, { 0x9670, 0xEBE4 }, { 0x9673, 0xF2E7 }, { 0x9675, 0xD7D5 },
	{ 0x9676, 0xD4B6 }, { 0x9677, 0xF9E8 }, { 0x9678, 0xD7C1 }, { 0x967D, 0xE5D5 }, { 0x9685, 0xE9EA }, { 0x9686, 0xD7CC }, { 0x968A, 0xD3E9 }, { 0x968B, 0xE2C9 },
	{ 0x968D, 0xFCDB }, { 0x968E, 0xCDAD }, { 0x9694, 0xCCB0 }, { 0x9695, 0xEAA2 }, { 0x9698, 0xE4F6 }, { 0x9699, 0xD0C0 }, { 0x969B, 0xF0B7 }, { 0x969C, 0xEEA1 },
	{ 0x96A3, 0xD7F6 }, { 0x96A7, 0xE2CA }, { 0x96A8, 0xE2CB }, { 0x96AA, 0xFACF }, { 0x96B1, 0xEBDF }, { 0x96B7, 0xD6CB }, { 0x96BB, 0xF4B4 }, { 0x96C0, 0xEDCD },
	{ 0x96C1, 0xE4D2 }, { 0x96C4, 0xEAA9 }, { 0x96C5, 0xE4BA }, { 0x96C6, 0xF3A2 }, { 0x96C7, 0xCDD2 }, { 0x96C9, 0xF6CB }, { 0x96CB, 0xF1E6 }, { 0x96CC, 0xEDC1 },
	{ 0x96CD, 0xE8BC }, { 0x96CE, 0xEED1 }, { 0x96D5, 0xF0E7 }, { 0x96D6, 0xE2CC }, { 0x96D9, 0xE4AA }, { 0x96DB, 0xF5E1 }, { 0x96DC, 0xEDDA }, { 0x96E2, 0xD7EE },
	{ 0x96E3, 0xD1F1 }, { 0x96E8, 0xE9EB }, { 0x96E9, 0xE9EC }, { 0x96EA, 0xE0E4 }, { 0x96EF, 0xDAA7 }, { 0x96F0, 0xDDD4 }, { 0x96F2, 0xEAA3 }, { 0x96F6, 0xD6C3 },
	{ 0x96F7, 0xD6F4 }, { 0x96F9, 0xDADF }, { 0x96FB, 0xEFB3 }, { 0x9700, 0xE2CD }, { 0x9706, 0xEFFD }, { 0x9707, 0xF2E8 }, { 0x9711, 0xEFC5 }, { 0x9713, 0xE7E7 },
	{ 0x9716, 0xD7FD }, { 0x9719, 0xE7CE }, { 0x971C, 0xDFDC }, { 0x971E, 0xF9C7 }, { 0x9727, 0xD9F6 }, { 0x9730, 0xDFAC }, { 0x9732, 0xD6DA }, { 0x9739, 0xDCA4 },
	{ 0x973D, 0xF0B8 }, { 0x9742, 0xD5FA }, { 0x9744, 0xE4F7 }, { 0x9748, 0xD6C4 }, { 0x9751, 0xF4EC }, { 0x9756, 0xEFFE }, { 0x975C, 0xF0A1 }, { 0x975E, 0xDEAA },
	{ 0x9761, 0xDABC }, { 0x9762, 0xD8FC }, { 0x9769, 0xFAD4 }, { 0x976D, 0xECE5 }, { 0x9774, 0xFCA8 }, { 0x9777, 0xECE6 }, { 0x977A, 0xD8CB }, { 0x978B, 0xFBB9 },
	{ 0x978D, 0xE4D3 }, { 0x978F, 0xCDF9 }, { 0x97A0, 0xCFD3 }, { 0x97A8, 0xCAEA }, { 0x97AB, 0xCFD4 }, { 0x97AD, 0xF8BD }, { 0x97C6, 0xF4C7 }, { 0x97CB, 0xEADF },
	{ 0x97D3, 0xF9DB }, { 0x97DC, 0xD4B7 }, { 0x97F3, 0xEBE5 }, { 0x97F6, 0xE1D2 }, { 0x97FB, 0xEAA4 }, { 0x97FF, 0xFAC2 }, { 0x9800, 0xFBE1 }, { 0x9801, 0xFAED },
	{ 0x9802, 0xF0A2 }, { 0x9803, 0xCCF1 }, { 0x9805, 0xFAA3 }, { 0x9806, 0xE2F7 }, { 0x9808, 0xE2CE }, { 0x980A, 0xE9F5 }, { 0x980C, 0xE1EB }, { 0x9810, 0xE7E8 },
	{ 0x9811, 0xE8D7 }, { 0x9812, 0xDAF8 }, { 0x9813, 0xD4CB }, { 0x9817, 0xF7F6 }, { 0x9818, 0xD6C5 }, { 0x982D, 0xD4E9 }, { 0x9830, 0xFAFA }, { 0x9838, 0xCCF2 },
	{ 0x9839, 0xF7DD }, { 0x983B, 0xDEBA }, { 0x9846, 0xCEA8 }, { 0x984C, 0xF0B9 }, { 0x984D, 0xE4FE }, { 0x984E, 0xE4C9 }, { 0x9854, 0xE4D4 }, { 0x9858, 0xEAC3 },
	{ 0x985A, 0xEFB4 }, { 0x985E, 0xD7BE }, { 0x9865, 0xFBE2 }, { 0x9867, 0xCDD3 }, { 0x986B, 0xEFB5 }, { 0x986F, 0xFAE9 }, { 0x98A8, 0xF9A6 }, { 0x98AF, 0xDFBD },
	{ 0x98B1, 0xF7C7 }, { 0x98C4, 0xF8FD }, { 0x98C7, 0xF8FC }, { 0x98DB, 0xDEAB }, { 0x98DC, 0xDBE8 }, { 0x98DF, 0xE3DD }, { 0x98E1, 0xE1E2 }, { 0x98E2, 0xD1C6 },
	{ 0x98ED, 0xF6D0 }, { 0x98EE, 0xEBE6 }, { 0x98EF, 0xDAF9 }, { 0x98F4, 0xECC7 }, { 0x98FC, 0xDEF8 }, { 0x98FD, 0xF8E9 }, { 0x98FE, 0xE3DE }, { 0x9903, 0xCEF5 },
	{ 0x9909, 0xFAC3 }, { 0x990A, 0xE5D7 }, { 0x990C, 0xECC8 }, { 0x9910, 0xF3C9 }, { 0x9913, 0xE4BB }, { 0x9918, 0xE6AE }, { 0x991E, 0xEFB6 }, { 0x9920, 0xDCBF },
	{ 0x9928, 0xCEBD }, { 0x9945, 0xD8C3 }, { 0x9949, 0xD0CF }, { 0x994B, 0xCFFA }, { 0x994C, 0xF3CA }, { 0x994D, 0xE0D7 }, { 0x9951, 0xD1C7 }, { 0x9952, 0xE9AE },
	{ 0x9954, 0xE8BD }, { 0x9957, 0xFAC4 }, { 0x9996, 0xE2CF }, { 0x9999, 0xFAC5 }, { 0x999D, 0xF9B8 }, { 0x99A5, 0xDCE0 }, { 0x99A8, 0xFBB0 }, { 0x99AC, 0xD8A9 },
	{ 0x99AD, 0xE5DF }, { 0x99AE, 0xF9A7 }, { 0x99B1, 0xF6EE }, { 0x99B3, 0xF6CC }, { 0x99B4, 0xE2F8 }, { 0x99B9, 0xECF1 }, { 0x99C1, 0xDAE0 }, { 0x99D0, 0xF1D2 },
	{ 0x99D1, 0xD2CC }, { 0x99D2, 0xCFCB }, { 0x99D5, 0xCABD }, { 0x99D9, 0xDDBF }, { 0x99DD, 0xF6EF }, { 0x99DF, 0xDEF9 }, { 0x99ED, 0xFAB4 }, { 0x99F1, 0xD5AD },
	{ 0x99FF, 0xF1E7 }, { 0x9A01, 0xDEBE }, { 0x9A08, 0xDCC0 }, { 0x9A0E, 0xD1C8 }, { 0x9A0F, 0xD1C9 }, { 0x9A19, 0xF8BE }, { 0x9A2B, 0xCBF6 }, { 0x9A30, 0xD4F9 },
	{ 0x9A36, 0xF5E2 }, { 0x9A37, 0xE1D3 }, { 0x9A40, 0xD8E9 }, { 0x9A43, 0xF8FE }, { 0x9A45, 0xCFCC }, { 0x9A4D, 0xFDA4 }, { 0x9A55, 0xCEF6 }, { 0x9A57, 0xFAD0 },
	{ 0x9A5A, 0xCCF3 }, { 0x9A5B, 0xE6BE }, { 0x9A5F, 0xF6AE }, { 0x9A62, 0xD5F0 }, { 0x9A65, 0xD1CA }, { 0x9A69, 0xFCBE }, { 0x9A6A, 0xD5F1 }, { 0x9AA8, 0xCDE9 },
	{ 0x9AB8, 0xFAB5 }, { 0x9AD3, 0xE2D0 }, { 0x9AD4, 0xF4F7 }, { 0x9AD8, 0xCDD4 }, { 0x9AE5, 0xE7A3 }, { 0x9AEE, 0xDBA5 }, { 0x9B1A, 0xE2D1 }, { 0x9B27, 0xD7A2 },
	{ 0x9B2A, 0xF7E3 }, { 0x9B31, 0xEAA6 }, { 0x9B3C, 0xD0A1 }, { 0x9B41, 0xCEDA }, { 0x9B42, 0xFBEB }, { 0x9B43, 0xDBA6 }, { 0x9B44, 0xDBDE }, { 0x9B45, 0xD8E5 },
	{ 0x9B4F, 0xEAE0 }, { 0x9B54, 0xD8AA }, { 0x9B5A, 0xE5E0 }, { 0x9B6F, 0xD6DB }, { 0x9B8E, 0xEFC6 }, { 0x9B91, 0xF8EA }, { 0x9B9F, 0xE4D5 }, { 0x9BAB, 0xCEF7 },
	{ 0x9BAE, 0xE0D8 }, { 0x9BC9, 0xD7EF }, { 0x9BD6, 0xF4ED }, { 0x9BE4, 0xCDE6 }, { 0x9BE8, 0xCCF4 }, { 0x9C0D, 0xF5E3 }, { 0x9C10, 0xE4CA }, { 0x9C12, 0xDCE1 },
	{ 0x9C15, 0xF9C8 }, { 0x9C25, 0xFCBF }, { 0x9C32, 0xE8A7 }, { 0x9C3B, 0xD8C4 }, { 0x9C47, 0xCBBE }, { 0x9C49, 0xDCAE }, { 0x9C57, 0xD7F7 }, { 0x9CE5, 0xF0E8 },
	{ 0x9CE7, 0xDDC0 }, { 0x9CE9, 0xCFCD }, { 0x9CF3, 0xDCF3 }, { 0x9CF4, 0xD9B0 }, { 0x9CF6, 0xE6E9 }, { 0x9D09, 0xE4BC }, { 0x9D1B, 0xEAC4 }, { 0x9D26, 0xE4EC },
	{ 0x9D28, 0xE4E5 }, { 0x9D3B, 0xFBF8 }, { 0x9D51, 0xCCBB }, { 0x9D5D, 0xE4BD }, { 0x9D60, 0xCDDC }, { 0x9D61, 0xD9F7 }, { 0x9D6C, 0xDDDF }, { 0x9D72, 0xEDCE },
	{ 0x9DA9, 0xD9D0 }, { 0x9DAF, 0xE5A3 }, { 0x9DB4, 0xF9CD }, { 0x9DC4, 0xCDAE }, { 0x9DD7, 0xCFCE }, { 0x9DF2, 0xF6AF }, { 0x9DF8, 0xFDD3 }, { 0x9DF9, 0xEBED },
	{ 0x9DFA, 0xD6DC }, { 0x9E1A, 0xE5A4 }, { 0x9E1E, 0xD5B6 }, { 0x9E75, 0xD6DD }, { 0x9E79, 0xF9E9 }, { 0x9E7D, 0xE7A4 }, { 0x9E7F, 0xD6E3 }, { 0x9E92, 0xD1CB },
	{ 0x9E93, 0xD6E4 }, { 0x9E97, 0xD5F2 }, { 0x9E9D, 0xDEFA }, { 0x9E9F, 0xD7F8 }, { 0x9EA5, 0xD8EA }, { 0x9EB4, 0xCFD5 }, { 0x9EB5, 0xD8FD }, { 0x9EBB, 0xD8AB },
	{ 0x9EBE, 0xFDCB }, { 0x9EC3, 0xFCDC }, { 0x9ECD, 0xE0A8 }, { 0x9ECE, 0xD5F3 }, { 0x9ED1, 0xFDD9 }, { 0x9ED4, 0xCCA3 }, { 0x9ED8, 0xD9F9 }, { 0x9EDB, 0xD3EA },
	{ 0x9EDC, 0xF5F5 }, { 0x9EDE, 0xEFC7 }, { 0x9EE8, 0xD3DA }, { 0x9EF4, 0xDABD }, { 0x9F07, 0xE8A8 }, { 0x9F08, 0xDCAF }, { 0x9F0E, 0xF0A3 }, { 0x9F13, 0xCDD5 },
	{ 0x9F20, 0xE0A9 }, { 0x9F3B, 0xDEAC }, { 0x9F4A, 0xF0BA }, { 0x9F4B, 0xEEB1 }, { 0x9F4E, 0xEEB2 }, { 0x9F52, 0xF6CD }, { 0x9F5F, 0xEED2 }, { 0x9F61, 0xD6C6 },
	{ 0x9F67, 0xE0E5 }, { 0x9F6A, 0xF3BB }, { 0x9F6C, 0xE5E1 }, { 0x9F77, 0xE4CB }, { 0x9F8D, 0xD7A3 }, { 0x9F90, 0xDBC2 }, { 0x9F95, 0xCAFE }, { 0x9F9C, 0xCFCF },
	{ 0xAC00, 0xB0A1 }, { 0xAC01, 0xB0A2 }, { 0xAC04, 0xB0A3 }, { 0xAC07, 0xB0A4 }, { 0xAC08, 0xB0A5 }, { 0xAC09, 0xB0A6 }, { 0xAC0A, 0xB0A7 }, { 0xAC10, 0xB0A8 },
	{ 0xAC11, 0xB0A9 }, { 0xAC12, 0xB0AA }, { 0xAC13, 0xB0AB }, { 0xAC14, 0xB0AC }, { 0xAC15, 0xB0AD }, { 0xAC16, 0xB0AE }, { 0xAC17, 0xB0AF }, { 0xAC19, 0xB0B0 },
	{ 0xAC1A, 0xB0B1 }, { 0xAC1B, 0xB0B2 }, { 0xAC1C, 0xB0B3 }, { 0xAC1D, 0xB0B4 }, { 0xAC20, 0xB0B5 }, { 0xAC24, 0xB0B6 }, { 0xAC2C, 0xB0B7 }, { 0xAC2D, 0xB0B8 },
	{ 0xAC2F, 0xB0B9 }, { 0xAC30, 0xB0BA }, { 0xAC31, 0xB0BB }, { 0xAC38, 0xB0BC }, { 0xAC39, 0xB0BD }, { 0xAC3C, 0xB0BE }, { 0xAC40, 0xB0BF }, { 0xAC4B, 0xB0C0 },
	{ 0xAC4D, 0xB0C1 }, { 0xAC54, 0xB0C2 }, { 0xAC58, 0xB0C3 }, { 0xAC5C, 0xB0C4 }, { 0xAC70, 0xB0C5 }, { 0xAC71, 0xB0C6 }, { 0xAC74, 0xB0C7 }, { 0xAC77, 0xB0C8 },
	{ 0xAC78, 0xB0C9 }, { 0xAC7A, 0xB0CA }, { 0xAC80, 0xB0CB }, { 0xAC81, 0xB0CC }, { 0xAC83, 0xB0CD }, { 0xAC84, 0xB0CE }, { 0xAC85, 0xB0CF }, { 0xAC86, 0xB0D0 },
	{ 0xAC89, 0xB0D1 }, { 0xAC8A, 0xB0D2 }, { 0xAC8B, 0xB0D3 }, { 0xAC8C, 0xB0D4 }, { 0xAC90, 0xB0D5 }, { 0xAC94, 0xB0D6 }, { 0xAC9C, 0xB0D7 }, { 0xAC9D, 0xB0D8 },
	{ 0xAC9F, 0xB0D9 }, { 0xACA0, 0xB0DA }, { 0xACA1, 0xB0DB }, { 0xACA8, 0xB0DC }, { 0xACA9, 0xB0DD }, { 0xACAA, 0xB0DE }, { 0xACAC, 0xB0DF }, { 0xACAF, 0xB0E0 },
	{ 0xACB0, 0xB0E1 }, { 0xACB8, 0xB0E2 }, { 0xACB9, 0xB0E3 }, { 0xACBB, 0xB0E4 }, { 0xACBC, 0xB0E5 }, { 0xACBD, 0xB0E6 }, { 0xACC1, 0xB0E7 }, { 0xACC4, 0xB0E8 },
	{ 0xACC8, 0xB0E9 }, { 0xACCC, 0xB0EA }, { 0xACD5, 0xB0EB }, { 0xACD7, 0xB0EC }, { 0xACE0, 0xB0ED }, { 0xACE1, 0xB0EE }, { 0xACE4, 0xB0EF }, { 0xACE7, 0xB0F0 },
	{ 0xACE8, 0xB0F1 }, { 0xACEA, 0xB0F2 }, { 0xACEC, 0xB0F3 }, { 0xACEF, 0xB0F4 }, { 0xACF0, 0xB0F5 }, { 0xACF1, 0xB0F6 }, { 0xACF3, 0xB0F7 }, { 0xACF5, 0xB0F8 },
	{ 0xACF6, 0xB0F9 }, { 0xACFC, 0xB0FA }, { 0xACFD, 0xB0FB }, { 0xAD00, 0xB0FC }, { 0xAD04, 0xB0FD }, { 0xAD06, 0xB0FE }, { 0xAD0C, 0xB1A1 }, { 0xAD0D, 0xB1A2 },
	{ 0xAD0F, 0xB1A3 }, { 0xAD11, 0xB1A4 }, { 0xAD18, 0xB1A5 }, { 0xAD1C, 0xB1A6 }, { 0xAD20, 0xB1A7 }, { 0xAD29, 0xB1A8 }, { 0xAD2C, 0xB1A9 }, { 0xAD2D, 0xB1AA },
	{ 0xAD34, 0xB1AB }, { 0xAD35, 0xB1AC }, { 0xAD38, 0xB1AD }, { 0xAD3C, 0xB1AE }, { 0xAD44, 0xB1AF }, { 0xAD45, 0xB1B0 }, { 0xAD47, 0xB1B1 }, { 0xAD49, 0xB1B2 },
	{ 0xAD50, 0xB1B3 }, { 0xAD54, 0xB1B4 }, { 0xAD58, 0xB1B5 }, { 0xAD61, 0xB1B6 }, { 0xAD63, 0xB1B7 }, { 0xAD6C, 0xB1B8 }, { 0xAD6D, 0xB1B9 }, { 0xAD70, 0xB1BA },
	{ 0xAD73, 0xB1BB }, { 0xAD74, 0xB1BC }, { 0xAD75, 0xB1BD }, { 0xAD76, 0xB1BE }, { 0xAD7B, 0xB1BF }, { 0xAD7C, 0xB1C0 }, { 0xAD7D, 0xB1C1 }, { 0xAD7F, 0xB1C2 },
	{ 0xAD81, 0xB1C3 }, { 0xAD82, 0xB1C4 }, { 0xAD88, 0xB1C5 }, { 0xAD89, 0xB1C6 }, { 0xAD8C, 0xB1C7 }, { 0xAD90, 0xB1C8 }, { 0xAD9C, 0xB1C9 }, { 0xAD9D, 0xB1CA },
	{ 0xADA4, 0xB1CB }, { 0xADB7, 0xB1CC }, { 0xADC0, 0xB1CD }, { 0xADC1, 0xB1CE }, { 0xADC4, 0xB1CF }, { 0xADC8, 0xB1D0 }, { 0xADD0, 0xB1D1 }, { 0xADD1, 0xB1D2 },
	{ 0xADD3, 0xB1D3 }, { 0xADDC, 0xB1D4 }, { 0xADE0, 0xB1D5 }, { 0xADE4, 0xB1D6 }, { 0xADF8, 0xB1D7 }, { 0xADF9, 0xB1D8 }, { 0xADFC, 0xB1D9 }, { 0xADFF, 0xB1DA },
	{ 0xAE00, 0xB1DB }, { 0xAE01, 0xB1DC }, { 0xAE08, 0xB1DD }, { 0xAE09, 0xB1DE }, { 0xAE0B, 0xB1DF }, { 0xAE0D, 0xB1E0 }, { 0xAE14, 0xB1E1 }, { 0xAE30, 0xB1E2 },
	{ 0xAE31, 0xB1E3 }, { 0xAE34, 0xB1E4 }, { 0xAE37, 0xB1E5 }, { 0xAE38, 0xB1E6 }, { 0xAE3A, 0xB1E7 }, { 0xAE40, 0xB1E8 }, { 0xAE41, 0xB1E9 }, { 0xAE43, 0xB1EA },
	{ 0xAE45, 0xB1EB }, { 0xAE46, 0xB1EC }, { 0xAE4A, 0xB1ED }, { 0xAE4C, 0xB1EE }, { 0xAE4D, 0xB1EF }, { 0xAE4E, 0xB1F0 }, { 0xAE50, 0xB1F1 }, { 0xAE54, 0xB1F2 },
	{ 0xAE56, 0xB1F3 }, { 0xAE5C, 0xB1F4 }, { 0xAE5D, 0xB1F5 }, { 0xAE5F, 0xB1F6 }, { 0xAE60, 0xB1F7 }, { 0xAE61, 0xB1F8 }, { 0xAE65, 0xB1F9 }, { 0xAE68, 0xB1FA },
	{ 0xAE69, 0xB1FB }, { 0xAE6C, 0xB1FC }, { 0xAE70, 0xB1FD }, { 0xAE78, 0xB1FE }, { 0xAE79, 0xB2A1 }, { 0xAE7B, 0xB2A2 }, { 0xAE7C, 0xB2A3 }, { 0xAE7D, 0xB2A4 },
	{ 0xAE84, 0xB2A5 }, { 0xAE85, 0xB2A6 }, { 0xAE8C, 0xB2A7 }, { 0xAEBC, 0xB2A8 }, { 0xAEBD, 0xB2A9 }, { 0xAEBE, 0xB2AA }, { 0xAEC0, 0xB2AB }, { 0xAEC4, 0xB2AC },
	{ 0xAECC, 0xB2AD }, { 0xAECD, 0xB2AE }, { 0xAECF, 0xB2AF }, { 0xAED0, 0xB2B0 }, { 0xAED1, 0xB2B1 }, { 0xAED8, 0xB2B2 }, { 0xAED9, 0xB2B3 }, { 0xAEDC, 0xB2B4 },
	{ 0xAEE8, 0xB2B5 }, { 0xAEEB, 0xB2B6 }, { 0xAEED, 0xB2B7 }, { 0xAEF4, 0xB2B8 }, { 0xAEF8, 0xB2B9 }, { 0xAEFC, 0xB2BA }, { 0xAF07, 0xB2BB }, { 0xAF08, 0xB2BC },
	{ 0xAF0D, 0xB2BD }, { 0xAF10, 0xB2BE }, { 0xAF2C, 0xB2BF }, { 0xAF2D, 0xB2C0 }, { 0xAF30, 0xB2C1 }, { 0xAF32, 0xB2C2 }, { 0xAF34, 0xB2C3 }, { 0xAF3C, 0xB2C4 },
	{ 0xAF3D, 0xB2C5 }, { 0xAF3F, 0xB2C6 }, { 0xAF41, 0xB2C7 }, { 0xAF42, 0xB2C8 }, { 0xAF43, 0xB2C9 }, { 0xAF48, 0xB2CA }, { 0xAF49, 0xB2CB }, { 0xAF50, 0xB2CC },
	{ 0xAF5C, 0xB2CD }, { 0xAF5D, 0xB2CE }, { 0xAF64, 0xB2CF }, { 0xAF65, 0xB2D0 }, { 0xAF79, 0xB2D1 }, { 0xAF80, 0xB2D2 }, { 0xAF84, 0xB2D3 }, { 0xAF88, 0xB2D4 },
	{ 0xAF90, 0xB2D5 }, { 0xAF91, 0xB2D6 }, { 0xAF95, 0xB2D7 }, { 0xAF9C, 0xB2D8 }, { 0xAFB8, 0xB2D9 }, { 0xAFB9, 0xB2DA }, { 0xAFBC, 0xB2DB }, { 0xAFC0, 0xB2DC },
	{ 0xAFC7, 0xB2DD }, { 0xAFC8, 0xB2DE }, { 0xAFC9, 0xB2DF }, { 0xAFCB, 0xB2E0 }, { 0xAFCD, 0xB2E1 }, { 0xAFCE, 0xB2E2 }, { 0xAFD4, 0xB2E3 }, { 0xAFDC, 0xB2E4 },
	{ 0xAFE8, 0xB2E5 }, { 0xAFE9, 0xB2E6 }, { 0xAFF0, 0xB2E7 }, { 0xAFF1, 0xB2E8 }, { 0xAFF4, 0xB2E9 }, { 0xAFF8, 0xB2EA }, { 0xB000, 0xB2EB }, { 0xB001, 0xB2EC },
	{ 0xB004, 0xB2ED }, { 0xB00C, 0xB2EE }, { 0xB010, 0xB2EF }, { 0xB014, 0xB2F0 }, { 0xB01C, 0xB2F1 }, { 0xB01D, 0xB2F2 }, { 0xB028, 0xB2F3 }, { 0xB044, 0xB2F4 },
	{ 0xB045, 0xB2F5 }, { 0xB048, 0xB2F6 }, { 0xB04A, 0xB2F7 }, { 0xB04C, 0xB2F8 }, { 0xB04E, 0xB2F9 }, { 0xB053, 0xB2FA }, { 0xB054, 0xB2FB }, { 0xB055, 0xB2FC },
	{ 0xB057, 0xB2FD }, { 0xB059, 0xB2FE }, { 0xB05D, 0xB3A1 }, { 0xB07C, 0xB3A2 }, { 0xB07D, 0xB3A3 }, { 0xB080, 0xB3A4 }, { 0xB084, 0xB3A5 }, { 0xB08C, 0xB3A6 },
	{ 0xB08D, 0xB3A7 }, { 0xB08F, 0xB3A8 }, { 0xB091, 0xB3A9 }, { 0xB098, 0xB3AA }, { 0xB099, 0xB3AB }, { 0xB09A, 0xB3AC }, { 0xB09C, 0xB3AD }, { 0xB09F, 0xB3AE },
	{ 0xB0A0, 0xB3AF }, { 0xB0A1, 0xB3B0 }, { 0xB0A2, 0xB3B1 }, { 0xB0A8, 0xB3B2 }, { 0xB0A9, 0xB3B3 }, { 0xB0AB, 0xB3B4 }, { 0xB0AC, 0xB3B5 }, { 0xB0AD, 0xB3B6 },
	{ 0xB0AE, 0xB3B7 }, { 0xB0AF, 0xB3B8 }, { 0xB0B1, 0xB3B9 }, { 0xB0B3, 0xB3BA }, { 0xB0B4, 0xB3BB }, { 0xB0B5, 0xB3BC }, { 0xB0B8, 0xB3BD }, { 0xB0BC, 0xB3BE },
	{ 0xB0C4, 0xB3BF }, { 0xB0C5, 0xB3C0 }, { 0xB0C7, 0xB3C1 }, { 0xB0C8, 0xB3C2 }, { 0xB0C9, 0xB3C3 }, { 0xB0D0, 0xB3C4 }, { 0xB0D1, 0xB3C5 }, { 0xB0D4, 0xB3C6 },
	{ 0xB0D8, 0xB3C7 }, { 0xB0E0, 0xB3C8 }, { 0xB0E5, 0xB3C9 }, { 0xB108, 0xB3CA }, { 0xB109, 0xB3CB }, { 0xB10B, 0xB3CC }, { 0xB10C, 0xB3CD }, { 0xB110, 0xB3CE },
	{ 0xB112, 0xB3CF }, { 0xB113, 0xB3D0 }, { 0xB118, 0xB3D1 }, { 0xB119, 0xB3D2 }, { 0xB11B, 0xB3D3 }, { 0xB11C, 0xB3D4 }, { 0xB11D, 0xB3D5 }, { 0xB123, 0xB3D6 },
	{ 0xB124, 0xB3D7 }, { 0xB125, 0xB3D8 }, { 0xB128, 0xB3D9 }, { 0xB12C, 0xB3DA }, { 0xB134, 0xB3DB }, { 0xB135, 0xB3DC }, { 0xB137, 0xB3DD }, { 0xB138, 0xB3DE },
	{ 0xB139, 0xB3DF }, { 0xB140, 0xB3E0 }, { 0xB141, 0xB3E1 }, { 0xB144, 0xB3E2 }, { 0xB148, 0xB3E3 }, { 0xB150, 0xB3E4 }, { 0xB151, 0xB3E5 }, { 0xB154, 0xB3E6 },
	{ 0xB155, 0xB3E7 }, { 0xB158, 0xB3E8 }, { 0xB15C, 0xB3E9 }, { 0xB160, 0xB3EA }, { 0xB178, 0xB3EB }, { 0xB179, 0xB3EC }, { 0xB17C, 0xB3ED }, { 0xB180, 0xB3EE },
	{ 0xB182, 0xB3EF }, { 0xB188, 0xB3F0 }, { 0xB189, 0xB3F1 }, { 0xB18B, 0xB3F2 }, { 0xB18D, 0xB3F3 }, { 0xB192, 0xB3F4 }, { 0xB193, 0xB3F5 }, { 0xB194, 0xB3F6 },
	{ 0xB198, 0xB3F7 }, { 0xB19C, 0xB3F8 }, { 0xB1A8, 0xB3F9 }, { 0xB1CC, 0xB3FA }, { 0xB1D0, 0xB3FB }, { 0xB1D4, 0xB3FC }, { 0xB1DC, 0xB3FD }, { 0xB1DD, 0xB3FE },
	{ 0xB1DF, 0xB4A1 }, { 0xB1E8, 0xB4A2 }, { 0xB1E9, 0xB4A3 }, { 0xB1EC, 0xB4A4 }, { 0xB1F0, 0xB4A5 }, { 0xB1F9, 0xB4A6 }, { 0xB1FB, 0xB4A7 }, { 0xB1FD, 0xB4A8 },
	{ 0xB204, 0xB4A9 }, { 0xB205, 0xB4AA }, { 0xB208, 0xB4AB }, { 0xB20B, 0xB4AC }, { 0xB20C, 0xB4AD }, { 0xB214, 0xB4AE }, { 0xB215, 0xB4AF }, { 0xB217, 0xB4B0 },
	{ 0xB219, 0xB4B1 }, { 0xB220, 0xB4B2 }, { 0xB234, 0xB4B3 }, { 0xB23C, 0xB4B4 }, { 0xB258, 0xB4B5 }, { 0xB25C, 0xB4B6 }, { 0xB260, 0xB4B7 }, { 0xB268, 0xB4B8 },
	{ 0xB269, 0xB4B9 }, { 0xB274, 0xB4BA }, { 0xB275, 0xB4BB }, { 0xB27C, 0xB4BC }, { 0xB284, 0xB4BD }, { 0xB285, 0xB4BE }, { 0xB289, 0xB4BF }, { 0xB290, 0xB4C0 },
	{ 0xB291, 0xB4C1 }, { 0xB294, 0xB4C2 }, { 0xB298, 0xB4C3 }, { 0xB299, 0xB4C4 }, { 0xB29A, 0xB4C5 }, { 0xB2A0, 0xB4C6 }, { 0xB2A1, 0xB4C7 }, { 0xB2A3, 0xB4C8 },
	{ 0xB2A5, 0xB4C9 }, { 0xB2A6, 0xB4CA }, { 0xB2AA, 0xB4CB }, { 0xB2AC, 0xB4CC }, { 0xB2B0, 0xB4CD }, { 0xB2B4, 0xB4CE }, { 0xB2C8, 0xB4CF }, { 0xB2C9, 0xB4D0 },
	{ 0xB2CC, 0xB4D1 }, { 0xB2D0, 0xB4D2 }, { 0xB2D2, 0xB4D3 }, { 0xB2D8, 0xB4D4 }, { 0xB2D9, 0xB4D5 }, { 0xB2DB, 0xB4D6 }, { 0xB2DD, 0xB4D7 }, { 0xB2E2, 0xB4D8 },
	{ 0xB2E4, 0xB4D9 }, { 0xB2E5, 0xB4DA }, { 0xB2E6, 0xB4DB }, { 0xB2E8, 0xB4DC }, { 0xB2EB, 0xB4DD }, { 0xB2EC, 0xB4DE }, { 0xB2ED, 0xB4DF }, { 0xB2EE, 0xB4E0 },
	{ 0xB2EF, 0xB4E1 }, { 0xB2F3, 0xB4E2 }, { 0xB2F4, 0xB4E3 }, { 0xB2F5, 0xB4E4 }, { 0xB2F7, 0xB4E5 }, { 0xB2F8, 0xB4E6 }, { 0xB2F9, 0xB4E7 }, { 0xB2FA, 0xB4E8 },
	{ 0xB2FB, 0xB4E9 }, { 0xB2FF, 0xB4EA }, { 0xB300, 0xB4EB }, { 0xB301, 0xB4EC }, { 0xB304, 0xB4ED }, { 0xB308, 0xB4EE }, { 0xB310, 0xB4EF }, { 0xB311, 0xB4F0 },
	{ 0xB313, 0xB4F1 }, { 0xB314, 0xB4F2 }, { 0xB315, 0xB4F3 }, { 0xB31C, 0xB4F4 }, { 0xB354, 0xB4F5 }, { 0xB355, 0xB4F6 }, { 0xB356, 0xB4F7 }, { 0xB358, 0xB4F8 },
	{ 0xB35B, 0xB4F9 }, { 0xB35C, 0xB4FA }, { 0xB35E, 0xB4FB }, { 0xB35F, 0xB4FC }, { 0xB364, 0xB4FD }, { 0xB365, 0xB4FE }, { 0xB367, 0xB5A1 }, { 0xB369, 0xB5A2 },
	{ 0xB36B, 0xB5A3 }, { 0xB36E, 0xB5A4 }, { 0xB370, 0xB5A5 }, { 0xB371, 0xB5A6 }, { 0xB374, 0xB5A7 }, { 0xB378, 0xB5A8 }, { 0xB380, 0xB5A9 }, { 0xB381, 0xB5AA },
	{ 0xB383, 0xB5AB }, { 0xB384, 0xB5AC }, { 0xB385, 0xB5AD }, { 0xB38C, 0xB5AE }, { 0xB390, 0xB5AF }, { 0xB394, 0xB5B0 }, { 0xB3A0, 0xB5B1 }, { 0xB3A1, 0xB5B2 },
	{ 0xB3A8, 0xB5B3 }, { 0xB3AC, 0xB5B4 }, { 0xB3C4, 0xB5B5 }, { 0xB3C5, 0xB5B6 }, { 0xB3C8, 0xB5B7 }, { 0xB3CB, 0xB5B8 }, { 0xB3CC, 0xB5B9 }, { 0xB3CE, 0xB5BA },
	{ 0xB3D0, 0xB5BB }, { 0xB3D4, 0xB5BC }, { 0xB3D5, 0xB5BD }, { 0xB3D7, 0xB5BE }, { 0xB3D9, 0xB5BF }, { 0xB3DB, 0xB5C0 }, { 0xB3DD, 0xB5C1 }, { 0xB3E0, 0xB5C2 },
	{ 0xB3E4, 0xB5C3 }, { 0xB3E8, 0xB5C4 }, { 0xB3FC, 0xB5C5 }, { 0xB410, 0xB5C6 }, { 0xB418, 0xB5C7 }, { 0xB41C, 0xB5C8 }, { 0xB420, 0xB5C9 }, { 0xB428, 0xB5CA },
	{ 0xB429, 0xB5CB }, { 0xB42B, 0xB5CC }, { 0xB434, 0xB5CD }, { 0xB450, 0xB5CE }, { 0xB451, 0xB5CF }, { 0xB454, 0xB5D0 }, { 0xB458, 0xB5D1 }, { 0xB460, 0xB5D2 },
	{ 0xB461, 0xB5D3 }, { 0xB463, 0xB5D4 }, { 0xB465, 0xB5D5 }, { 0xB46C, 0xB5D6 }, { 0xB480, 0xB5D7 }, { 0xB488, 0xB5D8 }, { 0xB49D, 0xB5D9 }, { 0xB4A4, 0xB5DA },
	{ 0xB4A8, 0xB5DB }, { 0xB4AC, 0xB5DC }, { 0xB4B5, 0xB5DD }, { 0xB4B7, 0xB5DE }, { 0xB4B9, 0xB5DF }, { 0xB4C0, 0xB5E0 }, { 0xB4C4, 0xB5E1 }, { 0xB4C8, 0xB5E2 },
	{ 0xB4D0, 0xB5E3 }, { 0xB4D5, 0xB5E4 }, { 0xB4DC, 0xB5E5 }, { 0xB4DD, 0xB5E6 }, { 0xB4E0, 0xB5E7 }, { 0xB4E3, 0xB5E8 }, { 0xB4E4, 0xB5E9 }, { 0xB4E6, 0xB5EA },
	{ 0xB4EC, 0xB5EB }, { 0xB4ED, 0xB5EC }, { 0xB4EF, 0xB5ED }, { 0xB4F1, 0xB5EE }, { 0xB4F8, 0xB5EF }, { 0xB514, 0xB5F0 }, { 0xB515, 0xB5F1 }, { 0xB518, 0xB5F2 },
	{ 0xB51B, 0xB5F3 }, { 0xB51C, 0xB5F4 }, { 0xB524, 0xB5F5 }, { 0xB525, 0xB5F6 }, { 0xB527, 0xB5F7 }, { 0xB528, 0xB5F8 }, { 0xB529, 0xB5F9 }, { 0xB52A, 0xB5FA },
	{ 0xB530, 0xB5FB }, { 0xB531, 0xB5FC }, { 0xB534, 0xB5FD }, { 0xB538, 0xB5FE }, { 0xB540, 0xB6A1 }, { 0xB541, 0xB6A2 }, { 0xB543, 0xB6A3 }, { 0xB544, 0xB6A4 },
	{ 0xB545, 0xB6A5 }, { 0xB54B, 0xB6A6 }, { 0xB54C, 0xB6A7 }, { 0xB54D, 0xB6A8 }, { 0xB550, 0xB6A9 }, { 0xB554, 0xB6AA }, { 0xB55C, 0xB6AB }, { 0xB55D, 0xB6AC },
	{ 0xB55F, 0xB6AD }, { 0xB560, 0xB6AE }, { 0xB561, 0xB6AF }, { 0xB5A0, 0xB6B0 }, { 0xB5A1, 0xB6B1 }, { 0xB5A4, 0xB6B2 }, { 0xB5A8, 0xB6B3 }, { 0xB5AA, 0xB6B4 },
	{ 0xB5AB, 0xB6B5 }, { 0xB5B0, 0xB6B6 }, { 0xB5B1, 0xB6B7 }, { 0xB5B3, 0xB6B8 }, { 0xB5B4, 0xB6B9 }, { 0xB5B5, 0xB6BA }, { 0xB5BB, 0xB6BB }, { 0xB5BC, 0xB6BC },
	{ 0xB5BD, 0xB6BD }, { 0xB5C0, 0xB6BE }, { 0xB5C4, 0xB6BF }, { 0xB5CC, 0xB6C0 }, { 0xB5CD, 0xB6C1 }, { 0xB5CF, 0xB6C2 }, { 0xB5D0, 0xB6C3 }, { 0xB5D1, 0xB6C4 },
	{ 0xB5D8, 0xB6C5 }, { 0xB5EC, 0xB6C6 }, { 0xB610, 0xB6C7 }, { 0xB611, 0xB6C8 }, { 0xB614, 0xB6C9 }, { 0xB618, 0xB6CA }, { 0xB625, 0xB6CB }, { 0xB62C, 0xB6CC },
	{ 0xB634, 0xB6CD }, { 0xB648, 0xB6CE }, { 0xB664, 0xB6CF }, { 0xB668, 0xB6D0 }, { 0xB69C, 0xB6D1 }, { 0xB69D, 0xB6D2 }, { 0xB6A0, 0xB6D3 }, { 0xB6A4, 0xB6D4 },
	{ 0xB6AB, 0xB6D5 }, { 0xB6AC, 0xB6D6 }, { 0xB6B1, 0xB6D7 }, { 0xB6D4, 0xB6D8 }, { 0xB6F0, 0xB6D9 }, { 0xB6F4, 0xB6DA }, { 0xB6F8, 0xB6DB }, { 0xB700, 0xB6DC },
	{ 0xB701, 0xB6DD }, { 0xB705, 0xB6DE }, { 0xB728, 0xB6DF }, { 0xB729, 0xB6E0 }, { 0xB72C, 0xB6E1 }, { 0xB72F, 0xB6E2 }, { 0xB730, 0xB6E3 }, { 0xB738, 0xB6E4 },
	{ 0xB739, 0xB6E5 }, { 0xB73B, 0xB6E6 }, { 0xB744, 0xB6E7 }, { 0xB748, 0xB6E8 }, { 0xB74C, 0xB6E9 }, { 0xB754, 0xB6EA }, { 0xB755, 0xB6EB }, { 0xB760, 0xB6EC },
	{ 0xB764, 0xB6ED }, { 0xB768, 0xB6EE }, { 0xB770, 0xB6EF }, { 0xB771, 0xB6F0 }, { 0xB773, 0xB6F1 }, { 0xB775, 0xB6F2 }, { 0xB77C, 0xB6F3 }, { 0xB77D, 0xB6F4 },
	{ 0xB780, 0xB6F5 }, { 0xB784, 0xB6F6 }, { 0xB78C, 0xB6F7 }, { 0xB78D, 0xB6F8 }, { 0xB78F, 0xB6F9 }, { 0xB790, 0xB6FA }, { 0xB791, 0xB6FB }, { 0xB792, 0xB6FC },
	{ 0xB796, 0xB6FD }, { 0xB797, 0xB6FE }, { 0xB798, 0xB7A1 }, { 0xB799, 0xB7A2 }, { 0xB79C, 0xB7A3 }, { 0xB7A0, 0xB7A4 }, { 0xB7A8, 0xB7A5 }, { 0xB7A9, 0xB7A6 },
	{ 0xB7AB, 0xB7A7 }, { 0xB7AC, 0xB7A8 }, { 0xB7AD, 0xB7A9 }, { 0xB7B4, 0xB7AA }, { 0xB7B5, 0xB7AB }, { 0xB7B8, 0xB7AC }, { 0xB7C7, 0xB7AD }, { 0xB7C9, 0xB7AE },
	{ 0xB7EC, 0xB7AF }, { 0xB7ED, 0xB7B0 }, { 0xB7F0, 0xB7B1 }, { 0xB7F4, 0xB7B2 }, { 0xB7FC, 0xB7B3 }, { 0xB7FD, 0xB7B4 }, { 0xB7FF, 0xB7B5 }, { 0xB800, 0xB7B6 },
	{ 0xB801, 0xB7B7 }, { 0xB807, 0xB7B8 }, { 0xB808, 0xB7B9 }, { 0xB809, 0xB7BA }, { 0xB80C, 0xB7BB }, { 0xB810, 0xB7BC }, { 0xB818, 0xB7BD }, { 0xB819, 0xB7BE },
	{ 0xB81B, 0xB7BF }, { 0xB81D, 0xB7C0 }, { 0xB824, 0xB7C1 }, { 0xB825, 0xB7C2 }, { 0xB828, 0xB7C3 }, { 0xB82C, 0xB7C4 }, { 0xB834, 0xB7C5 }, { 0xB835, 0xB7C6 },
	{ 0xB837, 0xB7C7 }, { 0xB838, 0xB7C8 }, { 0xB839, 0xB7C9 }, { 0xB840, 0xB7CA }, { 0xB844, 0xB7CB }, { 0xB851, 0xB7CC }, { 0xB853, 0xB7CD }, { 0xB85C, 0xB7CE },
	{ 0xB85D, 0xB7CF }, { 0xB860, 0xB7D0 }, { 0xB864, 0xB7D1 }, { 0xB86C, 0xB7D2 }, { 0xB86D, 0xB7D3 }, { 0xB86F, 0xB7D4 }, { 0xB871, 0xB7D5 }, { 0xB878, 0xB7D6 },
	{ 0xB87C, 0xB7D7 }, { 0xB88D, 0xB7D8 }, { 0xB8A8, 0xB7D9 }, { 0xB8B0, 0xB7DA }, { 0xB8B4, 0xB7DB }, { 0xB8B8, 0xB7DC }, { 0xB8C0, 0xB7DD }, { 0xB8C1, 0xB7DE },
	{ 0xB8C3, 0xB7DF }, { 0xB8C5, 0xB7E0 }, { 0xB8CC, 0xB7E1 }, { 0xB8D0, 0xB7E2 }, { 0xB8D4, 0xB7E3 }, { 0xB8DD, 0xB7E4 }, { 0xB8DF, 0xB7E5 }, { 0xB8E1, 0xB7E6 },
	{ 0xB8E8, 0xB7E7 }, { 0xB8E9, 0xB7E8 }, { 0xB8EC, 0xB7E9 }, { 0xB8F0, 0xB7EA }, { 0xB8F8, 0xB7EB }, { 0xB8F9, 0xB7EC }, { 0xB8FB, 0xB7ED }, { 0xB8FD, 0xB7EE },
	{ 0xB904, 0xB7EF }, { 0xB918, 0xB7F0 }, { 0xB920, 0xB7F1 }, { 0xB93C, 0xB7F2 }, { 0xB93D, 0xB7F3 }, { 0xB940, 0xB7F4 }, { 0xB944, 0xB7F5 }, { 0xB94C, 0xB7F6 },
	{ 0xB94F, 0xB7F7 }, { 0xB951, 0xB7F8 }, { 0xB958, 0xB7F9 }, { 0xB959, 0xB7FA }, { 0xB95C, 0xB7FB }, { 0xB960, 0xB7FC }, { 0xB968, 0xB7FD }, { 0xB969, 0xB7FE },
	{ 0xB96B, 0xB8A1 }, { 0xB96D, 0xB8A2 }, { 0xB974, 0xB8A3 }, { 0xB975, 0xB8A4 }, { 0xB978, 0xB8A5 }, { 0xB97C, 0xB8A6 }, { 0xB984, 0xB8A7 }, { 0xB985, 0xB8A8 },
	{ 0xB987, 0xB8A9 }, { 0xB989, 0xB8AA }, { 0xB98A, 0xB8AB }, { 0xB98D, 0xB8AC }, { 0xB98E, 0xB8AD }, { 0xB9AC, 0xB8AE }, { 0xB9AD, 0xB8AF }, { 0xB9B0, 0xB8B0 },
	{ 0xB9B4, 0xB8B1 }, { 0xB9BC, 0xB8B2 }, { 0xB9BD, 0xB8B3 }, { 0xB9BF, 0xB8B4 }, { 0xB9C1, 0xB8B5 }, { 0xB9C8, 0xB8B6 }, { 0xB9C9, 0xB8B7 }, { 0xB9CC, 0xB8B8 },
	{ 0xB9CE, 0xB8B9 }, { 0xB9CF, 0xB8BA }, { 0xB9D0, 0xB8BB }, { 0xB9D1, 0xB8BC }, { 0xB9D2, 0xB8BD }, { 0xB9D8, 0xB8BE }, { 0xB9D9, 0xB8BF }, { 0xB9DB, 0xB8C0 },
	{ 0xB9DD, 0xB8C1 }, { 0xB9DE, 0xB8C2 }, { 0xB9E1, 0xB8C3 }, { 0xB9E3, 0xB8C4 }, { 0xB9E4, 0xB8C5 }, { 0xB9E5, 0xB8C6 }, { 0xB9E8, 0xB8C7 }, { 0xB9EC, 0xB8C8 },
	{ 0xB9F4, 0xB8C9 }, { 0xB9F5, 0xB8CA }, { 0xB9F7, 0xB8CB }, { 0xB9F8, 0xB8CC }, { 0xB9F9, 0xB8CD }, { 0xB9FA, 0xB8CE }, { 0xBA00, 0xB8CF }, { 0xBA01, 0xB8D0 },
	{ 0xBA08, 0xB8D1 }, { 0xBA15, 0xB8D2 }, { 0xBA38, 0xB8D3 }, { 0xBA39, 0xB8D4 }, { 0xBA3C, 0xB8D5 }, { 0xBA40, 0xB8D6 }, { 0xBA42, 0xB8D7 }, { 0xBA48, 0xB8D8 },
	{ 0xBA49, 0xB8D9 }, { 0xBA4B, 0xB8DA }, { 0xBA4D, 0xB8DB }, { 0xBA4E, 0xB8DC }, { 0xBA53, 0xB8DD }, { 0xBA54, 0xB8DE }, { 0xBA55, 0xB8DF }, { 0xBA58, 0xB8E0 },
	{ 0xBA5C, 0xB8E1 }, { 0xBA64, 0xB8E2 }, { 0xBA65, 0xB8E3 }, { 0xBA67, 0xB8E4 }, { 0xBA68, 0xB8E5 }, { 0xBA69, 0xB8E6 }, { 0xBA70, 0xB8E7 }, { 0xBA71, 0xB8E8 },
	{ 0xBA74, 0xB8E9 }, { 0xBA78, 0xB8EA }, { 0xBA83, 0xB8EB }, { 0xBA84, 0xB8EC }, { 0xBA85, 0xB8ED }, { 0xBA87, 0xB8EE }, { 0xBA8C, 0xB8EF }, { 0xBAA8, 0xB8F0 },
	{ 0xBAA9, 0xB8F1 }, { 0xBAAB, 0xB8F2 }, { 0xBAAC, 0xB8F3 }, { 0xBAB0, 0xB8F4 }, { 0xBAB2, 0xB8F5 }, { 0xBAB8, 0xB8F6 }, { 0xBAB9, 0xB8F7 }, { 0xBABB, 0xB8F8 },
	{ 0xBABD, 0xB8F9 }, { 0xBAC4, 0xB8FA }, { 0xBAC8, 0xB8FB }, { 0xBAD8, 0xB8FC }, { 0xBAD9, 0xB8FD }, { 0xBAFC, 0xB8FE }, { 0xBB00, 0xB9A1 }, { 0xBB04, 0xB9A2 },
	{ 0xBB0D, 0xB9A3 }, { 0xBB0F, 0xB9A4 }, { 0xBB11, 0xB9A5 }, { 0xBB18, 0xB9A6 }, { 0xBB1C, 0xB9A7 }, { 0xBB20, 0xB9A8 }, { 0xBB29, 0xB9A9 }, { 0xBB2B, 0xB9AA },
	{ 0xBB34, 0xB9AB }, { 0xBB35, 0xB9AC }, { 0xBB36, 0xB9AD }, { 0xBB38, 0xB9AE }, { 0xBB3B, 0xB9AF }, { 0xBB3C, 0xB9B0 }, { 0xBB3D, 0xB9B1 }, { 0xBB3E, 0xB9B2 },
	{ 0xBB44, 0xB9B3 }, { 0xBB45, 0xB9B4 }, { 0xBB47, 0xB9B5 }, { 0xBB49, 0xB9B6 }, { 0xBB4D, 0xB9B7 }, { 0xBB4F, 0xB9B8 }, { 0xBB50, 0xB9B9 }, { 0xBB54, 0xB9BA },
	{ 0xBB58, 0xB9BB }, { 0xBB61, 0xB9BC }, { 0xBB63, 0xB9BD }, { 0xBB6C, 0xB9BE }, { 0xBB88, 0xB9BF }, { 0xBB8C, 0xB9C0 }, { 0xBB90, 0xB9C1 }, { 0xBBA4, 0xB9C2 },
	{ 0xBBA8, 0xB9C3 }, { 0xBBAC, 0xB9C4 }, { 0xBBB4, 0xB9C5 }, { 0xBBB7, 0xB9C6 }, { 0xBBC0, 0xB9C7 }, { 0xBBC4, 0xB9C8 }, { 0xBBC8, 0xB9C9 }, { 0xBBD0, 0xB9CA },
	{ 0xBBD3, 0xB9CB }, { 0xBBF8, 0xB9CC }, { 0xBBF9, 0xB9CD }, { 0xBBFC, 0xB9CE }, { 0xBBFF, 0xB9CF }, { 0xBC00, 0xB9D0 }, { 0xBC02, 0xB9D1 }, { 0xBC08, 0xB9D2 },
	{ 0xBC09, 0xB9D3 }, { 0xBC0B, 0xB9D4 }, { 0xBC0C, 0xB9D5 }, { 0xBC0D, 0xB9D6 }, { 0xBC0F, 0xB9D7 }, { 0xBC11, 0xB9D8 }, { 0xBC14, 0xB9D9 }, { 0xBC15, 0xB9DA },
	{ 0xBC16, 0xB9DB }, { 0xBC17, 0xB9DC }, { 0xBC18, 0xB9DD }, { 0xBC1B, 0xB9DE }, { 0xBC1C, 0xB9DF }, { 0xBC1D, 0xB9E0 }, { 0xBC1E, 0xB9E1 }, { 0xBC1F, 0xB9E2 },
	{ 0xBC24, 0xB9E3 }, { 0xBC25, 0xB9E4 }, { 0xBC27, 0xB9E5 }, { 0xBC29, 0xB9E6 }, { 0xBC2D, 0xB9E7 }, { 0xBC30, 0xB9E8 }, { 0xBC31, 0xB9E9 }, { 0xBC34, 0xB9EA },
	{ 0xBC38, 0xB9EB }, { 0xBC40, 0xB9EC }, { 0xBC41, 0xB9ED }, { 0xBC43, 0xB9EE }, { 0xBC44, 0xB9EF }, { 0xBC45, 0xB9F0 }, { 0xBC49, 0xB9F1 }, { 0xBC4C, 0xB9F2 },
	{ 0xBC4D, 0xB9F3 }, { 0xBC50, 0xB9F4 }, { 0xBC5D, 0xB9F5 }, { 0xBC84, 0xB9F6 }, { 0xBC85, 0xB9F7 }, { 0xBC88, 0xB9F8 }, { 0xBC8B, 0xB9F9 }, { 0xBC8C, 0xB9FA },
	{ 0xBC8E, 0xB9FB }, { 0xBC94, 0xB9FC }, { 0xBC95, 0xB9FD }, { 0xBC97, 0xB9FE }, { 0xBC99, 0xBAA1 }, { 0xBC9A, 0xBAA2 }, { 0xBCA0, 0xBAA3 }, { 0xBCA1, 0xBAA4 },
	{ 0xBCA4, 0xBAA5 }, { 0xBCA7, 0xBAA6 }, { 0xBCA8, 0xBAA7 }, { 0xBCB0, 0xBAA8 }, { 0xBCB1, 0xBAA9 }, { 0xBCB3, 0xBAAA }, { 0xBCB4, 0xBAAB }, { 0xBCB5, 0xBAAC },
	{ 0xBCBC, 0xBAAD }, { 0xBCBD, 0xBAAE }, { 0xBCC0, 0xBAAF }, { 0xBCC4, 0xBAB0 }, { 0xBCCD, 0xBAB1 }, { 0xBCCF, 0xBAB2 }, { 0xBCD0, 0xBAB3 }, { 0xBCD1, 0xBAB4 },
	{ 0xBCD5, 0xBAB5 }, { 0xBCD8, 0xBAB6 }, { 0xBCDC, 0xBAB7 }, { 0xBCF4, 0xBAB8 }, { 0xBCF5, 0xBAB9 }, { 0xBCF6, 0xBABA }, { 0xBCF8, 0xBABB }, { 0xBCFC, 0xBABC },
	{ 0xBD04, 0xBABD }, { 0xBD05, 0xBABE }, { 0xBD07, 0xBABF }, { 0xBD09, 0xBAC0 }, { 0xBD10, 0xBAC1 }, { 0xBD14, 0xBAC2 }, { 0xBD24, 0xBAC3 }, { 0xBD2C, 0xBAC4 },
	{ 0xBD40, 0xBAC5 }, { 0xBD48, 0xBAC6 }, { 0xBD49, 0xBAC7 }, { 0xBD4C, 0xBAC8 }, { 0xBD50, 0xBAC9 }, { 0xBD58, 0xBACA }, { 0xBD59, 0xBACB }, { 0xBD64, 0xBACC },
	{ 0xBD68, 0xBACD }, { 0xBD80, 0xBACE }, { 0xBD81, 0xBACF }, { 0xBD84, 0xBAD0 }, { 0xBD87, 0xBAD1 }, { 0xBD88, 0xBAD2 }, { 0xBD89, 0xBAD3 }, { 0xBD8A, 0xBAD4 },
	{ 0xBD90, 0xBAD5 }, { 0xBD91, 0xBAD6 }, { 0xBD93, 0xBAD7 }, { 0xBD95, 0xBAD8 }, { 0xBD99, 0xBAD9 }, { 0xBD9A, 0xBADA }, { 0xBD9C, 0xBADB }, { 0xBDA4, 0xBADC },
	{ 0xBDB0, 0xBADD }, { 0xBDB8, 0xBADE }, { 0xBDD4, 0xBADF }, { 0xBDD5, 0xBAE0 }, { 0xBDD8, 0xBAE1 }, { 0xBDDC, 0xBAE2 }, { 0xBDE9, 0xBAE3 }, { 0xBDF0, 0xBAE4 },
	{ 0xBDF4, 0xBAE5 }, { 0xBDF8, 0xBAE6 }, { 0xBE00, 0xBAE7 }, { 0xBE03, 0xBAE8 }, { 0xBE05, 0xBAE9 }, { 0xBE0C, 0xBAEA }, { 0xBE0D, 0xBAEB }, { 0xBE10, 0xBAEC },
	{ 0xBE14, 0xBAED }, { 0xBE1C, 0xBAEE }, { 0xBE1D, 0xBAEF }, { 0xBE1F, 0xBAF0 }, { 0xBE44, 0xBAF1 }, { 0xBE45, 0xBAF2 }, { 0xBE48, 0xBAF3 }, { 0xBE4C, 0xBAF4 },
	{ 0xBE4E, 0xBAF5 }, { 0xBE54, 0xBAF6 }, { 0xBE55, 0xBAF7 }, { 0xBE57, 0xBAF8 }, { 0xBE59, 0xBAF9 }, { 0xBE5A, 0xBAFA }, { 0xBE5B, 0xBAFB }, { 0xBE60, 0xBAFC },
	{ 0xBE61, 0xBAFD }, { 0xBE64, 0xBAFE }, { 0xBE68, 0xBBA1 }, { 0xBE6A, 0xBBA2 }, { 0xBE70, 0xBBA3 }, { 0xBE71, 0xBBA4 }, { 0xBE73, 0xBBA5 }, { 0xBE74, 0xBBA6 },
	{ 0xBE75, 0xBBA7 }, { 0xBE7B, 0xBBA8 }, { 0xBE7C, 0xBBA9 }, { 0xBE7D, 0xBBAA }, { 0xBE80, 0xBBAB }, { 0xBE84, 0xBBAC }, { 0xBE8C, 0xBBAD }, { 0xBE8D, 0xBBAE },
	{ 0xBE8F, 0xBBAF }, { 0xBE90, 0xBBB0 }, { 0xBE91, 0xBBB1 }, { 0xBE98, 0xBBB2 }, { 0xBE99, 0xBBB3 }, { 0xBEA8, 0xBBB4 }, { 0xBED0, 0xBBB5 }, { 0xBED1, 0xBBB6 },
	{ 0xBED4, 0xBBB7 }, { 0xBED7, 0xBBB8 }, { 0xBED8, 0xBBB9 }, { 0xBEE0, 0xBBBA }, { 0xBEE3, 0xBBBB }, { 0xBEE4, 0xBBBC }, { 0xBEE5, 0xBBBD }, { 0xBEEC, 0xBBBE },
	{ 0xBF01, 0xBBBF }, { 0xBF08, 0xBBC0 }, { 0xBF09, 0xBBC1 }, { 0xBF18, 0xBBC2 }, { 0xBF19, 0xBBC3 }, { 0xBF1B, 0xBBC4 }, { 0xBF1C, 0xBBC5 }, { 0xBF1D, 0xBBC6 },
	{ 0xBF40, 0xBBC7 }, { 0xBF41, 0xBBC8 }, { 0xBF44, 0xBBC9 }, { 0xBF48, 0xBBCA }, { 0xBF50, 0xBBCB }, { 0xBF51, 0xBBCC }, { 0xBF55, 0xBBCD }, { 0xBF94, 0xBBCE },
	{ 0xBFB0, 0xBBCF }, { 0xBFC5, 0xBBD0 }, { 0xBFCC, 0xBBD1 }, { 0xBFCD, 0xBBD2 }, { 0xBFD0, 0xBBD3 }, { 0xBFD4, 0xBBD4 }, { 0xBFDC, 0xBBD5 }, { 0xBFDF, 0xBBD6 },
	{ 0xBFE1, 0xBBD7 }, { 0xC03C, 0xBBD8 }, { 0xC051, 0xBBD9 }, { 0xC058, 0xBBDA }, { 0xC05C, 0xBBDB }, { 0xC060, 0xBBDC }, { 0xC068, 0xBBDD }, { 0xC069, 0xBBDE },
	{ 0xC090, 0xBBDF }, { 0xC091, 0xBBE0 }, { 0xC094, 0xBBE1 }, { 0xC098, 0xBBE2 }, { 0xC0A0, 0xBBE3 }, { 0xC0A1, 0xBBE4 }, { 0xC0A3, 0xBBE5 }, { 0xC0A5, 0xBBE6 },
	{ 0xC0AC, 0xBBE7 }, { 0xC0AD, 0xBBE8 }, { 0xC0AF, 0xBBE9 }, { 0xC0B0, 0xBBEA }, { 0xC0B3, 0xBBEB }, { 0xC0B4, 0xBBEC }, { 0xC0B5, 0xBBED }, { 0xC0B6, 0xBBEE },
	{ 0xC0BC, 0xBBEF }, { 0xC0BD, 0xBBF0 }, { 0xC0BF, 0xBBF1 }, { 0xC0C0, 0xBBF2 }, { 0xC0C1, 0xBBF3 }, { 0xC0C5, 0xBBF4 }, { 0xC0C8, 0xBBF5 }, { 0xC0C9, 0xBBF6 },
	{ 0xC0CC, 0xBBF7 }, { 0xC0D0, 0xBBF8 }, { 0xC0D8, 0xBBF9 }, { 0xC0D9, 0xBBFA }, { 0xC0DB, 0xBBFB }, { 0xC0DC, 0xBBFC }, { 0xC0DD, 0xBBFD }, { 0xC0E4, 0xBBFE },
	{ 0xC0E5, 0xBCA1 }, { 0xC0E8, 0xBCA2 }, { 0xC0EC, 0xBCA3 }, { 0xC0F4, 0xBCA4 }, { 0xC0F5, 0xBCA5 }, { 0xC0F7, 0xBCA6 }, { 0xC0F9, 0xBCA7 }, { 0xC100, 0xBCA8 },
	{ 0xC104, 0xBCA9 }, { 0xC108, 0xBCAA }, { 0xC110, 0xBCAB }, { 0xC115, 0xBCAC }, { 0xC11C, 0xBCAD }, { 0xC11D, 0xBCAE }, { 0xC11E, 0xBCAF }, { 0xC11F, 0xBCB0 },
	{ 0xC120, 0xBCB1 }, { 0xC123, 0xBCB2 }, { 0xC124, 0xBCB3 }, { 0xC126, 0xBCB4 }, { 0xC127, 0xBCB5 }, { 0xC12C, 0xBCB6 }, { 0xC12D, 0xBCB7 }, { 0xC12F, 0xBCB8 },
	{ 0xC130, 0xBCB9 }, { 0xC131, 0xBCBA }, { 0xC136, 0xBCBB }, { 0xC138, 0xBCBC }, { 0xC139, 0xBCBD }, { 0xC13C, 0xBCBE }, { 0xC140, 0xBCBF }, { 0xC148, 0xBCC0 },
	{ 0xC149, 0xBCC1 }, { 0xC14B, 0xBCC2 }, { 0xC14C, 0xBCC3 }, { 0xC14D, 0xBCC4 }, { 0xC154, 0xBCC5 }, { 0xC155, 0xBCC6 }, { 0xC158, 0xBCC7 }, { 0xC15C, 0xBCC8 },
	{ 0xC164, 0xBCC9 }, { 0xC165, 0xBCCA }, { 0xC167, 0xBCCB }, { 0xC168, 0xBCCC }, { 0xC169, 0xBCCD }, { 0xC170, 0xBCCE }, { 0xC174, 0xBCCF }, { 0xC178, 0xBCD0 },
	{ 0xC185, 0xBCD1 }, { 0xC18C, 0xBCD2 }, { 0xC18D, 0xBCD3 }, { 0xC18E, 0xBCD4 }, { 0xC190, 0xBCD5 }, { 0xC194, 0xBCD6 }, { 0xC196, 0xBCD7 }, { 0xC19C, 0xBCD8 },
	{ 0xC19D, 0xBCD9 }, { 0xC19F, 0xBCDA }, { 0xC1A1, 0xBCDB }, { 0xC1A5, 0xBCDC }, { 0xC1A8, 0xBCDD }, { 0xC1A9, 0xBCDE }, { 0xC1AC, 0xBCDF }, { 0xC1B0, 0xBCE0 },
	{ 0xC1BD, 0xBCE1 }, { 0xC1C4, 0xBCE2 }, { 0xC1C8, 0xBCE3 }, { 0xC1CC, 0xBCE4 }, { 0xC1D4, 0xBCE5 }, { 0xC1D7, 0xBCE6 }, { 0xC1D8, 0xBCE7 }, { 0xC1E0, 0xBCE8 },
	{ 0xC1E4, 0xBCE9 }, { 0xC1E8, 0xBCEA }, { 0xC1F0, 0xBCEB }, { 0xC1F1, 0xBCEC }, { 0xC1F3, 0xBCED }, { 0xC1FC, 0xBCEE }, { 0xC1FD, 0xBCEF }, { 0xC200, 0xBCF0 },
	{ 0xC204, 0xBCF1 }, { 0xC20C, 0xBCF2 }, { 0xC20D, 0xBCF3 }, { 0xC20F, 0xBCF4 }, { 0xC211, 0xBCF5 }, { 0xC218, 0xBCF6 }, { 0xC219, 0xBCF7 }, { 0xC21C, 0xBCF8 },
	{ 0xC21F, 0xBCF9 }, { 0xC220, 0xBCFA }, { 0xC228, 0xBCFB }, { 0xC229, 0xBCFC }, { 0xC22B, 0xBCFD }, { 0xC22D, 0xBCFE }, { 0xC22F, 0xBDA1 }, { 0xC231, 0xBDA2 },
	{ 0xC232, 0xBDA3 }, { 0xC234, 0xBDA4 }, { 0xC248, 0xBDA5 }, { 0xC250, 0xBDA6 }, { 0xC251, 0xBDA7 }, { 0xC254, 0xBDA8 }, { 0xC258, 0xBDA9 }, { 0xC260, 0xBDAA },
	{ 0xC265, 0xBDAB }, { 0xC26C, 0xBDAC }, { 0xC26D, 0xBDAD }, { 0xC270, 0xBDAE }, { 0xC274, 0xBDAF }, { 0xC27C, 0xBDB0 }, { 0xC27D, 0xBDB1 }, { 0xC27F, 0xBDB2 },
	{ 0xC281, 0xBDB3 }, { 0xC288, 0xBDB4 }, { 0xC289, 0xBDB5 }, { 0xC290, 0xBDB6 }, { 0xC298, 0xBDB7 }, { 0xC29B, 0xBDB8 }, { 0xC29D, 0xBDB9 }, { 0xC2A4, 0xBDBA },
	{ 0xC2A5, 0xBDBB }, { 0xC2A8, 0xBDBC }, { 0xC2AC, 0xBDBD }, { 0xC2AD, 0xBDBE }, { 0xC2B4, 0xBDBF }, { 0xC2B5, 0xBDC0 }, { 0xC2B7, 0xBDC1 }, { 0xC2B9, 0xBDC2 },
	{ 0xC2DC, 0xBDC3 }, { 0xC2DD, 0xBDC4 }, { 0xC2E0, 0xBDC5 }, { 0xC2E3, 0xBDC6 }, { 0xC2E4, 0xBDC7 }, { 0xC2EB, 0xBDC8 }, { 0xC2EC, 0xBDC9 }, { 0xC2ED, 0xBDCA },
	{ 0xC2EF, 0xBDCB }, { 0xC2F1, 0xBDCC }, { 0xC2F6, 0xBDCD }, { 0xC2F8, 0xBDCE }, { 0xC2F9, 0xBDCF }, { 0xC2FB, 0xBDD0 }, { 0xC2FC, 0xBDD1 }, { 0xC300, 0xBDD2 },
	{ 0xC308, 0xBDD3 }, { 0xC309, 0xBDD4 }, { 0xC30C, 0xBDD5 }, { 0xC30D, 0xBDD6 }, { 0xC313, 0xBDD7 }, { 0xC314, 0xBDD8 }, { 0xC315, 0xBDD9 }, { 0xC318, 0xBDDA },
	{ 0xC31C, 0xBDDB }, { 0xC324, 0xBDDC }, { 0xC325, 0xBDDD }, { 0xC328, 0xBDDE }, { 0xC329, 0xBDDF }, { 0xC345, 0xBDE0 }, { 0xC368, 0xBDE1 }, { 0xC369, 0xBDE2 },
	{ 0xC36C, 0xBDE3 }, { 0xC370, 0xBDE4 }, { 0xC372, 0xBDE5 }, { 0xC378, 0xBDE6 }, { 0xC379, 0xBDE7 }, { 0xC37C, 0xBDE8 }, { 0xC37D, 0xBDE9 }, { 0xC384, 0xBDEA },
	{ 0xC388, 0xBDEB }, { 0xC38C, 0xBDEC }, { 0xC3C0, 0xBDED }, { 0xC3D8, 0xBDEE }, { 0xC3D9, 0xBDEF }, { 0xC3DC, 0xBDF0 }, { 0xC3DF, 0xBDF1 }, { 0xC3E0, 0xBDF2 },
	{ 0xC3E2, 0xBDF3 }, { 0xC3E8, 0xBDF4 }, { 0xC3E9, 0xBDF5 }, { 0xC3ED, 0xBDF6 }, { 0xC3F4, 0xBDF7 }, { 0xC3F5, 0xBDF8 }, { 0xC3F8, 0xBDF9 }, { 0xC408, 0xBDFA },
	{ 0xC410, 0xBDFB }, { 0xC424, 0xBDFC }, { 0xC42C, 0xBDFD }, { 0xC430, 0xBDFE }, { 0xC434, 0xBEA1 }, { 0xC43C, 0xBEA2 }, { 0xC43D, 0xBEA3 }, { 0xC448, 0xBEA4 },
	{ 0xC464, 0xBEA5 }, { 0xC465, 0xBEA6 }, { 0xC468, 0xBEA7 }, { 0xC46C, 0xBEA8 }, { 0xC474, 0xBEA9 }, { 0xC475, 0xBEAA }, { 0xC479, 0xBEAB }, { 0xC480, 0xBEAC },
	{ 0xC494, 0xBEAD }, { 0xC49C, 0xBEAE }, { 0xC4B8, 0xBEAF }, { 0xC4BC, 0xBEB0 }, { 0xC4E9, 0xBEB1 }, { 0xC4F0, 0xBEB2 }, { 0xC4F1, 0xBEB3 }, { 0xC4F4, 0xBEB4 },
	{ 0xC4F8, 0xBEB5 }, { 0xC4FA, 0xBEB6 }, { 0xC4FF, 0xBEB7 }, { 0xC500, 0xBEB8 }, { 0xC501, 0xBEB9 }, { 0xC50C, 0xBEBA }, { 0xC510, 0xBEBB }, { 0xC514, 0xBEBC },
	{ 0xC51C, 0xBEBD }, { 0xC528, 0xBEBE }, { 0xC529, 0xBEBF }, { 0xC52C, 0xBEC0 }, { 0xC530, 0xBEC1 }, { 0xC538, 0xBEC2 }, { 0xC539, 0xBEC3 }, { 0xC53B, 0xBEC4 },
	{ 0xC53D, 0xBEC5 }, { 0xC544, 0xBEC6 }, { 0xC545, 0xBEC7 }, { 0xC548, 0xBEC8 }, { 0xC549, 0xBEC9 }, { 0xC54A, 0xBECA }, { 0xC54C, 0xBECB }, { 0xC54D, 0xBECC },
	{ 0xC54E, 0xBECD }, { 0xC553, 0xBECE }, { 0xC554, 0xBECF }, { 0xC555, 0xBED0 }, { 0xC557, 0xBED1 }, { 0xC558, 0xBED2 }, { 0xC559, 0xBED3 }, { 0xC55D, 0xBED4 },
	{ 0xC55E, 0xBED5 }, { 0xC560, 0xBED6 }, { 0xC561, 0xBED7 }, { 0xC564, 0xBED8 }, { 0xC568, 0xBED9 }, { 0xC570, 0xBEDA }, { 0xC571, 0xBEDB }, { 0xC573, 0xBEDC },
	{ 0xC574, 0xBEDD }, { 0xC575, 0xBEDE }, { 0xC57C, 0xBEDF }, { 0xC57D, 0xBEE0 }, { 0xC580, 0xBEE1 }, { 0xC584, 0xBEE2 }, { 0xC587, 0xBEE3 }, { 0xC58C, 0xBEE4 },
	{ 0xC58D, 0xBEE5 }, { 0xC58F, 0xBEE6 }, { 0xC591, 0xBEE7 }, { 0xC595, 0xBEE8 }, { 0xC597, 0xBEE9 }, { 0xC598, 0xBEEA }, { 0xC59C, 0xBEEB }, { 0xC5A0, 0xBEEC },
	{ 0xC5A9, 0xBEED }, { 0xC5B4, 0xBEEE }, { 0xC5B5, 0xBEEF }, { 0xC5B8, 0xBEF0 }, { 0xC5B9, 0xBEF1 }, { 0xC5BB, 0xBEF2 }, { 0xC5BC, 0xBEF3 }, { 0xC5BD, 0xBEF4 },
	{ 0xC5BE, 0xBEF5 }, { 0xC5C4, 0xBEF6 }, { 0xC5C5, 0xBEF7 }, { 0xC5C6, 0xBEF8 }, { 0xC5C7, 0xBEF9 }, { 0xC5C8, 0xBEFA }, { 0xC5C9, 0xBEFB }, { 0xC5CA, 0xBEFC },
	{ 0xC5CC, 0xBEFD }, { 0xC5CE, 0xBEFE }, { 0xC5D0, 0xBFA1 }, { 0xC5D1, 0xBFA2 }, { 0xC5D4, 0xBFA3 }, { 0xC5D8, 0xBFA4 }, { 0xC5E0, 0xBFA5 }, { 0xC5E1, 0xBFA6 },
	{ 0xC5E3, 0xBFA7 }, { 0xC5E5, 0xBFA8 }, { 0xC5EC, 0xBFA9 }, { 0xC5ED, 0xBFAA }, { 0xC5EE, 0xBFAB }, { 0xC5F0, 0xBFAC }, { 0xC5F4, 0xBFAD }, { 0xC5F6, 0xBFAE },
	{ 0xC5F7, 0xBFAF }, { 0xC5FC, 0xBFB0 }, { 0xC5FD, 0xBFB1 }, { 0xC5FE, 0xBFB2 }, { 0xC5FF, 0xBFB3 }, { 0xC600, 0xBFB4 }, { 0xC601, 0xBFB5 }, { 0xC605, 0xBFB6 },
	{ 0xC606, 0xBFB7 }, { 0xC607, 0xBFB8 }, { 0xC608, 0xBFB9 }, { 0xC60C, 0xBFBA }, { 0xC610, 0xBFBB }, { 0xC618, 0xBFBC }, { 0xC619, 0xBFBD }, { 0xC61B, 0xBFBE },
	{ 0xC61C, 0xBFBF }, { 0xC624, 0xBFC0 }, { 0xC625, 0xBFC1 }, { 0xC628, 0xBFC2 }, { 0xC62C, 0xBFC3 }, { 0xC62D, 0xBFC4 }, { 0xC62E, 0xBFC5 }, { 0xC630, 0xBFC6 },
	{ 0xC633, 0xBFC7 }, { 0xC634, 0xBFC8 }, { 0xC635, 0xBFC9 }, { 0xC637, 0xBFCA }, { 0xC639, 0xBFCB }, { 0xC63B, 0xBFCC }, { 0xC640, 0xBFCD }, { 0xC641, 0xBFCE },
	{ 0xC644, 0xBFCF }, { 0xC648, 0xBFD0 }, { 0xC650, 0xBFD1 }, { 0xC651, 0xBFD2 }, { 0xC653, 0xBFD3 }, { 0xC654, 0xBFD4 }, { 0xC655, 0xBFD5 }, { 0xC65C, 0xBFD6 },
	{ 0xC65D, 0xBFD7 }, { 0xC660, 0xBFD8 }, { 0xC66C, 0xBFD9 }, { 0xC66F, 0xBFDA }, { 0xC671, 0xBFDB }, { 0xC678, 0xBFDC }, { 0xC679, 0xBFDD }, { 0xC67C, 0xBFDE },
	{ 0xC680, 0xBFDF }, { 0xC688, 0xBFE0 }, { 0xC689, 0xBFE1 }, { 0xC68B, 0xBFE2 }, { 0xC68D, 0xBFE3 }, { 0xC694, 0xBFE4 }, { 0xC695, 0xBFE5 }, { 0xC698, 0xBFE6 },
	{ 0xC69C, 0xBFE7 }, { 0xC6A4, 0xBFE8 }, { 0xC6A5, 0xBFE9 }, { 0xC6A7, 0xBFEA }, { 0xC6A9, 0xBFEB }, { 0xC6B0, 0xBFEC }, { 0xC6B1, 0xBFED }, { 0xC6B4, 0xBFEE },
	{ 0xC6B8, 0xBFEF }, { 0xC6B9, 0xBFF0 }, { 0xC6BA, 0xBFF1 }, { 0xC6C0, 0xBFF2 }, { 0xC6C1, 0xBFF3 }, { 0xC6C3, 0xBFF4 }, { 0xC6C5, 0xBFF5 }, { 0xC6CC, 0xBFF6 },
	{ 0xC6CD, 0xBFF7 }, { 0xC6D0, 0xBFF8 }, { 0xC6D4, 0xBFF9 }, { 0xC6DC, 0xBFFA }, { 0xC6DD, 0xBFFB }, { 0xC6E0, 0xBFFC }, { 0xC6E1, 0xBFFD }, { 0xC6E8, 0xBFFE },
	{ 0xC6E9, 0xC0A1 }, { 0xC6EC, 0xC0A2 }, { 0xC6F0, 0xC0A3 }, { 0xC6F8, 0xC0A4 }, { 0xC6F9, 0xC0A5 }, { 0xC6FD, 0xC0A6 }, { 0xC704, 0xC0A7 }, { 0xC705, 0xC0A8 },
	{ 0xC708, 0xC0A9 }, { 0xC70C, 0xC0AA }, { 0xC714, 0xC0AB }, { 0xC715, 0xC0AC }, { 0xC717, 0xC0AD }, { 0xC719, 0xC0AE }, { 0xC720, 0xC0AF }, { 0xC721, 0xC0B0 },
	{ 0xC724, 0xC0B1 }, { 0xC728, 0xC0B2 }, { 0xC730, 0xC0B3 }, { 0xC731, 0xC0B4 }, { 0xC733, 0xC0B5 }, { 0xC735, 0xC0B6 }, { 0xC737, 0xC0B7 }, { 0xC73C, 0xC0B8 },
	{ 0xC73D, 0xC0B9 }, { 0xC740, 0xC0BA }, { 0xC744, 0xC0BB }, { 0xC74A, 0xC0BC }, { 0xC74C, 0xC0BD }, { 0xC74D, 0xC0BE }, { 0xC74F, 0xC0BF }, { 0xC751, 0xC0C0 },
	{ 0xC752, 0xC0C1 }, { 0xC753, 0xC0C2 }, { 0xC754, 0xC0C3 }, { 0xC755, 0xC0C4 }, { 0xC756, 0xC0C5 }, { 0xC757, 0xC0C6 }, { 0xC758, 0xC0C7 }, { 0xC75C, 0xC0C8 },
	{ 0xC760, 0xC0C9 }, { 0xC768, 0xC0CA }, { 0xC76B, 0xC0CB }, { 0xC774, 0xC0CC }, { 0xC775, 0xC0CD }, { 0xC778, 0xC0CE }, { 0xC77C, 0xC0CF }, { 0xC77D, 0xC0D0 },
	{ 0xC77E, 0xC0D1 }, { 0xC783, 0xC0D2 }, { 0xC784, 0xC0D3 }, { 0xC785, 0xC0D4 }, { 0xC787, 0xC0D5 }, { 0xC788, 0xC0D6 }, { 0xC789, 0xC0D7 }, { 0xC78A, 0xC0D8 },
	{ 0xC78E, 0xC0D9 }, { 0xC790, 0xC0DA }, { 0xC791, 0xC0DB }, { 0xC794, 0xC0DC }, { 0xC796, 0xC0DD }, { 0xC797, 0xC0DE }, { 0xC798, 0xC0DF }, { 0xC79A, 0xC0E0 },
	{ 0xC7A0, 0xC0E1 }, { 0xC7A1, 0xC0E2 }, { 0xC7A3, 0xC0E3 }, { 0xC7A4, 0xC0E4 }, { 0xC7A5, 0xC0E5 }, { 0xC7A6, 0xC0E6 }, { 0xC7AC, 0xC0E7 }, { 0xC7AD, 0xC0E8 },
	{ 0xC7B0, 0xC0E9 }, { 0xC7B4, 0xC0EA }, { 0xC7BC, 0xC0EB }, { 0xC7BD, 0xC0EC }, { 0xC7BF, 0xC0ED }, { 0xC7C0, 0xC0EE }, { 0xC7C1, 0xC0EF }, { 0xC7C8, 0xC0F0 },
	{ 0xC7C9, 0xC0F1 }, { 0xC7CC, 0xC0F2 }, { 0xC7CE, 0xC0F3 }, { 0xC7D0, 0xC0F4 }, { 0xC7D8, 0xC0F5 }, { 0xC7DD, 0xC0F6 }, { 0xC7E4, 0xC0F7 }, { 0xC7E8, 0xC0F8 },
	{ 0xC7EC, 0xC0F9 }, { 0xC800, 0xC0FA }, { 0xC801, 0xC0FB }, { 0xC804, 0xC0FC }, { 0xC808, 0xC0FD }, { 0xC80A, 0xC0FE }, { 0xC810, 0xC1A1 }, { 0xC811, 0xC1A2 },
	{ 0xC813, 0xC1A3 }, { 0xC815, 0xC1A4 }, { 0xC816, 0xC1A5 }, { 0xC81C, 0xC1A6 }, { 0xC81D, 0xC1A7 }, { 0xC820, 0xC1A8 }, { 0xC824, 0xC1A9 }, { 0xC82C, 0xC1AA },
	{ 0xC82D, 0xC1AB }, { 0xC82F, 0xC1AC }, { 0xC831, 0xC1AD }, { 0xC838, 0xC1AE }, { 0xC83C, 0xC1AF }, { 0xC840, 0xC1B0 }, { 0xC848, 0xC1B1 }, { 0xC849, 0xC1B2 },
	{ 0xC84C, 0xC1B3 }, { 0xC84D, 0xC1B4 }, { 0xC854, 0xC1B5 }, { 0xC870, 0xC1B6 }, { 0xC871, 0xC1B7 }, { 0xC874, 0xC1B8 }, { 0xC878, 0xC1B9 }, { 0xC87A, 0xC1BA },
	{ 0xC880, 0xC1BB }, { 0xC881, 0xC1BC }, { 0xC883, 0xC1BD }, { 0xC885, 0xC1BE }, { 0xC886, 0xC1BF }, { 0xC887, 0xC1C0 }, { 0xC88B, 0xC1C1 }, { 0xC88C, 0xC1C2 },
	{ 0xC88D, 0xC1C3 }, { 0xC894, 0xC1C4 }, { 0xC89D, 0xC1C5 }, { 0xC89F, 0xC1C6 }, { 0xC8A1, 0xC1C7 }, { 0xC8A8, 0xC1C8 }, { 0xC8BC, 0xC1C9 }, { 0xC8BD, 0xC1CA },
	{ 0xC8C4, 0xC1CB }, { 0xC8C8, 0xC1CC }, { 0xC8CC, 0xC1CD }, { 0xC8D4, 0xC1CE }, { 0xC8D5, 0xC1CF }, { 0xC8D7, 0xC1D0 }, { 0xC8D9, 0xC1D1 }, { 0xC8E0, 0xC1D2 },
	{ 0xC8E1, 0xC1D3 }, { 0xC8E4, 0xC1D4 }, { 0xC8F5, 0xC1D5 }, { 0xC8FC, 0xC1D6 }, { 0xC8FD, 0xC1D7 }, { 0xC900, 0xC1D8 }, { 0xC904, 0xC1D9 }, { 0xC905, 0xC1DA },
	{ 0xC906, 0xC1DB }, { 0xC90C, 0xC1DC }, { 0xC90D, 0xC1DD }, { 0xC90F, 0xC1DE }, { 0xC911, 0xC1DF }, { 0xC918, 0xC1E0 }, { 0xC92C, 0xC1E1 }, { 0xC934, 0xC1E2 },
	{ 0xC950, 0xC1E3 }, { 0xC951, 0xC1E4 }, { 0xC954, 0xC1E5 }, { 0xC958, 0xC1E6 }, { 0xC960, 0xC1E7 }, { 0xC961, 0xC1E8 }, { 0xC963, 0xC1E9 }, { 0xC96C, 0xC1EA },
	{ 0xC970, 0xC1EB }, { 0xC974, 0xC1EC }, { 0xC97C, 0xC1ED }, { 0xC988, 0xC1EE }, { 0xC989, 0xC1EF }, { 0xC98C, 0xC1F0 }, { 0xC990, 0xC1F1 }, { 0xC998, 0xC1F2 },
	{ 0xC999, 0xC1F3 }, { 0xC99B, 0xC1F4 }, { 0xC99D, 0xC1F5 }, { 0xC9C0, 0xC1F6 }, { 0xC9C1, 0xC1F7 }, { 0xC9C4, 0xC1F8 }, { 0xC9C7, 0xC1F9 }, { 0xC9C8, 0xC1FA },
	{ 0xC9CA, 0xC1FB }, { 0xC9D0, 0xC1FC }, { 0xC9D1, 0xC1FD }, { 0xC9D3, 0xC1FE }, { 0xC9D5, 0xC2A1 }, { 0xC9D6, 0xC2A2 }, { 0xC9D9, 0xC2A3 }, { 0xC9DA, 0xC2A4 },
	{ 0xC9DC, 0xC2A5 }, { 0xC9DD, 0xC2A6 }, { 0xC9E0, 0xC2A7 }, { 0xC9E2, 0xC2A8 }, { 0xC9E4, 0xC2A9 }, { 0xC9E7, 0xC2AA }, { 0xC9EC, 0xC2AB }, { 0xC9ED, 0xC2AC },
	{ 0xC9EF, 0xC2AD }, { 0xC9F0, 0xC2AE }, { 0xC9F1, 0xC2AF }, { 0xC9F8, 0xC2B0 }, { 0xC9F9, 0xC2B1 }, { 0xC9FC, 0xC2B2 }, { 0xCA00, 0xC2B3 }, { 0xCA08, 0xC2B4 },
	{ 0xCA09, 0xC2B5 }, { 0xCA0B, 0xC2B6 }, { 0xCA0C, 0xC2B7 }, { 0xCA0D, 0xC2B8 }, { 0xCA14, 0xC2B9 }, { 0xCA18, 0xC2BA }, { 0xCA29, 0xC2BB }, { 0xCA4C, 0xC2BC },
	{ 0xCA4D, 0xC2BD }, { 0xCA50, 0xC2BE }, { 0xCA54, 0xC2BF }, { 0xCA5C, 0xC2C0 }, { 0xCA5D, 0xC2C1 }, { 0xCA5F, 0xC2C2 }, { 0xCA60, 0xC2C3 }, { 0xCA61, 0xC2C4 },
	{ 0xCA68, 0xC2C5 }, { 0xCA7D, 0xC2C6 }, { 0xCA84, 0xC2C7 }, { 0xCA98, 0xC2C8 }, { 0xCABC, 0xC2C9 }, { 0xCABD, 0xC2CA }, { 0xCAC0, 0xC2CB }, { 0xCAC4, 0xC2CC },
	{ 0xCACC, 0xC2CD }, { 0xCACD, 0xC2CE }, { 0xCACF, 0xC2CF }, { 0xCAD1, 0xC2D0 }, { 0xCAD3, 0xC2D1 }, { 0xCAD8, 0xC2D2 }, { 0xCAD9, 0xC2D3 }, { 0xCAE0, 0xC2D4 },
	{ 0xCAEC, 0xC2D5 }, { 0xCAF4, 0xC2D6 }, { 0xCB08, 0xC2D7 }, { 0xCB10, 0xC2D8 }, { 0xCB14, 0xC2D9 }, { 0xCB18, 0xC2DA }, { 0xCB20, 0xC2DB }, { 0xCB21, 0xC2DC },
	{ 0xCB41, 0xC2DD }, { 0xCB48, 0xC2DE }, { 0xCB49, 0xC2DF }, { 0xCB4C, 0xC2E0 }, { 0xCB50, 0xC2E1 }, { 0xCB58, 0xC2E2 }, { 0xCB59, 0xC2E3 }, { 0xCB5D, 0xC2E4 },
	{ 0xCB64, 0xC2E5 }, { 0xCB78, 0xC2E6 }, { 0xCB79, 0xC2E7 }, { 0xCB9C, 0xC2E8 }, { 0xCBB8, 0xC2E9 }, { 0xCBD4, 0xC2EA }, { 0xCBE4, 0xC2EB }, { 0xCBE7, 0xC2EC },
	{ 0xCBE9, 0xC2ED }, { 0xCC0C, 0xC2EE }, { 0xCC0D, 0xC2EF }, { 0xCC10, 0xC2F0 }, { 0xCC14, 0xC2F1 }, { 0xCC1C, 0xC2F2 }, { 0xCC1D, 0xC2F3 }, { 0xCC21, 0xC2F4 },
	{ 0xCC22, 0xC2F5 }, { 0xCC27, 0xC2F6 }, { 0xCC28, 0xC2F7 }, { 0xCC29, 0xC2F8 }, { 0xCC2C, 0xC2F9 }, { 0xCC2E, 0xC2FA }, { 0xCC30, 0xC2FB }, { 0xCC38, 0xC2FC },
	{ 0xCC39, 0xC2FD }, { 0xCC3B, 0xC2FE }, { 0xCC3C, 0xC3A1 }, { 0xCC3D, 0xC3A2 }, { 0xCC3E, 0xC3A3 }, { 0xCC44, 0xC3A4 }, { 0xCC45, 0xC3A5 }, { 0xCC48, 0xC3A6 },
	{ 0xCC4C, 0xC3A7 }, { 0xCC54, 0xC3A8 }, { 0xCC55, 0xC3A9 }, { 0xCC57, 0xC3AA }, { 0xCC58, 0xC3AB }, { 0xCC59, 0xC3AC }, { 0xCC60, 0xC3AD }, { 0xCC64, 0xC3AE },
	{ 0xCC66, 0xC3AF }, { 0xCC68, 0xC3B0 }, { 0xCC70, 0xC3B1 }, { 0xCC75, 0xC3B2 }, { 0xCC98, 0xC3B3 }, { 0xCC99, 0xC3B4 }, { 0xCC9C, 0xC3B5 }, { 0xCCA0, 0xC3B6 },
	{ 0xCCA8, 0xC3B7 }, { 0xCCA9, 0xC3B8 }, { 0xCCAB, 0xC3B9 }, { 0xCCAC, 0xC3BA }, { 0xCCAD, 0xC3BB }, { 0xCCB4, 0xC3BC }, { 0xCCB5, 0xC3BD }, { 0xCCB8, 0xC3BE },
	{ 0xCCBC, 0xC3BF }, { 0xCCC4, 0xC3C0 }, { 0xCCC5, 0xC3C1 }, { 0xCCC7, 0xC3C2 }, { 0xCCC9, 0xC3C3 }, { 0xCCD0, 0xC3C4 }, { 0xCCD4, 0xC3C5 }, { 0xCCE4, 0xC3C6 },
	{ 0xCCEC, 0xC3C7 }, { 0xCCF0, 0xC3C8 }, { 0xCD01, 0xC3C9 }, { 0xCD08, 0xC3CA }, { 0xCD09, 0xC3CB }, { 0xCD0C, 0xC3CC }, { 0xCD10, 0xC3CD }, { 0xCD18, 0xC3CE },
	{ 0xCD19, 0xC3CF }, { 0xCD1B, 0xC3D0 }, { 0xCD1D, 0xC3D1 }, { 0xCD24, 0xC3D2 }, { 0xCD28, 0xC3D3 }, { 0xCD2C, 0xC3D4 }, { 0xCD39, 0xC3D5 }, { 0xCD5C, 0xC3D6 },
	{ 0xCD60, 0xC3D7 }, { 0xCD64, 0xC3D8 }, { 0xCD6C, 0xC3D9 }, { 0xCD6D, 0xC3DA }, { 0xCD6F, 0xC3DB }, { 0xCD71, 0xC3DC }, { 0xCD78, 0xC3DD }, { 0xCD88, 0xC3DE },
	{ 0xCD94, 0xC3DF }, { 0xCD95, 0xC3E0 }, { 0xCD98, 0xC3E1 }, { 0xCD9C, 0xC3E2 }, { 0xCDA4, 0xC3E3 }, { 0xCDA5, 0xC3E4 }, { 0xCDA7, 0xC3E5 }, { 0xCDA9, 0xC3E6 },
	{ 0xCDB0, 0xC3E7 }, { 0xCDC4, 0xC3E8 }, { 0xCDCC, 0xC3E9 }, { 0xCDD0, 0xC3EA }, { 0xCDE8, 0xC3EB }, { 0xCDEC, 0xC3EC }, { 0xCDF0, 0xC3ED }, { 0xCDF8, 0xC3EE },
	{ 0xCDF9, 0xC3EF }, { 0xCDFB, 0xC3F0 }, { 0xCDFD, 0xC3F1 }, { 0xCE04, 0xC3F2 }, { 0xCE08, 0xC3F3 }, { 0xCE0C, 0xC3F4 }, { 0xCE14, 0xC3F5 }, { 0xCE19, 0xC3F6 },
	{ 0xCE20, 0xC3F7 }, { 0xCE21, 0xC3F8 }, { 0xCE24, 0xC3F9 }, { 0xCE28, 0xC3FA }, { 0xCE30, 0xC3FB }, { 0xCE31, 0xC3FC }, { 0xCE33, 0xC3FD }, { 0xCE35, 0xC3FE },
	{ 0xCE58, 0xC4A1 }, { 0xCE59, 0xC4A2 }, { 0xCE5C, 0xC4A3 }, { 0xCE5F, 0xC4A4 }, { 0xCE60, 0xC4A5 }, { 0xCE61, 0xC4A6 }, { 0xCE68, 0xC4A7 }, { 0xCE69, 0xC4A8 },
	{ 0xCE6B, 0xC4A9 }, { 0xCE6D, 0xC4AA }, { 0xCE74, 0xC4AB }, { 0xCE75, 0xC4AC }, { 0xCE78, 0xC4AD }, { 0xCE7C, 0xC4AE }, { 0xCE84, 0xC4AF }, { 0xCE85, 0xC4B0 },
	{ 0xCE87, 0xC4B1 }, { 0xCE89, 0xC4B2 }, { 0xCE90, 0xC4B3 }, { 0xCE91, 0xC4B4 }, { 0xCE94, 0xC4B5 }, { 0xCE98, 0xC4B6 }, { 0xCEA0, 0xC4B7 }, { 0xCEA1, 0xC4B8 },
	{ 0xCEA3, 0xC4B9 }, { 0xCEA4, 0xC4BA }, { 0xCEA5, 0xC4BB }, { 0xCEAC, 0xC4BC }, { 0xCEAD, 0xC4BD }, { 0xCEC1, 0xC4BE }, { 0xCEE4, 0xC4BF }, { 0xCEE5, 0xC4C0 },
	{ 0xCEE8, 0xC4C1 }, { 0xCEEB, 0xC4C2 }, { 0xCEEC, 0xC4C3 }, { 0xCEF4, 0xC4C4 }, { 0xCEF5, 0xC4C5 }, { 0xCEF7, 0xC4C6 }, { 0xCEF8, 0xC4C7 }, { 0xCEF9, 0xC4C8 },
	{ 0xCF00, 0xC4C9 }, { 0xCF01, 0xC4CA }, { 0xCF04, 0xC4CB }, { 0xCF08, 0xC4CC }, { 0xCF10, 0xC4CD }, { 0xCF11, 0xC4CE }, { 0xCF13, 0xC4CF }, { 0xCF15, 0xC4D0 },
	{ 0xCF1C, 0xC4D1 }, { 0xCF20, 0xC4D2 }, { 0xCF24, 0xC4D3 }, { 0xCF2C, 0xC4D4 }, { 0xCF2D, 0xC4D5 }, { 0xCF2F, 0xC4D6 }, { 0xCF30, 0xC4D7 }, { 0xCF31, 0xC4D8 },
	{ 0xCF38, 0xC4D9 }, { 0xCF54, 0xC4DA }, { 0xCF55, 0xC4DB }, { 0xCF58, 0xC4DC }, { 0xCF5C, 0xC4DD }, { 0xCF64, 0xC4DE }, { 0xCF65, 0xC4DF }, { 0xCF67, 0xC4E0 },
	{ 0xCF69, 0xC4E1 }, { 0xCF70, 0xC4E2 }, { 0xCF71, 0xC4E3 }, { 0xCF74, 0xC4E4 }, { 0xCF78, 0xC4E5 }, { 0xCF80, 0xC4E6 }, { 0xCF85, 0xC4E7 }, { 0xCF8C, 0xC4E8 },
	{ 0xCFA1, 0xC4E9 }, { 0xCFA8, 0xC4EA }, { 0xCFB0, 0xC4EB }, { 0xCFC4, 0xC4EC }, { 0xCFE0, 0xC4ED }, { 0xCFE1, 0xC4EE }, { 0xCFE4, 0xC4EF }, { 0xCFE8, 0xC4F0 },
	{ 0xCFF0, 0xC4F1 }, { 0xCFF1, 0xC4F2 }, { 0xCFF3, 0xC4F3 }, { 0xCFF5, 0xC4F4 }, { 0xCFFC, 0xC4F5 }, { 0xD000, 0xC4F6 }, { 0xD004, 0xC4F7 }, { 0xD011, 0xC4F8 },
	{ 0xD018, 0xC4F9 }, { 0xD02D, 0xC4FA }, { 0xD034, 0xC4FB }, { 0xD035, 0xC4FC }, { 0xD038, 0xC4FD }, { 0xD03C, 0xC4FE }, { 0xD044, 0xC5A1 }, { 0xD045, 0xC5A2 },
	{ 0xD047, 0xC5A3 }, { 0xD049, 0xC5A4 }, { 0xD050, 0xC5A5 }, { 0xD054, 0xC5A6 }, { 0xD058, 0xC5A7 }, { 0xD060, 0xC5A8 }, { 0xD06C, 0xC5A9 }, { 0xD06D, 0xC5AA },
	{ 0xD070, 0xC5AB }, { 0xD074, 0xC5AC }, { 0xD07C, 0xC5AD }, { 0xD07D, 0xC5AE }, { 0xD081, 0xC5AF }, { 0xD0A4, 0xC5B0 }, { 0xD0A5, 0xC5B1 }, { 0xD0A8, 0xC5B2 },
	{ 0xD0AC, 0xC5B3 }, { 0xD0B4, 0xC5B4 }, { 0xD0B5, 0xC5B5 }, { 0xD0B7, 0xC5B6 }, { 0xD0B9, 0xC5B7 }, { 0xD0C0, 0xC5B8 }, { 0xD0C1, 0xC5B9 }, { 0xD0C4, 0xC5BA },
	{ 0xD0C8, 0xC5BB }, { 0xD0C9, 0xC5BC }, { 0xD0D0, 0xC5BD }, { 0xD0D1, 0xC5BE }, { 0xD0D3, 0xC5BF }, { 0xD0D4, 0xC5C0 }, { 0xD0D5, 0xC5C1 }, { 0xD0DC, 0xC5C2 },
	{ 0xD0DD, 0xC5C3 }, { 0xD0E0, 0xC5C4 }, { 0xD0E4, 0xC5C5 }, { 0xD0EC, 0xC5C6 }, { 0xD0ED, 0xC5C7 }, { 0xD0EF, 0xC5C8 }, { 0xD0F0, 0xC5C9 }, { 0xD0F1, 0xC5CA },
	{ 0xD0F8, 0xC5CB }, { 0xD10D, 0xC5CC }, { 0xD130, 0xC5CD }, { 0xD131, 0xC5CE }, { 0xD134, 0xC5CF }, { 0xD138, 0xC5D0 }, { 0xD13A, 0xC5D1 }, { 0xD140, 0xC5D2 },
	{ 0xD141, 0xC5D3 }, { 0xD143, 0xC5D4 }, { 0xD144, 0xC5D5 }, { 0xD145, 0xC5D6 }, { 0xD14C, 0xC5D7 }, { 0xD14D, 0xC5D8 }, { 0xD150, 0xC5D9 }, { 0xD154, 0xC5DA },
	{ 0xD15C, 0xC5DB }, { 0xD15D, 0xC5DC }, { 0xD15F, 0xC5DD }, { 0xD161, 0xC5DE }, { 0xD168, 0xC5DF }, { 0xD16C, 0xC5E0 }, { 0xD17C, 0xC5E1 }, { 0xD184, 0xC5E2 },
	{ 0xD188, 0xC5E3 }, { 0xD1A0, 0xC5E4 }, { 0xD1A1, 0xC5E5 }, { 0xD1A4, 0xC5E6 }, { 0xD1A8, 0xC5E7 }, { 0xD1B0, 0xC5E8 }, { 0xD1B1, 0xC5E9 }, { 0xD1B3, 0xC5EA },
	{ 0xD1B5, 0xC5EB }, { 0xD1BA, 0xC5EC }, { 0xD1BC, 0xC5ED }, { 0xD1C0, 0xC5EE }, { 0xD1D8, 0xC5EF }, { 0xD1F4, 0xC5F0 }, { 0xD1F8, 0xC5F1 }, { 0xD207, 0xC5F2 },
	{ 0xD209, 0xC5F3 }, { 0xD210, 0xC5F4 }, { 0xD22C, 0xC5F5 }, { 0xD22D, 0xC5F6 }, { 0xD230, 0xC5F7 }, { 0xD234, 0xC5F8 }, { 0xD23C, 0xC5F9 }, { 0xD23D, 0xC5FA },
	{ 0xD23F, 0xC5FB }, { 0xD241, 0xC5FC }, { 0xD248, 0xC5FD }, { 0xD25C, 0xC5FE }, { 0xD264, 0xC6A1 }, { 0xD280, 0xC6A2 }, { 0xD281, 0xC6A3 }, { 0xD284, 0xC6A4 },
	{ 0xD288, 0xC6A5 }, { 0xD290, 0xC6A6 }, { 0xD291, 0xC6A7 }, { 0xD295, 0xC6A8 }, { 0xD29C, 0xC6A9 }, { 0xD2A0, 0xC6AA }, { 0xD2A4, 0xC6AB }, { 0xD2AC, 0xC6AC },
	{ 0xD2B1, 0xC6AD }, { 0xD2B8, 0xC6AE }, { 0xD2B9, 0xC6AF }, { 0xD2BC, 0xC6B0 }, { 0xD2BF, 0xC6B1 }, { 0xD2C0, 0xC6B2 }, { 0xD2C2, 0xC6B3 }, { 0xD2C8, 0xC6B4 },
	{ 0xD2C9, 0xC6B5 }, { 0xD2CB, 0xC6B6 }, { 0xD2D4, 0xC6B7 }, { 0xD2D8, 0xC6B8 }, { 0xD2DC, 0xC6B9 }, { 0xD2E4, 0xC6BA }, { 0xD2E5, 0xC6BB }, { 0xD2F0, 0xC6BC },
	{ 0xD2F1, 0xC6BD }, { 0xD2F4, 0xC6BE }, { 0xD2F8, 0xC6BF }, { 0xD300, 0xC6C0 }, { 0xD301, 0xC6C1 }, { 0xD303, 0xC6C2 }, { 0xD305, 0xC6C3 }, { 0xD30C, 0xC6C4 },
	{ 0xD30D, 0xC6C5 }, { 0xD30E, 0xC6C6 }, { 0xD310, 0xC6C7 }, { 0xD314, 0xC6C8 }, { 0xD316, 0xC6C9 }, { 0xD31C, 0xC6CA }, { 0xD31D, 0xC6CB }, { 0xD31F, 0xC6CC },
	{ 0xD320, 0xC6CD }, { 0xD321, 0xC6CE }, { 0xD325, 0xC6CF }, { 0xD328, 0xC6D0 }, { 0xD329, 0xC6D1 }, { 0xD32C, 0xC6D2 }, { 0xD330, 0xC6D3 }, { 0xD338, 0xC6D4 },
	{ 0xD339, 0xC6D5 }, { 0xD33B, 0xC6D6 }, { 0xD33C, 0xC6D7 }, { 0xD33D, 0xC6D8 }, { 0xD344, 0xC6D9 }, { 0xD345, 0xC6DA }, { 0xD37C, 0xC6DB }, { 0xD37D, 0xC6DC },
	{ 0xD380, 0xC6DD }, { 0xD384, 0xC6DE }, { 0xD38C, 0xC6DF }, { 0xD38D, 0xC6E0 }, { 0xD38F, 0xC6E1 }, { 0xD390, 0xC6E2 }, { 0xD391, 0xC6E3 }, { 0xD398, 0xC6E4 },
	{ 0xD399, 0xC6E5 }, { 0xD39C, 0xC6E6 }, { 0xD3A0, 0xC6E7 }, { 0xD3A8, 0xC6E8 }, { 0xD3A9, 0xC6E9 }, { 0xD3AB, 0xC6EA }, { 0xD3AD, 0xC6EB }, { 0xD3B4, 0xC6EC },
	{ 0xD3B8, 0xC6ED }, { 0xD3BC, 0xC6EE }, { 0xD3C4, 0xC6EF }, { 0xD3C5, 0xC6F0 }, { 0xD3C8, 0xC6F1 }, { 0xD3C9, 0xC6F2 }, { 0xD3D0, 0xC6F3 }, { 0xD3D8, 0xC6F4 },
	{ 0xD3E1, 0xC6F5 }, { 0xD3E3, 0xC6F6 }, { 0xD3EC, 0xC6F7 }, { 0xD3ED, 0xC6F8 }, { 0xD3F0, 0xC6F9 }, { 0xD3F4, 0xC6FA }, { 0xD3FC, 0xC6FB }, { 0xD3FD, 0xC6FC },
	{ 0xD3FF, 0xC6FD }, { 0xD401, 0xC6FE }, { 0xD408, 0xC7A1 }, { 0xD41D, 0xC7A2 }, { 0xD440, 0xC7A3 }, { 0xD444, 0xC7A4 }, { 0xD45C, 0xC7A5 }, { 0xD460, 0xC7A6 },
	{ 0xD464, 0xC7A7 }, { 0xD46D, 0xC7A8 }, { 0xD46F, 0xC7A9 }, { 0xD478, 0xC7AA }, { 0xD479, 0xC7AB }, { 0xD47C, 0xC7AC }, { 0xD47F, 0xC7AD }, { 0xD480, 0xC7AE },
	{ 0xD482, 0xC7AF }, { 0xD488, 0xC7B0 }, { 0xD489, 0xC7B1 }, { 0xD48B, 0xC7B2 }, { 0xD48D, 0xC7B3 }, { 0xD494, 0xC7B4 }, { 0xD4A9, 0xC7B5 }, { 0xD4CC, 0xC7B6 },
	{ 0xD4D0, 0xC7B7 }, { 0xD4D4, 0xC7B8 }, { 0xD4DC, 0xC7B9 }, { 0xD4DF, 0xC7BA }, { 0xD4E8, 0xC7BB }, { 0xD4EC, 0xC7BC }, { 0xD4F0, 0xC7BD }, { 0xD4F8, 0xC7BE },
	{ 0xD4FB, 0xC7BF }, { 0xD4FD, 0xC7C0 }, { 0xD504, 0xC7C1 }, { 0xD508, 0xC7C2 }, { 0xD50C, 0xC7C3 }, { 0xD514, 0xC7C4 }, { 0xD515, 0xC7C5 }, { 0xD517, 0xC7C6 },
	{ 0xD53C, 0xC7C7 }, { 0xD53D, 0xC7C8 }, { 0xD540, 0xC7C9 }, { 0xD544, 0xC7CA }, { 0xD54C, 0xC7CB }, { 0xD54D, 0xC7CC }, { 0xD54F, 0xC7CD }, { 0xD551, 0xC7CE },
	{ 0xD558, 0xC7CF }, { 0xD559, 0xC7D0 }, { 0xD55C, 0xC7D1 }, { 0xD560, 0xC7D2 }, { 0xD565, 0xC7D3 }, { 0xD568, 0xC7D4 }, { 0xD569, 0xC7D5 }, { 0xD56B, 0xC7D6 },
	{ 0xD56D, 0xC7D7 }, { 0xD574, 0xC7D8 }, { 0xD575, 0xC7D9 }, { 0xD578, 0xC7DA }, { 0xD57C, 0xC7DB }, { 0xD584, 0xC7DC }, { 0xD585, 0xC7DD }, { 0xD587, 0xC7DE },
	{ 0xD588, 0xC7DF }, { 0xD589, 0xC7E0 }, { 0xD590, 0xC7E1 }, { 0xD5A5, 0xC7E2 }, { 0xD5C8, 0xC7E3 }, { 0xD5C9, 0xC7E4 }, { 0xD5CC, 0xC7E5 }, { 0xD5D0, 0xC7E6 },
	{ 0xD5D2, 0xC7E7 }, { 0xD5D8, 0xC7E8 }, { 0xD5D9, 0xC7E9 }, { 0xD5DB, 0xC7EA }, { 0xD5DD, 0xC7EB }, { 0xD5E4, 0xC7EC }, { 0xD5E5, 0xC7ED }, { 0xD5E8, 0xC7EE },
	{ 0xD5EC, 0xC7EF }, { 0xD5F4, 0xC7F0 }, { 0xD5F5, 0xC7F1 }, { 0xD5F7, 0xC7F2 }, { 0xD5F9, 0xC7F3 }, { 0xD600, 0xC7F4 }, { 0xD601, 0xC7F5 }, { 0xD604, 0xC7F6 },
	{ 0xD608, 0xC7F7 }, { 0xD610, 0xC7F8 }, { 0xD611, 0xC7F9 }, { 0xD613, 0xC7FA }, { 0xD614, 0xC7FB }, { 0xD615, 0xC7FC }, { 0xD61C, 0xC7FD }, { 0xD620, 0xC7FE },
	{ 0xD624, 0xC8A1 }, { 0xD62D, 0xC8A2 }, { 0xD638, 0xC8A3 }, { 0xD639, 0xC8A4 }, { 0xD63C, 0xC8A5 }, { 0xD640, 0xC8A6 }, { 0xD645, 0xC8A7 }, { 0xD648, 0xC8A8 },
	{ 0xD649, 0xC8A9 }, { 0xD64B, 0xC8AA }, { 0xD64D, 0xC8AB }, { 0xD651, 0xC8AC }, { 0xD654, 0xC8AD }, { 0xD655, 0xC8AE }, { 0xD658, 0xC8AF }, { 0xD65C, 0xC8B0 },
	{ 0xD667, 0xC8B1 }, { 0xD669, 0xC8B2 }, { 0xD670, 0xC8B3 }, { 0xD671, 0xC8B4 }, { 0xD674, 0xC8B5 }, { 0xD683, 0xC8B6 }, { 0xD685, 0xC8B7 }, { 0xD68C, 0xC8B8 },
	{ 0xD68D, 0xC8B9 }, { 0xD690, 0xC8BA }, { 0xD694, 0xC8BB }, { 0xD69D, 0xC8BC }, { 0xD69F, 0xC8BD }, { 0xD6A1, 0xC8BE }, { 0xD6A8, 0xC8BF }, { 0xD6AC, 0xC8C0 },
	{ 0xD6B0, 0xC8C1 }, { 0xD6B9, 0xC8C2 }, { 0xD6BB, 0xC8C3 }, { 0xD6C4, 0xC8C4 }, { 0xD6C5, 0xC8C5 }, { 0xD6C8, 0xC8C6 }, { 0xD6CC, 0xC8C7 }, { 0xD6D1, 0xC8C8 },
	{ 0xD6D4, 0xC8C9 }, { 0xD6D7, 0xC8CA }, { 0xD6D9, 0xC8CB }, { 0xD6E0, 0xC8CC }, { 0xD6E4, 0xC8CD }, { 0xD6E8, 0xC8CE }, { 0xD6F0, 0xC8CF }, { 0xD6F5, 0xC8D0 },
	{ 0xD6FC, 0xC8D1 }, { 0xD6FD, 0xC8D2 }, { 0xD700, 0xC8D3 }, { 0xD704, 0xC8D4 }, { 0xD711, 0xC8D5 }, { 0xD718, 0xC8D6 }, { 0xD719, 0xC8D7 }, { 0xD71C, 0xC8D8 },
	{ 0xD720, 0xC8D9 }, { 0xD728, 0xC8DA }, { 0xD729, 0xC8DB }, { 0xD72B, 0xC8DC }, { 0xD72D, 0xC8DD }, { 0xD734, 0xC8DE }, { 0xD735, 0xC8DF }, { 0xD738, 0xC8E0 },
	{ 0xD73C, 0xC8E1 }, { 0xD744, 0xC8E2 }, { 0xD747, 0xC8E3 }, { 0xD749, 0xC8E4 }, { 0xD750, 0xC8E5 }, { 0xD751, 0xC8E6 }, { 0xD754, 0xC8E7 }, { 0xD756, 0xC8E8 },
	{ 0xD757, 0xC8E9 }, { 0xD758, 0xC8EA }, { 0xD759, 0xC8EB }, { 0xD760, 0xC8EC }, { 0xD761, 0xC8ED }, { 0xD763, 0xC8EE }, { 0xD765, 0xC8EF }, { 0xD769, 0xC8F0 },
	{ 0xD76C, 0xC8F1 }, { 0xD770, 0xC8F2 }, { 0xD774, 0xC8F3 }, { 0xD77C, 0xC8F4 }, { 0xD77D, 0xC8F5 }, { 0xD781, 0xC8F6 }, { 0xD788, 0xC8F7 }, { 0xD789, 0xC8F8 },
	{ 0xD78C, 0xC8F9 }, { 0xD790, 0xC8FA }, { 0xD798, 0xC8FB }, { 0xD799, 0xC8FC }, { 0xD79B, 0xC8FD }, { 0xD79D, 0xC8FE }, { 0xF805, 0xA658 }, { 0xF806, 0xA663 },
	{ 0xF807, 0xA69F }, { 0xF808, 0xA68F }, { 0xF809, 0xA681 }, { 0xF80A, 0xA674 }, { 0xF80B, 0xA696 }, { 0xF80C, 0xA69A }, { 0xF83D, 0xA642 }, { 0xF840, 0xA16E },
	{ 0xF841, 0xA894 }, { 0xF842, 0xAC54 }, { 0xF843, 0xAC42 }, { 0xF844, 0xAC49 }, { 0xF845, 0xAC5F }, { 0xF846, 0xA867 }, { 0xF847, 0xA868 }, { 0xF848, 0xA89D },
	{ 0xF849, 0xA89C }, { 0xF84A, 0xAC4B }, { 0xF84B, 0xAC4A }, { 0xF84C, 0xA747 }, { 0xF84D, 0xA74B }, { 0xF84E, 0xA74C }, { 0xF84F, 0xA74D }, { 0xF900, 0xCBD0 },
	{ 0xF901, 0xCBD6 }, { 0xF902, 0xCBE7 }, { 0xF903, 0xCDCF }, { 0xF904, 0xCDE8 }, { 0xF905, 0xCEAD }, { 0xF906, 0xCFFB }, { 0xF907, 0xD0A2 }, { 0xF908, 0xD0B8 },
	{ 0xF909, 0xD0D0 }, { 0xF90A, 0xD0DD }, { 0xF90B, 0xD1D4 }, { 0xF90C, 0xD1D5 }, { 0xF90D, 0xD1D8 }, { 0xF90E, 0xD1DB }, { 0xF90F, 0xD1DC }, { 0xF910, 0xD1DD },
	{ 0xF911, 0xD1DE }, { 0xF912, 0xD1DF }, { 0xF913, 0xD1E0 }, { 0xF914, 0xD1E2 }, { 0xF915, 0xD1E3 }, { 0xF916, 0xD1E4 }, { 0xF917, 0xD1E5 }, { 0xF918, 0xD1E6 },
	{ 0xF919, 0xD1E8 }, { 0xF91A, 0xD1E9 }, { 0xF91B, 0xD1EA }, { 0xF91C, 0xD1EB }, { 0xF91D, 0xD1ED }, { 0xF91E, 0xD1EF }, { 0xF91F, 0xD1F0 }, { 0xF920, 0xD1F2 },
	{ 0xF921, 0xD1F6 }, { 0xF922, 0xD1FA }, { 0xF923, 0xD1FC }, { 0xF924, 0xD1FD }, { 0xF925, 0xD1FE }, { 0xF926, 0xD2A2 }, { 0xF927, 0xD2A3 }, { 0xF928, 0xD2A7 },
	{ 0xF929, 0xD2A8 }, { 0xF92A, 0xD2A9 }, { 0xF92B, 0xD2AA }, { 0xF92C, 0xD2AB }, { 0xF92D, 0xD2AD }, { 0xF92E, 0xD2B2 }, { 0xF92F, 0xD2BE }, { 0xF930, 0xD2C2 },
	{ 0xF931, 0xD2C3 }, { 0xF932, 0xD2C4 }, { 0xF933, 0xD2C6 }, { 0xF934, 0xD2C7 }, { 0xF935, 0xD2C8 }, { 0xF936, 0xD2C9 }, { 0xF937, 0xD2CA }, { 0xF938, 0xD2CB },
	{ 0xF939, 0xD2CD }, { 0xF93A, 0xD2CE }, { 0xF93B, 0xD2CF }, { 0xF93C, 0xD2D0 }, { 0xF93D, 0xD2D1 }, { 0xF93E, 0xD2D2 }, { 0xF93F, 0xD2D3 }, { 0xF940, 0xD2D4 },
	{ 0xF941, 0xD2D5 }, { 0xF942, 0xD2D6 }, { 0xF943, 0xD2D7 }, { 0xF944, 0xD2D9 }, { 0xF945, 0xD2DA }, { 0xF946, 0xD2DE }, { 0xF947, 0xD2DF }, { 0xF948, 0xD2E1 },
	{ 0xF949, 0xD2E2 }, { 0xF94A, 0xD2E4 }, { 0xF94B, 0xD2E5 }, { 0xF94C, 0xD2E6 }, { 0xF94D, 0xD2E7 }, { 0xF94E, 0xD2E8 }, { 0xF94F, 0xD2E9 }, { 0xF950, 0xD2EA },
	{ 0xF951, 0xD2EB }, { 0xF952, 0xD2F0 }, { 0xF953, 0xD2F1 }, { 0xF954, 0xD2F2 }, { 0xF955, 0xD2F3 }, { 0xF956, 0xD2F4 }, { 0xF957, 0xD2F5 }, { 0xF958, 0xD2F7 },
	{ 0xF959, 0xD2F8 }, { 0xF95A, 0xD4E6 }, { 0xF95B, 0xD4FC }, { 0xF95C, 0xD5A5 }, { 0xF95D, 0xD5AB }, { 0xF95E, 0xD5AE }, { 0xF95F, 0xD6B8 }, { 0xF960, 0xD6CD },
	{ 0xF961, 0xD7CB }, { 0xF962, 0xD7E4 }, { 0xF963, 0xDBC5 }, { 0xF964, 0xDBE4 }, { 0xF965, 0xDCA5 }, { 0xF966, 0xDDA5 }, { 0xF967, 0xDDD5 }, { 0xF968, 0xDDF4 },
	{ 0xF969, 0xDEFC }, { 0xF96A, 0xDEFE }, { 0xF96B, 0xDFB3 }, { 0xF96C, 0xDFE1 }, { 0xF96D, 0xDFE8 }, { 0xF96E, 0xE0F1 }, { 0xF96F, 0xE1AD }, { 0xF970, 0xE1ED },
	{ 0xF971, 0xE3F5 }, { 0xF972, 0xE4A1 }, { 0xF973, 0xE4A9 }, { 0xF974, 0xE5AE }, { 0xF975, 0xE5B1 }, { 0xF976, 0xE5B2 }, { 0xF977, 0xE5B9 }, { 0xF978, 0xE5BB },
	{ 0xF979, 0xE5BC }, { 0xF97A, 0xE5C4 }, { 0xF97B, 0xE5CE }, { 0xF97C, 0xE5D0 }, { 0xF97D, 0xE5D2 }, { 0xF97E, 0xE5D6 }, { 0xF97F, 0xE5FA }, { 0xF980, 0xE5FB },
	{ 0xF981, 0xE5FC }, { 0xF982, 0xE5FE }, { 0xF983, 0xE6A1 }, { 0xF984, 0xE6A4 }, { 0xF985, 0xE6A7 }, { 0xF986, 0xE6AD }, { 0xF987, 0xE6AF }, { 0xF988, 0xE6B0 },
	{ 0xF989, 0xE6B1 }, { 0xF98A, 0xE6B3 }, { 0xF98B, 0xE6B7 }, { 0xF98C, 0xE6B8 }, { 0xF98D, 0xE6BC }, { 0xF98E, 0xE6C4 }, { 0xF98F, 0xE6C6 }, { 0xF990, 0xE6C7 },
	{ 0xF991, 0xE6CA }, { 0xF992, 0xE6D2 }, { 0xF993, 0xE6D6 }, { 0xF994, 0xE6D9 }, { 0xF995, 0xE6DC }, { 0xF996, 0xE6DF }, { 0xF997, 0xE6E1 }, { 0xF998, 0xE6E4 },
	{ 0xF999, 0xE6E5 }, { 0xF99A, 0xE6E6 }, { 0xF99B, 0xE6E8 }, { 0xF99C, 0xE6EA }, { 0xF99D, 0xE6EB }, { 0xF99E, 0xE6EC }, { 0xF99F, 0xE6EF }, { 0xF9A0, 0xE6F1 },
	{ 0xF9A1, 0xE6F2 }, { 0xF9A2, 0xE6F5 }, { 0xF9A3, 0xE6F6 }, { 0xF9A4, 0xE6F7 }, { 0xF9A5, 0xE6F9 }, { 0xF9A6, 0xE7A1 }, { 0xF9A7, 0xE7A6 }, { 0xF9A8, 0xE7A9 },
	{ 0xF9A9, 0xE7AA }, { 0xF9AA, 0xE7AC }, { 0xF9AB, 0xE7AD }, { 0xF9AC, 0xE7B0 }, { 0xF9AD, 0xE7BF }, { 0xF9AE, 0xE7C1 }, { 0xF9AF, 0xE7C6 }, { 0xF9B0, 0xE7C7 },
	{ 0xF9B1, 0xE7CB }, { 0xF9B2, 0xE7CD }, { 0xF9B3, 0xE7CF }, { 0xF9B4, 0xE7D0 }, { 0xF9B5, 0xE7D3 }, { 0xF9B6, 0xE7DF }, { 0xF9B7, 0xE7E4 }, { 0xF9B8, 0xE7E6 },
	{ 0xF9B9, 0xE7F7 }, { 0xF9BA, 0xE8E7 }, { 0xF9BB, 0xE8E8 }, { 0xF9BC, 0xE8F0 }, { 0xF9BD, 0xE8F1 }, { 0xF9BE, 0xE8F7 }, { 0xF9BF, 0xE8F9 }, { 0xF9C0, 0xE8FB },
	{ 0xF9C1, 0xE8FE }, { 0xF9C2, 0xE9A7 }, { 0xF9C3, 0xE9AC }, { 0xF9C4, 0xE9CC }, { 0xF9C5, 0xE9F7 }, { 0xF9C6, 0xEAC1 }, { 0xF9C7, 0xEAE5 }, { 0xF9C8, 0xEAF4 },
	{ 0xF9C9, 0xEAF7 }, { 0xF9CA, 0xEAFC }, { 0xF9CB, 0xEAFE }, { 0xF9CC, 0xEBA4 }, { 0xF9CD, 0xEBA7 }, { 0xF9CE, 0xEBA9 }, { 0xF9CF, 0xEBAA }, { 0xF9D0, 0xEBBA },
	{ 0xF9D1, 0xEBBB }, { 0xF9D2, 0xEBBD }, { 0xF9D3, 0xEBC1 }, { 0xF9D4, 0xEBC2 }, { 0xF9D5, 0xEBC6 }, { 0xF9D6, 0xEBC7 }, { 0xF9D7, 0xEBCC }, { 0xF9D8, 0xEBCF },
	{ 0xF9D9, 0xEBD0 }, { 0xF9DA, 0xEBD1 }, { 0xF9DB, 0xEBD2 }, { 0xF9DC, 0xEBD8 }, { 0xF9DD, 0xECA6 }, { 0xF9DE, 0xECA7 }, { 0xF9DF, 0xECAA }, { 0xF9E0, 0xECAF },
	{ 0xF9E1, 0xECB0 }, { 0xF9E2, 0xECB1 }, { 0xF9E3, 0xECB2 }, { 0xF9E4, 0xECB5 }, { 0xF9E5, 0xECB8 }, { 0xF9E6, 0xECBA }, { 0xF9E7, 0xECC0 }, { 0xF9E8, 0xECC1 },
	{ 0xF9E9, 0xECC5 }, { 0xF9EA, 0xECC6 }, { 0xF9EB, 0xECC9 }, { 0xF9EC, 0xECCA }, { 0xF9ED, 0xECD5 }, { 0xF9EE, 0xECDD }, { 0xF9EF, 0xECDE }, { 0xF9F0, 0xECE1 },
	{ 0xF9F1, 0xECE4 }, { 0xF9F2, 0xECE7 }, { 0xF9F3, 0xECE8 }, { 0xF9F4, 0xECF7 }, { 0xF9F5, 0xECF8 }, { 0xF9F6, 0xECFA }, { 0xF9F7, 0xEDA1 }, { 0xF9F8, 0xEDA2 },
	{ 0xF9F9, 0xEDA3 }, { 0xF9FA, 0xEDEE }, { 0xF9FB, 0xEEDB }, { 0xF9FC, 0xF2BD }, { 0xF9FD, 0xF2FA }, { 0xF9FE, 0xF3B1 }, { 0xF9FF, 0xF4A7 }, { 0xFA00, 0xF4EE },
	{ 0xFA01, 0xF6F4 }, { 0xFA02, 0xF6F6 }, { 0xFA03, 0xF7B8 }, { 0xFA04, 0xF7C8 }, { 0xFA05, 0xF7D3 }, { 0xFA06, 0xF8DB }, { 0xFA07, 0xF8F0 }, { 0xFA08, 0xFAA1 },
	{ 0xFA09, 0xFAA2 }, { 0xFA0A, 0xFAE6 }, { 0xFA0B, 0xFCA9 }, { 0xFE59, 0xA14D }, { 0xFE5A, 0xA14E }, { 0xFF01, 0xA3A1 }, { 0xFF02, 0xA3A2 }, { 0xFF03, 0xA3A3 },
	{ 0xFF04, 0xA3A4 }, { 0xFF05, 0xA3A5 }, { 0xFF06, 0xA3A6 }, { 0xFF07, 0xA3A7 }, { 0xFF08, 0xA3A8 }, { 0xFF09, 0xA3A9 }, { 0xFF0A, 0xA3AA }, { 0xFF0B, 0xA3AB },
	{ 0xFF0C, 0xA3AC }, { 0xFF0D, 0xA3AD }, { 0xFF0E, 0xA3AE }, { 0xFF0F, 0xA3AF }, { 0xFF10, 0xA3B0 }, { 0xFF11, 0xA3B1 }, { 0xFF12, 0xA3B2 }, { 0xFF13, 0xA3B3 },
	{ 0xFF14, 0xA3B4 }, { 0xFF15, 0xA3B5 }, { 0xFF16, 0xA3B6 }, { 0xFF17, 0xA3B7 }, { 0xFF18, 0xA3B8 }, { 0xFF19, 0xA3B9 }, { 0xFF1A, 0xA3BA }, { 0xFF1B, 0xA3BB },
	{ 0xFF1C, 0xA3BC }, { 0xFF1D, 0xA3BD }, { 0xFF1E, 0xA3BE }, { 0xFF1F, 0xA3BF }, { 0xFF20, 0xA3C0 }, { 0xFF21, 0xA3C1 }, { 0xFF22, 0xA3C2 }, { 0xFF23, 0xA3C3 },
	{ 0xFF24, 0xA3C4 }, { 0xFF25, 0xA3C5 }, { 0xFF26, 0xA3C6 }, { 0xFF27, 0xA3C7 }, { 0xFF28, 0xA3C8 }, { 0xFF29, 0xA3C9 }, { 0xFF2A, 0xA3CA }, { 0xFF2B, 0xA3CB },
	{ 0xFF2C, 0xA3CC }, { 0xFF2D, 0xA3CD }, { 0xFF2E, 0xA3CE }, { 0xFF2F, 0xA3CF }, { 0xFF30, 0xA3D0 }, { 0xFF31, 0xA3D1 }, { 0xFF32, 0xA3D2 }, { 0xFF33, 0xA3D3 },
	{ 0xFF34, 0xA3D4 }, { 0xFF35, 0xA3D5 }, { 0xFF36, 0xA3D6 }, { 0xFF37, 0xA3D7 }, { 0xFF38, 0xA3D8 }, { 0xFF39, 0xA3D9 }, { 0xFF3A, 0xA3DA }, { 0xFF3B, 0xA3DB },
	{ 0xFF3C, 0xA1AC }, { 0xFF3D, 0xA3DD }, { 0xFF3E, 0xA3DE }, { 0xFF3F, 0xA3DF }, { 0xFF40, 0xA3E0 }, { 0xFF41, 0xA3E1 }, { 0xFF42, 0xA3E2 }, { 0xFF43, 0xA3E3 },
	{ 0xFF44, 0xA3E4 }, { 0xFF45, 0xA3E5 }, { 0xFF46, 0xA3E6 }, { 0xFF47, 0xA3E7 }, { 0xFF48, 0xA3E8 }, { 0xFF49, 0xA3E9 }, { 0xFF4A, 0xA3EA }, { 0xFF4B, 0xA3EB },
	{ 0xFF4C, 0xA3EC }, { 0xFF4D, 0xA3ED }, { 0xFF4E, 0xA3EE }, { 0xFF4F, 0xA3EF }, { 0xFF50, 0xA3F0 }, { 0xFF51, 0xA3F1 }, { 0xFF52, 0xA3F2 }, { 0xFF53, 0xA3F3 },
	{ 0xFF54, 0xA3F4 }, { 0xFF55, 0xA3F5 }, { 0xFF56, 0xA3F6 }, { 0xFF57, 0xA3F7 }, { 0xFF58, 0xA3F8 }, { 0xFF59, 0xA3F9 }, { 0xFF5A, 0xA3FA }, { 0xFF5B, 0xA3FB },
	{ 0xFF5C, 0xA3FC }, { 0xFF5D, 0xA3FD }, { 0xFFE6, 0xA3DC },
};


MacKoreanEncoding::MacKoreanEncoding():
	DoubleByteEncoding(_names, _charMap, _mappingTable, sizeof(_mappingTable)/sizeof(Mapping), _reverseMappingTable, sizeof(_reverseMappingTable)/sizeof(Mapping))
{
}


MacKoreanEncoding::~MacKoreanEncoding()
{
}


} // namespace Poco
