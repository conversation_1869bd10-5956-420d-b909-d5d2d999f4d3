<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Core">
      <UniqueIdentifier>{c753fd93-71ed-481b-9a5e-8423fb365d1c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Source Files">
      <UniqueIdentifier>{e3c3c07f-36df-41da-811b-4d8bb24de9c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Header Files">
      <UniqueIdentifier>{ee2a83b6-fa90-436b-b4d4-dc8ad4bf871a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams">
      <UniqueIdentifier>{e94ee398-40d3-4744-ac87-44670fc3cca4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\Source Files">
      <UniqueIdentifier>{b8f43561-6742-47f3-b063-cfd1ed044094}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\Header Files">
      <UniqueIdentifier>{1294ba45-fff8-4648-970e-4a29356beea9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\zlib">
      <UniqueIdentifier>{c630b315-9854-48a9-8b5e-513cc30de22f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading">
      <UniqueIdentifier>{128cc54c-da35-421b-9aae-8ee8710a4b48}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading\Source Files">
      <UniqueIdentifier>{8b9cb12f-15a5-4034-ac17-dc5a95d7daff}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading\Header Files">
      <UniqueIdentifier>{1889a735-bf96-4a03-9510-7f73fe91410f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt">
      <UniqueIdentifier>{7fc05b0c-b8d2-4b8e-9aa7-90bc68cec6a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt\Source Files">
      <UniqueIdentifier>{d2debcad-3917-4301-888f-5fbee55103dd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt\Header Files">
      <UniqueIdentifier>{cb3a1c0e-3f59-4381-9cf4-c73694fa16c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary">
      <UniqueIdentifier>{7ad16f51-370b-4cdf-8f47-373fd1e26297}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary\Source Files">
      <UniqueIdentifier>{e556d380-16e9-410c-bb67-450a43ab83cf}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary\Header Files">
      <UniqueIdentifier>{f12c7c88-ef89-4a68-96a0-96664c4a0f2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegularExpression">
      <UniqueIdentifier>{8d90af25-6c9f-45fb-8af0-7463fab03c44}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegularExpression\Source Files">
      <UniqueIdentifier>{3b04a0e6-f311-428b-af8f-c1a7a3d9fd10}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegularExpression\Header Files">
      <UniqueIdentifier>{5f191b86-441f-4da7-8b2f-02513d443466}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{f98d18b5-dfbc-4c35-8654-bf0c3fc815e9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{ed96b756-3566-40c7-bc07-b61535bcdba6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{a04d9b7c-652a-4243-bf01-fb008a91c6a9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Message Files">
      <UniqueIdentifier>{050790a7-f86f-4db4-8445-78e149b33dba}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Resource Files">
      <UniqueIdentifier>{90289141-efe6-483a-87d8-deb78499134e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications">
      <UniqueIdentifier>{27a7acc1-7768-4dce-8fb6-40b249b08465}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications\Source Files">
      <UniqueIdentifier>{e9b28661-70f1-4533-8d55-fecd2888abe4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications\Header Files">
      <UniqueIdentifier>{36aa6485-c596-4900-8e25-4cc06aa8cf49}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem">
      <UniqueIdentifier>{99f3f293-20ff-47f0-8ad2-5d75a60e111f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem\Source Files">
      <UniqueIdentifier>{3a9c04b2-3f1a-4ead-9080-f561de93d869}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem\Header Files">
      <UniqueIdentifier>{6904c90d-c347-43d8-8a6b-91f99de542a5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes">
      <UniqueIdentifier>{246e88e1-3650-4d35-9c95-d96b7b559348}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes\Source Files">
      <UniqueIdentifier>{65dd32ee-dd1e-49fb-aa87-579432609b7e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes\Header Files">
      <UniqueIdentifier>{4ad6c3a6-eb68-4076-8f86-c2d87a444135}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID">
      <UniqueIdentifier>{ce517ddf-e6d2-4a5b-98fc-1fe34287fb3e}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID\Source Files">
      <UniqueIdentifier>{e8a3181c-6c64-4082-b273-9b8519a39d30}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID\Header Files">
      <UniqueIdentifier>{1db1703f-8548-4607-bbd4-8adac7a2ae78}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime">
      <UniqueIdentifier>{2d189b53-c815-48c6-a133-35ab9a24a2ff}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime\Source Files">
      <UniqueIdentifier>{96a98fda-9d60-4ca7-983b-537d452ee74b}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime\Header Files">
      <UniqueIdentifier>{40b1191f-3dc7-48bd-bfcd-715b92e476c1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text">
      <UniqueIdentifier>{967f28a4-ffc8-4a16-a40f-e9fe9aa154fa}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Source Files">
      <UniqueIdentifier>{456d180a-d652-4811-90a5-8fcaa75547d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Header Files">
      <UniqueIdentifier>{2ec8e8b6-f3db-4590-96d1-a3c5ff52590f}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI">
      <UniqueIdentifier>{45088ff9-fe3e-4033-b55d-bea00d991803}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI\Source Files">
      <UniqueIdentifier>{ffe4c9b6-b7a1-4db3-b9cc-e442e83062be}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI\Header Files">
      <UniqueIdentifier>{6d1027ac-8839-4530-99bc-663244b4532b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks">
      <UniqueIdentifier>{aa406aec-8122-4853-9d50-99ba948709e7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks\Source Files">
      <UniqueIdentifier>{3da27dee-ce06-4549-bff7-4d667520fb78}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks\Header Files">
      <UniqueIdentifier>{8fdba01a-1c37-4e5c-b63b-5579c08050b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events">
      <UniqueIdentifier>{bdbeaf1c-efe1-40bc-bca4-13d185121ccd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events\Header Files">
      <UniqueIdentifier>{646f2579-2e44-4305-8f9e-c1b397613e67}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events\Source Files">
      <UniqueIdentifier>{7466c478-b197-45c0-8cfd-84b419a25488}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache">
      <UniqueIdentifier>{e9417176-ebda-4408-8dc6-37e8772432ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache\Header Files">
      <UniqueIdentifier>{67f612b3-7a55-463e-92cd-bed05e72736c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache\Source Files">
      <UniqueIdentifier>{e0d4bc35-7350-45e1-8dfd-5d6ddbe9c8e8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing">
      <UniqueIdentifier>{84674850-4492-4a46-b6f3-3701612a3f23}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing\Header Files">
      <UniqueIdentifier>{af84acf1-fa67-4302-8180-cf002790cdff}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing\Source Files">
      <UniqueIdentifier>{5300bc7b-d351-427b-a3e3-2f7d0a41fc50}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic">
      <UniqueIdentifier>{d339f979-c755-4abc-ac66-27bd13d66836}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\Header Files">
      <UniqueIdentifier>{56685e2b-f506-44ae-8d27-d0f65e27405e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\Source Files">
      <UniqueIdentifier>{7df1fa35-122a-4152-b81f-35be1aabd290}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegularExpression\PCRE2 Header Files">
      <UniqueIdentifier>{8bd7fdba-6cdf-4cc7-8ffd-e0eef416d7b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegularExpression\PCRE2 Source Files">
      <UniqueIdentifier>{f19826b3-7a4b-4d64-aaa3-eb1e084a71b2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Utf8Proc Header Files">
      <UniqueIdentifier>{8f30689e-ed76-4b8b-a9c1-5d3e41522741}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Utf8Proc Source Files">
      <UniqueIdentifier>{090b933c-3b18-41dc-8485-8e83f96ee2ad}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Ascii.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AtomicCounter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Bugcheck.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ByteOrder.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Checksum.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Debugger.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryIteratorStrategy.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Environment.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Environment_UNIX.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Environment_WIN32U.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Error.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Exception.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Format.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPEnvironment.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPEnvironment_C99.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPEnvironment_DEC.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPEnvironment_DUMMY.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPEnvironment_SUN.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPEnvironment_WIN32.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\JSONString.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MemoryPool.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NestedDiagnosticContext.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NumberFormatter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NumberParser.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NumericString.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RefCountedObject.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SortedDirectoryIterator.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\String.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StringTokenizer.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Void.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Base32Decoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Base32Encoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Base64Decoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Base64Encoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\BinaryReader.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\BinaryWriter.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CountingStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DeflatingStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FIFOBufferStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileStream_POSIX.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileStream_WIN32.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HexBinaryDecoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HexBinaryEncoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\InflatingStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LineEndingConverter.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MemoryStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NullStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamCopier.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamTokenizer.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TeeStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Token.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\adler32.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\compress.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\crc32.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\deflate.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\infback.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\inffast.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\inflate.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\inftrees.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\trees.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\zutil.c">
      <Filter>Streams\zlib</Filter>
    </ClCompile>
    <ClCompile Include="src\ActiveDispatcher.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Condition.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ErrorHandler.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Event.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Event_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Event_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Mutex.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Mutex_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Mutex_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Runnable.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RWLock.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RWLock_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RWLock_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Semaphore.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Semaphore_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Semaphore_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SignalHandler.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SynchronizedObject.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Thread.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Thread_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Thread_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadLocal.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadPool.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadTarget.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timer.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DigestEngine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DigestStream.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MD4Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MD5Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Random.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RandomStream.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SHA1Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Manifest.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedLibrary.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedLibrary_HPUX.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedLibrary_UNIX.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedLibrary_WIN32U.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RegularExpression.cpp">
      <Filter>RegularExpression\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ArchiveStrategy.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AsyncChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Channel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Configurable.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConsoleChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EventChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EventLogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Formatter.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FormattingChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LogFile.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LogFile_STD.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LogFile_WIN32U.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Logger.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingFactory.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingRegistry.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LogStream.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Message.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NullChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PatternFormatter.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PurgeStrategy.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RotateStrategy.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SimpleFileChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SplitterChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SyslogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WindowsConsoleChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AbstractObserver.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Notification.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NotificationCenter.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AsyncNotificationCenter.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NotificationQueue.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PriorityNotificationQueue.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimedNotificationQueue.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryIterator.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryIterator_UNIX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryIterator_WIN32U.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryWatcher.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\File.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\File_UNIX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\File_WIN32U.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Glob.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Path.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Path_UNIX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Path_WIN32U.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TemporaryFile.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedEvent.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedEvent_UNIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedEvent_WIN32U.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedMutex.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedMutex_UNIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedMutex_WIN32U.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PIDFile.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Pipe.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PipeImpl.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PipeImpl_DUMMY.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PipeImpl_POSIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PipeImpl_WIN32.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PipeStream.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Process.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Process_UNIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Process_WIN32U.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessRunner.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedMemory.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedMemory_DUMMY.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedMemory_POSIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedMemory_WIN32.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UUID.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UUIDGenerator.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Clock.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTime.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeFormat.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeFormatter.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeParser.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LocalDateTime.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Stopwatch.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timespan.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timestamp.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timezone.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timezone_UNIX.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timezone_WIN32.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ASCIIEncoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Latin1Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Latin2Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Latin9Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamConverter.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextBufferIterator.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextConverter.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextEncoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextIterator.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Unicode.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UnicodeConverter.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UTF16Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UTF32Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UTF8Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UTF8String.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Windows1250Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Windows1251Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Windows1252Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileStreamFactory.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\URI.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\URIStreamFactory.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\URIStreamOpener.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Task.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TaskManager.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TaskNotification.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EventArgs.cpp">
      <Filter>Events\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Hash.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HashStatistic.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Var.cpp">
      <Filter>Dynamic\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VarHolder.cpp">
      <Filter>Dynamic\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VarIterator.cpp">
      <Filter>Dynamic\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SHA2Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DataURIStream.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DataURIStreamFactory.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_auto_possess.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_chartables.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_chkdint.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_compile.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_config.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_context.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_convert.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_dfa_match.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_error.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_extuni.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_find_bracket.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_jit_compile.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_jit_match.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_jit_misc.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_maketables.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_match.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_match_data.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_newline.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_ord2utf.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_pattern_info.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_script_run.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_serialize.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_string_utils.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_study.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_substitute.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_substring.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_tables.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_ucd.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_ucptables.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_valid_utf.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pcre2_xclass.c">
      <Filter>RegularExpression\PCRE2 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VarVisitor.cpp" />
    <ClCompile Include="src\ActiveThreadPool.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utf8proc.c">
      <Filter>Text\Utf8Proc Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utf8proc_data.c">
      <Filter>Text\Utf8Proc Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\JSONFormatter.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Any.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Ascii.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AtomicCounter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AutoPtr.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AutoReleasePool.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Buffer.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Bugcheck.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ByteOrder.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Checksum.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Config.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Debugger.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DynamicAny.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DynamicAnyHolder.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DynamicFactory.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Environment.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Environment_UNIX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Environment_WIN32U.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Error.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Exception.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FIFOBuffer.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Format.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Foundation.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FPEnvironment.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FPEnvironment_C99.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FPEnvironment_DEC.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FPEnvironment_DUMMY.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FPEnvironment_SUN.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FPEnvironment_WIN32.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Instantiator.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\JSONString.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MemoryPool.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MetaProgramming.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedTuple.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NestedDiagnosticContext.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Nullable.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NumberFormatter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NumberParser.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NumericString.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ObjectPool.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Optional.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\OrderedMap.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\OrderedSet.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Platform.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Platform_POSIX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Platform_WIN32.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Poco.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RefCountedObject.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedPtr.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SingletonHolder.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SortedDirectoryIterator.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\String.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StringTokenizer.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Tuple.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TypeList.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Types.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UnWindows.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Version.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Void.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Base32Decoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Base32Encoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Base64Decoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Base64Encoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\BinaryReader.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\BinaryWriter.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\BufferAllocator.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\BufferedBidirectionalStreamBuf.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\BufferedStreamBuf.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\CountingStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DeflatingStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FileStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FileStream_POSIX.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FileStream_WIN32.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HexBinaryDecoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HexBinaryEncoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\InflatingStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LineEndingConverter.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MemoryStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NullStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StreamCopier.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StreamTokenizer.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StreamUtil.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TeeStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Token.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UnbufferedStreamBuf.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\crc32.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\deflate.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\inffast.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\inffixed.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\inflate.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\inftrees.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\trees.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\zconf.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\zlib.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="src\zutil.h">
      <Filter>Streams\zlib</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ActiveDispatcher.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ActiveMethod.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ActiveResult.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ActiveRunnable.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ActiveStarter.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Activity.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Condition.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ErrorHandler.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Event.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Event_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Event_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Mutex.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Mutex_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Mutex_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Runnable.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RunnableAdapter.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RWLock.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RWLock_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RWLock_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ScopedLock.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ScopedUnlock.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Semaphore.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Semaphore_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Semaphore_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SignalHandler.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SynchronizedObject.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Thread.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Thread_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Thread_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ThreadLocal.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ThreadPool.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ThreadTarget.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Timer.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DigestEngine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DigestStream.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HMACEngine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MD4Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MD5Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PBKDF2Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Random.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RandomStream.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SHA1Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ClassLibrary.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ClassLoader.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Manifest.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MetaObject.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedLibrary.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedLibrary_HPUX.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedLibrary_UNIX.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedLibrary_WIN32U.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RegularExpression.h">
      <Filter>RegularExpression\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ArchiveStrategy.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AsyncChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Channel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Configurable.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ConsoleChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\EventChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\EventLogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FileChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Formatter.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FormattingChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LogFile.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LogFile_STD.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LogFile_WIN32U.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Logger.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LoggingFactory.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LoggingRegistry.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LogStream.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Message.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NullChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PatternFormatter.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pocomsg.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PurgeStrategy.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RotateStrategy.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SimpleFileChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SplitterChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StreamChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SyslogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\WindowsConsoleChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AbstractObserver.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AsyncObserver.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AsyncNotificationCenter.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NObserver.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Notification.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NotificationCenter.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NotificationQueue.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Observer.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PriorityNotificationQueue.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TimedNotificationQueue.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DirectoryIterator.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DirectoryIterator_UNIX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DirectoryIterator_WIN32U.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DirectoryWatcher.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\File.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\File_UNIX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\File_WIN32U.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Glob.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Path.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Path_UNIX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Path_WIN32U.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TemporaryFile.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedEvent.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedEvent_UNIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedEvent_WIN32U.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedMutex.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedMutex_UNIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NamedMutex_WIN32U.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PIDFile.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Pipe.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PipeImpl.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PipeImpl_DUMMY.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PipeImpl_POSIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PipeImpl_WIN32.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PipeStream.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Process.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Process_UNIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Process_WIN32U.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ProcessRunner.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedMemory.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedMemory_DUMMY.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedMemory_POSIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SharedMemory_WIN32.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UUID.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UUIDGenerator.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Clock.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DateTime.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DateTimeFormat.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DateTimeFormatter.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DateTimeParser.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LocalDateTime.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Stopwatch.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Timespan.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Timestamp.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Timezone.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ASCIIEncoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Latin1Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Latin2Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Latin9Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ListMap.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StreamConverter.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TextBufferIterator.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TextConverter.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TextEncoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TextIterator.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Unicode.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UnicodeConverter.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UTF16Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UTF32Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UTF8Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UTF8String.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Windows1250Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Windows1251Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Windows1252Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FileStreamFactory.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\URI.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\URIStreamFactory.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\URIStreamOpener.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Task.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TaskManager.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\TaskNotification.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AbstractDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AbstractEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AbstractPriorityDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\BasicEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DefaultStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Delegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DirectoryIteratorStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\EventArgs.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Expire.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FIFOEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FIFOStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FunctionDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\FunctionPriorityDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\NotificationStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PriorityDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PriorityEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PriorityExpire.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PriorityStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RecursiveDirectoryIterator.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\RecursiveDirectoryIteratorImpl.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AbstractCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AbstractStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AccessExpirationDecorator.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AccessExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AccessExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\AccessExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ExpirationDecorator.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\KeyValueArgs.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LRUStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\StrategyCollection.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UniqueAccessExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UniqueAccessExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UniqueAccessExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UniqueExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UniqueExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\UniqueExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ValidArgs.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Hash.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HashFunction.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HashMap.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HashSet.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HashStatistic.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\HashTable.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\LinearHashTable.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SimpleHashTable.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Dynamic\Pair.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Dynamic\Struct.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Dynamic\Var.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Dynamic\VarHolder.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Dynamic\VarIterator.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\SHA2Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DataURIStream.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\DataURIStreamFactory.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pcre2.h">
      <Filter>RegularExpression\PCRE2 Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pcre2_config.h">
      <Filter>RegularExpression\PCRE2 Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pcre2_internal.h">
      <Filter>RegularExpression\PCRE2 Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pcre2_ucp.h">
      <Filter>RegularExpression\PCRE2 Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pcre2_intmodedep.h">
      <Filter>RegularExpression\PCRE2 Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\ActiveThreadPool.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utf8proc.h">
      <Filter>Text\Utf8Proc Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\JSONFormatter.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="src\pocomsg.rc">
      <Filter>Logging\Resource Files</Filter>
    </ResourceCompile>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="src\pocomsg.mc">
      <Filter>Logging\Message Files</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>