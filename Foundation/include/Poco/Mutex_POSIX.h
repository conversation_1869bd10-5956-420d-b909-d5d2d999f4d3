//
// Mutex_POSIX.h
//
// Library: Foundation
// Package: Threading
// Module:  Mutex
//
// Definition of the MutexImpl and FastMutexImpl classes for POSIX Threads.
//
// Copyright (c) 2004-2008, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#ifndef Foundation_Mutex_POSIX_INCLUDED
#define Foundation_Mutex_POSIX_INCLUDED


#include "Poco/Foundation.h"
#include "Poco/Exception.h"
#include "Poco/Error.h"
#include <pthread.h>


namespace Poco {


class Foundation_API MutexImpl
{
protected:
	MutexImpl();
	MutexImpl(bool fast);
	~MutexImpl();
	void lockImpl();
	bool tryLockImpl();
	bool tryLockImpl(long milliseconds);
	void unlockImpl();

private:
	pthread_mutex_t _mutex;
};


class Foundation_API FastMutexImpl: public MutexImpl
{
protected:
	FastMutexImpl();
	~FastMutexImpl();
};


//
// inlines
//
inline void MutexImpl::lockImpl()
{
	int rc;
	if ((rc = pthread_mutex_lock(&_mutex)))
		throw SystemException("cannot lock mutex", Error::getMessage(rc));
}


inline bool MutexImpl::tryLockImpl()
{
	int rc = pthread_mutex_trylock(&_mutex);
	if (rc == 0)
		return true;
	else if (rc == EBUSY)
		return false;
	else
		throw SystemException("cannot lock mutex", Error::getMessage(rc));
}


inline void MutexImpl::unlockImpl()
{
	int rc;
	if ((rc = pthread_mutex_unlock(&_mutex)))
		throw SystemException("cannot unlock mutex", Error::getMessage(rc));
}


} // namespace Poco


#endif // Foundation_Mutex_POSIX_INCLUDED
