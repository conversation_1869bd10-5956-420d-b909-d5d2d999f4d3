#
# Makefile
#
# Makefile for Poco Foundation Samples
#

.PHONY: projects
clean distclean all: projects
projects:
	$(MAKE) -C <PERSON>Method $(MAKECMDGOALS)
	$(MAKE) -C Activity $(MAKECMDGOALS)
	$(MAKE) -C Timer $(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
	$(MAKE) -C BinaryReaderWriter $(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
	$(MAKE) -C LineEndingConverter $(MA<PERSON><PERSON>DGOALS)
	$(MAKE) -C base64decode $(MAKECMDGOALS)
	$(MAKE) -C base64encode $(MAKECMDGOALS)
	$(MAKE) -C deflate $(MAKECMDGOALS)
	$(MAKE) -C inflate $(<PERSON>KECMDGOALS)
	$(MAKE) -C DateTime $(<PERSON><PERSON>CMDGOALS)
	$(MAKE) -C Logger $(MAKECMDGOALS)
	$(MAKE) -C grep $(MAKECMDGOALS)
	$(MAKE) -C dir $(MAKECMDGOALS)
	$(MAKE) -C md5 $(<PERSON>KECMDGOALS)
	$(MAKE) -C hmacmd5 $(MAKE<PERSON><PERSON>GOA<PERSON>)
	$(MAKE) -C NotificationQueue $(<PERSON><PERSON><PERSON><PERSON><PERSON>OA<PERSON>)
	$(MAKE) -C StringTokenizer $(<PERSON><PERSON><PERSON><PERSON>GOA<PERSON>)
	$(MAKE) -C URI $(MAKECMDGOALS)
	$(MAKE) -C uuidgen $(MAKECMDGOALS)
	$(MAKE) -C trace $(MAKECMDGOALS)
