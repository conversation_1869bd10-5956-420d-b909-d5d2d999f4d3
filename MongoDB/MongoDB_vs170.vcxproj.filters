<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{fc391fb8-f7d9-4e46-bafb-09d1a5385f2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{fa1b8e22-4489-4933-8182-7a46f1363e15}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Array.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Binary.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Connection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Cursor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Database.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DeleteRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Document.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Element.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\GetMoreRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\InsertRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\JavaScriptCode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KillCursorsRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Message.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MessageHeader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ObjectId.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OpMsgCursor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OpMsgMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\QueryRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RegularExpression.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ReplicaSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RequestMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ResponseMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UpdateRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\MongoDB\Array.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Binary.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\BSONReader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\BSONWriter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Connection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Cursor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Database.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\DeleteRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Document.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Element.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\GetMoreRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\InsertRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\JavaScriptCode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\KillCursorsRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\Message.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\MessageHeader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\MongoDB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\ObjectId.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\PoolableConnectionFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\QueryRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\RegularExpression.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\ReplicaSet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\RequestMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\ResponseMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\MongoDB\UpdateRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>