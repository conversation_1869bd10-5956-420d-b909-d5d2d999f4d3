<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="NetCore">
      <UniqueIdentifier>{39b5f6a8-2a04-4c56-a131-41de81ee930a}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Header Files">
      <UniqueIdentifier>{faa37093-1107-46f6-ab16-800af478c6d8}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Source Files">
      <UniqueIdentifier>{e4463556-71b1-4623-a2aa-cf42cff56f44}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets">
      <UniqueIdentifier>{151876a9-27bd-4af1-b30c-74b9b61a494a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Header Files">
      <UniqueIdentifier>{a6f8d4b1-c154-4d81-a3b5-b4665af86a8e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Source Files">
      <UniqueIdentifier>{cb7688c4-4d8c-453b-9ddb-cedec6cf4b38}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages">
      <UniqueIdentifier>{f672c8e1-329c-4b82-b1f3-c6fe38e99f1b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Header Files">
      <UniqueIdentifier>{98f26cee-6119-4d3c-9100-dcd410f2de59}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Source Files">
      <UniqueIdentifier>{0d341365-0376-4a41-8def-2c73321949e2}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP">
      <UniqueIdentifier>{354e6825-62f5-421a-886b-6f053e5739ac}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Header Files">
      <UniqueIdentifier>{91b65a36-fdd5-4648-a055-f9f12564ec0e}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Source Files">
      <UniqueIdentifier>{c0c782cf-40be-4da0-ba4c-09313c27897e}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{c658f45b-e825-4f9b-ba73-ab2b064bbc01}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{f0bf8cc1-f209-453b-978a-b997cc0e1007}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{c26c159a-6c4a-46c7-a56c-d9b04c47173a}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer">
      <UniqueIdentifier>{23a8fac3-dd0f-4621-a76a-89718355a86c}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Header Files">
      <UniqueIdentifier>{692b4163-dcb8-4f4f-959e-44c69dd8f291}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Source Files">
      <UniqueIdentifier>{dead6c4d-b206-4a20-bc09-8112b07be4f5}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient">
      <UniqueIdentifier>{ab4c84f4-656e-496d-b70d-7bc805738fbd}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Header Files">
      <UniqueIdentifier>{a75473d2-0c1e-4ebc-9580-6c460f4a260a}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Source Files">
      <UniqueIdentifier>{eb9d23a7-6133-4b76-8f7b-0ffc6b13aa41}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML">
      <UniqueIdentifier>{fb7c73be-a262-486a-9c68-a01d15581670}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Header Files">
      <UniqueIdentifier>{16656f38-4b19-4bdc-bccb-1c40f7490447}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Source Files">
      <UniqueIdentifier>{21aebe0c-f3c3-44ee-895b-a916d600ff91}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient">
      <UniqueIdentifier>{bcdb4b79-790d-4dea-8395-dc99ddf58216}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient\Header Files">
      <UniqueIdentifier>{97ae9069-cd28-4d92-be53-6e3cd4bd427a}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient\Source Files">
      <UniqueIdentifier>{e4597e3e-6507-4c42-aeca-03a96180627b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor">
      <UniqueIdentifier>{c6a2677d-45ee-4f1d-95f7-1fe1c0f194c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Header Files">
      <UniqueIdentifier>{855be090-4afb-45ce-9194-a26218a5908f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Source Files">
      <UniqueIdentifier>{14615fc3-9892-4157-b69f-032f25a1ef0d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{d46dd897-0b3e-4053-ad71-5b3924aeae53}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{e7f60724-bfcf-4ccb-bb64-ecd569b49d9e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{5b5f0edb-92fd-46b3-a449-781bb00a9ca4}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP">
      <UniqueIdentifier>{e77f16f4-8723-4338-9091-09f4582fe26a}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Header Files">
      <UniqueIdentifier>{3bac0f7f-ec42-46c2-9972-b425b392474f}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Source Files">
      <UniqueIdentifier>{b8c75628-e76a-45ac-9fde-6583b5d1c686}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP">
      <UniqueIdentifier>{0078be81-efeb-4471-ba9a-8fc29029c047}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Header Files">
      <UniqueIdentifier>{9422e2e1-95ee-4815-87c6-e3eb9a68e592}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Source Files">
      <UniqueIdentifier>{78535b2a-f235-4214-b0e9-b810028a824f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{19e7ee25-083c-427a-895e-fcebfcf6bebc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{f34e65eb-c020-4f53-b94a-fa95785b8d3e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{18a9544b-ac77-4f99-be8e-ba471d2e5e1e}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{445e1e6f-735f-46ea-aa41-a9db9e2132f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{27dac4e6-c87c-4bce-9f59-7ee281b6fd5b}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{23c1c1d3-81ca-4104-bbc8-8ee6a93dfce7}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth">
      <UniqueIdentifier>{e6ab3b82-b183-49b5-bea4-4c71967b4cdd}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Header Files">
      <UniqueIdentifier>{4ec09a11-a857-41a4-9c8d-fd8db3e3fe11}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Source Files">
      <UniqueIdentifier>{ea10da72-9c34-49fd-8748-c7915b981337}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP">
      <UniqueIdentifier>{41c1fd91-829d-41f4-8b9a-29ba47865c46}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Source Files">
      <UniqueIdentifier>{6426a534-b93a-4b37-9f8d-84815981d4ef}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Header Files">
      <UniqueIdentifier>{bd442f1c-4d95-4389-8344-3d11a890ea24}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM">
      <UniqueIdentifier>{3517c25c-1aa8-4a2c-a05b-7c7e92764286}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM\Header Files">
      <UniqueIdentifier>{fc9f9aef-728c-4a71-bb98-903fe29a25ba}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM\Source Files">
      <UniqueIdentifier>{92d7d620-c47e-4783-8d6f-efbe76fd613c}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Net\DNS.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HostEntry.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\IPAddress.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\IPAddressImpl.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Net.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetException.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetworkInterface.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketAddress.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketAddressImpl.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketDefs.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\DatagramSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\DatagramSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\DialogSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MulticastSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PollSet.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RawSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RawSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ServerSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ServerSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Socket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketStream.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\StreamSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\StreamSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\wepoll.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FilePartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MediaType.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MessageHeader.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MultipartReader.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MultipartWriter.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NameValueCollection.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NullPartHandler.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PartHandler.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PartStore.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\QuotedPrintableDecoder.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\QuotedPrintableEncoder.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\StringPartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPAuthenticationParams.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPBasicCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPBasicStreamBuf.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPBufferAllocator.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPChunkedStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPCookie.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPDigestCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPFixedLengthStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPHeaderStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPMessage.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPNTLMCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPRequest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPResponse.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSession.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServer.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerConnection.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerConnectionFactory.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerDispatcher.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerParams.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\AbstractHTTPRequestHandler.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPRequestHandler.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPRequestHandlerFactory.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServer.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerConnection.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerConnectionFactory.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerParams.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerRequest.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerRequestImpl.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerResponse.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerResponseImpl.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerSession.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPClientSession.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPIOStream.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSessionFactory.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSessionInstantiator.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPStreamFactory.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTMLForm.h">
      <Filter>HTML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPClientSession.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPStreamFactory.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ParallelSocketAcceptor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ParallelSocketReactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketAcceptor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketConnector.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketNotification.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketNotifier.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketReactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketProactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MailMessage.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MailRecipient.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MailStream.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\POP3ClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPClient.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPEventArgs.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPPacket.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPPacketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPSocket.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPSocketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPv4PacketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTPClient.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTPEventArgs.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTPPacket.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RemoteSyslogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RemoteSyslogListener.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SMTPChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\WebSocket.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\WebSocketImpl.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\OAuth10Credentials.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\OAuth20Credentials.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MultiSocketPoller.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SingleSocketPoller.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPClient.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPHandler.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPServer.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPServerParams.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPSocketReader.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTLMCredentials.h">
      <Filter>NTLM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSPINTLMCredentials.h">
      <Filter>NTLM\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\DNS.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HostEntry.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IPAddress.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IPAddressImpl.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Net.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetException.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetworkInterface.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketAddress.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketAddressImpl.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DatagramSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DatagramSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DialogSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MulticastSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PollSet.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RawSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RawSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ServerSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ServerSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Socket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketStream.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\wepoll.c">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FilePartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MediaType.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MessageHeader.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MultipartReader.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MultipartWriter.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NameValueCollection.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NullPartHandler.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartHandler.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartStore.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\QuotedPrintableDecoder.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\QuotedPrintableEncoder.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StringPartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPAuthenticationParams.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPBasicCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPBufferAllocator.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPChunkedStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPCookie.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPDigestCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPFixedLengthStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPHeaderStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPMessage.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPNTLMCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPResponse.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSession.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServer.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerConnection.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerConnectionFactory.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerDispatcher.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerParams.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AbstractHTTPRequestHandler.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequestHandler.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequestHandlerFactory.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServer.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerConnection.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerConnectionFactory.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerParams.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerRequest.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerRequestImpl.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerResponse.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerResponseImpl.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerSession.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPClientSession.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPIOStream.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSessionFactory.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSessionInstantiator.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPStreamFactory.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTMLForm.cpp">
      <Filter>HTML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPClientSession.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPStreamFactory.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketNotification.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketNotifier.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketReactor.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketProactor.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailMessage.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailRecipient.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailStream.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\POP3ClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPClient.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPEventArgs.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPPacket.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPPacketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPSocket.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPSocketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPv4PacketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPClient.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPEventArgs.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPPacket.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RemoteSyslogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RemoteSyslogListener.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SMTPChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocket.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketImpl.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuth10Credentials.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuth20Credentials.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPClient.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPServerParams.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTLMCredentials.cpp">
      <Filter>NTLM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSPINTLMCredentials.cpp">
      <Filter>NTLM\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>