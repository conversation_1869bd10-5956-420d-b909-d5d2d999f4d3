//
// SocketNotification.h
//
// Library: Net
// Package: Reactor
// Module:  SocketNotification
//
// Definition of the SocketNotification class.
//
// Copyright (c) 2005-2006, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#ifndef Net_SocketNotification_INCLUDED
#define Net_SocketNotification_INCLUDED


#include "Poco/Net/Net.h"
#include "Poco/Net/Socket.h"
#include "Poco/Notification.h"


namespace Poco {
namespace Net {


class SocketReactor;


class Net_API SocketNotification: public Poco::Notification
	/// The base class for all notifications generated by
	/// the SocketReactor.
{
public:
	explicit SocketNotification(SocketReactor* pReactor);
		/// Creates the SocketNotification for the given SocketReactor.

	~SocketNotification() override;
		/// Destroys the SocketNotification.

	SocketReactor& source() const;
		/// Returns the SocketReactor that generated the notification.

	Socket socket() const;
		/// Returns the socket that caused the notification.

protected:
	void setSocket(const Socket& socket);

private:
	SocketReactor* _pReactor;
	Socket         _socket;

	friend class SocketNotifier;
};


class Net_API ReadableNotification: public SocketNotification
	/// This notification is sent if a socket has become readable.
{
public:
	ReadableNotification(SocketReactor* pReactor);
		/// Creates the ReadableNotification for the given SocketReactor.

	~ReadableNotification() override;
		/// Destroys the ReadableNotification.
};


class Net_API WritableNotification: public SocketNotification
	/// This notification is sent if a socket has become writable.
{
public:
	WritableNotification(SocketReactor* pReactor);
		/// Creates the WritableNotification for the given SocketReactor.

	~WritableNotification() override;
		/// Destroys the WritableNotification.
};


class Net_API ErrorNotification: public SocketNotification
	/// This notification is sent if a socket has signalled an error.
{
public:
	ErrorNotification(SocketReactor* pReactor, int code = 0, const std::string& description = "");
		/// Creates the ErrorNotification for the given SocketReactor.

	ErrorNotification(SocketReactor* pReactor, const Socket& socket,
		int code = 0, const std::string& description = "");
		/// Creates the ErrorNotification for the given SocketReactor.

	~ErrorNotification() override;
		/// Destroys the ErrorNotification.

	int code() const;
		/// Returns the error code.

	const std::string& description() const;
		/// Returns error description.

private:
	int _code = 0;
	std::string _description;
};


inline int ErrorNotification::code() const
{
	return _code;
}


inline const std::string& ErrorNotification::description() const
{
	return _description;
}


class Net_API TimeoutNotification: public SocketNotification
	/// This notification is sent if no other event has occurred
	/// for a specified time.
{
public:
	TimeoutNotification(SocketReactor* pReactor);
		/// Creates the TimeoutNotification for the given SocketReactor.

	~TimeoutNotification() override;
		/// Destroys the TimeoutNotification.
};


class Net_API IdleNotification: public SocketNotification
	/// This notification is sent when the SocketReactor does
	/// not have any sockets to react to.
{
public:
	IdleNotification(SocketReactor* pReactor);
		/// Creates the IdleNotification for the given SocketReactor.

	~IdleNotification() override;
		/// Destroys the IdleNotification.
};


class Net_API ShutdownNotification: public SocketNotification
	/// This notification is sent when the SocketReactor is
	/// about to shut down.
{
public:
	ShutdownNotification(SocketReactor* pReactor);
		/// Creates the ShutdownNotification for the given SocketReactor.

	~ShutdownNotification() override;
		/// Destroys the ShutdownNotification.
};


//
// inlines
//
inline SocketReactor& SocketNotification::source() const
{
	return *_pReactor;
}


inline Socket SocketNotification::socket() const
{
	return _socket;
}


} } // namespace Poco::Net


#endif // Net_SocketNotification_INCLUDED
