#
# Makefile
#
# Makefile for Poco Net Samples
#

.PHONY: projects
clean distclean all: projects
projects:
	$(MAKE) -C dict $(MAKE<PERSON>DGOALS)
	$(MAKE) -C TimeServer $(MAKECMDGOALS)
	$(MAKE) -C httpget $(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
	$(MAKE) -C HTTPTimeServer $(MA<PERSON><PERSON><PERSON><PERSON>OA<PERSON>)
	$(MAKE) -C HTTPFormServer $(MAKECMDGOALS)
	$(MAKE) -C HTTPLoadTest $(MAKECMDGOALS)
	$(MAKE) -C download $(MAKECMDGOALS)
	$(MAKE) -C EchoServer $(MAKECMDGOALS)
	$(MAKE) -C Mail $(MAKECMDGOALS)
	$(MAKE) -C Ping $(<PERSON><PERSON>CMDGOALS)
	$(MAKE) -C WebSocketServer $(MAKECMDGOALS)
	$(MAKE) -C SMTPLogger $(MAKECMDGOALS)
	$(MAKE) -C ifconfig $(<PERSON><PERSON><PERSON>DGOA<PERSON>)
	$(MAKE) -C tcpserver $(MA<PERSON><PERSON><PERSON>GOA<PERSON>)
