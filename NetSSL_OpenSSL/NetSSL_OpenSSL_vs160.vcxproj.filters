<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SSLCore">
      <UniqueIdentifier>{433ea30a-ca6c-475e-a317-d43983a445c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Header Files">
      <UniqueIdentifier>{641d19de-a63d-4c86-a3c2-8c712e61394f}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Source Files">
      <UniqueIdentifier>{0a2143aa-8dcd-4508-85a1-14c51fe08386}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{61c64770-3e9c-4d42-a6b2-5e90e35df043}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{cfcc6cb0-3328-4d18-82e0-6c969a54e5e8}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{01865fad-d7c0-465c-b62f-cee681ab5d50}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets">
      <UniqueIdentifier>{86d099df-ebd6-461d-ab8b-75a811a8b4e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Header Files">
      <UniqueIdentifier>{56dbca83-cc9d-43f1-9323-7db4c56f906a}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Source Files">
      <UniqueIdentifier>{a5a8eff1-4419-4f34-b090-e08641dedfac}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{e8342a24-f538-444b-b5e0-3fe919ce5abb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{668950e4-eef3-49ec-b59a-574c849247c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{c43e39ff-10a0-468a-b75a-69006cd149eb}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient">
      <UniqueIdentifier>{7f74b0f3-ad83-437e-9f42-2e500351ad98}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Header Files">
      <UniqueIdentifier>{3a46d763-2427-4ba7-ab6e-a070b93aefab}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Source Files">
      <UniqueIdentifier>{0dcbdeac-d7ae-4a71-aa6d-744ed2a0fcfa}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Net\AcceptCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ConsoleCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Context.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\InvalidCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyConsoleHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyFileHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetSSL.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyPassphraseHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RejectCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Session.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLException.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLManager.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Utility.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\VerificationErrorArgs.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\X509Certificate.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSClientSession.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSSessionInstantiator.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSStreamFactory.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPSClientSession.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPSStreamFactory.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AcceptCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConsoleCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Context.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\InvalidCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyConsoleHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyFileHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyPassphraseHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RejectCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Session.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLException.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLManager.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VerificationErrorArgs.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\X509Certificate.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSession.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSSessionInstantiator.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactory.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSClientSession.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSStreamFactory.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>