<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SSLCore">
      <UniqueIdentifier>{028fa258-1e05-487c-be1f-2fb27677a2b1}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Header Files">
      <UniqueIdentifier>{3f0e26b2-27c7-43ac-a76d-cbb76aab4c7e}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Source Files">
      <UniqueIdentifier>{d1a5acb4-3ec4-4976-ad9b-8218b84593a6}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{ec1583b0-e32b-4ef0-a739-8e50865f6725}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{1dd38885-9d04-4942-8730-bff447d1216c}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{3ec86533-ac2d-4a77-9073-190748e4dc3c}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets">
      <UniqueIdentifier>{f338c81c-aceb-4541-988d-e93f08bc3744}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Header Files">
      <UniqueIdentifier>{205543d2-ddbb-42ee-a0ea-a8a5ab9b733f}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Source Files">
      <UniqueIdentifier>{c6eec17d-8efc-4318-ae6b-0ea504b4ee3c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{c89a507d-50c1-4553-a1cb-f3f08962577b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{e8ffef03-8020-4b41-8b76-95c00c46ea12}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{bf7c42e5-aa4e-4c9f-9f1d-026b5954eb48}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Net\AcceptCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\AutoSecBufferDesc.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ConsoleCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Context.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\InvalidCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyConsoleHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyFileHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetSSL.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyPassphraseHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RejectCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Session.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLException.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLManager.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Utility.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\VerificationErrorArgs.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\X509Certificate.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSClientSession.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSSessionInstantiator.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSStreamFactory.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AcceptCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConsoleCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Context.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\InvalidCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyConsoleHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyFileHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyPassphraseHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RejectCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Session.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLException.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLManager.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VerificationErrorArgs.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\X509Certificate.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSession.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSSessionInstantiator.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactory.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>