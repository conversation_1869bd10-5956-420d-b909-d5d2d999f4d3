<AppConfig>
	<PocoDoc>
		<files>
			<include>
				${PocoBuild}/*/include/Poco/*.h
				${PocoBuild}/*/include/Poco/*/*.h
				${PocoBuild}/*/include/Poco/*/*/*.h
				${PocoBuild}/*/include/Poco/*/*.h
				${PocoBuild}/*/*/include/Poco/*/*/*.h
			</include>
			<exclude>
				*_POSIX.h
				*_STD.h
				*_UNIX.h
				*_DEC.h
				*_SUN.h
				*_HPUX.h
				*_VX.h
				*_Android.h
				*_QNX.h
				*_UNIXODBC.h
				*_DUMMY.h
				*_C99.h
				*_WIN32.h
				${PocoBuild}/Util/include/Poco/Util/Units.h
			</exclude>
		</files>
		<pages>
			${PocoBuild}/doc/*.page,
			${PocoBuild}/*/doc/*.page
			${PocoBuild}/*/*/doc/*.page
		</pages>
		<resources>
			${PocoBase}/PocoDoc/resources/css,
			${PocoBase}/PocoDoc/resources/js,
			${PocoBase}/PocoDoc/resources/images,
			${PocoBase}/PocoDoc/resources/index.thtml,
			${PocoBuild}/*/doc/images
		</resources>
		<compiler>
			<exec>g++</exec>
			<options>
				${Includes},
				-I/usr/local/mysql/include,
				-I/usr/include/mysql,
				-I/usr/include/postgresql,
				-D_DEBUG,
				-E,
				-C,
				-DPOCO_NO_GCC_API_ATTRIBUTE,
				-DPOCO_DOC,
				-DPOCO_SILENCE_DEPRECATED
			</options>
			<path></path>
			<usePipe>true</usePipe>
		</compiler>
		<language>EN</language>
		<charset>utf-8</charset>
		<software>Applied Informatics C++ Libraries and Tools</software>
		<company>Applied Informatics Software Engineering GmbH and Contributors</company>
		<companyURI>http://www.appinf.com/</companyURI>
		<headerImage>images/headerlogo.png</headerImage>
	</PocoDoc>
	<Translations>
		<EN>
			<All_Base_Classes>All Base Classes</All_Base_Classes>
			<All_Symbols>All Symbols</All_Symbols>
			<Anonymous>Anonymous</Anonymous>
			<Constructors>Constructors</Constructors>
			<Class>Class</Class>
			<Deprecated>Deprecated</Deprecated>
			<Description>Description</Description>
			<Destructor>Destructor</Destructor>
			<Direct_Base_Classes>Direct Base Classes</Direct_Base_Classes>
			<Enumerations>Enumerations</Enumerations>
			<Functions>Functions</Functions>
			<Header>Header</Header>
			<iff>if and only if</iff>
			<Inheritance>Inheritance</Inheritance>
			<Inherited_Functions>Inherited Functions</Inherited_Functions>
			<is_deprecated>is deprecated and should no longer be used</is_deprecated>
			<Known_Derived_Classes>Known Derived Classes</Known_Derived_Classes>
			<Library>Library</Library>
			<Member_Functions>Member Functions</Member_Functions>
			<Member_Summary>Member Summary</Member_Summary>
			<more>more...</more>
			<Namespaces>Namespaces</Namespaces>
			<Namespace>Namespace</Namespace>
			<Nested_Classes>Nested Classes</Nested_Classes>
			<Package>Package</Package>
			<Packages>Packages</Packages>
			<Package_Index>Package Index</Package_Index>
			<See_also>See also</See_also>
			<Struct>Struct</Struct>
			<Symbol_Index>Symbol Index</Symbol_Index>
			<This>This</This>
			<Types>Types</Types>
			<Variables>Variables</Variables>
			<TOC>Contents</TOC>
			<Guides>User Guides and Tutorials</Guides>
			<AAAIntroduction>Introduction</AAAIntroduction>
		</EN>
	</Translations>

	<logging>
		<loggers>
			<root>
				<channel>c1</channel>
				<level>warning</level>
			</root>
		</loggers>
		<channels>
			<c1>
				<class>ConsoleChannel</class>
				<pattern>%s: [%p] %t</pattern>
			</c1>
		</channels>
	</logging>
</AppConfig>
