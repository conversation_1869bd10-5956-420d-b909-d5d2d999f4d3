progen.templatePath = ${application.configDir}templates
progen.libsuffix.debug_shared = d.lib
progen.libsuffix.debug_static_md = mdd.lib
progen.libsuffix.debug_static_mt = mtd.lib
progen.libsuffix.release_shared = .lib
progen.libsuffix.release_static_md = md.lib
progen.libsuffix.release_static_mt = mt.lib
progen.project.guidFromName.namespaceUUID = F4193868-E4EB-4090-9A01-344E7233004B

progen.postprocess.upgrade2008to2019.tool = ${system.env.VS170COMNTOOLS}\\..\\IDE\\DevEnv.exe
progen.postprocess.upgrade2008to2019.args = %;/Upgrade
progen.postprocess.upgrade2008to2019.deleteOriginalFile = true
progen.postprocess.upgrade2008to2019.deleteFiles = Backup;_UpgradeReport_Files;UpgradeLog.XML;UpgradeLog.htm
progen.postprocess.upgrade2008to2019.fix2019ProjectFile = true

progen.postprocess.upgrade2008to2022.tool = ${system.env.VS170COMNTOOLS}\\..\\IDE\\DevEnv.exe
progen.postprocess.upgrade2008to2022.args = %;/Upgrade
progen.postprocess.upgrade2008to2022.deleteOriginalFile = true
progen.postprocess.upgrade2008to2022.deleteFiles = Backup;_UpgradeReport_Files;UpgradeLog.XML;UpgradeLog.htm
progen.postprocess.upgrade2008to2022.fix2022ProjectFile = true

progen.backupProjectFile = false
progen.solution.applicationGUID = 8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942
