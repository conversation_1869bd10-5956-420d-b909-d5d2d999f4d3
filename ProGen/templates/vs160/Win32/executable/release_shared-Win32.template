<Configuration
	Name="release_shared|Win32"
	OutputDirectory="bin\"
	IntermediateDirectory="obj\${project.name}\$(ConfigurationName)"
	ConfigurationType="1"
	CharacterSet="2"
	>
	<Tool
		Name="VCPreBuildEventTool"
	/>
	<Tool
		Name="VCCustomBuildTool"
	/>
	<Tool
		Name="VCXMLDataGeneratorTool"
	/>
	<Tool
		Name="VCWebServiceProxyGeneratorTool"
	/>
	<Tool
		Name="VCMIDLTool"
	/>
	<Tool
		Name="VCCLCompilerTool"
		Optimization="2"
		InlineFunctionExpansion="1"
		EnableIntrinsicFunctions="true"
		FavorSizeOrSpeed="1"
		OmitFramePointers="true"
		AdditionalIncludeDirectories=".\include;${configuration.compiler.includes}"
		PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0500;${configuration.compiler.defines}"
		StringPooling="true"
		RuntimeLibrary="2"
		BufferSecurityCheck="false"
		TreatWChar_tAsBuiltInType="true"
		ForceConformanceInForLoopScope="true"
		RuntimeTypeInfo="true"
		UsePrecompiledHeader="0"
		WarningLevel="3"
		Detect64BitPortabilityProblems="false"
		DebugInformationFormat="0"
		CompileAs="0"
		ProgramDatabaseFileName="$(OutDir)$(TargetName).pdb"
		LanguageStandard="${vc.project.compiler.std.cpp}"
		LanguageStandard_C="${vc.project.compiler.std.c}"
		DisableSpecificWarnings="${configuration.compiler.disableWarnings}"
		AdditionalOptions="${configuration.compiler.additionalOptions}"
	/>
	<Tool
		Name="VCManagedResourceCompilerTool"
	/>
	<Tool
		Name="VCResourceCompilerTool"
	/>
	<Tool
		Name="VCPreLinkEventTool"
	/>
	<Tool
		Name="VCLinkerTool"
		AdditionalDependencies="${configuration.linker.dependencies}"
		OutputFile="bin\${project.target}.exe"
		LinkIncremental="1"
		AdditionalLibraryDirectories="${project.pocobase}\lib;${configuration.linker.libraries}"
		GenerateDebugInformation="false"
		SubSystem="1"
		OptimizeReferences="2"
		EnableCOMDATFolding="2"
		TargetMachine="1"
		AdditionalOptions="${configuration.linker.additionalOptions}"
	/>
	<Tool
		Name="VCALinkTool"
	/>
	<Tool
		Name="VCManifestTool"
	/>
	<Tool
		Name="VCXDCMakeTool"
	/>
	<Tool
		Name="VCBscMakeTool"
	/>
	<Tool
		Name="VCFxCopTool"
	/>
	<Tool
		Name="VCAppVerifierTool"
	/>
	<Tool
		Name="VCPostBuildEventTool"
	/>
</Configuration>
