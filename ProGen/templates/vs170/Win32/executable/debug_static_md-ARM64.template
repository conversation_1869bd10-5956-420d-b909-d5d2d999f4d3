<Configuration
	Name="debug_static_md|ARM64"
	OutputDirectory="binA64\static_md\"
	IntermediateDirectory="objA64\${project.name}\$(ConfigurationName)"
	ConfigurationType="1"
	CharacterSet="2"
	>
	<Tool
		Name="VCPreBuildEventTool"
	/>
	<Tool
		Name="VCCustomBuildTool"
	/>
	<Tool
		Name="VCXMLDataGeneratorTool"
	/>
	<Tool
		Name="VCWebServiceProxyGeneratorTool"
	/>
	<Tool
		Name="VCMIDLTool"
	/>
	<Tool
		Name="VCCLCompilerTool"
		Optimization="4"
		AdditionalIncludeDirectories=".\include;${configuration.compiler.includes}"
		PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0500;POCO_STATIC;${configuration.compiler.defines}"
		StringPooling="true"
		BasicRuntimeChecks="3"
		RuntimeLibrary="3"
		BufferSecurityCheck="true"
		TreatWChar_tAsBuiltInType="true"
		ForceConformanceInForLoopScope="true"
		RuntimeTypeInfo="true"
		UsePrecompiledHeader="0"
		WarningLevel="3"
		Detect64BitPortabilityProblems="false"
		DebugInformationFormat="3"
		CompileAs="0"
		ProgramDatabaseFileName="$(OutDir)$(TargetName).pdb"
		LanguageStandard="${vc.project.compiler.std.cpp}"
		LanguageStandard_C="${vc.project.compiler.std.c}"
		DisableSpecificWarnings="${configuration.compiler.disableWarnings}"
		AdditionalOptions="${configuration.compiler.additionalOptions}"
	/>
	<Tool
		Name="VCManagedResourceCompilerTool"
	/>
	<Tool
		Name="VCResourceCompilerTool"
	/>
	<Tool
		Name="VCPreLinkEventTool"
	/>
	<Tool
		Name="VCLinkerTool"
		AdditionalDependencies="iphlpapi.lib winmm.lib ${configuration.linker.dependencies}"
		OutputFile="binA64\static_md\${project.target}d.exe"
		LinkIncremental="2"
		AdditionalLibraryDirectories="${project.pocobase}\libA64;${configuration.linker.libraries}"
		SuppressStartupBanner="true"
		GenerateDebugInformation="true"
		ProgramDatabaseFile="$(OutDir)$(TargetName).pdb"
		SubSystem="1"
		TargetMachine="18"
		AdditionalOptions="${configuration.linker.additionalOptions}"
	/>
	<Tool
		Name="VCALinkTool"
	/>
	<Tool
		Name="VCManifestTool"
	/>
	<Tool
		Name="VCXDCMakeTool"
	/>
	<Tool
		Name="VCBscMakeTool"
	/>
	<Tool
		Name="VCFxCopTool"
	/>
	<Tool
		Name="VCAppVerifierTool"
	/>
	<Tool
		Name="VCPostBuildEventTool"
	/>
</Configuration>
