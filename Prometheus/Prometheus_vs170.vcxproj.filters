<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Prometheus">
      <UniqueIdentifier>{ff74e667-a6cd-4465-9a3c-68fc03208b95}</UniqueIdentifier>
    </Filter>
    <Filter Include="Prometheus\Header Files">
      <UniqueIdentifier>{98c081a4-a8db-49f2-b258-c587cb6f4e9d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Prometheus\Source Files">
      <UniqueIdentifier>{58fa03ef-b9d3-4f4d-8c6b-1e499396f07a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Prometheus\AtomicFloat.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\CallbackMetric.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Collector.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Counter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Exporter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Gauge.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Histogram.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\IntCounter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\IntGauge.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\LabeledMetric.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\LabeledMetricImpl.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Metric.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\MetricsRequestHandler.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\MetricsServer.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\ProcessCollector.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Prometheus.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Registry.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\TextExporter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\ThreadPoolCollector.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Collector.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Counter.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Gauge.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Histogram.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IntCounter.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IntGauge.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LabeledMetric.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MetricsRequestHandler.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MetricsServer.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessCollector.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Registry.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextExporter.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadPoolCollector.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>