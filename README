This is the README file for the POCO C++ Libraries.

In this document you will find a brief description of the directory layout,
as well as a description necessary steps to build the POCO C++ Libraries.

The Foundation library contains a platform abstraction layer (including classes
for multithreading, file system access, logging, etc.), as well as
a large number of useful utility classes, such various stream buffer and stream
classes, URI handling, and many more.
The XML library contains an XML parser with SAX2 and DOM interfaces,
as well as an XMLWriter.
The Util library contains classes for working with configuration files and
command line arguments, as well as various utility classes.
The Net library contains network classes (sockets, HTTP client/server, etc.).
All libraries come with a test suite and a number of sample programs.

The basic directory layout is as follows:

build/                     the build system for Unix and additional utility scripts
    config/                build configurations for various Unix platforms
    rules/                 common build rules for all platforms
    scripts/               build and utility scripts
    vxconfig/              VxWorks build configurations

cmake/                     Support files for CMake

bin/                       all executables (dynamic link libraries on Windows)
bin64/                     64-bit executables (and DLLs) on Windows

doc/                       additional documentation

lib/                       all libraries (import libraries on Windows)
lib64/                     64-bit libraries on Windows

CppUnit/                   project and make/build files for the CppUnit unit testing framework
    doc/                   additional documentation
    include/
        CppUnit/           header files for CppUnit
    src/                   source files for CppUnit
    WinTestRunner/         Windows GUI for CppUnit

Foundation/                project and make/build files for the Foundation library
    include/
        Poco/              header files for the Foundation library
    src/                   source files for the Foundation library
    testsuite/             project and make/build files for the Foundation testsuite
              src/         source files for the Foundation testsuite
              bin/         test suite executables
    samples/               sample applications for the Foundation library

XML/                       project and make/build files for the XML library
    include/
        Poco/
            XML/           header files for the core XML library
            SAX/           header files for SAX support
            DOM/           header files for DOM support
    src/                   source files for the XML library
    testsuite/             project and make/build files for the XML testsuite
              src/         source files for the XML testsuite
              bin/         test suite executables
    samples/               sample applications for the XML library

Net/                       project and make/build files for the Net library
    include/
        Poco/
            Net/           header files for the Net library
    src/                   source files for the Net library
    testsuite/             project and make/build files for the Net testsuite
              src/         source files for the Net testsuite
              bin/         test suite executables
    samples/               sample applications for the Net library


Depending on what package you have downloaded, there may be other libraries
as well (such as Data, Crypto, NetSSL_OpenSSL and Zip).


DOCUMENTATION
=============

Plenty of documentation (tutorial slides, articles and SDK reference)
is available at <https://docs.pocoproject.org/>.


EXTERNAL DEPENDENCIES
=====================

The following libraries require third-party software (header files and
libraries) being installed to build properly:

- NetSSL_OpenSSL, Crypto and JWT require OpenSSL.
- Data/ODBC requires ODBC
  (Microsoft ODBC on Windows, unixODBC or iODBC on Unix/Linux)
- Data/MySQL requires the MySQL or MariaDB client library.
- Data/PostgreSQL requires the PostgreSQL client library (libpq).

Most Unix/Linux systems already have OpenSSL preinstalled. If your system
does not have OpenSSL, please get it from https://www.openssl.org or
another source. You do not have to build OpenSSL yourself - a binary
distribution is fine (e.g., apt-get install openssl libssl-dev).
On macOS, install OpenSSL via Homebrew (brew install openssl).
On Windows, OpenSSL can be installed with vcpkg.

The easiest way to install OpenSSL on Windows is to get the pre-built
libraries from the pocoproject/openssl Git repository at
<https://github.com/pocoproject/openssl>. This repository is included
as a submodule in the poco GitHub repository, but not in the release
source code packages. You can also provide your own OpenSSL build by
editing the Visual Studio project files.

Depending on where you have installed the OpenSSL libraries,
you might have to edit the build script (buildwin.cmd), or add the
necessary paths to the INCLUDE and LIB environment variables.

The Data library requires ODBC support on your system if you want
to build the ODBC connector (which is the default). On Windows
platforms, ODBC should be readily available if you have the
Windows SDK. On Unix/Linux platforms, you can use iODBC
(preinstalled on macOS X) or unixODBC. For the MySQL connector,
the MySQL client libraries and header files are required.

The Data/ODBC and Data/MySQL Makefiles will search for the ODBC
and MySQL headers and libraries in various places. Nevertheless,
the Makefiles may not be able to find the headers and libraries.
In this case, please edit the Makefile in Data/ODBC and/or Data/MySQL
accordingly.


BUILDING WITH CMAKE
===================

The POCO C++ Libraries support CMake as a build system on Linux, macOS and Windows.
To build the POCO C++ Libraries with CMake:

1. create a cmake-build directory (e.g. in the POCO root directory):

$ mkdir cmake-build

2. and run CMake from there:

$ cd cmake-build
$ cmake ..
$ make -s -j (or build the generated Visual Studio solution on Windows)

If you cannot or do not want to build with CMake, there are other options,
described in the following.


BUILDING - USING VCPKG
======================

You can download and install poco using the vcpkg (https://vcpkg.io)
dependency manager:

$ git clone https://github.com/Microsoft/vcpkg.git
$ cd vcpkg
$ ./bootstrap-vcpkg.sh
$ ./vcpkg integrate install
$ ./vcpkg install poco

The Poco port in vcpkg is kept up to date by Microsoft team members and community contributors.
If the version is out of date, please create an issue or pull request(https://github.com/Microsoft/vcpkg)
on the vcpkg repository.


BUILDING - USING CONAN
======================

You can download and install poco using the Conan(https://github.com/conan-io/conan)
package manager. It needed to be installed first(https://conan.io/downloads.html):

You can install Poco libraries from Conan Center(https://conan.io/center.html):

$ conan install -r conancenter poco/1.13.3@

Or, you can download Poco recipe and build locally:

$ conan install -r conancenter poco/1.13.3@ --build=poco

The Poco recipe and packages in Conan Center are kept up to date by Conan team members and community contributors.
If the version is out of date, or you detect any wrong behavior, please create an issue or pull request(https://github.com/conan-io/conan-center-index)
on the Conan Center Index repository.


BUILDING ON WINDOWS
===================

Microsoft Visual Studio 2019 or newer is required to build the POCO C++ Libraries on
Windows platforms. Solution and project files for Visual Studio 2019 to 2022 are included.
64-bit (x64) builds are supported as well.
You can either build from within Visual Studio (Build->Batch Build->Select All;Rebuild)
or from the command line. To build from the command line, start the
Developer PowerShell for Visual Studio and cd to the directory where you
have extracted the POCO C++ Libraries sources. Then, run the buildwin.ps1 script
and pass the desired options.

To show available options, run:

> buildwin.ps1 -help

Example:

> buildwin.ps1 -vs 170 -action build -linkmode shared -config release -platform x64 -samples -tests

Certain libraries, like NetSSL_OpenSSL, Crypto or Data/MySQL have dependencies
to other libraries. The Visual Studio project files have been configured to
use vcpkg to install the required packages.

In order to run the test suite and the samples, the top-most bin directory containing
the shared libraries must be in the PATH environment variable.

IMPORTANT NOTE: Please make sure that the path to the directory containing the
POCO C++ Libraries source tree does not contain spaces. Otherwise, the Microsoft
message compiler may fail when building the Foundation library.


BUILDING ON UNIX/LINUX/macOS
============================

For building on Unix platforms, the POCO C++ Libraries come with their own
build system. The build system is based on GNU Make 5.0 (or newer), with the help
from a few shell scripts. If you do not have GNU Make 5.0 (or later) installed on
your machine, you will need to download it from
http://directory.fsf.org/devel/build/make.html>,
build and install it prior to building the POCO C++ Libraries.

You can check the version of GNU Make installed on your system with

> make --version

or

> gmake --version

Once you have GNU Make up and running, the rest is quite simple.
To extract the sources and build all libraries, testsuites and samples, simply

> gunzip poco-X.Y.tar.gz
> tar -xf poco-X.Y.tar
> cd poco-X.Y
> ./configure
> make -s

See the configure script source for a list of possible options.
For starters, we recommend --no-tests and --no-samples, to reduce build times.
On a multicore or multiprocessor machine, use parallel makes to speed up
the build (make -j4).

Once you have successfully built POCO, you can install it
to /usr/local (or another directory specified as parameter
to configure --prefix=<path>):

> sudo make -s install

You can omit certain components from the build. For example, you might
want to omit Data/ODBC or Data/MySQL if you do not have the corresponding
third-party libraries (iodbc or unixodbc, mysqlclient) installed
on your system. To do this, use the --omit argument to configure:

> ./configure --omit=Data/ODBC,Data/MySQL


IMPORTANT: Make sure that the path to the build directory does not
contain symbolic links. Furthermore, on macOS (or other systems
with case insensitive filesystems), make sure that the characters in
the path have the correct case. Otherwise you'll get an error saying
"Current working directory not under $PROJECT_BASE.".


BUILDING ON QNX NEUTRINO
========================

For QNX Neutrino, the Unix build system (see the instructions above) is used.
You can use the build system to cross-compile for a target platform on a Solaris or
Linux host. Unfortunately, the Cygwin-based Windows host environment has some major
quirks that prevent the build system from working there. You can also use the
build system on a self-hosted QNX system. The default build configuration for QNX
(found in build/config/QNX) is for a self-hosted x86 platform. To specify another
target, edit the CCVER setting in the build configuration file. For example, to
compile for a PowerPC target, specify CCVER=3.3.1,gcc_ntoppcbe.

Service Pack 1 for QNX Neutrino 6.3 must be installed, otherwise compiling the
Foundation library will fail due to a problem with the <list> header in the
default (Dinkumware) C++ standard library.

When building on QNX, you might want to disable NetSSL_OpenSSL, Crypto and
some Data connectors, unless you have the necessary third party components
available:

> ./configure --omit=NetSSL_OpenSSL,Crypto,Data/ODBC,Data/MySQL


BUILDING FOR VXWORKS
====================

Please see the VxWorks Platform Notes in the Reference Documentation for
more information. The Reference Documentation can be found online
at <http://pocoproject.org/docs/>.


MORE INFORMATION
================

For more information, see the POCO C++ Libraries website
at <https://pocoproject.org>.
