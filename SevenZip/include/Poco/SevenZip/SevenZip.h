//
// SevenZip.h
//
// Library: SevenZip
// Package: SevenZip
// Module:  SevenZip
//
// Basic definitions for the Poco SevenZip library.
// This file must be the first file included by every other SevenZip
// header file.
//
// Copyright (c) 2014, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#ifndef SevenZip_SevenZip_INCLUDED
#define SevenZip_SevenZip_INCLUDED


#include "Poco/Foundation.h"


//
// The following block is the standard way of creating macros which make exporting
// from a DLL simpler. All files within this DLL are compiled with the SevenZip_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see
// SevenZip_API functions as being imported from a DLL, wheras this DLL sees symbols
// defined with this macro as being exported.
//
#if defined(_WIN32) && defined(POCO_DLL)
	#if defined(SevenZip_EXPORTS)
		#define SevenZip_API __declspec(dllexport)
	#else
		#define SevenZip_API __declspec(dllimport)
	#endif
#endif


#if !defined(SevenZip_API)
	#if !defined(POCO_NO_GCC_API_ATTRIBUTE) && defined (__GNUC__) && (__GNUC__ >= 4)
		#define SevenZip_API __attribute__ ((visibility ("default")))
	#else
		#define SevenZip_API
	#endif
#endif


//
// Automatically link SevenZip library.
//
#if defined(_MSC_VER)
	#if !defined(POCO_NO_AUTOMATIC_LIBS) && !defined(SevenZip_EXPORTS)
		#pragma comment(lib, "PocoSevenZip" POCO_LIB_SUFFIX)
	#endif
#endif


#endif // SevenZip_SevenZip_INCLUDED
