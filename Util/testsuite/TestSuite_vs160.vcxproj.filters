<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Application">
      <UniqueIdentifier>{438eb1c0-d0de-45bf-b320-1279d707bc5c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Application\Header Files">
      <UniqueIdentifier>{1e1fbbc0-ff47-46d6-809d-12102bbb3b48}</UniqueIdentifier>
    </Filter>
    <Filter Include="Application\Source Files">
      <UniqueIdentifier>{128ee867-d2b6-401a-baad-bb4996a3bee3}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{704c0097-c5c6-4dab-a7ab-6a6bc2d0e5a9}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{eab30792-d142-449a-8412-a94157f445b9}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{ca6fdee4-a24b-43f4-b365-ebf3e3343506}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{722d5c8a-f8cc-4331-845b-3c1a173495ef}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{36809934-98df-4ca7-a7f0-8495389c1253}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration">
      <UniqueIdentifier>{0902ec24-350a-4eff-8fb5-6239c3ab7c5b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration\Header Files">
      <UniqueIdentifier>{611a2a23-0c30-4466-a6dc-d2591c712637}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration\Source Files">
      <UniqueIdentifier>{9b521e75-593a-4ced-8bc1-86c4885ff97a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options">
      <UniqueIdentifier>{b7a588e6-8654-41c4-baf6-2ad326c9891a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options\Header Files">
      <UniqueIdentifier>{f6508bad-0191-4241-a39a-4c0e8a70bdca}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options\Source Files">
      <UniqueIdentifier>{33161bbd-6977-4320-bd29-257b9f0279bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows">
      <UniqueIdentifier>{6c152c8b-70c7-495e-bcf1-72d01d2a88bd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows\Header Files">
      <UniqueIdentifier>{6b010bdb-b859-48f9-990b-5e4b0c0b13ad}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows\Source Files">
      <UniqueIdentifier>{5093e4bf-589e-408e-b4d6-dd1a234e69dd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer">
      <UniqueIdentifier>{53421e09-8092-4c21-b187-57561f6ceea8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer\Header Files">
      <UniqueIdentifier>{0ad6aa7e-a1e4-4db9-b2c7-6e84a5b9305c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer\Source Files">
      <UniqueIdentifier>{436fbd2a-badc-4b37-b007-b3d666ea3981}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\UtilTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\AbstractConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ConfigurationMapperTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ConfigurationTestSuite.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ConfigurationViewTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FilesystemConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\IniFileConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\JSONConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LayeredConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LocalConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LoggingConfiguratorTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MapConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PropertyFileConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SystemConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\XMLConfigurationTest.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HelpFormatterTest.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OptionProcessorTest.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OptionSetTest.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OptionsTestSuite.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OptionTest.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ValidatorTest.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WinConfigurationTest.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WindowsTestSuite.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WinRegistryTest.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WinServiceTest.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimerTest.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimerTestSuite.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\UtilTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AbstractConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationMapperTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationTestSuite.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationViewTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FilesystemConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IniFileConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\JSONConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LayeredConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LocalConfigurationViewTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingConfiguratorTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MapConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PropertyFileConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SystemConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLConfigurationTest.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HelpFormatterTest.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionProcessorTest.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionSetTest.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionsTestSuite.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionTest.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ValidatorTest.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinConfigurationTest.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WindowsTestSuite.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinRegistryTest.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinServiceTest.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimerTest.cpp">
      <Filter>Timer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimerTestSuite.cpp">
      <Filter>Timer\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>