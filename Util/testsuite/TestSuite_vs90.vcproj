<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	Name="TestSuite"
	Version="9.00"
	ProjectType="Visual C++"
	ProjectGUID="{E40E738C-447B-40F4-A878-EBA9A2459270}"
	RootNamespace="TestSuite"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<ToolFiles/>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include;..\..\XML\include;..\..\JSON\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\TestSuited.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include;..\..\XML\include;..\..\JSON\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include;..\..\XML\include;..\..\JSON\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_mt\TestSuited.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\lib"
				IgnoreDefaultLibraryNames="nafxcwd.lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_mt\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include;..\..\XML\include;..\..\JSON\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_mt\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				IgnoreDefaultLibraryNames="nafxcw.lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include;..\..\XML\include;..\..\JSON\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_md\TestSuited.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_md\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include;..\..\XML\include;..\..\JSON\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_md\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
	</Configurations>
	<References/>
	<Files>
		<Filter
			Name="Application">
			<Filter
				Name="Header Files"/>
			<Filter
				Name="Source Files"/>
		</Filter>
		<Filter
			Name="_Suite">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\UtilTestSuite.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\UtilTestSuite.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="_Driver">
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\Driver.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="Configuration">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\AbstractConfigurationTest.h"/>
				<File
					RelativePath=".\src\ConfigurationMapperTest.h"/>
				<File
					RelativePath=".\src\ConfigurationTestSuite.h"/>
				<File
					RelativePath=".\src\ConfigurationViewTest.h"/>
				<File
					RelativePath=".\src\FilesystemConfigurationTest.h"/>
				<File
					RelativePath=".\src\IniFileConfigurationTest.h"/>
				<File
					RelativePath=".\src\JSONConfigurationTest.h"/>
				<File
					RelativePath=".\src\LayeredConfigurationTest.h"/>
				<File
					RelativePath=".\src\LocalConfigurationTest.h"/>
				<File
					RelativePath=".\src\LoggingConfiguratorTest.h"/>
				<File
					RelativePath=".\src\MapConfigurationTest.h"/>
				<File
					RelativePath=".\src\PropertyFileConfigurationTest.h"/>
				<File
					RelativePath=".\src\SystemConfigurationTest.h"/>
				<File
					RelativePath=".\src\XMLConfigurationTest.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\AbstractConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\ConfigurationMapperTest.cpp"/>
				<File
					RelativePath=".\src\ConfigurationTestSuite.cpp"/>
				<File
					RelativePath=".\src\ConfigurationViewTest.cpp"/>
				<File
					RelativePath=".\src\FilesystemConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\IniFileConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\JSONConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\LayeredConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\LocalConfigurationViewTest.cpp"/>
				<File
					RelativePath=".\src\LoggingConfiguratorTest.cpp"/>
				<File
					RelativePath=".\src\MapConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\PropertyFileConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\SystemConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\XMLConfigurationTest.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="Options">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\HelpFormatterTest.h"/>
				<File
					RelativePath=".\src\OptionProcessorTest.h"/>
				<File
					RelativePath=".\src\OptionSetTest.h"/>
				<File
					RelativePath=".\src\OptionsTestSuite.h"/>
				<File
					RelativePath=".\src\OptionTest.h"/>
				<File
					RelativePath=".\src\ValidatorTest.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\HelpFormatterTest.cpp"/>
				<File
					RelativePath=".\src\OptionProcessorTest.cpp"/>
				<File
					RelativePath=".\src\OptionSetTest.cpp"/>
				<File
					RelativePath=".\src\OptionsTestSuite.cpp"/>
				<File
					RelativePath=".\src\OptionTest.cpp"/>
				<File
					RelativePath=".\src\ValidatorTest.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="Windows">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\WinConfigurationTest.h"/>
				<File
					RelativePath=".\src\WindowsTestSuite.h"/>
				<File
					RelativePath=".\src\WinRegistryTest.h"/>
				<File
					RelativePath=".\src\WinServiceTest.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\WinConfigurationTest.cpp"/>
				<File
					RelativePath=".\src\WindowsTestSuite.cpp"/>
				<File
					RelativePath=".\src\WinRegistryTest.cpp"/>
				<File
					RelativePath=".\src\WinServiceTest.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="Timer">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\TimerTest.h"/>
				<File
					RelativePath=".\src\TimerTestSuite.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\TimerTest.cpp"/>
				<File
					RelativePath=".\src\TimerTestSuite.cpp"/>
			</Filter>
		</Filter>
	</Files>
	<Globals/>
</VisualStudioProject>
