<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	Name="TestSuite"
	Version="9.00"
	ProjectType="Visual C++"
	ProjectGUID="{C9ACF028-17A1-44C2-8C09-DFD3BD9E7D45}"
	RootNamespace="TestSuite"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<ToolFiles/>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\TestSuited.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_mt\TestSuited.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\lib"
				IgnoreDefaultLibraryNames="nafxcwd.lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_mt\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_mt\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				IgnoreDefaultLibraryNames="nafxcw.lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_md\TestSuited.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_md\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies=""
				OutputFile="bin\static_md\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
	</Configurations>
	<References/>
	<Files>
		<Filter
			Name="XML">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\NamePoolTest.h"/>
				<File
					RelativePath=".\src\NameTest.h"/>
				<File
					RelativePath=".\src\XMLStreamParserTest.h"/>
				<File
					RelativePath=".\src\XMLWriterTest.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\NamePoolTest.cpp"/>
				<File
					RelativePath=".\src\NameTest.cpp"/>
				<File
					RelativePath=".\src\XMLStreamParserTest.cpp"/>
				<File
					RelativePath=".\src\XMLWriterTest.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="SAX">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\AttributesImplTest.h"/>
				<File
					RelativePath=".\src\NamespaceSupportTest.h"/>
				<File
					RelativePath=".\src\SAXParserTest.h"/>
				<File
					RelativePath=".\src\SAXTestSuite.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\AttributesImplTest.cpp"/>
				<File
					RelativePath=".\src\NamespaceSupportTest.cpp"/>
				<File
					RelativePath=".\src\SAXParserTest.cpp"/>
				<File
					RelativePath=".\src\SAXTestSuite.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="DOM">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\ChildNodesTest.h"/>
				<File
					RelativePath=".\src\DocumentTest.h"/>
				<File
					RelativePath=".\src\DocumentTypeTest.h"/>
				<File
					RelativePath=".\src\DOMTestSuite.h"/>
				<File
					RelativePath=".\src\ElementTest.h"/>
				<File
					RelativePath=".\src\EventTest.h"/>
				<File
					RelativePath=".\src\NodeAppenderTest.h"/>
				<File
					RelativePath=".\src\NodeIteratorTest.h"/>
				<File
					RelativePath=".\src\NodeTest.h"/>
				<File
					RelativePath=".\src\ParserWriterTest.h"/>
				<File
					RelativePath=".\src\TextTest.h"/>
				<File
					RelativePath=".\src\TreeWalkerTest.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\ChildNodesTest.cpp"/>
				<File
					RelativePath=".\src\DocumentTest.cpp"/>
				<File
					RelativePath=".\src\DocumentTypeTest.cpp"/>
				<File
					RelativePath=".\src\DOMTestSuite.cpp"/>
				<File
					RelativePath=".\src\ElementTest.cpp"/>
				<File
					RelativePath=".\src\EventTest.cpp"/>
				<File
					RelativePath=".\src\NodeAppenderTest.cpp"/>
				<File
					RelativePath=".\src\NodeIteratorTest.cpp"/>
				<File
					RelativePath=".\src\NodeTest.cpp"/>
				<File
					RelativePath=".\src\ParserWriterTest.cpp"/>
				<File
					RelativePath=".\src\TextTest.cpp"/>
				<File
					RelativePath=".\src\TreeWalkerTest.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="_Suite">
			<Filter
				Name="Header Files">
				<File
					RelativePath=".\src\XMLTestSuite.h"/>
			</Filter>
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\XMLTestSuite.cpp"/>
			</Filter>
		</Filter>
		<Filter
			Name="_Driver">
			<Filter
				Name="Source Files">
				<File
					RelativePath=".\src\Driver.cpp"/>
			</Filter>
		</Filter>
	</Files>
	<Globals/>
</VisualStudioProject>
