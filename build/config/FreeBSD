#
# FreeBSD
#
# Make settings for FreeBSD/clang
#

#
# General Settings
#
LINKMODE ?= SHARED

# arm sanitizers available in >=13.3 and >=14.1
SANITIZEFLAGS ?=
# sanitize flags:
#  -fsanitize=address
#  -fsanitize=undefined
#  -fsanitize=thread

#
# Define Tools
#
CC      = clang
CXX     = clang++
LINK    = $(CXX)
LIB     = ar -cr
RANLIB  = ranlib
SHLIB   = $(CXX) -shared -Wl,-soname,$(notdir $@) -o $@
SHLIBLN = $(POCO_BASE)/build/script/shlibln
DEP     = $(POCO_BASE)/build/script/makedepend.gcc
STRIP   = strip
SHELL   = sh
RM      = rm -rf
CP      = cp
MKDIR   = mkdir -p

#
# Extension for Shared Libraries
#
SHAREDLIBEXT     = .so.$(target_version)
SHAREDLIBLINKEXT = .so

#
# Compiler and Linker Flags
#
CFLAGS          = $(SANITIZEFLAGS) -std=c11
CFLAGS32        =
CFLAGS64        =
CXXFLAGS        = $(SANITIZEFLAGS) -std=c++17 -Wall
CXXFLAGS32      =
CXXFLAGS64      =
LINKFLAGS       = $(SANITIZEFLAGS)
LINKFLAGS32     =
LINKFLAGS64     =
STATICOPT_CC    =
STATICOPT_CXX   =
STATICOPT_LINK  = -static
SHAREDOPT_CC    = -fPIC
SHAREDOPT_CXX   = -fPIC
SHAREDOPT_LINK  = -Wl,-rpath,$(LIBPATH)
DEBUGOPT_CC     = -g -D_DEBUG
DEBUGOPT_CXX    = -g -D_DEBUG
DEBUGOPT_LINK   = -g
RELEASEOPT_CC   = -O2 -DNDEBUG
RELEASEOPT_CXX  = -O2 -DNDEBUG
RELEASEOPT_LINK = -O2

#
# System Specific Flags
#
SYSFLAGS = -D_REENTRANT -D_THREAD_SAFE

#
# System Specific Libraries
#
SYSLIBS  = -lpthread
