#
# compile
#
# Compile rule definitions for makefiles
#

#
# Target-specific include paths
#
INCLUDE += $(foreach l,$(target_includes),-I$(l))

#
# Targets
#
.PHONY: all all_static all_shared all_debug all_release \
        clean static_debug static_release shared_debug shared_release

all: $(DEFAULT_TARGET)
all_static: static_debug static_release
all_shared: shared_debug shared_release
all_debug: static_debug shared_debug
all_release: static_release shared_release

#
# Create directories if necessary
#
.PHONY: objdirs libdirs bindirs static_bindirs
objdirs: $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_SHARED) $(OBJPATH_DEBUG_SHARED)
libdirs: objdirs $(LIBPATH)
bindirs: objdirs $(BINPATH)
static_bindirs: objdirs $(BINPATH)/static

$(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_SHARED) $(OBJPATH_DEBUG_SHARED) $(LIBPATH) $(BINPATH) $(BINPATH)/static:
	$(MKDIR) $@

#
# Rules for compiling
#

# Regular sources

$(OBJPATH_DEBUG_STATIC)/%.o: $(SRCDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(DEBUGOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(SRCDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(RELEASEOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_STATIC)/%.o: $(SRCDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(DEBUGOPT_CC) $(STATICOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(SRCDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(RELEASEOPT_CC) $(STATICOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(SRCDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(DEBUGOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(SRCDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(RELEASEOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(SRCDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(DEBUGOPT_CC) $(SHAREDOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(SRCDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(RELEASEOPT_CC) $(SHAREDOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

# Generated sources

$(OBJPATH_DEBUG_STATIC)/%.o: $(GENDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(DEBUGOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(GENDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(RELEASEOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_STATIC)/%.o: $(GENDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(DEBUGOPT_CC) $(STATICOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(GENDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(RELEASEOPT_CC) $(STATICOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(GENDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(DEBUGOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(GENDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS) $(RELEASEOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(GENDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(DEBUGOPT_CC) $(SHAREDOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(GENDIR)/%.c $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CC) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS) $(RELEASEOPT_CC) $(SHAREDOPT_CC) -c $< -o $@ $(POCO_BUILD_STDERR)

ifndef POCO_DATA_NO_SQL_PARSER

# SQL parser sources

SQLDIR       = SQLParser/src
SQLPARSERDIR = $(SQLDIR)/parser
SQLSQLDIR    = $(SQLDIR)/sql
SQLUTILDIR   = $(SQLDIR)/util

# SQLParser
$(OBJPATH_DEBUG_STATIC)/%.o: $(SQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(SQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(SQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(SQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	@echo "** Compiling" $@ "(release, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

# parser
$(OBJPATH_DEBUG_STATIC)/%.o: $(SQLPARSERDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(SQLPARSERDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(SQLPARSERDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(SQLPARSERDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

# sql
$(OBJPATH_DEBUG_STATIC)/%.o: $(SQLSQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(SQLSQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(SQLSQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(SQLSQLDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

# util
$(OBJPATH_DEBUG_STATIC)/%.o: $(SQLUTILDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_STATIC)/%.o: $(SQLUTILDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, static)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(STATICOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_DEBUG_SHARED)/%.o: $(SQLUTILDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(debug, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(DEBUGOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

$(OBJPATH_RELEASE_SHARED)/%.o: $(SQLUTILDIR)/%.cpp $(DEPPATH)/%.d $(POCO_BASE)/build/config/$(POCO_CONFIG)
	@echo "** Compiling" $< "(release, shared)"
	$(CXX) $(INCLUDE) $(CXXFLAGS) $(RELEASEOPT_CXX) $(SHAREDOPT_CXX) -c $< -o $@ $(POCO_BUILD_STDERR)

endif # !POCO_DATA_NO_SQL_PARSER

#
# Rules for creating dependency information
#

# Regular sources rules

$(DEPPATH)/%.d: $(SRCDIR)/%.cpp
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(SRCDIR)/$(patsubst %.d,%.cpp,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS)
endif
endif

$(DEPPATH)/%.d: $(SRCDIR)/%.c
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(SRCDIR)/$(patsubst %.d,%.c,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS)
endif
endif

# Generated sources rules

$(DEPPATH)/%.d: $(GENDIR)/%.cpp
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(GENDIR)/$(patsubst %.d,%.cpp,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CXXFLAGS) $(EXTRA_CXXFLAGS)
endif
endif

$(DEPPATH)/%.d: $(GENDIR)/%.c
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(GENDIR)/$(patsubst %.d,%.c,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CFLAGS) $(EXTRA_CFLAGS)
endif
endif

ifndef POCO_DATA_NO_SQL_PARSER

# Data SQL parser sources rules

$(DEPPATH)/%.d: $(SQLDIR)/%.cpp
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(SQLDIR)/$(patsubst %.d,%.cpp,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CXXFLAGS);
endif
endif

$(DEPPATH)/%.d: $(SQLDIR)/parser/%.cpp
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(SQLDIR)/parser/$(patsubst %.d,%.cpp,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CXXFLAGS)
endif
endif

$(DEPPATH)/%.d: $(SQLDIR)/sql/%.cpp
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(SQLDIR)/sql/$(patsubst %.d,%.cpp,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CXXFLAGS)
endif
endif

$(DEPPATH)/%.d: $(SQLDIR)/util/%.cpp
ifneq ($(MAKECMDGOALS),clean)
ifneq ($(MAKECMDGOALS),distclean)
	@echo "** Creating dependency info for" $^
	$(MKDIR) $(DEPPATH)
	$(DEP) $(SQLDIR)/util/$(patsubst %.d,%.cpp,$(notdir $@)) $@ $(OBJPATH_DEBUG_STATIC) $(OBJPATH_RELEASE_STATIC) $(OBJPATH_DEBUG_SHARED) $(OBJPATH_RELEASE_SHARED) $(INCLUDE) $(CXXFLAGS)
endif
endif

endif # !POCO_DATA_NO_SQL_PARSER

depend: $(addprefix $(DEPPATH)/,$(addsuffix .d,$(objects)))
