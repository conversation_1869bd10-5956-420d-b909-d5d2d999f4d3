POCO C++ Libraries Windows Platform Notes
AAAIntroduction

!!!Introduction

The Windows versions of the POCO C++ Libraries are built with Visual Studio.
Basic support for building with MinGW is there as well, but at this time
is not officially supported and may or may not work.

Starting with release 1.10.0 the POCO C++ Libraries support new
build configurations in the solution files for Visual Studio 2015 and newer,
for building Win32 (32-bit) and x64 (64-bit) variants.

The following build configurations are available:
  * debug_shared, release_shared: dynamic link libraries (DLL) and dynamically linked executables, with DLL C/C++ runtime libraries.
  * debug_static_md, release_static_md: static libraries and statically linked executables with DLL C/C++ runtime libraries.
  * debug_static_mt, release_static_mt: static libraries and statically linked executables with static runtime libraries.


!!!Libraries And Linking Considerations

!!Automatic Linking of POCO C++ Libraries

The POCO C++ Libraries header files contain <[#pragma comment]> directives that enable
automatic linking of the required POCO libraries to an application. For this to work,
a few rules must be kept in mind.

  * The default is to link the POCO C++ Libraries dynamically (DLL).
  * To link statically, the code using the POCO C++ Libraries must be compiled
    with the preprocessor symbol <[POCO_STATIC]> defined.


!!Library Naming Conventions

The following naming conventions are used:

  * DLL import libraries are named PocoLIB.lib for the release build
    and PocoLIBd.lib for the debug build.
  * Static libraries built using the static multithreaded C/C++ runtime libraries
    are named PocoLIBmt.lib (release) and PocoLIBmtd.lib (debug).
  * Static libraries built using the DLL C/C++ runtime libraries are
    named PocoLIBmd.lib (release) and PocoLIBmdd.lib (debug).

32-bit libraries are placed in the <*lib*> directory. 64-bit libraries
are placed in the <*lib64*> directory. DLLs are placed in <*bin*> (32-bit)
or <*bin64*> (64-bit). 64-bit DLLs are named PocoLIB64.dll for release
and PocoLIB64d.dll for debug, respectively.


!!MSVC Compiler Notes

!!!C++ standard version not properly defined

In order to work around that, `/Zc:__cplusplus` compiler flag is necesary.

See the following issues for details and explanations:

  * https://github.com/pocoproject/poco/issues/3665
  * https://devblogs.microsoft.com/cppblog/msvc-now-correctly-reports-__cplusplus/

