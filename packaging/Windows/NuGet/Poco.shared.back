     </dependencies>
  </metadata>
  <files>
    <file src='..\..\poco.png' 					target='image\poco.png' />
	<file src='..\..\..\bin\Poco$id$.*' 		target='build\native\bin\' />
	<file src='..\..\..\bin\Poco$id$d.*' 		target='build\native\bin\' />
    <file src='..\..\..\lib\Poco$id$.*' 		target='build\native\lib\'   exclude= '..\..\..\lib\Poco$id$md*.*;..\..\..\lib\Poco$id$mt*.*' />
    <file src='..\..\..\lib\Poco$id$d.*' 		target='build\native\lib\'   exclude= '..\..\..\lib\Poco$id$md*.*;..\..\..\lib\Poco$id$mt*.*' />
    <file src='..\..\..\bin64\Poco$id$64.*' 	target='build\native\bin64\' />
    <file src='..\..\..\bin64\Poco$id$64d.*' 	target='build\native\bin64\' />
    <file src='..\..\..\lib64\Poco$id$.*' 		target='build\native\lib64\' exclude= '..\..\..\lib64\Poco$id$md*.*;..\..\..\lib64\Poco$id$mt*.*' />
    <file src='..\..\..\lib64\Poco$id$d.*' 		target='build\native\lib64\' exclude= '..\..\..\lib64\Poco$id$md*.*;..\..\..\lib64\Poco$id$mt*.*' />
    <file src='Poco.shared.targets' 			target='build\native\Poco.$id$.vs$vs$.shared.targets' />
   </files>
</package>
