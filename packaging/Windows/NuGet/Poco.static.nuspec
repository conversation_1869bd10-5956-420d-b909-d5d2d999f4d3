<?xml version='1.0' encoding='utf-8'?>
<package xmlns='http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd'>
  <metadata>
    <id>Poco.$id$.vs$vs$.$mode$</id>
    <version>$version$</version>
    <title>Pocoproject Poco Foundation vs$vs$ $mode$</title>
    <authors>Applied Informatics &amp; Contributors</authors>
    <owners>Guenter Obiltschnig &amp; Aleksandar Fabijanic</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type='expression'>BSL-1.0</license>
    <icon>image\poco.png</icon>
    <projectUrl>https://pocoproject.org/</projectUrl>
    <description>Modern, powerful open source C++ class libraries for building network- and internet-based applications that run on desktop, server, mobile and embedded systems.</description>
    <releaseNotes>releaseNotes</releaseNotes>
    <copyright>Copyright 2006-2020</copyright>
    <language></language>
    <tags>native string filesystem thread date log event regex uri uuid cache nativepackage sockets mime http ftp mail pop3 smtp html sax sax2 dom xml</tags>
    <repository type='git' url='**************:pocoproject/poco.git' branch='poco-$version$' />
    <dependencies>
      <dependency id='Poco.$id$.vs$vs$.noarch' version='$version$' />
<dependency id='Poco.Foundation.vs$vs$.static' version='$version$' />
<dependency id='Poco.Net.vs$vs$.static' version='$version$' />
﻿     </dependencies>
  </metadata>
  <files>
    <file src='..\..\poco.png' 					target='image\poco.png' />
	<file src='..\..\..\lib\Poco$id$MD*.*' 		target='build\native\lib\' />
 	<file src='..\..\..\lib\Poco$id$MT*.*' 		target='build\native\lib\' />
	<file src='..\..\..\lib64\Poco$id$MD*.*' 	target='build\native\lib64\' />
    <file src='..\..\..\lib64\Poco$id$MT*.*' 	target='build\native\lib64\' />
    <file src='Poco.static.targets' 			target='build\native\Poco.$id$.vs$vs$.static.targets' />
   </files>
</package>
