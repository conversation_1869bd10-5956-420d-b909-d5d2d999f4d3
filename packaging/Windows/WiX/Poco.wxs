<?xml version="1.0" encoding="utf-8"?>
<?define BuildFull = 'true' ?>
<?define BuildDir = '' ?>
<?define ProductName = 'Poco' ?>
<?define ProductVersion = '$(var.VERSION)' ?>
<?define ProductFullVersion = '$(var.VERSION)' ?>
<?define ProductAuthor = 'Applied Informatics Software Engineering GmbH and Contributors' ?>
<?define ProductAppFolder = 'InstallLocation' ?>
<?define VisualStudio = '$(var.VSYEAR)' ?>
<?define PlatformToolSetVersion = '$(var.Toolset)' ?>
<?if $(var.Platform) = x64 ?>
<?define ProductDisplayName = '$(var.ProductName) 64-bit' ?>
<!-- <?define ProductId = 'DAEF6430-C2C6-427F-BB76-B17908FC3162' ?> -->
<?define ProductId = '*' ?>
<?define ProductUpgradeCode = '9E3C69C0-915B-47A4-9970-30E030A04453' ?>
<?define Win64 = 'yes' ?>
<?define PlatformProgramFilesFolder = 'ProgramFiles64Folder' ?>
<?define WindowsSystemFolder = 'System64Folder' ?>
<?else ?>
<?define ProductDisplayName = '$(var.ProductName) 32-bit' ?>
<!-- <?define ProductId = 'BA83B7A6-8FB1-41FF-9EE9-EC7F46331372' ?>  -->
<?define ProductId = '*' ?>
<?define ProductUpgradeCode = '1B5020D6-46A8-4788-A7F9-FEF26CE08522' ?>
<?define Win64 = 'no' ?>
<?define PlatformProgramFilesFolder = 'ProgramFilesFolder' ?>
<?define WindowsSystemFolder = 'SystemFolder' ?>
<?endif ?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product Id="$(var.ProductId)" Name="$(var.ProductDisplayName) $(var.ProductVersion)" Language="1033" Version="$(var.ProductFullVersion)" Manufacturer="$(var.ProductAuthor)" UpgradeCode="$(var.ProductUpgradeCode)">
        <Package Description="Poco $(var.Platform) Installer for $(var.Platform)" Comments="Poco is a registered trademark of $(var.ProductAuthor)" InstallerVersion="300" Compressed="yes" Manufacturer="$(var.ProductAuthor)" Platform="$(var.Platform)" />
        <Media Id="1" Cabinet="simple.cab" EmbedCab="yes" />
        <MajorUpgrade AllowDowngrades="no" Schedule="afterInstallFinalize" MigrateFeatures="yes" DowngradeErrorMessage="A later or current version of [ProductName] is already installed. To install this version, uninstall the installed version first." />
        <!-- Property gets value from registry to set up installation folder for upgrades -->
        <Property Id="APPLICATIONFOLDER" Secure="yes">
            <RegistrySearch Id="AppFolderRegistrySearch" Type="raw" Root="HKLM" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[WIX_UPGRADE_DETECTED]" Name="$(var.ProductAppFolder)" Win64="$(var.Win64)" />
        </Property>
        <!-- Define destination folder (name of app folder only) -->
        <Property Id="ApplicationFolderName" Value="$(var.ProductName)" />
        <!-- Default to per-machine installation -->
        <Property Id="WixAppFolder" Value="WixPerMachineFolder" />
        <!--
	To disable per-user installation scope, uncomment this section
	<WixVariable Id='WixUISupportPerUser' Value='0' />
	-->
        <Property Id="ARPPRODUCTICON" Value="icon.ico" />
        <!--
		<Property Id="MSIINSTALLPERUSER" Value="1" />
        <Property Id="ALLUSERS" Value="2" />
	-->
        <WixVariable Id="WixUILicenseRtf" Value="../../Boost License.rtf" Overridable="no" />
        <WixVariable Id="WixUIDialogBmp" Value="Poco 312x312.jpg" />
        <!--
		Set default destination folder (full path) unless it comes from registry
		SetDirectory schedules the action prior to WixSetDefaultPerMachiGneFolder;
		code updated for manually scheduled elements to schedule between
		WixSetDefaultPerMachineFolder and WixSetPerMachineFolder.
		-->
        <SetDirectory Id="APPLICATIONFOLDER" Value="[$(var.PlatformProgramFilesFolder)][ApplicationFolderName]">APPLICATIONFOLDER=""</SetDirectory>
        <CustomAction Id="OverwriteWixSetDefaultPerMachineFolder" Property="WixPerMachineFolder" Value="[APPLICATIONFOLDER]" Execute="immediate" />
        <!-- Save destination folder in Add/Remove programs (ARP) registry key -->
        <SetProperty Id="ARPINSTALLLOCATION" Value="[APPLICATIONFOLDER]" After="CostFinalize" />
        <InstallUISequence>
            <!-- Corrects WixUI_Advanced bug (http://bit.ly/hrbM7Y) -->
            <Custom Action="OverwriteWixSetDefaultPerMachineFolder" After="WixSetDefaultPerMachineFolder" />
        </InstallUISequence>
        <InstallExecuteSequence>
            <!-- Corrects WixUI_Advanced bug (http://bit.ly/hrbM7Y) -->
            <Custom Action="OverwriteWixSetDefaultPerMachineFolder" After="WixSetDefaultPerMachineFolder" />
            <!-- Do not delete Windows service configuration on upgrade -->
            <DeleteServices>NOT UPGRADINGPRODUCTCODE</DeleteServices>
        </InstallExecuteSequence>
        <Directory Id="TARGETDIR" Name="SourceDir">
            <!-- Program files -->
            <Directory Id="$(var.PlatformProgramFilesFolder)">
                <Directory Id="APPLICATIONFOLDER" Name="$(var.ProductName)" />
            </Directory>
            <!-- Start menu (shortcuts) -->
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ShortcutsFolder" Name="$(var.ProductName)" />
            </Directory>
        </Directory>
        <DirectoryRef Id="APPLICATIONFOLDER">
            <Directory Id="Version" Name="$(var.ProductVersion)">
                <Directory Id="VisualStudio" Name="$(var.PlatformToolSetVersion)">
                    <Directory Id="bin" Name="bin">
                        <?if $(var.Platform) = x64 ?>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit64.shared.debug" DiskId="1" Guid="1139732C-64FC-4F3C-82F0-2442CBEB3E43">
                            <File Id="CPPUNIT64D.DLL" Name="PocoCppUnit64d.dll" Source="$(var.POCO)\bin64\PocoCppUnit64d.dll" />
                            <File Id="CPPUNIT64D.PDB" Name="PocoCppUnit64d.pdb" Source="$(var.POCO)\bin64\PocoCppUnit64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit64.shared.release" DiskId="1" Guid="C2C5F39F-91A5-43F2-8ACC-4FC1775BC100">
                            <File Id="CPPUNIT64.DLL" Name="PocoCppUnit64.dll" Source="$(var.POCO)\bin64\PocoCppUnit64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto64.shared.debug" DiskId="1" Guid="3E77B3AE-79A3-4E81-9C13-048680980662">
                            <File Id="POCOCRYPTO64D.DLL" Name="PocoCrypto64d.dll" Source="$(var.POCO)\bin64\PocoCrypto64d.dll" />
                            <File Id="POCOCRYPTO64D.PDB" Name="PocoCrypto64d.pdb" Source="$(var.POCO)\bin64\PocoCrypto64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto64.shared.release" DiskId="1" Guid="1CBBD14B-65FF-44FF-B64C-DAE9712CB62A">
                            <File Id="POCOCRYPTO64.DLL" Name="PocoCrypto64.dll" Source="$(var.POCO)\bin64\PocoCrypto64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data64.shared.debug" DiskId="1" Guid="60670FD1-68DA-4EE9-8835-213D977032F9">
                            <File Id="POCODATA64D.DLL" Name="PocoData64d.dll" Source="$(var.POCO)\bin64\PocoData64d.dll" />
                            <File Id="POCODATA64D.PDB" Name="PocoData64d.pdb" Source="$(var.POCO)\bin64\PocoData64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data64.shared.release" DiskId="1" Guid="3D7814BC-BB4F-4754-829A-43BAF460FE52">
                            <File Id="POCODATA64.DLL" Name="PocoData64.dll" Source="$(var.POCO)\bin64\PocoData64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL64.shared.debug" DiskId="1" Guid="8F9A5346-644B-41AC-8173-05CD89EDD77D">
                            <File Id="POCODATAMYSQL64D.DLL" Name="PocoDataMySQL64d.dll" Source="$(var.POCO)\bin64\PocoDataMySQL64d.dll" />
                            <File Id="POCODATAMYSQL64D.PDB" Name="PocoDataMySQL64d.pdb" Source="$(var.POCO)\bin64\PocoDataMySQL64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL64.shared.release" DiskId="1" Guid="0931B2A7-7453-4019-BE90-01DC5E9B6052">
                            <File Id="POCODATAMYSQL64.DLL" Name="PocoDataMySQL64.dll" Source="$(var.POCO)\bin64\PocoDataMySQL64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC64.shared.debug" DiskId="1" Guid="2E35760A-6CCE-4337-8C86-C54B87A39B3F">
                            <File Id="POCODATAODBC64D.DLL" Name="PocoDataODBC64d.dll" Source="$(var.POCO)\bin64\PocoDataODBC64d.dll" />
                            <File Id="POCODATAODBC64D.PDB" Name="PocoDataODBC64d.pdb" Source="$(var.POCO)\bin64\PocoDataODBC64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC64.shared.release" DiskId="1" Guid="614C09FC-AA05-4ADF-82D7-09CD4341B697">
                            <File Id="POCODATAODBC64.DLL" Name="PocoDataODBC64.dll" Source="$(var.POCO)\bin64\PocoDataODBC64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite64.shared.debug" DiskId="1" Guid="D2338CB6-264C-41F6-AA93-2506716BF79D">
                            <File Id="POCODATASQLITE64D.DLL" Name="PocoDataSQLite64d.dll" Source="$(var.POCO)\bin64\PocoDataSQLite64d.dll" />
                            <File Id="POCODATASQLITE64D.PDB" Name="PocoDataSQLite64d.pdb" Source="$(var.POCO)\bin64\PocoDataSQLite64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite64.shared.release" DiskId="1" Guid="065D2D21-8BEE-4DAF-9CC9-30CFB9E6152E">
                            <File Id="POCODATASQLITE64.DLL" Name="PocoDataSQLite64.dll" Source="$(var.POCO)\bin64\PocoDataSQLite64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL64.shared.debug" DiskId="1" Guid="390F5FF7-0CA9-4CA3-AF6A-98AF04371F5D">
                            <File Id="POCODataPostgreSQL64D.DLL" Name="PocoDataPostgreSQL64d.dll" Source="$(var.POCO)\bin64\PocoDataPostgreSQL64d.dll" />
                            <File Id="POCODataPostgreSQL64D.PDB" Name="PocoDataPostgreSQL64d.pdb" Source="$(var.POCO)\bin64\PocoDataPostgreSQL64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL64.shared.release" DiskId="1" Guid="B0C66A77-24BF-4711-A663-374177BB9C8F">
                            <File Id="POCODataPostgreSQL64.DLL" Name="PocoDataPostgreSQL64.dll" Source="$(var.POCO)\bin64\PocoDataPostgreSQL64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation64.shared.debug" DiskId="1" Guid="11130E01-30F8-4855-960D-2C9D133428B1">
                            <File Id="POCOFOUNDATION64D.DLL" Name="PocoFoundation64d.dll" Source="$(var.POCO)\bin64\PocoFoundation64d.dll" />
                            <File Id="POCOFOUNDATION64D.PDB" Name="PocoFoundation64d.pdb" Source="$(var.POCO)\bin64\PocoFoundation64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation64.shared.release" DiskId="1" Guid="A802DF2E-B117-4987-88AB-F2956AF7AEBD">
                            <File Id="POCOFOUNDATION64.DLL" Name="PocoFoundation64.dll" Source="$(var.POCO)\bin64\PocoFoundation64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON64.shared.debug" DiskId="1" Guid="62263FB7-4B37-4A6D-B428-5C6F156C72B3">
                            <File Id="POCOJSON64D.DLL" Name="PocoJSON64d.dll" Source="$(var.POCO)\bin64\PocoJSON64d.dll" />
                            <File Id="POCOJSON64D.PDB" Name="PocoJSON64d.pdb" Source="$(var.POCO)\bin64\PocoJSON64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON64.shared.release" DiskId="1" Guid="815DBE00-DB89-4088-8C89-268970EA1386">
                            <File Id="POCOJSON64.DLL" Name="PocoJSON64.dll" Source="$(var.POCO)\bin64\PocoJSON64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB64.shared.debug" DiskId="1" Guid="7F97A522-1BE3-4D3A-907B-D07DF4BD5A72">
                            <File Id="POCOMONGODB64D.DLL" Name="PocoMongoDB64d.dll" Source="$(var.POCO)\bin64\PocoMongoDB64d.dll" />
                            <File Id="POCOMONGODB64D.PDB" Name="PocoMongoDB64d.pdb" Source="$(var.POCO)\bin64\PocoMongoDB64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB64.shared.release" DiskId="1" Guid="226856C8-E89E-4BBE-AA66-0E92EEF7B8F2">
                            <File Id="POCOMONGODB64.DLL" Name="PocoMongoDB64.dll" Source="$(var.POCO)\bin64\PocoMongoDB64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net64.shared.debug" DiskId="1" Guid="C02341DC-830F-4514-B9B8-35E21195B4A0">
                            <File Id="POCONET64D.DLL" Name="PocoNet64d.dll" Source="$(var.POCO)\bin64\PocoNet64d.dll" />
                            <File Id="POCONET64D.PDB" Name="PocoNet64d.pdb" Source="$(var.POCO)\bin64\PocoNet64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net64.shared.release" DiskId="1" Guid="2A926103-001B-4BAE-B859-4D38EAF61497">
                            <File Id="POCONET64.DLL" Name="PocoNet64.dll" Source="$(var.POCO)\bin64\PocoNet64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL64.shared.debug" DiskId="1" Guid="EF45EAE3-2256-4F30-BA02-14E9FA7A64DE">
                            <File Id="POCONETSSL64D.DLL" Name="PocoNetSSL64d.dll" Source="$(var.POCO)\bin64\PocoNetSSL64d.dll" />
                            <File Id="POCONETSSL64D.PDB" Name="PocoNetSSL64d.pdb" Source="$(var.POCO)\bin64\PocoNetSSL64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL64.shared.release" DiskId="1" Guid="FDB676C5-8FDA-493C-A3DB-E428D261260C">
                            <File Id="POCONETSSL64.DLL" Name="PocoNetSSL64.dll" Source="$(var.POCO)\bin64\PocoNetSSL64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin64.shared.debug" DiskId="1" Guid="09278DB7-FF79-4469-9D90-791F2E2B759B">
                            <File Id="POCONETSSLWIN64D.DLL" Name="PocoNetSSLWin64d.dll" Source="$(var.POCO)\bin64\PocoNetSSLWin64d.dll" />
                            <File Id="POCONETSSLWIN64D.PDB" Name="PocoNetSSLWin64d.pdb" Source="$(var.POCO)\bin64\PocoNetSSLWin64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin64.shared.release" DiskId="1" Guid="208EAAA1-8250-461B-8CE3-BF9D327A87AF">
                            <File Id="POCONETSSLWIN64.DLL" Name="PocoNetSSLWin64.dll" Source="$(var.POCO)\bin64\PocoNetSSLWin64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF64.shared.debug" DiskId="1" Guid="*************-4E9F-AA69-1A71775C1DA4">
                            <File Id="POCOPDF64D.DLL" Name="PocoPDF64d.dll" Source="$(var.POCO)\bin64\PocoPDF64d.dll" />
                            <File Id="POCOPDF64D.PDB" Name="PocoPDF64d.pdb" Source="$(var.POCO)\bin64\PocoPDF64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF64.shared.release" DiskId="1" Guid="D06AA6D3-E5E7-4F8A-966D-AC0AC9A9EF43">
                            <File Id="POCOPDF64.DLL" Name="PocoPDF64.dll" Source="$(var.POCO)\bin64\PocoPDF64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="UTIL64.shared.debug" DiskId="1" Guid="927B3F39-CFFF-4869-BAB3-A7B79C65FAD8">
                            <File Id="POCOUTIL64D.DLL" Name="PocoUtil64d.dll" Source="$(var.POCO)\bin64\PocoUtil64d.dll" />
                            <File Id="POCOUTIL64D.PDB" Name="PocoUtil64d.pdb" Source="$(var.POCO)\bin64\PocoUtil64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="UTIL64.shared.release" DiskId="1" Guid="A6D419FF-2FC7-42D8-BD36-34F49A8183FE">
                            <File Id="POCOUTIL64.DLL" Name="PocoUtil64.dll" Source="$(var.POCO)\bin64\PocoUtil64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML64.shared.debug" DiskId="1" Guid="C168BCE5-BD99-46D2-978B-E3B54E620773">
                            <File Id="POCOXML64D.DLL" Name="PocoXML64d.dll" Source="$(var.POCO)\bin64\PocoXML64d.dll" />
                            <File Id="POCOXML64D.PDB" Name="PocoXML64d.pdb" Source="$(var.POCO)\bin64\PocoXML64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML64.shared.release" DiskId="1" Guid="FC832F2A-3D3B-419F-B57F-188EE923EFC6">
                            <File Id="POCOXML64.DLL" Name="PocoXML64.dll" Source="$(var.POCO)\bin64\PocoXML64.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="ZIP64.shared.debug" DiskId="1" Guid="FC711EEA-CAA3-47F1-90CA-E75E6E410537">
                            <File Id="POCOZIP64D.DLL" Name="PocoZip64d.dll" Source="$(var.POCO)\bin64\PocoZip64d.dll" />
                            <File Id="POCOZIP64D.PDB" Name="PocoZip64d.pdb" Source="$(var.POCO)\bin64\PocoZip64d.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="ZIP64.shared.release" DiskId="1" Guid="CDA3FB29-040B-495E-A147-FCA39821F987">
                            <File Id="POCOZIP64.DLL" Name="PocoZip64.dll" Source="$(var.POCO)\bin64\PocoZip64.dll" />
                        </Component>
                        <?else ?>
                        <Component Win64="$(var.Win64)" Id="Foundation.shared.debug" Guid="C184FF76-7E69-47F8-8516-4B52B97E37A7">
                            <File Id="POCOFOUNDATIOND.DLL" Name="PocoFoundationd.dll" Source="$(var.POCO)\bin\PocoFoundationd.dll" />
                            <File Id="POCOFOUNDATIOND.PDB" Name="PocoFoundationd.pdb" Source="$(var.POCO)\bin\PocoFoundationd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation.shared.release">
                            <File Id="POCOFOUNDATION.DLL" Name="PocoFoundation.dll" Source="$(var.POCO)\bin\PocoFoundation.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net.shared.debug" Guid="28805B8B-1D2F-43C8-B57C-662843F2CAB9">
                            <File Id="POCONETD.DLL" Name="PocoNetd.dll" Source="$(var.POCO)\bin\PocoNetd.dll" />
                            <File Id="POCONETD.PDB" Name="PocoNetd.pdb" Source="$(var.POCO)\bin\PocoNetd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net.shared.release" Guid="517C1D06-CCDC-46ED-9B33-1BBEE91FCD11">
                            <File Id="POCONET.DLL" Name="PocoNet.dll" Source="$(var.POCO)\bin\PocoNet.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util.shared.debug" Guid="4BA9FD60-D53D-469A-ACE7-34FB424A3406">
                            <File Id="POCOUTILD.DLL" Name="PocoUtild.dll" Source="$(var.POCO)\bin\PocoUtild.dll" />
                            <File Id="POCOUTILD.PDB" Name="PocoUtild.pdb" Source="$(var.POCO)\bin\PocoUtild.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util.shared.release" Guid="A4712A1A-C02A-4992-8FCD-01BA636D1A1F">
                            <File Id="POCOUTIL.DLL" Name="PocoUtil.dll" Source="$(var.POCO)\bin\PocoUtil.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML.shared.debug" Guid="6F75C53B-BB37-448F-A539-8A0C989BACFD">
                            <File Id="POCOXMLD.DLL" Name="PocoXMLd.dll" Source="$(var.POCO)\bin\PocoXMLd.dll" />
                            <File Id="POCOXMLD.PDB" Name="PocoXMLd.pdb" Source="$(var.POCO)\bin\PocoXMLd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML.shared.release" Guid="0BA10C18-FC00-4BF0-B61C-4ADE893CDB5C">
                            <File Id="POCOXML.DLL" Name="PocoXML.dll" Source="$(var.POCO)\bin\PocoXML.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON.shared.debug" Guid="C9F71A29-821E-4614-84F3-B322253611EC">
                            <File Id="POCOJSOND.DLL" Name="PocoJSONd.dll" Source="$(var.POCO)\bin\PocoJSONd.dll" />
                            <File Id="POCOJSOND.PDB" Name="PocoJSONd.pdb" Source="$(var.POCO)\bin\PocoJSONd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON.shared.release" Guid="7FD5F9E1-DF29-4A32-A11F-45755C1F9E6E">
                            <File Id="POCOJSON.DLL" Name="PocoJSON.dll" Source="$(var.POCO)\bin\PocoJSON.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL.shared.debug" Guid="C02291F7-57F7-43B4-8722-2C411E8E7FD9">
                            <File Id="POCONETSSLD.DLL" Name="PocoNetSSLd.dll" Source="$(var.POCO)\bin\PocoNetSSLd.dll" />
                            <File Id="POCONETSSLD.PDB" Name="PocoNetSSLd.pdb" Source="$(var.POCO)\bin\PocoNetSSLd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL.shared.release" Guid="CD3D7F34-E15A-408E-AAE0-85D427448227">
                            <File Id="POCONETSSL.DLL" Name="PocoNetSSL.dll" Source="$(var.POCO)\bin\PocoNetSSL.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB.shared.debug" Guid="A0B62DD7-0A5F-4E34-B2CD-1CA08E522B8F">
                            <File Id="POCOMONGODBD.DLL" Name="PocoMongoDBd.dll" Source="$(var.POCO)\bin\PocoMongoDBd.dll" />
                            <File Id="POCOMONGODBD.PDB" Name="PocoMongoDBd.pdb" Source="$(var.POCO)\bin\PocoMongoDBd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB.shared.release" Guid="AED8C4C2-FC12-4048-BD10-0FBD15F3DDFA">
                            <File Id="POCOMONGODB.DLL" Name="PocoMongoDB.dll" Source="$(var.POCO)\bin\PocoMongoDB.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto.shared.debug" Guid="EAEC10D7-D8BC-4615-9C99-12AF75670571">
                            <File Id="POCOCRYPTOD.DLL" Name="PocoCryptod.dll" Source="$(var.POCO)\bin\PocoCryptod.dll" />
                            <File Id="POCOCRYPTOD.PDB" Name="PocoCryptod.pdb" Source="$(var.POCO)\bin\PocoCryptod.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto.shared.release" Guid="05A7AEB7-F0BC-4995-A8DC-330543A06B4C">
                            <File Id="POCOCRYPTO.DLL" Name="PocoCrypto.dll" Source="$(var.POCO)\bin\PocoCrypto.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data.shared.debug" Guid="BC028A22-44AF-43D8-8349-14FBB65A8AF9">
                            <File Id="POCODATAD.DLL" Name="PocoDatad.dll" Source="$(var.POCO)\bin\PocoDatad.dll" />
                            <File Id="POCODATAD.PDB" Name="PocoDatad.pdb" Source="$(var.POCO)\bin\PocoDatad.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data.shared.release" Guid="843F5FF9-8609-4A2B-AA06-4A54E2B6F988">
                            <File Id="POCODATA.DLL" Name="PocoData.dll" Source="$(var.POCO)\bin\PocoData.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL.shared.debug" DiskId="1" Guid="BDAA8978-2CDB-41C5-8A10-71FE2D3CF27F">
                            <File Id="POCODATAMYSQL64D.DLL" Name="PocoDataMySQLd.dll" Source="$(var.POCO)\bin\PocoDataMySQLd.dll" />
                            <File Id="POCODATAMYSQL64D.PDB" Name="PocoDataMySQLd.pdb" Source="$(var.POCO)\bin\PocoDataMySQLd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL.shared.release" DiskId="1" Guid="E1F922E1-2123-42E8-A26E-3E5FCCB475F9">
                            <File Id="POCODATAMYSQL64.DLL" Name="PocoDataMySQL.dll" Source="$(var.POCO)\bin\PocoDataMySQL.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC.shared.debug" Guid="FE02019A-F229-438F-AADE-5F7199C4D423">
                            <File Id="POCODATAODBCD.DLL" Name="PocoDataODBCd.dll" Source="$(var.POCO)\bin\PocoDataODBCd.dll" />
                            <File Id="POCODATAODBCD.PDB" Name="PocoDataODBCd.pdb" Source="$(var.POCO)\bin\PocoDataODBCd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC.shared.release" Guid="2AA7D2AA-9A7D-4B4A-A667-8856436916DB">
                            <File Id="POCODATAODBC.DLL" Name="PocoDataODBC.dll" Source="$(var.POCO)\bin\PocoDataODBC.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite.shared.debug" Guid="96C2206F-C6AA-4DC5-819A-B2BF783DACD2">
                            <File Id="POCODATASQLITED.DLL" Name="PocoDataSQLited.dll" Source="$(var.POCO)\bin\PocoDataSQLited.dll" />
                            <File Id="POCODATASQLITED.PDB" Name="PocoDataSQLited.pdb" Source="$(var.POCO)\bin\PocoDataSQLited.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite.shared.release" Guid="ECDE1FD4-4EDA-4695-9B03-8906099044A9">
                            <File Id="POCODATASQLITE.DLL" Name="PocoDataSQLite.dll" Source="$(var.POCO)\bin\PocoDataSQLite.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL.shared.debug" Guid="FCA6CF33-E959-4955-88F6-21796BBB7743">
                            <File Id="POCODataPostgreSQLD.DLL" Name="PocoDataPostgreSQLd.dll" Source="$(var.POCO)\bin\PocoDataPostgreSQLd.dll" />
                            <File Id="POCODataPostgreSQLD.PDB" Name="PocoDataPostgreSQLd.pdb" Source="$(var.POCO)\bin\PocoDataPostgreSQLd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL.shared.release" Guid="FF708009-E321-45B7-B52A-04A26D536EC3">
                            <File Id="POCODataPostgreSQL.DLL" Name="PocoDataPostgreSQL.dll" Source="$(var.POCO)\bin\PocoDataPostgreSQL.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin.shared.debug" Guid="0428E44D-072C-47C3-97A8-52F81F420F62">
                            <File Id="POCONETSSLWIND.DLL" Name="PocoNetSSLWind.dll" Source="$(var.POCO)\bin\PocoNetSSLWind.dll" />
                            <File Id="POCONETSSLWIND.PDB" Name="PocoNetSSLWind.pdb" Source="$(var.POCO)\bin\PocoNetSSLWind.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin.shared.release" Guid="CF757B4F-B795-4B6F-A22C-4E08C0706DDE">
                            <File Id="POCONETSSLWIN.DLL" Name="PocoNetSSLWin.dll" Source="$(var.POCO)\bin\PocoNetSSLWin.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF.shared.debug" DiskId="1" Guid="6B5318D5-5D6C-4168-BD2B-3EAA05D701E6">
                            <File Id="POCOPDFD.DLL" Name="PocoPDFd.dll" Source="$(var.POCO)\bin\PocoPDFd.dll" />
                            <File Id="POCOPDFD.PDB" Name="PocoPDFd.pdb" Source="$(var.POCO)\bin\PocoPDFd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF.shared.release" DiskId="1" Guid="251BAA5F-4540-4D02-AF54-168A26D77CE3">
                            <File Id="POCOPDF.DLL" Name="PocoPDF.dll" Source="$(var.POCO)\bin\PocoPDF.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip.shared.debug" Guid="1A9595A7-464F-4389-9A9C-F29C4EA1E5FB">
                            <File Id="POCOZIPD.DLL" Name="PocoZipd.dll" Source="$(var.POCO)\bin\PocoZipd.dll" />
                            <File Id="POCOZIPD.PDB" Name="PocoZipd.pdb" Source="$(var.POCO)\bin\PocoZipd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip.shared.release" Guid="46A76B96-C9F5-4DEE-A375-63182A1280EE">
                            <File Id="POCOZIP.DLL" Name="PocoZip.dll" Source="$(var.POCO)\bin\PocoZip.dll" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit.shared.debug" Guid="4A42815C-2F00-4DDB-8069-6A86D5623193">
                            <File Id="CPPUNITD.DLL" Name="PocoCppUnitd.dll" Source="$(var.POCO)\bin\PocoCppUnitd.dll" />
                            <File Id="CPPUNITD.PDB" Name="PocoCppUnitd.pdb" Source="$(var.POCO)\bin\PocoCppUnitd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit.shared.release" Guid="7988AACC-327C-4F63-A095-DF553C6C3E86">
                            <File Id="CPPUNIT.DLL" Name="PocoCppUnit.dll" Source="$(var.POCO)\bin\PocoCppUnit.dll" />
                        </Component>
                        <?endif ?>
                    </Directory>
                    <Directory Id="lib" Name="lib">
                        <?if $(var.Platform) = x64 ?>
                        <Component Win64="$(var.Win64)" Id="Foundation64.static.debug" Guid="832C20AC-D283-4920-9A44-BE158B456308">
                            <File Id="POCOFOUNDATION64MDD.LIB" Name="PocoFoundation64mdd.lib" Source="$(var.POCO)\lib64\PocoFoundationmdd.lib" />
                            <File Id="POCOFOUNDATION64MDD.PDB" Name="pocofoundation64mdd.pdb" Source="$(var.POCO)\lib64\pocofoundationmdd.pdb" />
                            <File Id="POCOFOUNDATION64MTD.LIB" Name="PocoFoundation64mtd.lib" Source="$(var.POCO)\lib64\PocoFoundationmtd.lib" />
                            <File Id="POCOFOUNDATION64MTD.PDB" Name="pocofoundation64mtd.pdb" Source="$(var.POCO)\lib64\pocofoundationmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation64.static.release" Guid="1F35BC92-C5F4-4862-896A-93C53153DD78">
                            <File Id="POCOFOUNDATION64MD.LIB" Name="PocoFoundation64md.lib" Source="$(var.POCO)\lib64\PocoFoundationmd.lib" />
                            <File Id="POCOFOUNDATION64MT.LIB" Name="PocoFoundation64mt.lib" Source="$(var.POCO)\lib64\PocoFoundationmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net64.static.debug" Guid="0CEF9190-B5AE-4FE0-9A03-B0C4EE3BB778">
                            <File Id="POCONET64MDD.LIB" Name="PocoNet64mdd.lib" Source="$(var.POCO)\lib64\PocoNetmdd.lib" />
                            <File Id="POCONET64MDD.PDB" Name="poconet64mdd.pdb" Source="$(var.POCO)\lib64\poconetmdd.pdb" />
                            <File Id="POCONET64MTD.LIB" Name="PocoNet64mtd.lib" Source="$(var.POCO)\lib64\PocoNetmtd.lib" />
                            <File Id="POCONET64MTD.PDB" Name="poconet64mtd.pdb" Source="$(var.POCO)\lib64\poconetmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net64.static.release" Guid="9ABC73AD-25D7-4499-AB0C-************">
                            <File Id="POCONET64MD.LIB" Name="PocoNet64md.lib" Source="$(var.POCO)\lib64\PocoNetmd.lib" />
                            <File Id="POCONET64MT.LIB" Name="PocoNet64mt.lib" Source="$(var.POCO)\lib64\PocoNetmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util64.static.debug" Guid="FB801F5C-1D0B-49F9-8D86-38DEE198C475">
                            <File Id="POCOUTIL64MDD.LIB" Name="PocoUtil64mdd.lib" Source="$(var.POCO)\lib64\PocoUtilmdd.lib" />
                            <File Id="POCOUTIL64MDD.PDB" Name="pocoutil64mdd.pdb" Source="$(var.POCO)\lib64\pocoutilmdd.pdb" />
                            <File Id="POCOUTIL64MTD.LIB" Name="PocoUtil64mtd.lib" Source="$(var.POCO)\lib64\PocoUtilmtd.lib" />
                            <File Id="POCOUTIL64MTD.PDB" Name="pocoutil64mtd.pdb" Source="$(var.POCO)\lib64\pocoutilmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util64.static.release" Guid="1B55397E-34A4-4CDC-A7AF-787A390DB2D4">
                            <File Id="POCOUTIL64MD.LIB" Name="PocoUtil64md.lib" Source="$(var.POCO)\lib64\PocoUtilmd.lib" />
                            <File Id="POCOUTIL64MT.LIB" Name="PocoUtil64mt.lib" Source="$(var.POCO)\lib64\PocoUtilmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML64.static.debug" Guid="577D31AA-9BE5-444D-B114-E87CE6EB04D5">
                            <File Id="POCOXML64MDD.LIB" Name="PocoXML64mdd.lib" Source="$(var.POCO)\lib64\PocoXMLmdd.lib" />
                            <File Id="POCOXML64MDD.PDB" Name="pocoxml64mdd.pdb" Source="$(var.POCO)\lib64\pocoxmlmdd.pdb" />
                            <File Id="POCOXML64MTD.LIB" Name="PocoXML64mtd.lib" Source="$(var.POCO)\lib64\PocoXMLmtd.lib" />
                            <File Id="POCOXML64MTD.PDB" Name="Pocoxml64mtd.pdb" Source="$(var.POCO)\lib64\pocoxmlmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML64.static.release" Guid="939AA46C-E11D-4B6A-A519-E815AF322156">
                            <File Id="POCOXML64MD.LIB" Name="PocoXML64md.lib" Source="$(var.POCO)\lib64\PocoXMLmd.lib" />
                            <File Id="POCOXML64MT.LIB" Name="PocoXML64mt.lib" Source="$(var.POCO)\lib64\PocoXMLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON64.static.debug" Guid="9C91855C-6041-42BF-99CE-7C9323C630B6">
                            <File Id="POCOJSON64MDD.LIB" Name="PocoJSON64mdd.lib" Source="$(var.POCO)\lib64\PocoJSONmdd.lib" />
                            <File Id="POCOJSON64MDD.PDB" Name="pocojson64mdd.pdb" Source="$(var.POCO)\lib64\pocojsonmdd.pdb" />
                            <File Id="POCOJSON64MTD.LIB" Name="PocoJSON64mtd.lib" Source="$(var.POCO)\lib64\PocoJSONmtd.lib" />
                            <File Id="POCOJSON64MTD.PDB" Name="pocojson64mtd.pdb" Source="$(var.POCO)\lib64\pocojsonmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON64.static.release" Guid="019422B9-5A58-4595-BE28-28F8DB320764">
                            <File Id="POCOJSON64MD.LIB" Name="PocoJSON64md.lib" Source="$(var.POCO)\lib64\PocoJSONmd.lib" />
                            <File Id="POCOJSON64MT.LIB" Name="PocoJSON64mt.lib" Source="$(var.POCO)\lib64\PocoJSONmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL64.static.debug" Guid="A84090EC-8518-4557-B895-BFE8F54047C7">
                            <File Id="POCONETSS64LMDD.LIB" Name="PocoNetSSL64mdd.lib" Source="$(var.POCO)\lib64\PocoNetSSLmdd.lib" />
                            <File Id="POCONETSS64LMDD.PDB" Name="poconetssl64mdd.pdb" Source="$(var.POCO)\lib64\poconetsslmdd.pdb" />
                            <File Id="POCONETSS64LMTD.LIB" Name="PocoNetSSL64mtd.lib" Source="$(var.POCO)\lib64\PocoNetSSLmtd.lib" />
                            <File Id="POCONETSSL64MTD.PDB" Name="poconetssl64mtd.pdb" Source="$(var.POCO)\lib64\poconetsslmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL64.static.release" Guid="093E533E-59B2-40E6-B9CC-4EE9417D7C8C">
                            <File Id="POCONETSS64LMD.LIB" Name="PocoNetSSL64md.lib" Source="$(var.POCO)\lib64\PocoNetSSLmd.lib" />
                            <File Id="POCONETSS64LMT.LIB" Name="PocoNetSSL64mt.lib" Source="$(var.POCO)\lib64\PocoNetSSLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB64.static.debug" Guid="76B48D5A-EF13-4E7F-93FE-39F1B67ED682">
                            <File Id="POCOMONGODB64MDD.LIB" Name="PocoMongoDB64mdd.lib" Source="$(var.POCO)\lib64\PocoMongoDBmdd.lib" />
                            <File Id="POCOMONGODB64MDD.PDB" Name="pocomongodb64mdd.pdb" Source="$(var.POCO)\lib64\pocomongodbmdd.pdb" />
                            <File Id="POCOMONGODB64MTD.LIB" Name="PocoMongoDB64mtd.lib" Source="$(var.POCO)\lib64\PocoMongoDBmtd.lib" />
                            <File Id="POCOMONGODB64MTD.PDB" Name="pocomongodb64mtd.pdb" Source="$(var.POCO)\lib64\pocomongodbmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB64.static.release" Guid="15FBC6B5-**************-11164268CAEC">
                            <File Id="POCOMONGODB64MD.LIB" Name="PocoMongoDB64md.lib" Source="$(var.POCO)\lib64\PocoMongoDBmd.lib" />
                            <File Id="POCOMONGODB64MT.LIB" Name="PocoMongoDB64mt.lib" Source="$(var.POCO)\lib64\PocoMongoDBmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto64.static.debug" Guid="0D082893-DF9C-4900-AF54-45813DDABAC6">
                            <File Id="POCOCRYPTO64MDD.LIB" Name="PocoCrypto64mdd.lib" Source="$(var.POCO)\lib64\PocoCryptomdd.lib" />
                            <File Id="POCOCRYPTO64MDD.PDB" Name="pococrypto64mdd.pdb" Source="$(var.POCO)\lib64\pococryptomdd.pdb" />
                            <File Id="POCOCRYPTO64MTD.LIB" Name="PocoCrypto64mtd.lib" Source="$(var.POCO)\lib64\PocoCryptomtd.lib" />
                            <File Id="POCOCRYPTO64MTD.PDB" Name="pococrypto64mtd.pdb" Source="$(var.POCO)\lib64\pococryptomtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto64.static.release" Guid="7A77AE62-217A-4269-8F87-9D957B42460C">
                            <File Id="POCOCRYPTO64MD.LIB" Name="PocoCrypto64md.lib" Source="$(var.POCO)\lib64\PocoCryptomd.lib" />
                            <File Id="POCOCRYPTO64MT.LIB" Name="PocoCrypto64mt.lib" Source="$(var.POCO)\lib64\PocoCryptomt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data64.static.debug" Guid="75E219B6-B236-4527-BA5D-FDE792A8AC63">
                            <File Id="POCODATA64MDD.LIB" Name="PocoData64mdd.lib" Source="$(var.POCO)\lib64\PocoDatamdd.lib" />
                            <File Id="POCODATA64MDD.PDB" Name="pocodata64mdd.pdb" Source="$(var.POCO)\lib64\pocodatamdd.pdb" />
                            <File Id="POCODATA64MTD.LIB" Name="PocoData64mtd.lib" Source="$(var.POCO)\lib64\PocoDatamtd.lib" />
                            <File Id="POCODATA64MTD.PDB" Name="pocodata64mtd.pdb" Source="$(var.POCO)\lib64\pocodatamtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data64.static.release" Guid="95B3334B-2F19-4810-BB41-F5F9E3415FDD">
                            <File Id="POCODATA64MD.LIB" Name="PocoData64md.lib" Source="$(var.POCO)\lib64\PocoDatamd.lib" />
                            <File Id="POCODATA64MT.LIB" Name="PocoData64mt.lib" Source="$(var.POCO)\lib64\PocoDatamt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC64.static.debug" Guid="5818B110-4664-4D48-8062-1C10914189E7">
                            <File Id="POCODATAODBC64MDD.LIB" Name="PocoDataODBC64mdd.lib" Source="$(var.POCO)\lib64\PocoDataODBCmdd.lib" />
                            <File Id="POCODATAODBC64MDD.PDB" Name="pocodataodbc64mdd.pdb" Source="$(var.POCO)\lib64\pocodataodbcmdd.pdb" />
                            <File Id="POCODATAODBC64MTD.LIB" Name="PocoDataODBC64mtd.lib" Source="$(var.POCO)\lib64\PocoDataODBCmtd.lib" />
                            <File Id="POCODATAODBC64MTD.PDB" Name="pocodataodbc64mtd.pdb" Source="$(var.POCO)\lib64\pocodataodbcmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC64.static.release" Guid="2D3DB6EE-F3DB-4341-8C9A-33D1A6704EF3">
                            <File Id="POCODATAODBC64MD.LIB" Name="PocoDataODBC64md.lib" Source="$(var.POCO)\lib64\PocoDataODBCmd.lib" />
                            <File Id="POCODATAODBC64MT.LIB" Name="PocoDataODBC64mt.lib" Source="$(var.POCO)\lib64\PocoDataODBCmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite64.static.debug" Guid="2D59BA2E-0A6A-4F3C-86CF-A4DD99E3237C">
                            <File Id="POCODATASQLITE64MDD.LIB" Name="PocoDataSQLite64mdd.lib" Source="$(var.POCO)\lib64\PocoDataSQLitemdd.lib" />
                            <File Id="POCODATASQLITE64MDD.PDB" Name="pocodatasqlite64mdd.pdb" Source="$(var.POCO)\lib64\pocodatasqlitemdd.pdb" />
                            <File Id="POCODATASQLITE64MTD.LIB" Name="PocoDataSQLite64mtd.lib" Source="$(var.POCO)\lib64\PocoDataSQLitemtd.lib" />
                            <File Id="POCODATASQLITE64MTD.PDB" Name="pocodatasqlite64mtd.pdb" Source="$(var.POCO)\lib64\pocodatasqlitemtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite64.static.release" Guid="23F831FC-4DDA-4CBD-87C5-F98D429A70C1">
                            <File Id="POCODATASQLITE64MD.LIB" Name="PocoDataSQLite64md.lib" Source="$(var.POCO)\lib64\PocoDataSQLitemd.lib" />
                            <File Id="POCODATASQLITE64MT.LIB" Name="PocoDataSQLite64mt.lib" Source="$(var.POCO)\lib64\PocoDataSQLitemt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL64.static.debug" Guid="C9E4D208-C1DF-4622-BFE3-F88F1B0DA95B">
                            <File Id="POCODATAPOSTGRESQL64MDD.LIB" Name="PocoDataPostgreSQL64mdd.lib" Source="$(var.POCO)\lib64\PocoDataPostgreSQLmdd.lib" />
                            <File Id="POCODATAPOSTGRESQL64MDD.PDB" Name="pocoDataPostgreSQL64mdd.pdb" Source="$(var.POCO)\lib64\pocoDataPostgreSQLmdd.pdb" />
                            <File Id="POCODATAPOSTGRESQL64MTD.LIB" Name="PocoDataPostgreSQL64mtd.lib" Source="$(var.POCO)\lib64\PocoDataPostgreSQLmtd.lib" />
                            <File Id="POCODATAPOSTGRESQL64MTD.PDB" Name="pocoDataPostgreSQL64mtd.pdb" Source="$(var.POCO)\lib64\pocoDataPostgreSQLmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL64.static.release" Guid="74C3FFF4-1DB3-45D0-AB96-F89A85A39D30">
                            <File Id="POCODATAPOSTGRESQL64MD.LIB" Name="PocoDataPostgreSQL64md.lib" Source="$(var.POCO)\lib64\PocoDataPostgreSQLmd.lib" />
                            <File Id="POCODATAPOSTGRESQL64MT.LIB" Name="PocoDataPostgreSQL64mt.lib" Source="$(var.POCO)\lib64\PocoDataPostgreSQLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL64.static.debug" Guid="4A5EB45E-024C-45F1-BDB6-3799DAA755AE">
                            <File Id="POCODATAMYSQL64MDD.LIB" Name="PocoDataMySQL64mdd.lib" Source="$(var.POCO)\lib64\PocoDataMySQLmdd.lib" />
                            <File Id="POCODATAMYSQL64MDD.PDB" Name="pocodatamysql64mdd.pdb" Source="$(var.POCO)\lib64\pocodatamysqlmdd.pdb" />
                            <File Id="POCODATAMYSQL64MTD.LIB" Name="PocoDataMySQL64mtd.lib" Source="$(var.POCO)\lib64\PocoDataMySQLmtd.lib" />
                            <File Id="POCODATAMYSQL64MTD.PDB" Name="pocodatamysql64mtd.pdb" Source="$(var.POCO)\lib64\pocodatamysqlmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL64.static.release" Guid="D7CC865C-8337-418C-89A1-437BCD3221EE">
                            <File Id="POCODATAMYSQL64MD.LIB" Name="PocoDataMySQL64md.lib" Source="$(var.POCO)\lib64\PocoDataMySQLmd.lib" />
                            <File Id="POCODATAMYSQL64MT.LIB" Name="PocoDataMySQL64mt.lib" Source="$(var.POCO)\lib64\PocoDataMySQLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin64.static.debug" Guid="B93225A4-72DC-485C-B3C1-C5EC87EF03D4">
                            <File Id="POCONETSSLWIN64MDD.LIB" Name="PocoNetSSLWin64mdd.lib" Source="$(var.POCO)\lib64\PocoNetSSLWinmdd.lib" />
                            <File Id="POCONETSSLWIN64MDD.PDB" Name="poconetsslwin64mdd.pdb" Source="$(var.POCO)\lib64\poconetsslwinmdd.pdb" />
                            <File Id="POCONETSSLWIN64MTD.LIB" Name="PocoNetSSLWin64mtd.lib" Source="$(var.POCO)\lib64\PocoNetSSLWinmtd.lib" />
                            <File Id="POCONETSSLWIN64MTD.PDB" Name="poconetsslwin64mtd.pdb" Source="$(var.POCO)\lib64\poconetsslwinmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin64.static.release" Guid="5395461B-7CBE-4E31-B3C8-B320EA12168D">
                            <File Id="POCONETSSLWIN64MD.LIB" Name="PocoNetSSLWin64md.lib" Source="$(var.POCO)\lib64\PocoNetSSLWinmd.lib" />
                            <File Id="POCONETSSLWIN64MT.LIB" Name="PocoNetSSLWin64mt.lib" Source="$(var.POCO)\lib64\PocoNetSSLWinmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip64.static.debug" Guid="4B506E74-612D-47F6-8450-766F7C714F41">
                            <File Id="POCOZIP64MDD.LIB" Name="PocoZip64mdd.lib" Source="$(var.POCO)\lib64\PocoZipmdd.lib" />
                            <File Id="POCOZIP64MDD.PDB" Name="pocozip64mdd.pdb" Source="$(var.POCO)\lib64\pocozipmdd.pdb" />
                            <File Id="POCOZIP64MTD.LIB" Name="PocoZip64mtd.lib" Source="$(var.POCO)\lib64\PocoZipmtd.lib" />
                            <File Id="POCOZIP64MTD.PDB" Name="pocozip64mtd.pdb" Source="$(var.POCO)\lib64\pocozipmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip64.static.release" Guid="254BFA6F-12B6-421E-8C82-753FD63BD263">
                            <File Id="POCOZIP64MD.LIB" Name="PocoZip64md.lib" Source="$(var.POCO)\lib64\PocoZipmd.lib" />
                            <File Id="POCOZIP64MT.LIB" Name="PocoZip64mt.lib" Source="$(var.POCO)\lib64\PocoZipmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit64.static.debug" Guid="6D625EF5-7905-4AAF-BE86-F0E8F3A43209">
                            <File Id="CPPUNIT64MDD.LIB" Name="PocoCppUnit64mdd.lib" Source="$(var.POCO)\lib64\PocoCppUnitmdd.lib" />
                            <File Id="CPPUNIT64MDD.PDB" Name="PocoCppUnit64mdd.pdb" Source="$(var.POCO)\lib64\PocoCppUnitmdd.pdb" />
                            <File Id="CPPUNIT64MTD.LIB" Name="PocoCppUnit64mtd.lib" Source="$(var.POCO)\lib64\PocoCppUnitmtd.lib" />
                            <File Id="CPPUNIT64MTD.PDB" Name="PocoCppUnit64mtd.pdb" Source="$(var.POCO)\lib64\PocoCppUnitmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit64.static.release" Guid="14E1151E-C40C-43C3-A6B8-9AD8B20E83C5">
                            <File Id="CPPUNIT64MD.LIB" Name="PocoCppUnit64md.lib" Source="$(var.POCO)\lib64\PocoCppUnitmd.lib" />
                            <File Id="CPPUNIT64MT.LIB" Name="PocoCppUnit64mt.lib" Source="$(var.POCO)\lib64\PocoCppUnitmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF64.static.debug" Guid="26D0414F-132E-40DC-92E4-6697B2876A3F">
                            <File Id="POCOPDF64MDD.LIB" Name="PocoPDF64mdd.lib" Source="$(var.POCO)\lib64\PocoPDFmdd.lib" />
                            <File Id="POCOPDF64MDD.PDB" Name="pocopdf64mdd.pdb" Source="$(var.POCO)\lib64\pocopdfmdd.pdb" />
                            <File Id="POCOPDF64MTD.LIB" Name="PocoPDF64mtd.lib" Source="$(var.POCO)\lib64\PocoPDFmtd.lib" />
                            <File Id="POCOPDF64MTD.PDB" Name="pocopdf64mtd.pdb" Source="$(var.POCO)\lib64\pocopdfmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF64.static.release" Guid="452E43B6-B81D-4719-A2C5-9AA291223678">
                            <File Id="POCOPDF64MD.LIB" Name="PocoPDF64md.lib" Source="$(var.POCO)\lib64\PocoPDFmd.lib" />
                            <File Id="POCOPDF64MT.LIB" Name="PocoPDF64mt.lib" Source="$(var.POCO)\lib64\PocoPDFmt.lib" />
                        </Component>
                        <?else ?>
                        <Component Win64="$(var.Win64)" Id="Foundation.static.debug" Guid="6799BABA-3A46-48C6-B26B-36E0BF258CA7">
                            <File Id="POCOFOUNDATIONMDD.LIB" Name="PocoFoundationmdd.lib" Source="$(var.POCO)\lib\PocoFoundationmdd.lib" />
                            <File Id="POCOFOUNDATIONMDD.PDB" Name="pocofoundationmdd.pdb" Source="$(var.POCO)\lib\pocofoundationmdd.pdb" />
                            <File Id="POCOFOUNDATIONMTD.LIB" Name="PocoFoundationmtd.lib" Source="$(var.POCO)\lib\PocoFoundationmtd.lib" />
                            <File Id="POCOFOUNDATIONMTD.PDB" Name="pocofoundationmtd.pdb" Source="$(var.POCO)\lib\pocofoundationmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation.static.release" Guid="1F35BC92-C5F4-4862-896A-93C53153DD78">
                            <File Id="POCOFOUNDATIONMD.LIB" Name="PocoFoundationmd.lib" Source="$(var.POCO)\lib\PocoFoundationmd.lib" />
                            <File Id="POCOFOUNDATIONMT.LIB" Name="PocoFoundationmt.lib" Source="$(var.POCO)\lib\PocoFoundationmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net.static.debug" Guid="4F3872CA-0A00-4DD7-B50F-3E2322CEE0DA">
                            <File Id="POCONETMDD.LIB" Name="PocoNetmdd.lib" Source="$(var.POCO)\lib\PocoNetmdd.lib" />
                            <File Id="POCONETMDD.PDB" Name="poconetmdd.pdb" Source="$(var.POCO)\lib\poconetmdd.pdb" />
                            <File Id="POCONETMTD.LIB" Name="PocoNetmtd.lib" Source="$(var.POCO)\lib\PocoNetmtd.lib" />
                            <File Id="POCONETMTD.PDB" Name="poconetmtd.pdb" Source="$(var.POCO)\lib\poconetmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net.static.release" Guid="9ABC73AD-25D7-4499-AB0C-************">
                            <File Id="POCONETMD.LIB" Name="PocoNetmd.lib" Source="$(var.POCO)\lib\PocoNetmd.lib" />
                            <File Id="POCONETMT.LIB" Name="PocoNetmt.lib" Source="$(var.POCO)\lib\PocoNetmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util.static.debug" Guid="863EDB69-8D44-43D2-B581-BEC3BA32E620">
                            <File Id="POCOUTILMDD.LIB" Name="PocoUtilmdd.lib" Source="$(var.POCO)\lib\PocoUtilmdd.lib" />
                            <File Id="POCOUTILMDD.PDB" Name="pocoutilmdd.pdb" Source="$(var.POCO)\lib\pocoutilmdd.pdb" />
                            <File Id="POCOUTILMTD.LIB" Name="PocoUtilmtd.lib" Source="$(var.POCO)\lib\PocoUtilmtd.lib" />
                            <File Id="POCOUTILMTD.PDB" Name="pocoutilmtd.pdb" Source="$(var.POCO)\lib\pocoutilmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util.static.release" Guid="1B55397E-34A4-4CDC-A7AF-787A390DB2D4">
                            <File Id="POCOUTILMD.LIB" Name="PocoUtilmd.lib" Source="$(var.POCO)\lib\PocoUtilmd.lib" />
                            <File Id="POCOUTILMT.LIB" Name="PocoUtilmt.lib" Source="$(var.POCO)\lib\PocoUtilmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML.static.debug" Guid="0BD560AB-F83C-4E37-82B7-EAC3FD0F3059">
                            <File Id="POCOXMLMDD.LIB" Name="PocoXMLmdd.lib" Source="$(var.POCO)\lib\PocoXMLmdd.lib" />
                            <File Id="POCOXMLMDD.PDB" Name="pocoxmlmdd.pdb" Source="$(var.POCO)\lib\pocoxmlmdd.pdb" />
                            <File Id="POCOXMLMTD.LIB" Name="PocoXMLmtd.lib" Source="$(var.POCO)\lib\PocoXMLmtd.lib" />
                            <File Id="POCOXMLMTD.PDB" Name="pocoxmlmtd.pdb" Source="$(var.POCO)\lib\pocoxmlmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML.static.release" Guid="939AA46C-E11D-4B6A-A519-E815AF322156">
                            <File Id="POCOXMLMD.LIB" Name="PocoXMLmd.lib" Source="$(var.POCO)\lib\PocoXMLmd.lib" />
                            <File Id="POCOXMLMT.LIB" Name="PocoXMLmt.lib" Source="$(var.POCO)\lib\PocoXMLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON.static.debug" Guid="9B96C89D-63FD-40CF-B234-3982FEDEE962">
                            <File Id="POCOJSONMDD.LIB" Name="PocoJSONmdd.lib" Source="$(var.POCO)\lib\PocoJSONmdd.lib" />
                            <File Id="POCOJSONMDD.PDB" Name="pocojsonmdd.pdb" Source="$(var.POCO)\lib\pocojsonmdd.pdb" />
                            <File Id="POCOJSONMTD.LIB" Name="PocoJSONmtd.lib" Source="$(var.POCO)\lib\PocoJSONmtd.lib" />
                            <File Id="POCOJSONMTD.PDB" Name="pocojsonmtd.pdb" Source="$(var.POCO)\lib\pocojsonmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON.static.release" Guid="019422B9-5A58-4595-BE28-28F8DB320764">
                            <File Id="POCOJSONMD.LIB" Name="PocoJSONmd.lib" Source="$(var.POCO)\lib\PocoJSONmd.lib" />
                            <File Id="POCOJSONMT.LIB" Name="PocoJSONmt.lib" Source="$(var.POCO)\lib\PocoJSONmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL.static.debug" Guid="048A5AC5-D07E-4AEB-99C1-95B590FC620E">
                            <File Id="POCONETSSLMDD.LIB" Name="PocoNetSSLmdd.lib" Source="$(var.POCO)\lib\PocoNetSSLmdd.lib" />
                            <File Id="POCONETSSLMDD.PDB" Name="poconetsslmdd.pdb" Source="$(var.POCO)\lib\poconetsslmdd.pdb" />
                            <File Id="POCONETSSLMTD.LIB" Name="PocoNetSSLmtd.lib" Source="$(var.POCO)\lib\PocoNetSSLmtd.lib" />
                            <File Id="POCONETSSLMTD.PDB" Name="poconetsslmtd.pdb" Source="$(var.POCO)\lib\poconetsslmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL.static.release" Guid="093E533E-59B2-40E6-B9CC-4EE9417D7C8C">
                            <File Id="POCONETSSLMD.LIB" Name="PocoNetSSLmd.lib" Source="$(var.POCO)\lib\PocoNetSSLmd.lib" />
                            <File Id="POCONETSSLMT.LIB" Name="PocoNetSSLmt.lib" Source="$(var.POCO)\lib\PocoNetSSLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB.static.debug" Guid="54D16763-C943-4E2A-A4EE-CC4688F0FB36">
                            <File Id="POCOMONGODBMDD.LIB" Name="PocoMongoDBmdd.lib" Source="$(var.POCO)\lib\PocoMongoDBmdd.lib" />
                            <File Id="POCOMONGODBMDD.PDB" Name="pocomongodbmdd.pdb" Source="$(var.POCO)\lib\pocomongodbmdd.pdb" />
                            <File Id="POCOMONGODBMTD.LIB" Name="PocoMongoDBmtd.lib" Source="$(var.POCO)\lib\PocoMongoDBmtd.lib" />
                            <File Id="POCOMONGODBMTD.PDB" Name="pocomongodbmtd.pdb" Source="$(var.POCO)\lib\pocomongodbmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB.static.release" Guid="15FBC6B5-**************-11164268CAEC">
                            <File Id="POCOMONGODBMD.LIB" Name="PocoMongoDBmd.lib" Source="$(var.POCO)\lib\PocoMongoDBmd.lib" />
                            <File Id="POCOMONGODBMT.LIB" Name="PocoMongoDBmt.lib" Source="$(var.POCO)\lib\PocoMongoDBmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto.static.debug" Guid="CE2992D2-4D56-44B2-A4EE-79B638BD05A8">
                            <File Id="POCOCRYPTOMDD.LIB" Name="PocoCryptomdd.lib" Source="$(var.POCO)\lib\PocoCryptomdd.lib" />
                            <File Id="POCOCRYPTOMDD.PDB" Name="pococryptomdd.pdb" Source="$(var.POCO)\lib\pococryptomdd.pdb" />
                            <File Id="POCOCRYPTOMTD.LIB" Name="PocoCryptomtd.lib" Source="$(var.POCO)\lib\PocoCryptomtd.lib" />
                            <File Id="POCOCRYPTOMTD.PDB" Name="pococryptomtd.pdb" Source="$(var.POCO)\lib\pococryptomtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto.static.release" Guid="7A77AE62-217A-4269-8F87-9D957B42460C">
                            <File Id="POCOCRYPTOMD.LIB" Name="PocoCryptomd.lib" Source="$(var.POCO)\lib\PocoCryptomd.lib" />
                            <File Id="POCOCRYPTOMT.LIB" Name="PocoCryptomt.lib" Source="$(var.POCO)\lib\PocoCryptomt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data.static.debug" Guid="DD35AC73-E4C8-429E-A7D0-E17167F4FD9C">
                            <File Id="POCODATAMDD.LIB" Name="PocoDatamdd.lib" Source="$(var.POCO)\lib\PocoDatamdd.lib" />
                            <File Id="POCODATAMDD.PDB" Name="pocodatamdd.pdb" Source="$(var.POCO)\lib\pocodatamdd.pdb" />
                            <File Id="POCODATAMTD.LIB" Name="PocoDatamtd.lib" Source="$(var.POCO)\lib\PocoDatamtd.lib" />
                            <File Id="POCODATAMTD.PDB" Name="pocodatamtd.pdb" Source="$(var.POCO)\lib\pocodatamtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data.static.release" Guid="95B3334B-2F19-4810-BB41-F5F9E3415FDD">
                            <File Id="POCODATAMD.LIB" Name="PocoDatamd.lib" Source="$(var.POCO)\lib\PocoDatamd.lib" />
                            <File Id="POCODATAMT.LIB" Name="PocoDatamt.lib" Source="$(var.POCO)\lib\PocoDatamt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC.static.debug" Guid="79F5E5DA-421A-4982-A2C1-59A3D921329F">
                            <File Id="POCODATAODBCMDD.LIB" Name="PocoDataODBCmdd.lib" Source="$(var.POCO)\lib\PocoDataODBCmdd.lib" />
                            <File Id="POCODATAODBCMDD.PDB" Name="pocodataodbcmdd.pdb" Source="$(var.POCO)\lib\pocodataodbcmdd.pdb" />
                            <File Id="POCODATAODBCMTD.LIB" Name="PocoDataODBCmtd.lib" Source="$(var.POCO)\lib\PocoDataODBCmtd.lib" />
                            <File Id="POCODATAODBCMTD.PDB" Name="pocodataodbcmtd.pdb" Source="$(var.POCO)\lib\pocodataodbcmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC.static.release" Guid="2D3DB6EE-F3DB-4341-8C9A-33D1A6704EF3">
                            <File Id="POCODATAODBCMD.LIB" Name="PocoDataODBCmd.lib" Source="$(var.POCO)\lib\PocoDataODBCmd.lib" />
                            <File Id="POCODATAODBCMT.LIB" Name="PocoDataODBCmt.lib" Source="$(var.POCO)\lib\PocoDataODBCmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite.static.debug" Guid="662F1977-31F3-478A-A0FA-3BB809BD0264">
                            <File Id="POCODATASQLITEMDD.LIB" Name="PocoDataSQLitemdd.lib" Source="$(var.POCO)\lib\PocoDataSQLitemdd.lib" />
                            <File Id="POCODATASQLITEMDD.PDB" Name="pocodatasqlitemdd.pdb" Source="$(var.POCO)\lib\pocodatasqlitemdd.pdb" />
                            <File Id="POCODATASQLITEMTD.LIB" Name="PocoDataSQLitemtd.lib" Source="$(var.POCO)\lib\PocoDataSQLitemtd.lib" />
                            <File Id="POCODATASQLITEMTD.PDB" Name="pocodatasqlitemtd.pdb" Source="$(var.POCO)\lib\pocodatasqlitemtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite.static.release" Guid="23F831FC-4DDA-4CBD-87C5-F98D429A70C1">
                            <File Id="POCODATASQLITEMD.LIB" Name="PocoDataSQLitemd.lib" Source="$(var.POCO)\lib\PocoDataSQLitemd.lib" />
                            <File Id="POCODATASQLITEMT.LIB" Name="PocoDataSQLitemt.lib" Source="$(var.POCO)\lib\PocoDataSQLitemt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL.static.debug" Guid="4D5B3239-0736-4717-9EED-76F3A8CAB565">
                            <File Id="POCODATAPOSTGRESQLMDD.LIB" Name="PocoDataPostgreSQLmdd.lib" Source="$(var.POCO)\lib\PocoDataPostgreSQLmdd.lib" />
                            <File Id="POCODATAPOSTGRESQLMDD.PDB" Name="pocoDataPostgreSQLmdd.pdb" Source="$(var.POCO)\lib\pocoDataPostgreSQLmdd.pdb" />
                            <File Id="POCODATAPOSTGRESQLMTD.LIB" Name="PocoDataPostgreSQLmtd.lib" Source="$(var.POCO)\lib\PocoDataPostgreSQLmtd.lib" />
                            <File Id="POCODATAPOSTGRESQLMTD.PDB" Name="pocoDataPostgreSQLmtd.pdb" Source="$(var.POCO)\lib\pocoDataPostgreSQLmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL.static.release" Guid="E827B78F-06FA-41E8-AD8F-44AD2CA8A64E">
                            <File Id="POCODATAPOSTGRESQLMD.LIB" Name="PocoDataPostgreSQLmd.lib" Source="$(var.POCO)\lib\PocoDataPostgreSQLmd.lib" />
                            <File Id="POCODATAPOSTGRESQLMT.LIB" Name="PocoDataPostgreSQLmt.lib" Source="$(var.POCO)\lib\PocoDataPostgreSQLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL.static.debug" Guid="C2EA8639-A80D-4271-ABFE-65C00FF7B227">
                            <File Id="POCODATAMYSQLMDD.LIB" Name="PocoDataMySQLmdd.lib" Source="$(var.POCO)\lib\PocoDataMySQLmdd.lib" />
                            <File Id="POCODATAMYSQLMDD.PDB" Name="pocodatamysqlmdd.pdb" Source="$(var.POCO)\lib\pocodatamysqlmdd.pdb" />
                            <File Id="POCODATAMYSQLMTD.LIB" Name="PocoDataMySQLmtd.lib" Source="$(var.POCO)\lib\PocoDataMySQLmtd.lib" />
                            <File Id="POCODATAMYSQLMTD.PDB" Name="pocodatamysqlmtd.pdb" Source="$(var.POCO)\lib\pocodatamysqlmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL.static.release" Guid="D7CC865C-8337-418C-89A1-437BCD3221EE">
                            <File Id="POCODATAMYSQLMD.LIB" Name="PocoDataMySQLmd.lib" Source="$(var.POCO)\lib\PocoDataMySQLmd.lib" />
                            <File Id="POCODATAMYSQLMT.LIB" Name="PocoDataMySQLmt.lib" Source="$(var.POCO)\lib\PocoDataMySQLmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin.static.debug" Guid="B18B941E-6B32-48CB-ABE6-B1E89FC344EA">
                            <File Id="POCONETSSLWINMDD.LIB" Name="PocoNetSSLWinmdd.lib" Source="$(var.POCO)\lib\PocoNetSSLWinmdd.lib" />
                            <File Id="POCONETSSLWINMDD.PDB" Name="poconetsslwinmdd.pdb" Source="$(var.POCO)\lib\poconetsslwinmdd.pdb" />
                            <File Id="POCONETSSLWINMTD.LIB" Name="PocoNetSSLWinmtd.lib" Source="$(var.POCO)\lib\PocoNetSSLWinmtd.lib" />
                            <File Id="POCONETSSLWINMTD.PDB" Name="poconetsslwinmtd.pdb" Source="$(var.POCO)\lib\poconetsslwinmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin.static.release" Guid="5395461B-7CBE-4E31-B3C8-B320EA12168D">
                            <File Id="POCONETSSLWINMD.LIB" Name="PocoNetSSLWinmd.lib" Source="$(var.POCO)\lib\PocoNetSSLWinmd.lib" />
                            <File Id="POCONETSSLWINMT.LIB" Name="PocoNetSSLWinmt.lib" Source="$(var.POCO)\lib\PocoNetSSLWinmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip.static.debug" Guid="10BDE135-257C-469B-8948-3CDB55D0C809">
                            <File Id="POCOZIPMDD.LIB" Name="PocoZipmdd.lib" Source="$(var.POCO)\lib\PocoZipmdd.lib" />
                            <File Id="POCOZIPMDD.PDB" Name="pocozipmdd.pdb" Source="$(var.POCO)\lib\pocozipmdd.pdb" />
                            <File Id="POCOZIPMTD.LIB" Name="PocoZipmtd.lib" Source="$(var.POCO)\lib\PocoZipmtd.lib" />
                            <File Id="POCOZIPMTD.PDB" Name="pocozipmtd.pdb" Source="$(var.POCO)\lib\pocozipmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip.static.release" Guid="254BFA6F-12B6-421E-8C82-753FD63BD263">
                            <File Id="POCOZIPMD.LIB" Name="PocoZipmd.lib" Source="$(var.POCO)\lib\PocoZipmd.lib" />
                            <File Id="POCOZIPMT.LIB" Name="PocoZipmt.lib" Source="$(var.POCO)\lib\PocoZipmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit.static.debug" Guid="9E600FA9-1051-44DE-A239-B78520160017">
                            <File Id="CPPUNITMDD.LIB" Name="PocoCppUnitmdd.lib" Source="$(var.POCO)\lib\PocoCppUnitmdd.lib" />
                            <File Id="CPPUNITMDD.PDB" Name="PocoCppUnitmdd.pdb" Source="$(var.POCO)\lib\PocoCppUnitmdd.pdb" />
                            <File Id="CPPUNITMTD.LIB" Name="PocoCppUnitmtd.lib" Source="$(var.POCO)\lib\PocoCppUnitmtd.lib" />
                            <File Id="CPPUNITMTD.PDB" Name="PocoCppUnitmtd.pdb" Source="$(var.POCO)\lib\PocoCppUnitmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit.static.release" Guid="14E1151E-C40C-43C3-A6B8-9AD8B20E83C5">
                            <File Id="CPPUNITMD.LIB" Name="PocoCppUnitmd.lib" Source="$(var.POCO)\lib\PocoCppUnitmd.lib" />
                            <File Id="CPPUNITMT.LIB" Name="PocoCppUnitmt.lib" Source="$(var.POCO)\lib\PocoCppUnitmt.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF.static.debug" Guid="382A3D4B-2C3A-4427-B52A-96EE10F2C6F9">
                            <File Id="POCOPDFMDD.LIB" Name="PocoPDFmdd.lib" Source="$(var.POCO)\lib\PocoPDFmdd.lib" />
                            <File Id="POCOPDFMDD.PDB" Name="pocopdfmdd.pdb" Source="$(var.POCO)\lib\pocopdfmdd.pdb" />
                            <File Id="POCOPDFMTD.LIB" Name="PocoPDFmtd.lib" Source="$(var.POCO)\lib\PocoPDFmtd.lib" />
                            <File Id="POCOPDFMTD.PDB" Name="pocopdfmtd.pdb" Source="$(var.POCO)\lib\pocopdfmtd.pdb" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF.static.release" Guid="452E43B6-B81D-4719-A2C5-9AA291223678">
                            <File Id="POCOPDFMD.LIB" Name="PocoPDFmd.lib" Source="$(var.POCO)\lib\PocoPDFmd.lib" />
                            <File Id="POCOPDFMT.LIB" Name="PocoPDFmt.lib" Source="$(var.POCO)\lib\PocoPDFmt.lib" />
                        </Component>
                        <?endif ?>
                        <?if $(var.Platform) = x64 ?>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit64.import.debug" DiskId="1" Guid="5F3ACDD1-B092-46F4-AC0F-115972C57102">
                            <File Id="CPPUNIT64D.LIB" Name="PocoCppUnit64d.lib" Source="$(var.POCO)\lib64\PocoCppUnitd.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit64.import.release" DiskId="1" Guid="EE55C781-8DF5-49D3-9940-D7689590D0CE">
                            <File Id="CPPUNIT64.LIB" Name="PocoCppUnit64.lib" Source="$(var.POCO)\lib64\PocoCppUnit.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto64.import.debug" DiskId="1" Guid="30A5F403-5C87-4854-98A8-E60801CB52F4">
                            <File Id="POCOCRYPTO64D.LIB" Name="PocoCrypto64d.lib" Source="$(var.POCO)\lib64\PocoCryptod.lib" />
                            <File Id="POCOCRYPTO64D.EXP" Name="PocoCrypto64d.exp" Source="$(var.POCO)\lib64\PocoCryptod.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto64.import.release" DiskId="1" Guid="87F889FB-7182-41D1-8061-AA9451AEFD6A">
                            <File Id="POCOCRYPTO64.LIB" Name="PocoCrypto64.lib" Source="$(var.POCO)\lib64\PocoCrypto.lib" />
                            <File Id="POCOCRYPTO64.EXP" Name="PocoCrypto64.exp" Source="$(var.POCO)\lib64\PocoCrypto.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data64.import.debug" DiskId="1" Guid="EA078D0F-B952-457A-BF21-2EAE24785FC2">
                            <File Id="POCODATA64D.LIB" Name="PocoData64d.lib" Source="$(var.POCO)\lib64\PocoDatad.lib" />
                            <File Id="POCODATA64D.EXP" Name="PocoData64d.exp" Source="$(var.POCO)\lib64\PocoDatad.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data64.import.release" DiskId="1" Guid="46E7C8B7-1AAC-4C54-A57A-BAE0C5BBEE90">
                            <File Id="POCODATA64.LIB" Name="PocoData64.lib" Source="$(var.POCO)\lib64\PocoData.lib" />
                            <File Id="POCODATA64.EXP" Name="PocoData64.exp" Source="$(var.POCO)\lib64\PocoData.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL64.import.debug" DiskId="1" Guid="802A3BF2-E30F-41FE-A361-476DBCA54B1F">
                            <File Id="POCODATAMYSQL64D.LIB" Name="PocoDataMySQL64d.lib" Source="$(var.POCO)\lib64\PocoDataMySQLd.lib" />
                            <File Id="POCODATAMYSQL64D.EXP" Name="PocoDataMySQL64d.exp" Source="$(var.POCO)\lib64\PocoDataMySQLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL64.import.release" DiskId="1" Guid="63C83E6F-9199-4569-BA9A-79341A03F9C7">
                            <File Id="POCODATAMYSQL64.LIB" Name="PocoDataMySQL64.lib" Source="$(var.POCO)\lib64\PocoDataMySQL.lib" />
                            <File Id="POCODATAMYSQL64.EXP" Name="PocoDataMySQL64.exp" Source="$(var.POCO)\lib64\PocoDataMySQL.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC64.import.debug" DiskId="1" Guid="296C258E-8D80-4151-9514-078207D37C35">
                            <File Id="POCODATAODBC64D.LIB" Name="PocoDataODBC64d.lib" Source="$(var.POCO)\lib64\PocoDataODBCd.lib" />
                            <File Id="POCODATAODBC64D.EXP" Name="PocoDataODBC64d.exp" Source="$(var.POCO)\lib64\PocoDataODBCd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC64.import.release" DiskId="1" Guid="B1CF4921-06C2-4580-9B33-1E152CE3DC83">
                            <File Id="POCODATAODBC64.LIB" Name="PocoDataODBC64.lib" Source="$(var.POCO)\lib64\PocoDataODBC.lib" />
                            <File Id="POCODATAODBC64.EXP" Name="PocoDataODBC64.exp" Source="$(var.POCO)\lib64\PocoDataODBC.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite64.import.debug" DiskId="1" Guid="C912C195-FAB7-450B-B13A-FA7784635081">
                            <File Id="POCODATASQLITE64D.LIB" Name="PocoDataSQLite64d.lib" Source="$(var.POCO)\lib64\PocoDataSQLited.lib" />
                            <File Id="POCODATASQLITE64D.EXP" Name="PocoDataSQLite64d.exp" Source="$(var.POCO)\lib64\PocoDataSQLited.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite64.import.release" DiskId="1" Guid="5CCC88D5-362E-4681-A218-74A750BA4C0D">
                            <File Id="POCODATASQLITE64.LIB" Name="PocoDataSQLite64.lib" Source="$(var.POCO)\lib64\PocoDataSQLite.lib" />
                            <File Id="POCODATASQLITE64.EXP" Name="PocoDataSQLite64.exp" Source="$(var.POCO)\lib64\PocoDataSQLite.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL64.import.debug" DiskId="1" Guid="ED0E2BDB-6357-464F-9476-CFA5B7FF2BF4">
                            <File Id="POCODATAPOSTGRESQL64D.LIB" Name="PocoDataPostgreSQL64d.lib" Source="$(var.POCO)\lib64\PocoDataPostgreSQLd.lib" />
                            <File Id="POCODATAPOSTGRESQL64D.EXP" Name="PocoDataPostgreSQL64d.exp" Source="$(var.POCO)\lib64\PocoDataPostgreSQLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL64.import.release" DiskId="1" Guid="6B0B3ACC-5386-4C26-BC9C-FA338CF48317">
                            <File Id="POCODataPostgreSQL64.LIB" Name="PocoDataPostgreSQL64.lib" Source="$(var.POCO)\lib64\PocoDataPostgreSQL.lib" />
                            <File Id="POCODataPostgreSQL64.EXP" Name="PocoDataPostgreSQL64.exp" Source="$(var.POCO)\lib64\PocoDataPostgreSQL.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation64.import.debug" DiskId="1" Guid="3A02770F-522D-44D5-8A84-B66F8B486311">
                            <File Id="POCOFOUNDATION64D.LIB" Name="PocoFoundation64d.lib" Source="$(var.POCO)\lib64\PocoFoundationd.lib" />
                            <File Id="POCOFOUNDATION64D.EXP" Name="PocoFoundation64d.exp" Source="$(var.POCO)\lib64\PocoFoundationd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation64.import.release" DiskId="1" Guid="9491AEB1-097B-461A-8152-D9140E201246">
                            <File Id="POCOFOUNDATION64.LIB" Name="PocoFoundation64.lib" Source="$(var.POCO)\lib64\PocoFoundation.lib" />
                            <File Id="POCOFOUNDATION64.EXP" Name="PocoFoundation64.exp" Source="$(var.POCO)\lib64\PocoFoundation.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON64.import.debug" DiskId="1" Guid="8DFDA9F2-AC3E-4732-8AA3-5FF244651A49">
                            <File Id="POCOJSON64D.LIB" Name="PocoJSON64d.lib" Source="$(var.POCO)\lib64\PocoJSONd.lib" />
                            <File Id="POCOJSON64D.EXP" Name="PocoJSON64d.exp" Source="$(var.POCO)\lib64\PocoJSONd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON64.import.release" DiskId="1" Guid="92E68D7E-8DC7-47C9-92E7-B83F24535419">
                            <File Id="POCOJSON64.LIB" Name="PocoJSON64.lib" Source="$(var.POCO)\lib64\PocoJSON.lib" />
                            <File Id="POCOJSON64.EXP" Name="PocoJSON64.exp" Source="$(var.POCO)\lib64\PocoJSON.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB64.import.debug" DiskId="1" Guid="00E3841D-9BF5-4466-B892-E4766E7E8910">
                            <File Id="POCOMONGODB64D.LIB" Name="PocoMongoDB64d.lib" Source="$(var.POCO)\lib64\PocoMongoDBd.lib" />
                            <File Id="POCOMONGODB64D.EXP" Name="PocoMongoDB64d.exp" Source="$(var.POCO)\lib64\PocoMongoDBd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB64.import.release" DiskId="1" Guid="085BCF20-1607-4449-BD3B-F6A67C583205">
                            <File Id="POCOMONGODB64.LIB" Name="PocoMongoDB64.lib" Source="$(var.POCO)\lib64\PocoMongoDB.lib" />
                            <File Id="POCOMONGODB64.EXP" Name="PocoMongoDB64.exp" Source="$(var.POCO)\lib64\PocoMongoDB.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net64.import.debug" DiskId="1" Guid="1854F2A6-1135-470C-B839-13E7E60FF8AE">
                            <File Id="POCONET64D.LIB" Name="PocoNet64d.lib" Source="$(var.POCO)\lib64\PocoNetd.lib" />
                            <File Id="POCONET64D.EXP" Name="PocoNet64d.exp" Source="$(var.POCO)\lib64\PocoNetd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net64.import.release" DiskId="1" Guid="7D58D986-6230-4230-89B3-D0864AB429C0">
                            <File Id="POCONET64.LIB" Name="PocoNet64.lib" Source="$(var.POCO)\lib64\PocoNet.lib" />
                            <File Id="POCONET64.EXP" Name="PocoNet64.exp" Source="$(var.POCO)\lib64\PocoNet.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL64.import.debug" DiskId="1" Guid="8B63193A-344B-481C-888D-2C917DFB954E">
                            <File Id="POCONETSS64LD.LIB" Name="PocoNetSSL64d.lib" Source="$(var.POCO)\lib64\PocoNetSSLd.lib" />
                            <File Id="POCONETSS64LD.EXP" Name="PocoNetSSL64d.exp" Source="$(var.POCO)\lib64\PocoNetSSLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL64.import.release" DiskId="1" Guid="476994D9-E766-4799-A21B-3CF693AF6940">
                            <File Id="POCONETSS64L.LIB" Name="PocoNetSSL64.lib" Source="$(var.POCO)\lib64\PocoNetSSL.lib" />
                            <File Id="POCONETSS64L.EXP" Name="PocoNetSSL64.exp" Source="$(var.POCO)\lib64\PocoNetSSL.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin64.import.debug" DiskId="1" Guid="EDE4562D-D066-453D-A76B-D7148F9F5763">
                            <File Id="POCONETSSLWIN64D.LIB" Name="PocoNetSSLWin64d.lib" Source="$(var.POCO)\lib64\PocoNetSSLWind.lib" />
                            <File Id="POCONETSSLWIN64D.EXP" Name="PocoNetSSLWin64d.exp" Source="$(var.POCO)\lib64\PocoNetSSLWind.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin64.import.release" DiskId="1" Guid="ED599741-9F6E-4A19-9395-21AA02B7548F">
                            <File Id="POCONETSSLWIN64.LIB" Name="PocoNetSSLWin64.lib" Source="$(var.POCO)\lib64\PocoNetSSLWin.lib" />
                            <File Id="POCONETSSLWIN64.EXP" Name="PocoNetSSLWin64.exp" Source="$(var.POCO)\lib64\PocoNetSSLWin.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF64.import.debug" DiskId="1" Guid="88B7AE34-F032-4125-811B-F21101EE2083">
                            <File Id="POCOPDF64D.EXP" Name="PocoPDF64d.exp" Source="$(var.POCO)\lib64\PocoPDFd.exp" />
                            <File Id="POCOPDF64D.LIB" Name="PocoPDF64d.lib" Source="$(var.POCO)\lib64\PocoPDFd.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF64.import.release" DiskId="1" Guid="7934A004-5AAE-42F8-A673-8355F8232C2C">
                            <File Id="POCOPDF64.EXP" Name="PocoPDF64.exp" Source="$(var.POCO)\lib64\PocoPDF.exp" />
                            <File Id="POCOPDF64.LIB" Name="PocoPDF64.lib" Source="$(var.POCO)\lib64\PocoPDF.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="UTIL64.import.debug" DiskId="1" Guid="CE4F7999-39DC-4670-B2DF-B4CD68FD47E3">
                            <File Id="POCOUTIL64D.LIB" Name="PocoUtil64d.lib" Source="$(var.POCO)\lib64\PocoUtild.lib" />
                            <File Id="POCOUTIL64D.EXP" Name="PocoUtil64d.exp" Source="$(var.POCO)\lib64\PocoUtild.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="UTIL64.import.release" DiskId="1" Guid="37A7AE32-77CB-4306-84E0-94FAB364B662">
                            <File Id="POCOUTIL64.LIB" Name="PocoUtil64.lib" Source="$(var.POCO)\lib64\PocoUtil.lib" />
                            <File Id="POCOUTIL64.EXP" Name="PocoUtil64.exp" Source="$(var.POCO)\lib64\PocoUtil.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML64.import.debug" DiskId="1" Guid="B8F8C798-BC86-41EE-B349-B6B5F42BDC3A">
                            <File Id="POCOXML64D.LIB" Name="PocoXML64d.lib" Source="$(var.POCO)\lib64\PocoXMLd.lib" />
                            <File Id="POCOXML64D.EXP" Name="PocoXML64d.exp" Source="$(var.POCO)\lib64\PocoXMLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML64.import.release" DiskId="1" Guid="0308D11C-76F3-4280-8297-331A54CE827C">
                            <File Id="POCOXML64.LIB" Name="PocoXML64.lib" Source="$(var.POCO)\lib64\PocoXML.lib" />
                            <File Id="POCOXML64.EXP" Name="PocoXML64.exp" Source="$(var.POCO)\lib64\PocoXML.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="ZIP64.import.debug" DiskId="1" Guid="41304D78-5EB3-4E09-8A30-EF12FED62595">
                            <File Id="POCOZIP64D.LIB" Name="PocoZip64d.lib" Source="$(var.POCO)\lib64\PocoZipd.lib" />
                            <File Id="POCOZIP64D.EXP" Name="PocoZip64d.exp" Source="$(var.POCO)\lib64\PocoZipd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="ZIP64.import.release" DiskId="1" Guid="2B2880DE-A33F-45EA-858D-613D505AAC1E">
                            <File Id="POCOZIP64.LIB" Name="PocoZip64.lib" Source="$(var.POCO)\lib64\PocoZip.lib" />
                            <File Id="POCOZIP64.EXP" Name="PocoZip64.exp" Source="$(var.POCO)\lib64\PocoZip.exp" />
                        </Component>
                        <?else ?>
                        <Component Win64="$(var.Win64)" Id="Foundation.import.debug" Guid="6080BF06-94CD-4DFA-ACAE-45B7F03007AC">
                            <File Id="POCOFOUNDATIOND.LIB" Name="PocoFoundationd.lib" Source="$(var.POCO)\lib\PocoFoundationd.lib" />
                            <File Id="POCOFOUNDATIOND.EXP" Name="PocoFoundationd.exp" Source="$(var.POCO)\lib\PocoFoundationd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Foundation.import.release" Guid="7ADA6AEC-B09A-4B05-9E6E-8D83CA63FF0E">
                            <File Id="POCOFOUNDATION.LIB" Name="PocoFoundation.lib" Source="$(var.POCO)\lib\PocoFoundation.lib" />
                            <File Id="POCOFOUNDATION.EXP" Name="PocoFoundation.exp" Source="$(var.POCO)\lib\PocoFoundation.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net.import.debug" Guid="535C7C67-982D-4378-8CE7-7C602519C8C2">
                            <File Id="POCONETD.LIB" Name="PocoNetd.lib" Source="$(var.POCO)\lib\PocoNetd.lib" />
                            <File Id="POCONETD.EXP" Name="PocoNetd.exp" Source="$(var.POCO)\lib\PocoNetd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Net.import.release" Guid="959714F7-144A-475C-BC19-E0CD83515D7F">
                            <File Id="POCONET.LIB" Name="PocoNet.lib" Source="$(var.POCO)\lib\PocoNet.lib" />
                            <File Id="POCONET.EXP" Name="PocoNet.exp" Source="$(var.POCO)\lib\PocoNet.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util.import.debug" Guid="B7759A2C-403E-417F-AF5C-AFF5D51B7445">
                            <File Id="POCOUTILD.LIB" Name="PocoUtild.lib" Source="$(var.POCO)\lib\PocoUtild.lib" />
                            <File Id="POCOUTILD.EXP" Name="PocoUtild.exp" Source="$(var.POCO)\lib\PocoUtild.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Util.import.release" Guid="EFB7A20D-71C3-431F-A1E7-B01804E05D83">
                            <File Id="POCOUTIL.LIB" Name="PocoUtil.lib" Source="$(var.POCO)\lib\PocoUtil.lib" />
                            <File Id="POCOUTIL.EXP" Name="PocoUtil.exp" Source="$(var.POCO)\lib\PocoUtil.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML.import.debug" Guid="65917410-82FA-4156-9516-F128DA18770C">
                            <File Id="POCOXMLD.LIB" Name="PocoXMLd.lib" Source="$(var.POCO)\lib\PocoXMLd.lib" />
                            <File Id="POCOXMLD.EXP" Name="PocoXMLd.exp" Source="$(var.POCO)\lib\PocoXMLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="XML.import.release" Guid="8171ED68-C59C-4EDB-9C96-1A9DF5E0BF6E">
                            <File Id="POCOXML.LIB" Name="PocoXML.lib" Source="$(var.POCO)\lib\PocoXML.lib" />
                            <File Id="POCOXML.EXP" Name="PocoXML.exp" Source="$(var.POCO)\lib\PocoXML.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON.import.debug" Guid="85ED0732-E1BE-49D3-8828-A5DDDC3FF044">
                            <File Id="POCOJSOND.LIB" Name="PocoJSONd.lib" Source="$(var.POCO)\lib\PocoJSONd.lib" />
                            <File Id="POCOJSOND.EXP" Name="PocoJSONd.exp" Source="$(var.POCO)\lib\PocoJSONd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="JSON.import.release" Guid="EB6DEE26-35B0-4D1F-8CE4-931027D4CFB4">
                            <File Id="POCOJSON.LIB" Name="PocoJSON.lib" Source="$(var.POCO)\lib\PocoJSON.lib" />
                            <File Id="POCOJSON.EXP" Name="PocoJSON.exp" Source="$(var.POCO)\lib\PocoJSON.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL.import.debug" Guid="D14B1DD8-FD2A-40D4-9A2C-BA89C174BD71">
                            <File Id="POCONETSSLD.LIB" Name="PocoNetSSLd.lib" Source="$(var.POCO)\lib\PocoNetSSLd.lib" />
                            <File Id="POCONETSSLD.EXP" Name="PocoNetSSLd.exp" Source="$(var.POCO)\lib\PocoNetSSLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSL.import.release" Guid="D5F40B19-B686-4D74-9A02-A181670711BC">
                            <File Id="POCONETSSL.LIB" Name="PocoNetSSL.lib" Source="$(var.POCO)\lib\PocoNetSSL.lib" />
                            <File Id="POCONETSSL.EXP" Name="PocoNetSSL.exp" Source="$(var.POCO)\lib\PocoNetSSL.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB.import.debug" Guid="0BB44E06-A442-4481-9412-2F5CB7A0606C">
                            <File Id="POCOMONGODBD.LIB" Name="PocoMongoDBd.lib" Source="$(var.POCO)\lib\PocoMongoDBd.lib" />
                            <File Id="POCOMONGODBD.EXP" Name="PocoMongoDBd.exp" Source="$(var.POCO)\lib\PocoMongoDBd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="MongoDB.import.release" Guid="4266DB42-8F2B-4013-8846-D19FDB30C92E">
                            <File Id="POCOMONGODB.LIB" Name="PocoMongoDB.lib" Source="$(var.POCO)\lib\PocoMongoDB.lib" />
                            <File Id="POCOMONGODB.EXP" Name="PocoMongoDB.exp" Source="$(var.POCO)\lib\PocoMongoDB.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto.import.debug" Guid="BD70B6BE-72FB-48B2-B75E-18913EAB0D0F">
                            <File Id="POCOCRYPTOD.LIB" Name="PocoCryptod.lib" Source="$(var.POCO)\lib\PocoCryptod.lib" />
                            <File Id="POCOCRYPTOD.EXP" Name="PocoCryptod.exp" Source="$(var.POCO)\lib\PocoCryptod.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Crypto.import.release" Guid="155A2BD8-37B9-4F4D-A073-685120D97627">
                            <File Id="POCOCRYPTO.LIB" Name="PocoCrypto.lib" Source="$(var.POCO)\lib\PocoCrypto.lib" />
                            <File Id="POCOCRYPTO.EXP" Name="PocoCrypto.exp" Source="$(var.POCO)\lib\PocoCrypto.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data.import.debug" Guid="C6800C3E-5DA3-4E95-BA55-51273A40802F">
                            <File Id="POCODATAD.LIB" Name="PocoDatad.lib" Source="$(var.POCO)\lib\PocoDatad.lib" />
                            <File Id="POCODATAD.EXP" Name="PocoDatad.exp" Source="$(var.POCO)\lib\PocoDatad.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Data.import.release" Guid="6BC5C136-200C-45C2-98B8-499B1F610953">
                            <File Id="POCODATA.LIB" Name="PocoData.lib" Source="$(var.POCO)\lib\PocoData.lib" />
                            <File Id="POCODATA.EXP" Name="PocoData.exp" Source="$(var.POCO)\lib\PocoData.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL.import.debug" DiskId="1" Guid="1214743F-71ED-4C93-93C7-83D8305C779B">
                            <File Id="POCODATAMYSQLD.LIB" Name="PocoDataMySQLd.lib" Source="$(var.POCO)\lib\PocoDataMySQLd.lib" />
                            <File Id="POCODATAMYSQLD.EXP" Name="PocoDataMySQLd.exp" Source="$(var.POCO)\lib\PocoDataMySQLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataMySQL.import.release" DiskId="1" Guid="3B5BAA9C-D812-4D89-B51F-BDA7D82CC9F2">
                            <File Id="POCODATAMYSQL.LIB" Name="PocoDataMySQL.lib" Source="$(var.POCO)\lib\PocoDataMySQL.lib" />
                            <File Id="POCODATAMYSQL.EXP" Name="PocoDataMySQL.exp" Source="$(var.POCO)\lib\PocoDataMySQL.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC.import.debug" Guid="524788E7-5098-4C05-937A-6963166E524E">
                            <File Id="POCODATAODBCD.LIB" Name="PocoDataODBCd.lib" Source="$(var.POCO)\lib\PocoDataODBCd.lib" />
                            <File Id="POCODATAODBCD.EXP" Name="PocoDataODBCd.exp" Source="$(var.POCO)\lib\PocoDataODBCd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataODBC.import.release" Guid="D2E0924A-97E0-4A3B-BCAF-14371B16F87E">
                            <File Id="POCODATAODBC.LIB" Name="PocoDataODBC.lib" Source="$(var.POCO)\lib\PocoDataODBC.lib" />
                            <File Id="POCODATAODBC.EXP" Name="PocoDataODBC.exp" Source="$(var.POCO)\lib\PocoDataODBC.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite.import.debug" Guid="4EF65086-731F-4213-93C4-0E5F477A50F3">
                            <File Id="POCODATASQLITED.LIB" Name="PocoDataSQLited.lib" Source="$(var.POCO)\lib\PocoDataSQLited.lib" />
                            <File Id="POCODATASQLITED.EXP" Name="PocoDataSQLited.exp" Source="$(var.POCO)\lib\PocoDataSQLited.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataSQLite.import.release" Guid="5266FB89-8A5D-4FED-8667-185D03F5D44C">
                            <File Id="POCODATASQLITE.LIB" Name="PocoDataSQLite.lib" Source="$(var.POCO)\lib\PocoDataSQLite.lib" />
                            <File Id="POCODATASQLITE.EXP" Name="PocoDataSQLite.exp" Source="$(var.POCO)\lib\PocoDataSQLite.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL.import.debug" Guid="0542F84E-9346-498A-AD64-7173650E8F41">
                            <File Id="POCODATAPOSTGRESQLD.LIB" Name="PocoDataPostgreSQLd.lib" Source="$(var.POCO)\lib\PocoDataPostgreSQLd.lib" />
                            <File Id="POCODATAPOSTGRESQLD.EXP" Name="PocoDataPostgreSQLd.exp" Source="$(var.POCO)\lib\PocoDataPostgreSQLd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="DataPostgreSQL.import.release" Guid="EC681B9F-4AC6-40F8-A9E7-3BFF23315943">
                            <File Id="POCODATAPOSTGRESQL.LIB" Name="PocoDataPostgreSQL.lib" Source="$(var.POCO)\lib\PocoDataPostgreSQL.lib" />
                            <File Id="POCODATAPOSTGRESQL.EXP" Name="PocoDataPostgreSQL.exp" Source="$(var.POCO)\lib\PocoDataPostgreSQL.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin.import.debug" Guid="A15DE4D9-A7C7-4D6C-AB6A-D62206B959EA">
                            <File Id="POCONETSSLWIND.LIB" Name="PocoNetSSLWind.lib" Source="$(var.POCO)\lib\PocoNetSSLWind.lib" />
                            <File Id="POCONETSSLWIND.EXP" Name="PocoNetSSLWind.exp" Source="$(var.POCO)\lib\PocoNetSSLWind.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NetSSLWin.import.release" Guid="1CF84BD5-D15B-43C2-B040-38A81B9DC3C0">
                            <File Id="POCONETSSLWIN.LIB" Name="PocoNetSSLWin.lib" Source="$(var.POCO)\lib\PocoNetSSLWin.lib" />
                            <File Id="POCONETSSLWIN.EXP" Name="PocoNetSSLWin.exp" Source="$(var.POCO)\lib\PocoNetSSLWin.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF.import.debug" DiskId="1" Guid="6B8D577F-96B3-4830-ABE9-572B4F40B6EA">
                            <File Id="POCOPDFD.EXP" Name="PocoPDFd.exp" Source="$(var.POCO)\lib\PocoPDFd.exp" />
                            <File Id="POCOPDFD.LIB" Name="PocoPDFd.lib" Source="$(var.POCO)\lib\PocoPDFd.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PDF.import.release" DiskId="1" Guid="71DE13BC-F4CB-4570-B94D-ACC346307149">
                            <File Id="POCOPDF.EXP" Name="PocoPDF.exp" Source="$(var.POCO)\lib\PocoPDF.exp" />
                            <File Id="POCOPDF.LIB" Name="PocoPDF.lib" Source="$(var.POCO)\lib\PocoPDF.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip.import.debug" Guid="4C70922F-39D4-4ABD-9B85-AC4B44F9AA59">
                            <File Id="POCOZIPD.LIB" Name="PocoZipd.lib" Source="$(var.POCO)\lib\PocoZipd.lib" />
                            <File Id="POCOZIPD.EXP" Name="PocoZipd.exp" Source="$(var.POCO)\lib\PocoZipd.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="Zip.import.release" Guid="53596152-D7B0-40A2-A237-74D2352C6117">
                            <File Id="POCOZIP.LIB" Name="PocoZip.lib" Source="$(var.POCO)\lib\PocoZip.lib" />
                            <File Id="POCOZIP.EXP" Name="PocoZip.exp" Source="$(var.POCO)\lib\PocoZip.exp" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit.import.debug" Guid="4C4E3549-53BE-46AF-8B04-A6FC29B91165">
                            <File Id="CPPUNITD.LIB" Name="PocoCppUnitd.lib" Source="$(var.POCO)\lib\PocoCppUnitd.lib" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PocoCppUnit.import.release" Guid="D5187EFC-E0F7-4FA7-9A61-4D822B681631">
                            <File Id="CPPUNIT.LIB" Name="PocoCppUnit.lib" Source="$(var.POCO)\lib\PocoCppUnit.lib" />
                        </Component>
                        <?endif ?>
                    </Directory>
                    <Directory Id="inc" Name="inc">
                        <Directory Id="POCO" Name="Poco">
                            <Component Win64="$(var.Win64)" Id="Poco.inc" DiskId="1" Guid="2446CCCB-43D3-49D6-B55E-840F2053B301">
                                <File Id="ABSTRACTCACHE.H" Name="AbstractCache.h" Source="$(var.POCO)\Foundation\include\Poco\AbstractCache.h" />
                                <File Id="ABSTRACTDELEGATE.H" Name="AbstractDelegate.h" Source="$(var.POCO)\Foundation\include\Poco\AbstractDelegate.h" />
                                <File Id="ABSTRACTEVENT.H" Name="AbstractEvent.h" Source="$(var.POCO)\Foundation\include\Poco\AbstractEvent.h" />
                                <File Id="ABSTRACTOBSERVER.H" Name="AbstractObserver.h" Source="$(var.POCO)\Foundation\include\Poco\AbstractObserver.h" />
                                <File Id="ABSTRACTPRIORITYDELEGATE.H" Name="AbstractPriorityDelegate.h" Source="$(var.POCO)\Foundation\include\Poco\AbstractPriorityDelegate.h" />
                                <File Id="ABSTRACTSTRATEGY.H" Name="AbstractStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\AbstractStrategy.h" />
                                <File Id="ACCESSEXPIRATIONDECORATOR.H" Name="AccessExpirationDecorator.h" Source="$(var.POCO)\Foundation\include\Poco\AccessExpirationDecorator.h" />
                                <File Id="ACCESSEXPIRECACHE.H" Name="AccessExpireCache.h" Source="$(var.POCO)\Foundation\include\Poco\AccessExpireCache.h" />
                                <File Id="ACCESSEXPIRELRUCACHE.H" Name="AccessExpireLRUCache.h" Source="$(var.POCO)\Foundation\include\Poco\AccessExpireLRUCache.h" />
                                <File Id="ACCESSEXPIRESTRATEGY.H" Name="AccessExpireStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\AccessExpireStrategy.h" />
                                <File Id="ACTIVEDISPATCHER.H" Name="ActiveDispatcher.h" Source="$(var.POCO)\Foundation\include\Poco\ActiveDispatcher.h" />
                                <File Id="ACTIVEMETHOD.H" Name="ActiveMethod.h" Source="$(var.POCO)\Foundation\include\Poco\ActiveMethod.h" />
                                <File Id="ACTIVERESULT.H" Name="ActiveResult.h" Source="$(var.POCO)\Foundation\include\Poco\ActiveResult.h" />
                                <File Id="ACTIVERUNNABLE.H" Name="ActiveRunnable.h" Source="$(var.POCO)\Foundation\include\Poco\ActiveRunnable.h" />
                                <File Id="ACTIVESTARTER.H" Name="ActiveStarter.h" Source="$(var.POCO)\Foundation\include\Poco\ActiveStarter.h" />
                                <File Id="ACTIVITY.H" Name="Activity.h" Source="$(var.POCO)\Foundation\include\Poco\Activity.h" />
                                <File Id="ALIGNMENT.H" Name="Alignment.h" Source="$(var.POCO)\Foundation\include\Poco\Alignment.h" />
                                <File Id="ANY.H" Name="Any.h" Source="$(var.POCO)\Foundation\include\Poco\Any.h" />
                                <File Id="ARCHIVESTRATEGY.H" Name="ArchiveStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\ArchiveStrategy.h" />
                                <File Id="ARRAY.H" Name="Array.h" Source="$(var.POCO)\Foundation\include\Poco\Array.h" />
                                <File Id="ASCII.H" Name="Ascii.h" Source="$(var.POCO)\Foundation\include\Poco\Ascii.h" />
                                <File Id="ASCIIENCODING.H" Name="ASCIIEncoding.h" Source="$(var.POCO)\Foundation\include\Poco\ASCIIEncoding.h" />
                                <File Id="ASYNCCHANNEL.H" Name="AsyncChannel.h" Source="$(var.POCO)\Foundation\include\Poco\AsyncChannel.h" />
                                <File Id="ATOMICCOUNTER.H" Name="AtomicCounter.h" Source="$(var.POCO)\Foundation\include\Poco\AtomicCounter.h" />
                                <File Id="AUTOPTR.H" Name="AutoPtr.h" Source="$(var.POCO)\Foundation\include\Poco\AutoPtr.h" />
                                <File Id="AUTORELEASEPOOL.H" Name="AutoReleasePool.h" Source="$(var.POCO)\Foundation\include\Poco\AutoReleasePool.h" />
                                <File Id="BASE32DECODER.H" Name="Base32Decoder.h" Source="$(var.POCO)\Foundation\include\Poco\Base32Decoder.h" />
                                <File Id="BASE32ENCODER.H" Name="Base32Encoder.h" Source="$(var.POCO)\Foundation\include\Poco\Base32Encoder.h" />
                                <File Id="BASE64DECODER.H" Name="Base64Decoder.h" Source="$(var.POCO)\Foundation\include\Poco\Base64Decoder.h" />
                                <File Id="BASE64ENCODER.H" Name="Base64Encoder.h" Source="$(var.POCO)\Foundation\include\Poco\Base64Encoder.h" />
                                <File Id="BASICEVENT.H" Name="BasicEvent.h" Source="$(var.POCO)\Foundation\include\Poco\BasicEvent.h" />
                                <File Id="BINARYREADER.H" Name="BinaryReader.h" Source="$(var.POCO)\Foundation\include\Poco\BinaryReader.h" />
                                <File Id="BINARYWRITER.H" Name="BinaryWriter.h" Source="$(var.POCO)\Foundation\include\Poco\BinaryWriter.h" />
                                <File Id="BUFFER.H" Name="Buffer.h" Source="$(var.POCO)\Foundation\include\Poco\Buffer.h" />
                                <File Id="BUFFERALLOCATOR.H" Name="BufferAllocator.h" Source="$(var.POCO)\Foundation\include\Poco\BufferAllocator.h" />
                                <File Id="BUFFEREDBIDIRECTIONALSTREAMBUF.H" Name="BufferedBidirectionalStreamBuf.h" Source="$(var.POCO)\Foundation\include\Poco\BufferedBidirectionalStreamBuf.h" />
                                <File Id="BUFFEREDSTREAMBUF.H" Name="BufferedStreamBuf.h" Source="$(var.POCO)\Foundation\include\Poco\BufferedStreamBuf.h" />
                                <File Id="BUGCHECK.H" Name="Bugcheck.h" Source="$(var.POCO)\Foundation\include\Poco\Bugcheck.h" />
                                <File Id="BYTEORDER.H" Name="ByteOrder.h" Source="$(var.POCO)\Foundation\include\Poco\ByteOrder.h" />
                                <File Id="CHANNEL.H" Name="Channel.h" Source="$(var.POCO)\Foundation\include\Poco\Channel.h" />
                                <File Id="CHECKSUM.H" Name="Checksum.h" Source="$(var.POCO)\Foundation\include\Poco\Checksum.h" />
                                <File Id="CLASSLIBRARY.H" Name="ClassLibrary.h" Source="$(var.POCO)\Foundation\include\Poco\ClassLibrary.h" />
                                <File Id="CLASSLOADER.H" Name="ClassLoader.h" Source="$(var.POCO)\Foundation\include\Poco\ClassLoader.h" />
                                <File Id="CLOCK.H" Name="Clock.h" Source="$(var.POCO)\Foundation\include\Poco\Clock.h" />
                                <File Id="CONDITION.H" Name="Condition.h" Source="$(var.POCO)\Foundation\include\Poco\Condition.h" />
                                <File Id="CONFIG.H" Name="Config.h" Source="$(var.POCO)\Foundation\include\Poco\Config.h" />
                                <File Id="CONFIGURABLE.H" Name="Configurable.h" Source="$(var.POCO)\Foundation\include\Poco\Configurable.h" />
                                <File Id="CONSOLECHANNEL.H" Name="ConsoleChannel.h" Source="$(var.POCO)\Foundation\include\Poco\ConsoleChannel.h" />
                                <File Id="COUNTINGSTREAM.H" Name="CountingStream.h" Source="$(var.POCO)\Foundation\include\Poco\CountingStream.h" />
                                <File Id="DATETIME.H" Name="DateTime.h" Source="$(var.POCO)\Foundation\include\Poco\DateTime.h" />
                                <File Id="DATETIMEFORMAT.H" Name="DateTimeFormat.h" Source="$(var.POCO)\Foundation\include\Poco\DateTimeFormat.h" />
                                <File Id="DATETIMEFORMATTER.H" Name="DateTimeFormatter.h" Source="$(var.POCO)\Foundation\include\Poco\DateTimeFormatter.h" />
                                <File Id="DATETIMEPARSER.H" Name="DateTimeParser.h" Source="$(var.POCO)\Foundation\include\Poco\DateTimeParser.h" />
                                <File Id="DEBUGGER.H" Name="Debugger.h" Source="$(var.POCO)\Foundation\include\Poco\Debugger.h" />
                                <File Id="DEFAULTSTRATEGY.H" Name="DefaultStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\DefaultStrategy.h" />
                                <File Id="DEFLATINGSTREAM.H" Name="DeflatingStream.h" Source="$(var.POCO)\Foundation\include\Poco\DeflatingStream.h" />
                                <File Id="DELEGATE.H" Name="Delegate.h" Source="$(var.POCO)\Foundation\include\Poco\Delegate.h" />
                                <File Id="DIGESTENGINE.H" Name="DigestEngine.h" Source="$(var.POCO)\Foundation\include\Poco\DigestEngine.h" />
                                <File Id="DIGESTSTREAM.H" Name="DigestStream.h" Source="$(var.POCO)\Foundation\include\Poco\DigestStream.h" />
                                <File Id="DIRECTORYITERATOR.H" Name="DirectoryIterator.h" Source="$(var.POCO)\Foundation\include\Poco\DirectoryIterator.h" />
                                <File Id="DIRECTORYITERATORSTRATEGY.H" Name="DirectoryIteratorStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\DirectoryIteratorStrategy.h" />
                                <File Id="DIRECTORYITERATOR_WIN32.H" Name="DirectoryIterator_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\DirectoryIterator_WIN32.h" />
                                <File Id="DIRECTORYWATCHER.H" Name="DirectoryWatcher.h" Source="$(var.POCO)\Foundation\include\Poco\DirectoryWatcher.h" />
                                <File Id="DYNAMICANY.H" Name="DynamicAny.h" Source="$(var.POCO)\Foundation\include\Poco\DynamicAny.h" />
                                <File Id="DYNAMICANYHOLDER.H" Name="DynamicAnyHolder.h" Source="$(var.POCO)\Foundation\include\Poco\DynamicAnyHolder.h" />
                                <File Id="DYNAMICFACTORY.H" Name="DynamicFactory.h" Source="$(var.POCO)\Foundation\include\Poco\DynamicFactory.h" />
                                <File Id="DYNAMICSTRUCT.H" Name="DynamicStruct.h" Source="$(var.POCO)\Foundation\include\Poco\DynamicStruct.h" />
                                <File Id="ENVIRONMENT.H" Name="Environment.h" Source="$(var.POCO)\Foundation\include\Poco\Environment.h" />
                                <File Id="ENVIRONMENT_WIN32.H" Name="Environment_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Environment_WIN32.h" />
                                <File Id="ERROR.H" Name="Error.h" Source="$(var.POCO)\Foundation\include\Poco\Error.h" />
                                <File Id="ERRORHANDLER.H" Name="ErrorHandler.h" Source="$(var.POCO)\Foundation\include\Poco\ErrorHandler.h" />
                                <File Id="EVENT.H" Name="Event.h" Source="$(var.POCO)\Foundation\include\Poco\Event.h" />
                                <File Id="EVENT_WIN32.H" Name="Event_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Event_WIN32.h" />
                                <File Id="EVENTARGS.H" Name="EventArgs.h" Source="$(var.POCO)\Foundation\include\Poco\EventArgs.h" />
                                <File Id="EVENTLOGCHANNEL.H" Name="EventLogChannel.h" Source="$(var.POCO)\Foundation\include\Poco\EventLogChannel.h" />
                                <File Id="EXCEPTION.H" Name="Exception.h" Source="$(var.POCO)\Foundation\include\Poco\Exception.h" />
                                <File Id="EXPIRATIONDECORATOR.H" Name="ExpirationDecorator.h" Source="$(var.POCO)\Foundation\include\Poco\ExpirationDecorator.h" />
                                <File Id="EXPIRE.H" Name="Expire.h" Source="$(var.POCO)\Foundation\include\Poco\Expire.h" />
                                <File Id="EXPIRECACHE.H" Name="ExpireCache.h" Source="$(var.POCO)\Foundation\include\Poco\ExpireCache.h" />
                                <File Id="EXPIRELRUCACHE.H" Name="ExpireLRUCache.h" Source="$(var.POCO)\Foundation\include\Poco\ExpireLRUCache.h" />
                                <File Id="EXPIRESTRATEGY.H" Name="ExpireStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\ExpireStrategy.h" />
                                <File Id="FIFOBUFFER.H" Name="FIFOBuffer.h" Source="$(var.POCO)\Foundation\include\Poco\FIFOBuffer.h" />
                                <File Id="FIFOBUFFERSTREAM.H" Name="FIFOBufferStream.h" Source="$(var.POCO)\Foundation\include\Poco\FIFOBufferStream.h" />
                                <File Id="FIFOEVENT.H" Name="FIFOEvent.h" Source="$(var.POCO)\Foundation\include\Poco\FIFOEvent.h" />
                                <File Id="FIFOSTRATEGY.H" Name="FIFOStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\FIFOStrategy.h" />
                                <File Id="FILE.H" Name="File.h" Source="$(var.POCO)\Foundation\include\Poco\File.h" />
                                <File Id="FILECHANNEL.H" Name="FileChannel.h" Source="$(var.POCO)\Foundation\include\Poco\FileChannel.h" />
                                <File Id="FILESTREAM.H" Name="FileStream.h" Source="$(var.POCO)\Foundation\include\Poco\FileStream.h" />
                                <File Id="FILESTREAMFACTORY.H" Name="FileStreamFactory.h" Source="$(var.POCO)\Foundation\include\Poco\FileStreamFactory.h" />
                                <File Id="FILESTREAM_WIN32.H" Name="FileStream_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\FileStream_WIN32.h" />
                                <File Id="FILE_WIN32.H" Name="File_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\File_WIN32.h" />
                                <File Id="FORMAT.H" Name="Format.h" Source="$(var.POCO)\Foundation\include\Poco\Format.h" />
                                <File Id="FORMATTER.H" Name="Formatter.h" Source="$(var.POCO)\Foundation\include\Poco\Formatter.h" />
                                <File Id="FORMATTINGCHANNEL.H" Name="FormattingChannel.h" Source="$(var.POCO)\Foundation\include\Poco\FormattingChannel.h" />
                                <File Id="FOUNDATION.H" Name="Foundation.h" Source="$(var.POCO)\Foundation\include\Poco\Foundation.h" />
                                <File Id="FPENVIRONMENT.H" Name="FPEnvironment.h" Source="$(var.POCO)\Foundation\include\Poco\FPEnvironment.h" />
                                <File Id="FPENVIRONMENT_C99.H" Name="FPEnvironment_C99.h" Source="$(var.POCO)\Foundation\include\Poco\FPEnvironment_C99.h" />
                                <File Id="FPENVIRONMENT_WIN32.H" Name="FPEnvironment_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\FPEnvironment_WIN32.h" />
                                <File Id="FUNCTIONDELEGATE.H" Name="FunctionDelegate.h" Source="$(var.POCO)\Foundation\include\Poco\FunctionDelegate.h" />
                                <File Id="FUNCTIONPRIORITYDELEGATE.H" Name="FunctionPriorityDelegate.h" Source="$(var.POCO)\Foundation\include\Poco\FunctionPriorityDelegate.h" />
                                <File Id="GLOB.H" Name="Glob.h" Source="$(var.POCO)\Foundation\include\Poco\Glob.h" />
                                <File Id="HASH.H" Name="Hash.h" Source="$(var.POCO)\Foundation\include\Poco\Hash.h" />
                                <File Id="HASHFUNCTION.H" Name="HashFunction.h" Source="$(var.POCO)\Foundation\include\Poco\HashFunction.h" />
                                <File Id="HASHMAP.H" Name="HashMap.h" Source="$(var.POCO)\Foundation\include\Poco\HashMap.h" />
                                <File Id="HASHSET.H" Name="HashSet.h" Source="$(var.POCO)\Foundation\include\Poco\HashSet.h" />
                                <File Id="HASHSTATISTIC.H" Name="HashStatistic.h" Source="$(var.POCO)\Foundation\include\Poco\HashStatistic.h" />
                                <File Id="HASHTABLE.H" Name="HashTable.h" Source="$(var.POCO)\Foundation\include\Poco\HashTable.h" />
                                <File Id="HEXBINARYDECODER.H" Name="HexBinaryDecoder.h" Source="$(var.POCO)\Foundation\include\Poco\HexBinaryDecoder.h" />
                                <File Id="HEXBINARYENCODER.H" Name="HexBinaryEncoder.h" Source="$(var.POCO)\Foundation\include\Poco\HexBinaryEncoder.h" />
                                <File Id="HMACENGINE.H" Name="HMACEngine.h" Source="$(var.POCO)\Foundation\include\Poco\HMACEngine.h" />
                                <File Id="INFLATINGSTREAM.H" Name="InflatingStream.h" Source="$(var.POCO)\Foundation\include\Poco\InflatingStream.h" />
                                <File Id="INSTANTIATOR.H" Name="Instantiator.h" Source="$(var.POCO)\Foundation\include\Poco\Instantiator.h" />
                                <File Id="KEYVALUEARGS.H" Name="KeyValueArgs.h" Source="$(var.POCO)\Foundation\include\Poco\KeyValueArgs.h" />
                                <File Id="LATIN1ENCODING.H" Name="Latin1Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\Latin1Encoding.h" />
                                <File Id="LATIN2ENCODING.H" Name="Latin2Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\Latin2Encoding.h" />
                                <File Id="LATIN9ENCODING.H" Name="Latin9Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\Latin9Encoding.h" />
                                <File Id="LINEARHASHTABLE.H" Name="LinearHashTable.h" Source="$(var.POCO)\Foundation\include\Poco\LinearHashTable.h" />
                                <File Id="LINEENDINGCONVERTER.H" Name="LineEndingConverter.h" Source="$(var.POCO)\Foundation\include\Poco\LineEndingConverter.h" />
                                <File Id="LISTMAP.H" Name="ListMap.h" Source="$(var.POCO)\Foundation\include\Poco\ListMap.h" />
                                <File Id="LOCALDATETIME.H" Name="LocalDateTime.h" Source="$(var.POCO)\Foundation\include\Poco\LocalDateTime.h" />
                                <File Id="LOGFILE.H" Name="LogFile.h" Source="$(var.POCO)\Foundation\include\Poco\LogFile.h" />
                                <File Id="LOGFILE_STD.H" Name="LogFile_STD.h" Source="$(var.POCO)\Foundation\include\Poco\LogFile_STD.h" />
                                <File Id="LOGFILE_WIN32.H" Name="LogFile_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\LogFile_WIN32.h" />
                                <File Id="LOGGER.H" Name="Logger.h" Source="$(var.POCO)\Foundation\include\Poco\Logger.h" />
                                <File Id="LOGGINGFACTORY.H" Name="LoggingFactory.h" Source="$(var.POCO)\Foundation\include\Poco\LoggingFactory.h" />
                                <File Id="LOGGINGREGISTRY.H" Name="LoggingRegistry.h" Source="$(var.POCO)\Foundation\include\Poco\LoggingRegistry.h" />
                                <File Id="LOGSTREAM.H" Name="LogStream.h" Source="$(var.POCO)\Foundation\include\Poco\LogStream.h" />
                                <File Id="LRUCACHE.H" Name="LRUCache.h" Source="$(var.POCO)\Foundation\include\Poco\LRUCache.h" />
                                <File Id="LRUSTRATEGY.H" Name="LRUStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\LRUStrategy.h" />
                                <File Id="MANIFEST.H" Name="Manifest.h" Source="$(var.POCO)\Foundation\include\Poco\Manifest.h" />
                                <File Id="MD4ENGINE.H" Name="MD4Engine.h" Source="$(var.POCO)\Foundation\include\Poco\MD4Engine.h" />
                                <File Id="MD5ENGINE.H" Name="MD5Engine.h" Source="$(var.POCO)\Foundation\include\Poco\MD5Engine.h" />
                                <File Id="MEMORYPOOL.H" Name="MemoryPool.h" Source="$(var.POCO)\Foundation\include\Poco\MemoryPool.h" />
                                <File Id="MEMORYSTREAM.H" Name="MemoryStream.h" Source="$(var.POCO)\Foundation\include\Poco\MemoryStream.h" />
                                <File Id="MESSAGE.H" Name="Message.h" Source="$(var.POCO)\Foundation\include\Poco\Message.h" />
                                <File Id="METAOBJECT.H" Name="MetaObject.h" Source="$(var.POCO)\Foundation\include\Poco\MetaObject.h" />
                                <File Id="METAPROGRAMMING.H" Name="MetaProgramming.h" Source="$(var.POCO)\Foundation\include\Poco\MetaProgramming.h" />
                                <File Id="MUTEX.H" Name="Mutex.h" Source="$(var.POCO)\Foundation\include\Poco\Mutex.h" />
                                <File Id="MUTEX_WIN32.H" Name="Mutex_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Mutex_WIN32.h" />
                                <File Id="NAMEDEVENT.H" Name="NamedEvent.h" Source="$(var.POCO)\Foundation\include\Poco\NamedEvent.h" />
                                <File Id="NAMEDEVENT_ANDROID.H" Name="NamedEvent_Android.h" Source="$(var.POCO)\Foundation\include\Poco\NamedEvent_Android.h" />
                                <File Id="NAMEDEVENT_WIN32.H" Name="NamedEvent_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\NamedEvent_WIN32.h" />
                                <File Id="NAMEDMUTEX.H" Name="NamedMutex.h" Source="$(var.POCO)\Foundation\include\Poco\NamedMutex.h" />
                                <File Id="NAMEDMUTEX_ANDROID.H" Name="NamedMutex_Android.h" Source="$(var.POCO)\Foundation\include\Poco\NamedMutex_Android.h" />
                                <File Id="NAMEDMUTEX_WIN32.H" Name="NamedMutex_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\NamedMutex_WIN32.h" />
                                <File Id="NAMEDTUPLE.H" Name="NamedTuple.h" Source="$(var.POCO)\Foundation\include\Poco\NamedTuple.h" />
                                <File Id="NESTEDDIAGNOSTICCONTEXT.H" Name="NestedDiagnosticContext.h" Source="$(var.POCO)\Foundation\include\Poco\NestedDiagnosticContext.h" />
                                <File Id="NOBSERVER.H" Name="NObserver.h" Source="$(var.POCO)\Foundation\include\Poco\NObserver.h" />
                                <File Id="NOTIFICATION.H" Name="Notification.h" Source="$(var.POCO)\Foundation\include\Poco\Notification.h" />
                                <File Id="NOTIFICATIONCENTER.H" Name="NotificationCenter.h" Source="$(var.POCO)\Foundation\include\Poco\NotificationCenter.h" />
                                <File Id="NOTIFICATIONQUEUE.H" Name="NotificationQueue.h" Source="$(var.POCO)\Foundation\include\Poco\NotificationQueue.h" />
                                <File Id="NOTIFICATIONSTRATEGY.H" Name="NotificationStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\NotificationStrategy.h" />
                                <File Id="NULLABLE.H" Name="Nullable.h" Source="$(var.POCO)\Foundation\include\Poco\Nullable.h" />
                                <File Id="NULLCHANNEL.H" Name="NullChannel.h" Source="$(var.POCO)\Foundation\include\Poco\NullChannel.h" />
                                <File Id="NULLSTREAM.H" Name="NullStream.h" Source="$(var.POCO)\Foundation\include\Poco\NullStream.h" />
                                <File Id="NUMBERFORMATTER.H" Name="NumberFormatter.h" Source="$(var.POCO)\Foundation\include\Poco\NumberFormatter.h" />
                                <File Id="NUMBERPARSER.H" Name="NumberParser.h" Source="$(var.POCO)\Foundation\include\Poco\NumberParser.h" />
                                <File Id="NUMERICSTRING.H" Name="NumericString.h" Source="$(var.POCO)\Foundation\include\Poco\NumericString.h" />
                                <File Id="OBJECTPOOL.H" Name="ObjectPool.h" Source="$(var.POCO)\Foundation\include\Poco\ObjectPool.h" />
                                <File Id="OBSERVER.H" Name="Observer.h" Source="$(var.POCO)\Foundation\include\Poco\Observer.h" />
                                <File Id="OPTIONAL.H" Name="Optional.h" Source="$(var.POCO)\Foundation\include\Poco\Optional.h" />
                                <File Id="PATH.H" Name="Path.h" Source="$(var.POCO)\Foundation\include\Poco\Path.h" />
                                <File Id="PATH_WIN32.H" Name="Path_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Path_WIN32.h" />
                                <File Id="PATTERNFORMATTER.H" Name="PatternFormatter.h" Source="$(var.POCO)\Foundation\include\Poco\PatternFormatter.h" />
                                <File Id="PBKDF2ENGINE.H" Name="PBKDF2Engine.h" Source="$(var.POCO)\Foundation\include\Poco\PBKDF2Engine.h" />
                                <File Id="PIPE.H" Name="Pipe.h" Source="$(var.POCO)\Foundation\include\Poco\Pipe.h" />
                                <File Id="PIPEIMPL.H" Name="PipeImpl.h" Source="$(var.POCO)\Foundation\include\Poco\PipeImpl.h" />
                                <File Id="PIPEIMPL_WIN32.H" Name="PipeImpl_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\PipeImpl_WIN32.h" />
                                <File Id="PIPESTREAM.H" Name="PipeStream.h" Source="$(var.POCO)\Foundation\include\Poco\PipeStream.h" />
                                <File Id="PLATFORM.H" Name="Platform.h" Source="$(var.POCO)\Foundation\include\Poco\Platform.h" />
                                <File Id="PLATFORM_WIN32.H" Name="Platform_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Platform_WIN32.h" />
                                <File Id="POCO.H" Name="Poco.h" Source="$(var.POCO)\Foundation\include\Poco\Poco.h" />
                                <File Id="PRIORITYDELEGATE.H" Name="PriorityDelegate.h" Source="$(var.POCO)\Foundation\include\Poco\PriorityDelegate.h" />
                                <File Id="PRIORITYEVENT.H" Name="PriorityEvent.h" Source="$(var.POCO)\Foundation\include\Poco\PriorityEvent.h" />
                                <File Id="PRIORITYEXPIRE.H" Name="PriorityExpire.h" Source="$(var.POCO)\Foundation\include\Poco\PriorityExpire.h" />
                                <File Id="PRIORITYNOTIFICATIONQUEUE.H" Name="PriorityNotificationQueue.h" Source="$(var.POCO)\Foundation\include\Poco\PriorityNotificationQueue.h" />
                                <File Id="PRIORITYSTRATEGY.H" Name="PriorityStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\PriorityStrategy.h" />
                                <File Id="PROCESS.H" Name="Process.h" Source="$(var.POCO)\Foundation\include\Poco\Process.h" />
                                <File Id="PROCESS_WIN32.H" Name="Process_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Process_WIN32.h" />
                                <File Id="PURGESTRATEGY.H" Name="PurgeStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\PurgeStrategy.h" />
                                <File Id="RANDOM.H" Name="Random.h" Source="$(var.POCO)\Foundation\include\Poco\Random.h" />
                                <File Id="RANDOMSTREAM.H" Name="RandomStream.h" Source="$(var.POCO)\Foundation\include\Poco\RandomStream.h" />
                                <File Id="RECURSIVEDIRECTORYITERATOR.H" Name="RecursiveDirectoryIterator.h" Source="$(var.POCO)\Foundation\include\Poco\RecursiveDirectoryIterator.h" />
                                <File Id="RECURSIVEDIRECTORYITERATORIMPL.H" Name="RecursiveDirectoryIteratorImpl.h" Source="$(var.POCO)\Foundation\include\Poco\RecursiveDirectoryIteratorImpl.h" />
                                <File Id="REFCOUNTEDOBJECT.H" Name="RefCountedObject.h" Source="$(var.POCO)\Foundation\include\Poco\RefCountedObject.h" />
                                <File Id="REGULAREXPRESSION.H" Name="RegularExpression.h" Source="$(var.POCO)\Foundation\include\Poco\RegularExpression.h" />
                                <File Id="ROTATESTRATEGY.H" Name="RotateStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\RotateStrategy.h" />
                                <File Id="RUNNABLE.H" Name="Runnable.h" Source="$(var.POCO)\Foundation\include\Poco\Runnable.h" />
                                <File Id="RUNNABLEADAPTER.H" Name="RunnableAdapter.h" Source="$(var.POCO)\Foundation\include\Poco\RunnableAdapter.h" />
                                <File Id="RWLOCK.H" Name="RWLock.h" Source="$(var.POCO)\Foundation\include\Poco\RWLock.h" />
                                <File Id="RWLOCK_ANDROID.H" Name="RWLock_Android.h" Source="$(var.POCO)\Foundation\include\Poco\RWLock_Android.h" />
                                <File Id="RWLOCK_WIN32.H" Name="RWLock_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\RWLock_WIN32.h" />
                                <File Id="SCOPEDLOCK.H" Name="ScopedLock.h" Source="$(var.POCO)\Foundation\include\Poco\ScopedLock.h" />
                                <File Id="SCOPEDUNLOCK.H" Name="ScopedUnlock.h" Source="$(var.POCO)\Foundation\include\Poco\ScopedUnlock.h" />
                                <File Id="SEMAPHORE.H" Name="Semaphore.h" Source="$(var.POCO)\Foundation\include\Poco\Semaphore.h" />
                                <File Id="SEMAPHORE_WIN32.H" Name="Semaphore_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Semaphore_WIN32.h" />
                                <File Id="SHA1ENGINE.H" Name="SHA1Engine.h" Source="$(var.POCO)\Foundation\include\Poco\SHA1Engine.h" />
                                <File Id="SHAREDLIBRARY.H" Name="SharedLibrary.h" Source="$(var.POCO)\Foundation\include\Poco\SharedLibrary.h" />
                                <File Id="SHAREDLIBRARY_WIN32.H" Name="SharedLibrary_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\SharedLibrary_WIN32.h" />
                                <File Id="SHAREDMEMORY.H" Name="SharedMemory.h" Source="$(var.POCO)\Foundation\include\Poco\SharedMemory.h" />
                                <File Id="SHAREDMEMORY_WIN32.H" Name="SharedMemory_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\SharedMemory_WIN32.h" />
                                <File Id="SHAREDPTR.H" Name="SharedPtr.h" Source="$(var.POCO)\Foundation\include\Poco\SharedPtr.h" />
                                <File Id="SIGNALHANDLER.H" Name="SignalHandler.h" Source="$(var.POCO)\Foundation\include\Poco\SignalHandler.h" />
                                <File Id="SIMPLEFILECHANNEL.H" Name="SimpleFileChannel.h" Source="$(var.POCO)\Foundation\include\Poco\SimpleFileChannel.h" />
                                <File Id="SIMPLEHASHTABLE.H" Name="SimpleHashTable.h" Source="$(var.POCO)\Foundation\include\Poco\SimpleHashTable.h" />
                                <File Id="SINGLETONHOLDER.H" Name="SingletonHolder.h" Source="$(var.POCO)\Foundation\include\Poco\SingletonHolder.h" />
                                <File Id="SORTEDDIRECTORYITERATOR.H" Name="SortedDirectoryIterator.h" Source="$(var.POCO)\Foundation\include\Poco\SortedDirectoryIterator.h" />
                                <File Id="SPLITTERCHANNEL.H" Name="SplitterChannel.h" Source="$(var.POCO)\Foundation\include\Poco\SplitterChannel.h" />
                                <File Id="STOPWATCH.H" Name="Stopwatch.h" Source="$(var.POCO)\Foundation\include\Poco\Stopwatch.h" />
                                <File Id="STRATEGYCOLLECTION.H" Name="StrategyCollection.h" Source="$(var.POCO)\Foundation\include\Poco\StrategyCollection.h" />
                                <File Id="STREAMCHANNEL.H" Name="StreamChannel.h" Source="$(var.POCO)\Foundation\include\Poco\StreamChannel.h" />
                                <File Id="STREAMCONVERTER.H" Name="StreamConverter.h" Source="$(var.POCO)\Foundation\include\Poco\StreamConverter.h" />
                                <File Id="STREAMCOPIER.H" Name="StreamCopier.h" Source="$(var.POCO)\Foundation\include\Poco\StreamCopier.h" />
                                <File Id="STREAMTOKENIZER.H" Name="StreamTokenizer.h" Source="$(var.POCO)\Foundation\include\Poco\StreamTokenizer.h" />
                                <File Id="STREAMUTIL.H" Name="StreamUtil.h" Source="$(var.POCO)\Foundation\include\Poco\StreamUtil.h" />
                                <File Id="STRING.H" Name="String.h" Source="$(var.POCO)\Foundation\include\Poco\String.h" />
                                <File Id="STRINGTOKENIZER.H" Name="StringTokenizer.h" Source="$(var.POCO)\Foundation\include\Poco\StringTokenizer.h" />
                                <File Id="SYNCHRONIZEDOBJECT.H" Name="SynchronizedObject.h" Source="$(var.POCO)\Foundation\include\Poco\SynchronizedObject.h" />
                                <File Id="SYSLOGCHANNEL.H" Name="SyslogChannel.h" Source="$(var.POCO)\Foundation\include\Poco\SyslogChannel.h" />
                                <File Id="TASK.H" Name="Task.h" Source="$(var.POCO)\Foundation\include\Poco\Task.h" />
                                <File Id="TASKMANAGER.H" Name="TaskManager.h" Source="$(var.POCO)\Foundation\include\Poco\TaskManager.h" />
                                <File Id="TASKNOTIFICATION.H" Name="TaskNotification.h" Source="$(var.POCO)\Foundation\include\Poco\TaskNotification.h" />
                                <File Id="TEESTREAM.H" Name="TeeStream.h" Source="$(var.POCO)\Foundation\include\Poco\TeeStream.h" />
                                <File Id="TEMPORARYFILE.H" Name="TemporaryFile.h" Source="$(var.POCO)\Foundation\include\Poco\TemporaryFile.h" />
                                <File Id="TEXTBUFFERITERATOR.H" Name="TextBufferIterator.h" Source="$(var.POCO)\Foundation\include\Poco\TextBufferIterator.h" />
                                <File Id="TEXTCONVERTER.H" Name="TextConverter.h" Source="$(var.POCO)\Foundation\include\Poco\TextConverter.h" />
                                <File Id="TEXTENCODING.H" Name="TextEncoding.h" Source="$(var.POCO)\Foundation\include\Poco\TextEncoding.h" />
                                <File Id="TEXTITERATOR.H" Name="TextIterator.h" Source="$(var.POCO)\Foundation\include\Poco\TextIterator.h" />
                                <File Id="THREAD.H" Name="Thread.h" Source="$(var.POCO)\Foundation\include\Poco\Thread.h" />
                                <File Id="THREAD_WIN32.H" Name="Thread_WIN32.h" Source="$(var.POCO)\Foundation\include\Poco\Thread_WIN32.h" />
                                <File Id="THREADLOCAL.H" Name="ThreadLocal.h" Source="$(var.POCO)\Foundation\include\Poco\ThreadLocal.h" />
                                <File Id="THREADPOOL.H" Name="ThreadPool.h" Source="$(var.POCO)\Foundation\include\Poco\ThreadPool.h" />
                                <File Id="THREADTARGET.H" Name="ThreadTarget.h" Source="$(var.POCO)\Foundation\include\Poco\ThreadTarget.h" />
                                <File Id="TIMEDNOTIFICATIONQUEUE.H" Name="TimedNotificationQueue.h" Source="$(var.POCO)\Foundation\include\Poco\TimedNotificationQueue.h" />
                                <File Id="TIMER.H" Name="Timer.h" Source="$(var.POCO)\Foundation\include\Poco\Timer.h" />
                                <File Id="TIMESPAN.H" Name="Timespan.h" Source="$(var.POCO)\Foundation\include\Poco\Timespan.h" />
                                <File Id="TIMESTAMP.H" Name="Timestamp.h" Source="$(var.POCO)\Foundation\include\Poco\Timestamp.h" />
                                <File Id="TIMEZONE.H" Name="Timezone.h" Source="$(var.POCO)\Foundation\include\Poco\Timezone.h" />
                                <File Id="TOKEN.H" Name="Token.h" Source="$(var.POCO)\Foundation\include\Poco\Token.h" />
                                <File Id="TUPLE.H" Name="Tuple.h" Source="$(var.POCO)\Foundation\include\Poco\Tuple.h" />
                                <File Id="TYPELIST.H" Name="TypeList.h" Source="$(var.POCO)\Foundation\include\Poco\TypeList.h" />
                                <File Id="TYPES.H" Name="Types.h" Source="$(var.POCO)\Foundation\include\Poco\Types.h" />
                                <File Id="UNBUFFEREDSTREAMBUF.H" Name="UnbufferedStreamBuf.h" Source="$(var.POCO)\Foundation\include\Poco\UnbufferedStreamBuf.h" />
                                <File Id="UNICODE.H" Name="Unicode.h" Source="$(var.POCO)\Foundation\include\Poco\Unicode.h" />
                                <File Id="UNICODECONVERTER.H" Name="UnicodeConverter.h" Source="$(var.POCO)\Foundation\include\Poco\UnicodeConverter.h" />
                                <File Id="UNIQUEACCESSEXPIRECACHE.H" Name="UniqueAccessExpireCache.h" Source="$(var.POCO)\Foundation\include\Poco\UniqueAccessExpireCache.h" />
                                <File Id="UNIQUEACCESSEXPIRELRUCACHE.H" Name="UniqueAccessExpireLRUCache.h" Source="$(var.POCO)\Foundation\include\Poco\UniqueAccessExpireLRUCache.h" />
                                <File Id="UNIQUEACCESSEXPIRESTRATEGY.H" Name="UniqueAccessExpireStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\UniqueAccessExpireStrategy.h" />
                                <File Id="UNIQUEEXPIRECACHE.H" Name="UniqueExpireCache.h" Source="$(var.POCO)\Foundation\include\Poco\UniqueExpireCache.h" />
                                <File Id="UNIQUEEXPIRELRUCACHE.H" Name="UniqueExpireLRUCache.h" Source="$(var.POCO)\Foundation\include\Poco\UniqueExpireLRUCache.h" />
                                <File Id="UNIQUEEXPIRESTRATEGY.H" Name="UniqueExpireStrategy.h" Source="$(var.POCO)\Foundation\include\Poco\UniqueExpireStrategy.h" />
                                <File Id="UNWINDOWS.H" Name="UnWindows.h" Source="$(var.POCO)\Foundation\include\Poco\UnWindows.h" />
                                <File Id="URI.H" Name="URI.h" Source="$(var.POCO)\Foundation\include\Poco\URI.h" />
                                <File Id="URISTREAMFACTORY.H" Name="URIStreamFactory.h" Source="$(var.POCO)\Foundation\include\Poco\URIStreamFactory.h" />
                                <File Id="URISTREAMOPENER.H" Name="URIStreamOpener.h" Source="$(var.POCO)\Foundation\include\Poco\URIStreamOpener.h" />
                                <File Id="UTF16ENCODING.H" Name="UTF16Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\UTF16Encoding.h" />
                                <File Id="UTF32ENCODING.H" Name="UTF32Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\UTF32Encoding.h" />
                                <File Id="UTF8ENCODING.H" Name="UTF8Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\UTF8Encoding.h" />
                                <File Id="UTF8STRING.H" Name="UTF8String.h" Source="$(var.POCO)\Foundation\include\Poco\UTF8String.h" />
                                <File Id="UTFSTRING.H" Name="UTFString.h" Source="$(var.POCO)\Foundation\include\Poco\UTFString.h" />
                                <File Id="UUID.H" Name="UUID.h" Source="$(var.POCO)\Foundation\include\Poco\UUID.h" />
                                <File Id="UUIDGENERATOR.H" Name="UUIDGenerator.h" Source="$(var.POCO)\Foundation\include\Poco\UUIDGenerator.h" />
                                <File Id="VALIDARGS.H" Name="ValidArgs.h" Source="$(var.POCO)\Foundation\include\Poco\ValidArgs.h" />
                                <File Id="VERSION.H" Name="Version.h" Source="$(var.POCO)\Foundation\include\Poco\Version.h" />
                                <File Id="VOID.H" Name="Void.h" Source="$(var.POCO)\Foundation\include\Poco\Void.h" />
                                <File Id="WINDOWS1250ENCODING.H" Name="Windows1250Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\Windows1250Encoding.h" />
                                <File Id="WINDOWS1251ENCODING.H" Name="Windows1251Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\Windows1251Encoding.h" />
                                <File Id="WINDOWS1252ENCODING.H" Name="Windows1252Encoding.h" Source="$(var.POCO)\Foundation\include\Poco\Windows1252Encoding.h" />
                                <File Id="WINDOWSCONSOLECHANNEL.H" Name="WindowsConsoleChannel.h" Source="$(var.POCO)\Foundation\include\Poco\WindowsConsoleChannel.h" />
                                <File Id="ZCONF.H" Name="zconf.h" Source="$(var.POCO)\Foundation\include\Poco\zconf.h" />
                                <File Id="ZLIB.H" Name="zlib.h" Source="$(var.POCO)\Foundation\include\Poco\zlib.h" />
                            </Component>
                            <Directory Id="DYNAMIC" Name="Dynamic">
                                <Component Win64="$(var.Win64)" Id="Poco.Dynamic.inc" DiskId="1" Guid="96B9A353-16C3-4EAB-9DC3-EAFC839DA199">
                                    <File Id="PAIR.H" Name="Pair.h" Source="$(var.POCO)\Foundation\include\Poco\Dynamic\Pair.h" />
                                    <File Id="STRUCT.H" Name="Struct.h" Source="$(var.POCO)\Foundation\include\Poco\Dynamic\Struct.h" />
                                    <File Id="VAR.H" Name="Var.h" Source="$(var.POCO)\Foundation\include\Poco\Dynamic\Var.h" />
                                    <File Id="VARHOLDER.H" Name="VarHolder.h" Source="$(var.POCO)\Foundation\include\Poco\Dynamic\VarHolder.h" />
                                    <File Id="VARITERATOR.H" Name="VarIterator.h" Source="$(var.POCO)\Foundation\include\Poco\Dynamic\VarIterator.h" />
                                </Component>
                            </Directory>
                            <Directory Id="CRYPTO" Name="Crypto">
                                <Component Win64="$(var.Win64)" Id="Poco.Crypto.inc" DiskId="1" Guid="B9DD9D7A-F1C4-4D6B-B556-55E70881D123">
                                    <File Id="CIPHER.H" Name="Cipher.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\Cipher.h" />
                                    <File Id="CIPHERFACTORY.H" Name="CipherFactory.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\CipherFactory.h" />
                                    <File Id="CIPHERIMPL.H" Name="CipherImpl.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\CipherImpl.h" />
                                    <File Id="CIPHERKEY.H" Name="CipherKey.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\CipherKey.h" />
                                    <File Id="CIPHERKEYIMPL.H" Name="CipherKeyImpl.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\CipherKeyImpl.h" />
                                    <File Id="CRYPTO.H" Name="Crypto.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\Crypto.h" />
                                    <File Id="CRYPTOSTREAM.H" Name="CryptoStream.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\CryptoStream.h" />
                                    <File Id="CRYPTOTRANSFORM.H" Name="CryptoTransform.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\CryptoTransform.h" />
                                    <File Id="DIGESTENGINE.H_1" Name="DigestEngine.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\DigestEngine.h" />
                                    <File Id="OPENSSLINITIALIZER.H" Name="OpenSSLInitializer.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\OpenSSLInitializer.h" />
                                    <File Id="RSACIPHERIMPL.H" Name="RSACipherImpl.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\RSACipherImpl.h" />
                                    <File Id="RSADIGESTENGINE.H" Name="RSADigestEngine.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\RSADigestEngine.h" />
                                    <File Id="RSAKEY.H" Name="RSAKey.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\RSAKey.h" />
                                    <File Id="RSAKEYIMPL.H" Name="RSAKeyImpl.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\RSAKeyImpl.h" />
                                    <File Id="X509CERTIFICATE.H" Name="X509Certificate.h" Source="$(var.POCO)\Crypto\include\Poco\Crypto\X509Certificate.h" />
                                </Component>
                            </Directory>
                            <Directory Id="DATA" Name="Data">
                                <Component Win64="$(var.Win64)" Id="Poco.Data.inc" DiskId="1" Guid="56EDFF7F-0199-4AEA-B140-3FE3C423BFF8">
                                    <!--
                                <File Id="ABSTRACTBINDER.H" Name="AbstractBinder.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractBinder.h" />
-->
                                    <File Id="ABSTRACTBINDING.H" Name="AbstractBinding.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractBinding.h" />
                                    <File Id="ABSTRACTEXTRACTION.H" Name="AbstractExtraction.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractExtraction.h" />
                                    <File Id="ABSTRACTEXTRACTOR.H" Name="AbstractExtractor.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractExtractor.h" />
                                    <File Id="ABSTRACTPREPARATION.H" Name="AbstractPreparation.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractPreparation.h" />
                                    <File Id="ABSTRACTPREPARATOR.H" Name="AbstractPreparator.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractPreparator.h" />
                                    <File Id="ABSTRACTSESSIONIMPL.H" Name="AbstractSessionImpl.h" Source="$(var.POCO)\Data\include\Poco\Data\AbstractSessionImpl.h" />
                                    <File Id="ARCHIVESTRATEGY.H_1" Name="ArchiveStrategy.h" Source="$(var.POCO)\Data\include\Poco\Data\ArchiveStrategy.h" />
                                    <File Id="AUTOTRANSACTION.H" Name="AutoTransaction.h" Source="$(var.POCO)\Data\include\Poco\Data\AutoTransaction.h" />
                                    <File Id="BINDING.H" Name="Binding.h" Source="$(var.POCO)\Data\include\Poco\Data\Binding.h" />
                                    <File Id="BULK.H" Name="Bulk.h" Source="$(var.POCO)\Data\include\Poco\Data\Bulk.h" />
                                    <File Id="BULKBINDING.H" Name="BulkBinding.h" Source="$(var.POCO)\Data\include\Poco\Data\BulkBinding.h" />
                                    <File Id="BULKEXTRACTION.H" Name="BulkExtraction.h" Source="$(var.POCO)\Data\include\Poco\Data\BulkExtraction.h" />
                                    <File Id="COLUMN.H" Name="Column.h" Source="$(var.POCO)\Data\include\Poco\Data\Column.h" />
                                    <File Id="CONNECTOR.H" Name="Connector.h" Source="$(var.POCO)\Data\include\Poco\Data\Connector.h" />
                                    <File Id="CONSTANTS.H" Name="Constants.h" Source="$(var.POCO)\Data\include\Poco\Data\Constants.h" />
                                    <File Id="DATA.H" Name="Data.h" Source="$(var.POCO)\Data\include\Poco\Data\Data.h" />
                                    <File Id="DATAEXCEPTION.H" Name="DataException.h" Source="$(var.POCO)\Data\include\Poco\Data\DataException.h" />
                                    <File Id="DATE.H" Name="Date.h" Source="$(var.POCO)\Data\include\Poco\Data\Date.h" />
                                    <File Id="DYNAMICDATETIME.H" Name="DynamicDateTime.h" Source="$(var.POCO)\Data\include\Poco\Data\DynamicDateTime.h" />
                                    <File Id="DYNAMICLOB.H" Name="DynamicLOB.h" Source="$(var.POCO)\Data\include\Poco\Data\DynamicLOB.h" />
                                    <File Id="EXTRACTION.H" Name="Extraction.h" Source="$(var.POCO)\Data\include\Poco\Data\Extraction.h" />
                                    <File Id="LIMIT.H" Name="Limit.h" Source="$(var.POCO)\Data\include\Poco\Data\Limit.h" />
                                    <File Id="LOB.H" Name="LOB.h" Source="$(var.POCO)\Data\include\Poco\Data\LOB.h" />
                                    <File Id="LOBSTREAM.H" Name="LOBStream.h" Source="$(var.POCO)\Data\include\Poco\Data\LOBStream.h" />
                                    <File Id="METACOLUMN.H" Name="MetaColumn.h" Source="$(var.POCO)\Data\include\Poco\Data\MetaColumn.h" />
                                    <File Id="POOLEDSESSIONHOLDER.H" Name="PooledSessionHolder.h" Source="$(var.POCO)\Data\include\Poco\Data\PooledSessionHolder.h" />
                                    <File Id="POOLEDSESSIONIMPL.H" Name="PooledSessionImpl.h" Source="$(var.POCO)\Data\include\Poco\Data\PooledSessionImpl.h" />
                                    <File Id="POSITION.H" Name="Position.h" Source="$(var.POCO)\Data\include\Poco\Data\Position.h" />
                                    <File Id="PREPARATION.H" Name="Preparation.h" Source="$(var.POCO)\Data\include\Poco\Data\Preparation.h" />
                                    <File Id="RANGE.H" Name="Range.h" Source="$(var.POCO)\Data\include\Poco\Data\Range.h" />
                                    <File Id="RECORDSET.H" Name="RecordSet.h" Source="$(var.POCO)\Data\include\Poco\Data\RecordSet.h" />
                                    <File Id="ROW.H" Name="Row.h" Source="$(var.POCO)\Data\include\Poco\Data\Row.h" />
                                    <File Id="ROWFILTER.H" Name="RowFilter.h" Source="$(var.POCO)\Data\include\Poco\Data\RowFilter.h" />
                                    <File Id="ROWFORMATTER.H" Name="RowFormatter.h" Source="$(var.POCO)\Data\include\Poco\Data\RowFormatter.h" />
                                    <File Id="ROWITERATOR.H" Name="RowIterator.h" Source="$(var.POCO)\Data\include\Poco\Data\RowIterator.h" />
                                    <File Id="SESSION.H" Name="Session.h" Source="$(var.POCO)\Data\include\Poco\Data\Session.h" />
                                    <File Id="SESSIONFACTORY.H" Name="SessionFactory.h" Source="$(var.POCO)\Data\include\Poco\Data\SessionFactory.h" />
                                    <File Id="SESSIONIMPL.H" Name="SessionImpl.h" Source="$(var.POCO)\Data\include\Poco\Data\SessionImpl.h" />
                                    <File Id="SESSIONPOOL.H" Name="SessionPool.h" Source="$(var.POCO)\Data\include\Poco\Data\SessionPool.h" />
                                    <File Id="SESSIONPOOLCONTAINER.H" Name="SessionPoolContainer.h" Source="$(var.POCO)\Data\include\Poco\Data\SessionPoolContainer.h" />
                                    <File Id="SIMPLEROWFORMATTER.H" Name="SimpleRowFormatter.h" Source="$(var.POCO)\Data\include\Poco\Data\SimpleRowFormatter.h" />
                                    <File Id="SQLCHANNEL.H" Name="SQLChannel.h" Source="$(var.POCO)\Data\include\Poco\Data\SQLChannel.h" />
                                    <File Id="STATEMENT.H" Name="Statement.h" Source="$(var.POCO)\Data\include\Poco\Data\Statement.h" />
                                    <File Id="STATEMENTCREATOR.H" Name="StatementCreator.h" Source="$(var.POCO)\Data\include\Poco\Data\StatementCreator.h" />
                                    <File Id="STATEMENTIMPL.H" Name="StatementImpl.h" Source="$(var.POCO)\Data\include\Poco\Data\StatementImpl.h" />
                                    <File Id="TIME.H" Name="Time.h" Source="$(var.POCO)\Data\include\Poco\Data\Time.h" />
                                    <File Id="TRANSACTION.H" Name="Transaction.h" Source="$(var.POCO)\Data\include\Poco\Data\Transaction.h" />
                                    <File Id="TYPEHANDLER.H" Name="TypeHandler.h" Source="$(var.POCO)\Data\include\Poco\Data\TypeHandler.h" />
                                </Component>
                                <Directory Id="MYSQL" Name="MySQL">
                                    <Component Win64="$(var.Win64)" Id="Poco.Data.MySQL.inc" DiskId="1" Guid="F46CC846-0846-4DAE-9ADD-1B450A8F061C">
                                        <File Id="BINDER.H" Name="Binder.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\Binder.h" />
                                        <File Id="CONNECTOR.H_1" Name="Connector.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\Connector.h" />
                                        <File Id="EXTRACTOR.H" Name="Extractor.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\Extractor.h" />
                                        <File Id="MYSQL.H" Name="MySQL.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\MySQL.h" />
                                        <File Id="MYSQLEXCEPTION.H" Name="MySQLException.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\MySQLException.h" />
                                        <File Id="MYSQLSTATEMENTIMPL.H" Name="MySQLStatementImpl.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\MySQLStatementImpl.h" />
                                        <File Id="RESULTMETADATA.H" Name="ResultMetadata.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\ResultMetadata.h" />
                                        <File Id="SESSIONHANDLE.H" Name="SessionHandle.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\SessionHandle.h" />
                                        <File Id="SESSIONIMPL.H_1" Name="SessionImpl.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\SessionImpl.h" />
                                        <File Id="STATEMENTEXECUTOR.H" Name="StatementExecutor.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\StatementExecutor.h" />
                                        <File Id="UTILITY.H_1" Name="Utility.h" Source="$(var.POCO)\Data\MySQL\include\Poco\Data\MySQL\Utility.h" />
                                    </Component>
                                </Directory>
                                <Directory Id="ODBC" Name="ODBC">
                                    <Component Win64="$(var.Win64)" Id="Poco.Data.ODBC.inc" DiskId="1" Guid="21357A1A-A847-491C-9A54-99A450516E33">
                                        <File Id="BINDER.H_1" Name="Binder.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Binder.h" />
                                        <File Id="CONNECTIONHANDLE.H" Name="ConnectionHandle.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\ConnectionHandle.h" />
                                        <File Id="CONNECTOR.H_2" Name="Connector.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Connector.h" />
                                        <File Id="DIAGNOSTICS.H" Name="Diagnostics.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Diagnostics.h" />
                                        <File Id="ENVIRONMENTHANDLE.H" Name="EnvironmentHandle.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\EnvironmentHandle.h" />
                                        <File Id="ERROR.H_1" Name="Error.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Error.h" />
                                        <File Id="EXTRACTOR.H_1" Name="Extractor.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Extractor.h" />
                                        <File Id="HANDLE.H" Name="Handle.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Handle.h" />
                                        <File Id="ODBC.H" Name="ODBC.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\ODBC.h" />
                                        <File Id="ODBCEXCEPTION.H" Name="ODBCException.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\ODBCException.h" />
                                        <File Id="ODBCMETACOLUMN.H" Name="ODBCMetaColumn.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\ODBCMetaColumn.h" />
                                        <File Id="ODBCSTATEMENTIMPL.H" Name="ODBCStatementImpl.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\ODBCStatementImpl.h" />
                                        <File Id="PARAMETER.H_1" Name="Parameter.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Parameter.h" />
                                        <File Id="PREPARATOR.H" Name="Preparator.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Preparator.h" />
                                        <File Id="SESSIONIMPL.H_2" Name="SessionImpl.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\SessionImpl.h" />
                                        <File Id="TYPEINFO.H" Name="TypeInfo.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\TypeInfo.h" />
                                        <File Id="UNICODE.H_1" Name="Unicode.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Unicode.h" />
                                        <File Id="UNICODE_WIN32.H" Name="Unicode_WIN32.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Unicode_WIN32.h" />
                                        <File Id="UTILITY.H_2" Name="Utility.h" Source="$(var.POCO)\Data\ODBC\include\Poco\Data\ODBC\Utility.h" />
                                    </Component>
                                </Directory>
                                <Directory Id="SQLITE" Name="SQLite">
                                    <Component Win64="$(var.Win64)" Id="Poco.Data.SQLite.inc" DiskId="1" Guid="75A000B4-AD8A-4453-B8F7-A9D1AC63BCA3">
                                        <File Id="BINDER.H_2" Name="Binder.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\Binder.h" />
                                        <File Id="CONNECTOR.H_3" Name="Connector.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\Connector.h" />
                                        <File Id="EXTRACTOR.H_2" Name="Extractor.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\Extractor.h" />
                                        <File Id="NOTIFIER.H" Name="Notifier.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\Notifier.h" />
                                        <File Id="SESSIONIMPL.H_3" Name="SessionImpl.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\SessionImpl.h" />
                                        <File Id="SQLITE.H" Name="SQLite.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\SQLite.h" />
                                        <File Id="SQLITEEXCEPTION.H" Name="SQLiteException.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\SQLiteException.h" />
                                        <File Id="SQLITESTATEMENTIMPL.H" Name="SQLiteStatementImpl.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\SQLiteStatementImpl.h" />
                                        <File Id="UTILITY.H_3" Name="Utility.h" Source="$(var.POCO)\Data\SQLite\include\Poco\Data\SQLite\Utility.h" />
                                    </Component>
                                </Directory>
                                <Directory Id="POSTGRESQL" Name="PostgreSQL">
                                    <Component Win64="$(var.Win64)" Id="Poco.Data.PostgreSQL.inc" DiskId="1" Guid="F1DE7C5E-6E7E-46F9-8C48-91DEA02AA413">
                                        <File Id="BINDER.H_3" Name="Binder.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\Binder.h" />
                                        <File Id="CONNECTOR.H_4" Name="Connector.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\Connector.h" />
                                        <File Id="EXTRACTOR.H_3" Name="Extractor.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\Extractor.h" />
                                        <File Id="POSTGRESQL.H" Name="PostgreSQL.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\PostgreSQL.h" />
                                        <File Id="POSTGRESQLEXCEPTION.H" Name="PostgreSQLException.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\PostgreSQLException.h" />
                                        <File Id="POSTGRESQLSTATEMENTIMPL.H" Name="PostgreSQLStatementImpl.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\PostgreSQLStatementImpl.h" />
                                        <File Id="POSTGRESQLTYPES.H" Name="PostgreSQLTypes.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\PostgreSQLTypes.h" />
                                        <File Id="SESSIONHANDLE.H_1" Name="SessionHandle.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\SessionHandle.h" />
                                        <File Id="SESSIONIMPL.H_4" Name="SessionImpl.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\SessionImpl.h" />
                                        <File Id="STATEMENTEXECUTOR.H_1" Name="StatementExecutor.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\StatementExecutor.h" />
                                        <File Id="UTILITY.H_4" Name="Utility.h" Source="$(var.POCO)\Data\PostgreSQL\include\Poco\Data\PostgreSQL\Utility.h" />
                                    </Component>
                                </Directory>
                            </Directory>
                            <Directory Id="JSON" Name="JSON">
                                <Component Win64="$(var.Win64)" Id="Poco.JSON.inc" DiskId="1" Guid="5E06611F-78EE-4E5C-8F73-5E1CB5AAB8D4">
                                    <File Id="ARRAY.H_1" Name="Array.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Array.h" />
                                    <File Id="HANDLER.H" Name="Handler.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Handler.h" />
                                    <File Id="JSON.H" Name="JSON.h" Source="$(var.POCO)\JSON\include\Poco\JSON\JSON.h" />
                                    <File Id="JSONEXCEPTION.H" Name="JSONException.h" Source="$(var.POCO)\JSON\include\Poco\JSON\JSONException.h" />
                                    <File Id="OBJECT.H" Name="Object.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Object.h" />
                                    <File Id="PARSEHANDLER.H" Name="ParseHandler.h" Source="$(var.POCO)\JSON\include\Poco\JSON\ParseHandler.h" />
                                    <File Id="PARSER.H_1" Name="Parser.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Parser.h" />
                                    <File Id="PRINTHANDLER.H" Name="PrintHandler.h" Source="$(var.POCO)\JSON\include\Poco\JSON\PrintHandler.h" />
                                    <File Id="QUERY.H" Name="Query.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Query.h" />
                                    <File Id="TEMPLATE.H" Name="Template.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Template.h" />
                                    <File Id="TEMPLATECACHE.H" Name="TemplateCache.h" Source="$(var.POCO)\JSON\include\Poco\JSON\TemplateCache.h" />
                                    <File Id="STRINGIFIER.H" Name="Stringifier.h" Source="$(var.POCO)\JSON\include\Poco\JSON\Stringifier.h" />
                                </Component>
                            </Directory>
                            <Directory Id="MONGODB" Name="MongoDB">
                                <Component Win64="$(var.Win64)" Id="Poco.MongoDB.inc" DiskId="1" Guid="CAB8C38E-05F0-4C91-9BDD-801DEEFEE806">
                                    <File Id="ARRAY.H_2" Name="Array.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Array.h" />
                                    <File Id="BINARY.H" Name="Binary.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Binary.h" />
                                    <File Id="BSONREADER.H" Name="BSONReader.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\BSONReader.h" />
                                    <File Id="BSONWRITER.H" Name="BSONWriter.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\BSONWriter.h" />
                                    <File Id="CONNECTION.H" Name="Connection.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Connection.h" />
                                    <File Id="CURSOR.H" Name="Cursor.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Cursor.h" />
                                    <File Id="DATABASE.H" Name="Database.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Database.h" />
                                    <File Id="DELETEREQUEST.H" Name="DeleteRequest.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\DeleteRequest.h" />
                                    <File Id="DOCUMENT.H" Name="Document.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Document.h" />
                                    <File Id="ELEMENT.H" Name="Element.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Element.h" />
                                    <File Id="GETMOREREQUEST.H" Name="GetMoreRequest.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\GetMoreRequest.h" />
                                    <File Id="INSERTREQUEST.H" Name="InsertRequest.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\InsertRequest.h" />
                                    <File Id="JAVASCRIPTCODE.H" Name="JavaScriptCode.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\JavaScriptCode.h" />
                                    <File Id="KILLCURSORSREQUEST.H" Name="KillCursorsRequest.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\KillCursorsRequest.h" />
                                    <File Id="MESSAGE.H_1" Name="Message.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\Message.h" />
                                    <File Id="MESSAGEHEADER.H" Name="MessageHeader.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\MessageHeader.h" />
                                    <File Id="MONGODB.H" Name="MongoDB.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\MongoDB.h" />
                                    <File Id="OBJECTID.H" Name="ObjectId.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\ObjectId.h" />
                                    <File Id="POOLABLECONNECTIONFACTORY.H" Name="PoolableConnectionFactory.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\PoolableConnectionFactory.h" />
                                    <File Id="QUERYREQUEST.H" Name="QueryRequest.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\QueryRequest.h" />
                                    <File Id="REGULAREXPRESSION.H_1" Name="RegularExpression.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\RegularExpression.h" />
                                    <File Id="REPLICASET.H" Name="ReplicaSet.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\ReplicaSet.h" />
                                    <File Id="REQUESTMESSAGE.H" Name="RequestMessage.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\RequestMessage.h" />
                                    <File Id="RESPONSEMESSAGE.H" Name="ResponseMessage.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\ResponseMessage.h" />
                                    <File Id="UPDATEREQUEST.H" Name="UpdateRequest.h" Source="$(var.POCO)\MongoDB\include\Poco\MongoDB\UpdateRequest.h" />
                                </Component>
                            </Directory>
                            <Directory Id="NET" Name="Net">
                                <Component Win64="$(var.Win64)" Id="Poco.Net.inc" DiskId="1" Guid="315FA058-4721-4AC3-876F-D0D1811680E4">
                                    <File Id="ABSTRACTHTTPREQUESTHANDLER.H" Name="AbstractHTTPRequestHandler.h" Source="$(var.POCO)\Net\include\Poco\Net\AbstractHTTPRequestHandler.h" />
                                    <File Id="DATAGRAMSOCKET.H" Name="DatagramSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\DatagramSocket.h" />
                                    <File Id="DATAGRAMSOCKETIMPL.H" Name="DatagramSocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\DatagramSocketImpl.h" />
                                    <File Id="DIALOGSOCKET.H" Name="DialogSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\DialogSocket.h" />
                                    <File Id="DNS.H" Name="DNS.h" Source="$(var.POCO)\Net\include\Poco\Net\DNS.h" />
                                    <File Id="FILEPARTSOURCE.H" Name="FilePartSource.h" Source="$(var.POCO)\Net\include\Poco\Net\FilePartSource.h" />
                                    <File Id="FTPCLIENTSESSION.H" Name="FTPClientSession.h" Source="$(var.POCO)\Net\include\Poco\Net\FTPClientSession.h" />
                                    <File Id="FTPSTREAMFACTORY.H" Name="FTPStreamFactory.h" Source="$(var.POCO)\Net\include\Poco\Net\FTPStreamFactory.h" />
                                    <File Id="HOSTENTRY.H" Name="HostEntry.h" Source="$(var.POCO)\Net\include\Poco\Net\HostEntry.h" />
                                    <File Id="HTMLFORM.H" Name="HTMLForm.h" Source="$(var.POCO)\Net\include\Poco\Net\HTMLForm.h" />
                                    <File Id="HTTPAUTHENTICATIONPARAMS.H" Name="HTTPAuthenticationParams.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPAuthenticationParams.h" />
                                    <File Id="HTTPBASICCREDENTIALS.H" Name="HTTPBasicCredentials.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPBasicCredentials.h" />
                                    <File Id="HTTPBASICSTREAMBUF.H" Name="HTTPBasicStreamBuf.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPBasicStreamBuf.h" />
                                    <File Id="HTTPBUFFERALLOCATOR.H" Name="HTTPBufferAllocator.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPBufferAllocator.h" />
                                    <File Id="HTTPCHUNKEDSTREAM.H" Name="HTTPChunkedStream.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPChunkedStream.h" />
                                    <File Id="HTTPCLIENTSESSION.H" Name="HTTPClientSession.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPClientSession.h" />
                                    <File Id="HTTPCOOKIE.H" Name="HTTPCookie.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPCookie.h" />
                                    <File Id="HTTPCREDENTIALS.H" Name="HTTPCredentials.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPCredentials.h" />
                                    <File Id="HTTPDIGESTCREDENTIALS.H" Name="HTTPDigestCredentials.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPDigestCredentials.h" />
                                    <File Id="HTTPFIXEDLENGTHSTREAM.H" Name="HTTPFixedLengthStream.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPFixedLengthStream.h" />
                                    <File Id="HTTPHEADERSTREAM.H" Name="HTTPHeaderStream.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPHeaderStream.h" />
                                    <File Id="HTTPIOSTREAM.H" Name="HTTPIOStream.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPIOStream.h" />
                                    <File Id="HTTPMESSAGE.H" Name="HTTPMessage.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPMessage.h" />
                                    <File Id="HTTPREQUEST.H" Name="HTTPRequest.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPRequest.h" />
                                    <File Id="HTTPREQUESTHANDLER.H" Name="HTTPRequestHandler.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPRequestHandler.h" />
                                    <File Id="HTTPREQUESTHANDLERFACTORY.H" Name="HTTPRequestHandlerFactory.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPRequestHandlerFactory.h" />
                                    <File Id="HTTPRESPONSE.H" Name="HTTPResponse.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPResponse.h" />
                                    <File Id="HTTPSERVER.H" Name="HTTPServer.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServer.h" />
                                    <File Id="HTTPSERVERCONNECTION.H" Name="HTTPServerConnection.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerConnection.h" />
                                    <File Id="HTTPSERVERCONNECTIONFACTORY.H" Name="HTTPServerConnectionFactory.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerConnectionFactory.h" />
                                    <File Id="HTTPSERVERPARAMS.H" Name="HTTPServerParams.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerParams.h" />
                                    <File Id="HTTPSERVERREQUEST.H" Name="HTTPServerRequest.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerRequest.h" />
                                    <File Id="HTTPSERVERREQUESTIMPL.H" Name="HTTPServerRequestImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerRequestImpl.h" />
                                    <File Id="HTTPSERVERRESPONSE.H" Name="HTTPServerResponse.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerResponse.h" />
                                    <File Id="HTTPSERVERRESPONSEIMPL.H" Name="HTTPServerResponseImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerResponseImpl.h" />
                                    <File Id="HTTPSERVERSESSION.H" Name="HTTPServerSession.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPServerSession.h" />
                                    <File Id="HTTPSESSION.H" Name="HTTPSession.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPSession.h" />
                                    <File Id="HTTPSESSIONFACTORY.H" Name="HTTPSessionFactory.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPSessionFactory.h" />
                                    <File Id="HTTPSESSIONINSTANTIATOR.H" Name="HTTPSessionInstantiator.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPSessionInstantiator.h" />
                                    <File Id="HTTPSTREAM.H" Name="HTTPStream.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPStream.h" />
                                    <File Id="HTTPSTREAMFACTORY.H" Name="HTTPStreamFactory.h" Source="$(var.POCO)\Net\include\Poco\Net\HTTPStreamFactory.h" />
                                    <File Id="ICMPCLIENT.H" Name="ICMPClient.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPClient.h" />
                                    <File Id="ICMPEVENTARGS.H" Name="ICMPEventArgs.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPEventArgs.h" />
                                    <File Id="ICMPPACKET.H" Name="ICMPPacket.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPPacket.h" />
                                    <File Id="ICMPPACKETIMPL.H" Name="ICMPPacketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPPacketImpl.h" />
                                    <File Id="ICMPSOCKET.H" Name="ICMPSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPSocket.h" />
                                    <File Id="ICMPSOCKETIMPL.H" Name="ICMPSocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPSocketImpl.h" />
                                    <File Id="ICMPV4PACKETIMPL.H" Name="ICMPv4PacketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\ICMPv4PacketImpl.h" />
                                    <File Id="IPADDRESS.H" Name="IPAddress.h" Source="$(var.POCO)\Net\include\Poco\Net\IPAddress.h" />
                                    <File Id="IPADDRESSIMPL.H" Name="IPAddressImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\IPAddressImpl.h" />
                                    <File Id="MAILMESSAGE.H" Name="MailMessage.h" Source="$(var.POCO)\Net\include\Poco\Net\MailMessage.h" />
                                    <File Id="MAILRECIPIENT.H" Name="MailRecipient.h" Source="$(var.POCO)\Net\include\Poco\Net\MailRecipient.h" />
                                    <File Id="MAILSTREAM.H" Name="MailStream.h" Source="$(var.POCO)\Net\include\Poco\Net\MailStream.h" />
                                    <File Id="MEDIATYPE.H" Name="MediaType.h" Source="$(var.POCO)\Net\include\Poco\Net\MediaType.h" />
                                    <File Id="MESSAGEHEADER.H_1" Name="MessageHeader.h" Source="$(var.POCO)\Net\include\Poco\Net\MessageHeader.h" />
                                    <File Id="MULTICASTSOCKET.H" Name="MulticastSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\MulticastSocket.h" />
                                    <File Id="MULTIPARTREADER.H" Name="MultipartReader.h" Source="$(var.POCO)\Net\include\Poco\Net\MultipartReader.h" />
                                    <File Id="NAMEVALUECOLLECTION.H" Name="NameValueCollection.h" Source="$(var.POCO)\Net\include\Poco\Net\NameValueCollection.h" />
                                    <File Id="NET.H" Name="Net.h" Source="$(var.POCO)\Net\include\Poco\Net\Net.h" />
                                    <File Id="NETEXCEPTION.H" Name="NetException.h" Source="$(var.POCO)\Net\include\Poco\Net\NetException.h" />
                                    <File Id="NETWORKINTERFACE.H" Name="NetworkInterface.h" Source="$(var.POCO)\Net\include\Poco\Net\NetworkInterface.h" />
                                    <File Id="NTPCLIENT.H" Name="NTPClient.h" Source="$(var.POCO)\Net\include\Poco\Net\NTPClient.h" />
                                    <File Id="NTPEVENTARGS.H" Name="NTPEventArgs.h" Source="$(var.POCO)\Net\include\Poco\Net\NTPEventArgs.h" />
                                    <File Id="NTPPACKET.H" Name="NTPPacket.h" Source="$(var.POCO)\Net\include\Poco\Net\NTPPacket.h" />
                                    <File Id="NULLPARTHANDLER.H" Name="NullPartHandler.h" Source="$(var.POCO)\Net\include\Poco\Net\NullPartHandler.h" />
                                    <File Id="OAUTH10CREDENTIALS.H" Name="OAuth10Credentials.h" Source="$(var.POCO)\Net\include\Poco\Net\OAuth10Credentials.h" />
                                    <File Id="OAUTH20CREDENTIALS.H" Name="OAuth20Credentials.h" Source="$(var.POCO)\Net\include\Poco\Net\OAuth20Credentials.h" />
                                    <File Id="PARALLELSOCKETACCEPTOR.H" Name="ParallelSocketAcceptor.h" Source="$(var.POCO)\Net\include\Poco\Net\ParallelSocketAcceptor.h" />
                                    <File Id="PARALLELSOCKETREACTOR.H" Name="ParallelSocketReactor.h" Source="$(var.POCO)\Net\include\Poco\Net\ParallelSocketReactor.h" />
                                    <File Id="PARTHANDLER.H" Name="PartHandler.h" Source="$(var.POCO)\Net\include\Poco\Net\PartHandler.h" />
                                    <File Id="PARTSOURCE.H" Name="PartSource.h" Source="$(var.POCO)\Net\include\Poco\Net\PartSource.h" />
                                    <File Id="PARTSTORE.H" Name="PartStore.h" Source="$(var.POCO)\Net\include\Poco\Net\PartStore.h" />
                                    <File Id="POP3CLIENTSESSION.H" Name="POP3ClientSession.h" Source="$(var.POCO)\Net\include\Poco\Net\POP3ClientSession.h" />
                                    <File Id="QUOTEDPRINTABLEDECODER.H" Name="QuotedPrintableDecoder.h" Source="$(var.POCO)\Net\include\Poco\Net\QuotedPrintableDecoder.h" />
                                    <File Id="QUOTEDPRINTABLEENCODER.H" Name="QuotedPrintableEncoder.h" Source="$(var.POCO)\Net\include\Poco\Net\QuotedPrintableEncoder.h" />
                                    <File Id="RAWSOCKET.H" Name="RawSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\RawSocket.h" />
                                    <File Id="RAWSOCKETIMPL.H" Name="RawSocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\RawSocketImpl.h" />
                                    <File Id="REMOTESYSLOGCHANNEL.H" Name="RemoteSyslogChannel.h" Source="$(var.POCO)\Net\include\Poco\Net\RemoteSyslogChannel.h" />
                                    <File Id="REMOTESYSLOGLISTENER.H" Name="RemoteSyslogListener.h" Source="$(var.POCO)\Net\include\Poco\Net\RemoteSyslogListener.h" />
                                    <File Id="SERVERSOCKET.H" Name="ServerSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\ServerSocket.h" />
                                    <File Id="SERVERSOCKETIMPL.H" Name="ServerSocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\ServerSocketImpl.h" />
                                    <File Id="SMTPCHANNEL.H" Name="SMTPChannel.h" Source="$(var.POCO)\Net\include\Poco\Net\SMTPChannel.h" />
                                    <File Id="SMTPCLIENTSESSION.H" Name="SMTPClientSession.h" Source="$(var.POCO)\Net\include\Poco\Net\SMTPClientSession.h" />
                                    <File Id="SOCKET.H" Name="Socket.h" Source="$(var.POCO)\Net\include\Poco\Net\Socket.h" />
                                    <File Id="SOCKETACCEPTOR.H" Name="SocketAcceptor.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketAcceptor.h" />
                                    <File Id="SOCKETADDRESS.H" Name="SocketAddress.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketAddress.h" />
                                    <File Id="SOCKETADDRESSIMPL.H" Name="SocketAddressImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketAddressImpl.h" />
                                    <File Id="SOCKETCONNECTOR.H" Name="SocketConnector.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketConnector.h" />
                                    <File Id="SOCKETDEFS.H" Name="SocketDefs.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketDefs.h" />
                                    <File Id="SOCKETIMPL.H" Name="SocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketImpl.h" />
                                    <File Id="SOCKETNOTIFICATION.H" Name="SocketNotification.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketNotification.h" />
                                    <File Id="SOCKETNOTIFIER.H" Name="SocketNotifier.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketNotifier.h" />
                                    <File Id="SOCKETREACTOR.H" Name="SocketReactor.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketReactor.h" />
                                    <File Id="SOCKETSTREAM.H" Name="SocketStream.h" Source="$(var.POCO)\Net\include\Poco\Net\SocketStream.h" />
                                    <File Id="STREAMSOCKET.H" Name="StreamSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\StreamSocket.h" />
                                    <File Id="STREAMSOCKETIMPL.H" Name="StreamSocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\StreamSocketImpl.h" />
                                    <File Id="STRINGPARTSOURCE.H" Name="StringPartSource.h" Source="$(var.POCO)\Net\include\Poco\Net\StringPartSource.h" />
                                    <File Id="TCPSERVER.H" Name="TCPServer.h" Source="$(var.POCO)\Net\include\Poco\Net\TCPServer.h" />
                                    <File Id="TCPSERVERCONNECTION.H" Name="TCPServerConnection.h" Source="$(var.POCO)\Net\include\Poco\Net\TCPServerConnection.h" />
                                    <File Id="TCPSERVERCONNECTIONFACTORY.H" Name="TCPServerConnectionFactory.h" Source="$(var.POCO)\Net\include\Poco\Net\TCPServerConnectionFactory.h" />
                                    <File Id="TCPSERVERDISPATCHER.H" Name="TCPServerDispatcher.h" Source="$(var.POCO)\Net\include\Poco\Net\TCPServerDispatcher.h" />
                                    <File Id="TCPSERVERPARAMS.H" Name="TCPServerParams.h" Source="$(var.POCO)\Net\include\Poco\Net\TCPServerParams.h" />
                                    <File Id="WEBSOCKET.H" Name="WebSocket.h" Source="$(var.POCO)\Net\include\Poco\Net\WebSocket.h" />
                                    <File Id="WEBSOCKETIMPL.H" Name="WebSocketImpl.h" Source="$(var.POCO)\Net\include\Poco\Net\WebSocketImpl.h" />
                                </Component>
                            </Directory>
                            <Directory Id="NET_1" Name="Net">
                                <Component Win64="$(var.Win64)" Id="Poco.NetSSL_OpenSSL.inc" DiskId="1" Guid="D07B86F1-D456-4110-ABE5-2636BF75286C">
                                    <File Id="ACCEPTCERTIFICATEHANDLER.H" Name="AcceptCertificateHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\AcceptCertificateHandler.h" />
                                    <File Id="CERTIFICATEHANDLERFACTORY.H" Name="CertificateHandlerFactory.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\CertificateHandlerFactory.h" />
                                    <File Id="CERTIFICATEHANDLERFACTORYMGR.H" Name="CertificateHandlerFactoryMgr.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\CertificateHandlerFactoryMgr.h" />
                                    <File Id="CONSOLECERTIFICATEHANDLER.H" Name="ConsoleCertificateHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\ConsoleCertificateHandler.h" />
                                    <File Id="CONTEXT.H" Name="Context.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\Context.h" />
                                    <File Id="HTTPSCLIENTSESSION.H" Name="HTTPSClientSession.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\HTTPSClientSession.h" />
                                    <File Id="HTTPSSESSIONINSTANTIATOR.H" Name="HTTPSSessionInstantiator.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\HTTPSSessionInstantiator.h" />
                                    <File Id="HTTPSSTREAMFACTORY.H" Name="HTTPSStreamFactory.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\HTTPSStreamFactory.h" />
                                    <File Id="INVALIDCERTIFICATEHANDLER.H" Name="InvalidCertificateHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\InvalidCertificateHandler.h" />
                                    <File Id="KEYCONSOLEHANDLER.H" Name="KeyConsoleHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\KeyConsoleHandler.h" />
                                    <File Id="KEYFILEHANDLER.H" Name="KeyFileHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\KeyFileHandler.h" />
                                    <File Id="NETSSL.H" Name="NetSSL.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\NetSSL.h" />
                                    <File Id="PRIVATEKEYFACTORY.H" Name="PrivateKeyFactory.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\PrivateKeyFactory.h" />
                                    <File Id="PRIVATEKEYFACTORYMGR.H" Name="PrivateKeyFactoryMgr.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\PrivateKeyFactoryMgr.h" />
                                    <File Id="PRIVATEKEYPASSPHRASEHANDLER.H" Name="PrivateKeyPassphraseHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\PrivateKeyPassphraseHandler.h" />
                                    <File Id="REJECTCERTIFICATEHANDLER.H" Name="RejectCertificateHandler.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\RejectCertificateHandler.h" />
                                    <File Id="SECURESERVERSOCKET.H" Name="SecureServerSocket.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SecureServerSocket.h" />
                                    <File Id="SECURESERVERSOCKETIMPL.H" Name="SecureServerSocketImpl.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SecureServerSocketImpl.h" />
                                    <File Id="SECURESMTPCLIENTSESSION.H" Name="SecureSMTPClientSession.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SecureSMTPClientSession.h" />
                                    <File Id="SECURESOCKETIMPL.H" Name="SecureSocketImpl.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SecureSocketImpl.h" />
                                    <File Id="SECURESTREAMSOCKET.H" Name="SecureStreamSocket.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SecureStreamSocket.h" />
                                    <File Id="SECURESTREAMSOCKETIMPL.H" Name="SecureStreamSocketImpl.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SecureStreamSocketImpl.h" />
                                    <File Id="SESSION_1.H" Name="Session.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\Session.h" />
                                    <File Id="SSLEXCEPTION.H" Name="SSLException.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SSLException.h" />
                                    <File Id="SSLMANAGER.H" Name="SSLManager.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\SSLManager.h" />
                                    <File Id="UTILITY_1.H" Name="Utility.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\Utility.h" />
                                    <File Id="VERIFICATIONERRORARGS.H" Name="VerificationErrorArgs.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\VerificationErrorArgs.h" />
                                    <File Id="X509CERTIFICATE_1.H" Name="X509Certificate.h" Source="$(var.POCO)\NetSSL_OpenSSL\include\Poco\Net\X509Certificate.h" />
                                </Component>
                            </Directory>
                            <Directory Id="PDF" Name="PDF">
                                <Component Win64="$(var.Win64)" Id="Poco.PDF.inc" DiskId="1" Guid="4BC0FE82-9EB1-407B-982A-F2D4FE5DA6E9">
                                    <File Id="ADLER32.C" Name="adler32.c" Source="$(var.POCO)\PDF\include\Poco\PDF\adler32.c" />
                                    <File Id="CRC32.H" Name="crc32.h" Source="$(var.POCO)\PDF\include\Poco\PDF\crc32.h" />
                                    <File Id="DEFLATE.C" Name="deflate.c" Source="$(var.POCO)\PDF\include\Poco\PDF\deflate.c" />
                                    <File Id="DEFLATE.H" Name="deflate.h" Source="$(var.POCO)\PDF\include\Poco\PDF\deflate.h" />
                                    <File Id="DESTINATION.H" Name="Destination.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Destination.h" />
                                    <File Id="DOCUMENT.H_1" Name="Document.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Document.h" />
                                    <File Id="ENCODER.H" Name="Encoder.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Encoder.h" />
                                    <File Id="FONT.H" Name="Font.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Font.h" />
                                    <File Id="HPDF.H" Name="hpdf.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf.h" />
                                    <File Id="HPDF_ANNOTATION.H" Name="hpdf_annotation.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_annotation.h" />
                                    <File Id="HPDF_CATALOG.H" Name="hpdf_catalog.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_catalog.h" />
                                    <File Id="HPDF_CONF.H" Name="hpdf_conf.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_conf.h" />
                                    <File Id="HPDF_CONSTS.H" Name="hpdf_consts.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_consts.h" />
                                    <File Id="HPDF_DESTINATION.H" Name="hpdf_destination.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_destination.h" />
                                    <File Id="HPDF_DOC.H" Name="hpdf_doc.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_doc.h" />
                                    <File Id="HPDF_ENCODER.H" Name="hpdf_encoder.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_encoder.h" />
                                    <File Id="HPDF_ENCRYPT.H" Name="hpdf_encrypt.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_encrypt.h" />
                                    <File Id="HPDF_ENCRYPTDICT.H" Name="hpdf_encryptdict.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_encryptdict.h" />
                                    <File Id="HPDF_ERROR.H" Name="hpdf_error.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_error.h" />
                                    <File Id="HPDF_EXT_GSTATE.H" Name="hpdf_ext_gstate.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_ext_gstate.h" />
                                    <File Id="HPDF_FONT.H" Name="hpdf_font.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_font.h" />
                                    <File Id="HPDF_FONTDEF.H" Name="hpdf_fontdef.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_fontdef.h" />
                                    <File Id="HPDF_GSTATE.H" Name="hpdf_gstate.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_gstate.h" />
                                    <File Id="HPDF_IMAGE.H" Name="hpdf_image.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_image.h" />
                                    <File Id="HPDF_INFO.H" Name="hpdf_info.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_info.h" />
                                    <File Id="HPDF_LIST.H" Name="hpdf_list.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_list.h" />
                                    <File Id="HPDF_MMGR.H" Name="hpdf_mmgr.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_mmgr.h" />
                                    <File Id="HPDF_OBJECTS.H" Name="hpdf_objects.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_objects.h" />
                                    <File Id="HPDF_OUTLINE.H" Name="hpdf_outline.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_outline.h" />
                                    <File Id="HPDF_PAGES.H" Name="hpdf_pages.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_pages.h" />
                                    <File Id="HPDF_PAGE_LABEL.H" Name="hpdf_page_label.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_page_label.h" />
                                    <File Id="HPDF_STREAMS.H" Name="hpdf_streams.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_streams.h" />
                                    <File Id="HPDF_TYPES.H" Name="hpdf_types.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_types.h" />
                                    <File Id="HPDF_UTILS.H" Name="hpdf_utils.h" Source="$(var.POCO)\PDF\include\Poco\PDF\hpdf_utils.h" />
                                    <File Id="IMAGE.H" Name="Image.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Image.h" />
                                    <File Id="INFFAST.H" Name="inffast.h" Source="$(var.POCO)\PDF\include\Poco\PDF\inffast.h" />
                                    <File Id="INFFIXED.H" Name="inffixed.h" Source="$(var.POCO)\PDF\include\Poco\PDF\inffixed.h" />
                                    <File Id="INFLATE.H" Name="inflate.h" Source="$(var.POCO)\PDF\include\Poco\PDF\inflate.h" />
                                    <File Id="INFTREES.H" Name="inftrees.h" Source="$(var.POCO)\PDF\include\Poco\PDF\inftrees.h" />
                                    <File Id="LINKANNOTATION.H" Name="LinkAnnotation.h" Source="$(var.POCO)\PDF\include\Poco\PDF\LinkAnnotation.h" />
                                    <File Id="OUTLINE.H" Name="Outline.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Outline.h" />
                                    <File Id="PAGE.H" Name="Page.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Page.h" />
                                    <File Id="PDF.H" Name="PDF.h" Source="$(var.POCO)\PDF\include\Poco\PDF\PDF.h" />
                                    <File Id="PDFEXCEPTION.H" Name="PDFException.h" Source="$(var.POCO)\PDF\include\Poco\PDF\PDFException.h" />
                                    <File Id="PNG.H" Name="png.h" Source="$(var.POCO)\PDF\include\Poco\PDF\png.h" />
                                    <File Id="PNGCONF.H" Name="pngconf.h" Source="$(var.POCO)\PDF\include\Poco\PDF\pngconf.h" />
                                    <File Id="RESOURCE.H" Name="Resource.h" Source="$(var.POCO)\PDF\include\Poco\PDF\Resource.h" />
                                    <File Id="TEXTANNOTATION.H" Name="TextAnnotation.h" Source="$(var.POCO)\PDF\include\Poco\PDF\TextAnnotation.h" />
                                    <File Id="TREES.H" Name="trees.h" Source="$(var.POCO)\PDF\include\Poco\PDF\trees.h" />
                                    <File Id="ZCONF.H_1" Name="zconf.h" Source="$(var.POCO)\PDF\include\Poco\PDF\zconf.h" />
                                    <File Id="ZLIB.H_1" Name="zlib.h" Source="$(var.POCO)\PDF\include\Poco\PDF\zlib.h" />
                                    <File Id="ZUTIL.H" Name="zutil.h" Source="$(var.POCO)\PDF\include\Poco\PDF\zutil.h" />
                                </Component>
                            </Directory>
                            <Directory Id="SEVENZIP" Name="SevenZip">
                                <Component Win64="$(var.Win64)" Id="Poco.SevenZip.inc" DiskId="1" Guid="B32BEE21-FF6D-49DA-AD57-F459D2FCFB6F">
                                    <File Id="ARCHIVE.H" Name="Archive.h" Source="$(var.POCO)\SevenZip\include\Poco\SevenZip\Archive.h" />
                                    <File Id="ARCHIVEENTRY.H" Name="ArchiveEntry.h" Source="$(var.POCO)\SevenZip\include\Poco\SevenZip\ArchiveEntry.h" />
                                    <File Id="SEVENZIP.H" Name="SevenZip.h" Source="$(var.POCO)\SevenZip\include\Poco\SevenZip\SevenZip.h" />
                                </Component>
                            </Directory>
                            <Directory Id="UTIL" Name="Util">
                                <Component Win64="$(var.Win64)" Id="Poco.UTIL.inc" DiskId="1" Guid="6EDC87D2-FD92-4E20-AFEA-FDFBB9783AB8">
                                    <File Id="ABSTRACTCONFIGURATION.H" Name="AbstractConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\AbstractConfiguration.h" />
                                    <File Id="APPLICATION.H" Name="Application.h" Source="$(var.POCO)\Util\include\Poco\Util\Application.h" />
                                    <File Id="CONFIGURATIONMAPPER.H" Name="ConfigurationMapper.h" Source="$(var.POCO)\Util\include\Poco\Util\ConfigurationMapper.h" />
                                    <File Id="CONFIGURATIONVIEW.H" Name="ConfigurationView.h" Source="$(var.POCO)\Util\include\Poco\Util\ConfigurationView.h" />
                                    <File Id="FILESYSTEMCONFIGURATION.H" Name="FilesystemConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\FilesystemConfiguration.h" />
                                    <File Id="HELPFORMATTER.H" Name="HelpFormatter.h" Source="$(var.POCO)\Util\include\Poco\Util\HelpFormatter.h" />
                                    <File Id="INIFILECONFIGURATION.H" Name="IniFileConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\IniFileConfiguration.h" />
                                    <File Id="INTVALIDATOR.H" Name="IntValidator.h" Source="$(var.POCO)\Util\include\Poco\Util\IntValidator.h" />
                                    <File Id="JSONCONFIGURATION.H" Name="JSONConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\JSONConfiguration.h" />
                                    <File Id="LAYEREDCONFIGURATION.H" Name="LayeredConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\LayeredConfiguration.h" />
                                    <File Id="LOGGINGCONFIGURATOR.H" Name="LoggingConfigurator.h" Source="$(var.POCO)\Util\include\Poco\Util\LoggingConfigurator.h" />
                                    <File Id="LOGGINGSUBSYSTEM.H" Name="LoggingSubsystem.h" Source="$(var.POCO)\Util\include\Poco\Util\LoggingSubsystem.h" />
                                    <File Id="MAPCONFIGURATION.H" Name="MapConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\MapConfiguration.h" />
                                    <File Id="OPTION.H" Name="Option.h" Source="$(var.POCO)\Util\include\Poco\Util\Option.h" />
                                    <File Id="OPTIONCALLBACK.H" Name="OptionCallback.h" Source="$(var.POCO)\Util\include\Poco\Util\OptionCallback.h" />
                                    <File Id="OPTIONEXCEPTION.H" Name="OptionException.h" Source="$(var.POCO)\Util\include\Poco\Util\OptionException.h" />
                                    <File Id="OPTIONPROCESSOR.H" Name="OptionProcessor.h" Source="$(var.POCO)\Util\include\Poco\Util\OptionProcessor.h" />
                                    <File Id="OPTIONSET.H" Name="OptionSet.h" Source="$(var.POCO)\Util\include\Poco\Util\OptionSet.h" />
                                    <File Id="PROPERTYFILECONFIGURATION.H" Name="PropertyFileConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\PropertyFileConfiguration.h" />
                                    <File Id="REGEXPVALIDATOR.H" Name="RegExpValidator.h" Source="$(var.POCO)\Util\include\Poco\Util\RegExpValidator.h" />
                                    <File Id="SERVERAPPLICATION.H" Name="ServerApplication.h" Source="$(var.POCO)\Util\include\Poco\Util\ServerApplication.h" />
                                    <File Id="SUBSYSTEM.H" Name="Subsystem.h" Source="$(var.POCO)\Util\include\Poco\Util\Subsystem.h" />
                                    <File Id="SYSTEMCONFIGURATION.H" Name="SystemConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\SystemConfiguration.h" />
                                    <File Id="TIMER.H_1" Name="Timer.h" Source="$(var.POCO)\Util\include\Poco\Util\Timer.h" />
                                    <File Id="TIMERTASK.H" Name="TimerTask.h" Source="$(var.POCO)\Util\include\Poco\Util\TimerTask.h" />
                                    <File Id="TIMERTASKADAPTER.H" Name="TimerTaskAdapter.h" Source="$(var.POCO)\Util\include\Poco\Util\TimerTaskAdapter.h" />
                                    <File Id="UNITS.H" Name="Units.h" Source="$(var.POCO)\Util\include\Poco\Util\Units.h" />
                                    <File Id="UTIL.H" Name="Util.h" Source="$(var.POCO)\Util\include\Poco\Util\Util.h" />
                                    <File Id="VALIDATOR.H" Name="Validator.h" Source="$(var.POCO)\Util\include\Poco\Util\Validator.h" />
                                    <File Id="WINREGISTRYCONFIGURATION.H" Name="WinRegistryConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\WinRegistryConfiguration.h" />
                                    <File Id="WINREGISTRYKEY.H" Name="WinRegistryKey.h" Source="$(var.POCO)\Util\include\Poco\Util\WinRegistryKey.h" />
                                    <File Id="WINSERVICE.H" Name="WinService.h" Source="$(var.POCO)\Util\include\Poco\Util\WinService.h" />
                                    <File Id="XMLCONFIGURATION.H" Name="XMLConfiguration.h" Source="$(var.POCO)\Util\include\Poco\Util\XMLConfiguration.h" />
                                </Component>
                            </Directory>
                            <Directory Id="DOM" Name="DOM">
                                <Component Win64="$(var.Win64)" Id="Poco.DOM.inc" DiskId="1" Guid="EE374909-EB3E-4751-A643-85F1953E3935">
                                    <File Id="ABSTRACTCONTAINERNODE.H" Name="AbstractContainerNode.h" Source="$(var.POCO)\XML\include\Poco\DOM\AbstractContainerNode.h" />
                                    <File Id="ABSTRACTNODE.H" Name="AbstractNode.h" Source="$(var.POCO)\XML\include\Poco\DOM\AbstractNode.h" />
                                    <File Id="ATTR.H" Name="Attr.h" Source="$(var.POCO)\XML\include\Poco\DOM\Attr.h" />
                                    <File Id="ATTRMAP.H" Name="AttrMap.h" Source="$(var.POCO)\XML\include\Poco\DOM\AttrMap.h" />
                                    <File Id="AUTOPTR.H_1" Name="AutoPtr.h" Source="$(var.POCO)\XML\include\Poco\DOM\AutoPtr.h" />
                                    <File Id="CDATASECTION.H" Name="CDATASection.h" Source="$(var.POCO)\XML\include\Poco\DOM\CDATASection.h" />
                                    <File Id="CHARACTERDATA.H" Name="CharacterData.h" Source="$(var.POCO)\XML\include\Poco\DOM\CharacterData.h" />
                                    <File Id="CHILDNODESLIST.H" Name="ChildNodesList.h" Source="$(var.POCO)\XML\include\Poco\DOM\ChildNodesList.h" />
                                    <File Id="COMMENT.H" Name="Comment.h" Source="$(var.POCO)\XML\include\Poco\DOM\Comment.h" />
                                    <File Id="DOCUMENT.H_2" Name="Document.h" Source="$(var.POCO)\XML\include\Poco\DOM\Document.h" />
                                    <File Id="DOCUMENTEVENT.H" Name="DocumentEvent.h" Source="$(var.POCO)\XML\include\Poco\DOM\DocumentEvent.h" />
                                    <File Id="DOCUMENTFRAGMENT.H" Name="DocumentFragment.h" Source="$(var.POCO)\XML\include\Poco\DOM\DocumentFragment.h" />
                                    <File Id="DOCUMENTTYPE.H" Name="DocumentType.h" Source="$(var.POCO)\XML\include\Poco\DOM\DocumentType.h" />
                                    <File Id="DOMBUILDER.H" Name="DOMBuilder.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMBuilder.h" />
                                    <File Id="DOMEXCEPTION.H" Name="DOMException.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMException.h" />
                                    <File Id="DOMIMPLEMENTATION.H" Name="DOMImplementation.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMImplementation.h" />
                                    <File Id="DOMOBJECT.H" Name="DOMObject.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMObject.h" />
                                    <File Id="DOMPARSER.H" Name="DOMParser.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMParser.h" />
                                    <File Id="DOMSERIALIZER.H" Name="DOMSerializer.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMSerializer.h" />
                                    <File Id="DOMWRITER.H" Name="DOMWriter.h" Source="$(var.POCO)\XML\include\Poco\DOM\DOMWriter.h" />
                                    <File Id="DTDMAP.H" Name="DTDMap.h" Source="$(var.POCO)\XML\include\Poco\DOM\DTDMap.h" />
                                    <File Id="ELEMENT.H_1" Name="Element.h" Source="$(var.POCO)\XML\include\Poco\DOM\Element.h" />
                                    <File Id="ELEMENTSBYTAGNAMELIST.H" Name="ElementsByTagNameList.h" Source="$(var.POCO)\XML\include\Poco\DOM\ElementsByTagNameList.h" />
                                    <File Id="ENTITY.H" Name="Entity.h" Source="$(var.POCO)\XML\include\Poco\DOM\Entity.h" />
                                    <File Id="ENTITYREFERENCE.H" Name="EntityReference.h" Source="$(var.POCO)\XML\include\Poco\DOM\EntityReference.h" />
                                    <File Id="EVENT.H_1" Name="Event.h" Source="$(var.POCO)\XML\include\Poco\DOM\Event.h" />
                                    <File Id="EVENTDISPATCHER.H" Name="EventDispatcher.h" Source="$(var.POCO)\XML\include\Poco\DOM\EventDispatcher.h" />
                                    <File Id="EVENTEXCEPTION.H" Name="EventException.h" Source="$(var.POCO)\XML\include\Poco\DOM\EventException.h" />
                                    <File Id="EVENTLISTENER.H" Name="EventListener.h" Source="$(var.POCO)\XML\include\Poco\DOM\EventListener.h" />
                                    <File Id="EVENTTARGET.H" Name="EventTarget.h" Source="$(var.POCO)\XML\include\Poco\DOM\EventTarget.h" />
                                    <File Id="MUTATIONEVENT.H" Name="MutationEvent.h" Source="$(var.POCO)\XML\include\Poco\DOM\MutationEvent.h" />
                                    <File Id="NAMEDNODEMAP.H" Name="NamedNodeMap.h" Source="$(var.POCO)\XML\include\Poco\DOM\NamedNodeMap.h" />
                                    <File Id="NODE.H" Name="Node.h" Source="$(var.POCO)\XML\include\Poco\DOM\Node.h" />
                                    <File Id="NODEAPPENDER.H" Name="NodeAppender.h" Source="$(var.POCO)\XML\include\Poco\DOM\NodeAppender.h" />
                                    <File Id="NODEFILTER.H" Name="NodeFilter.h" Source="$(var.POCO)\XML\include\Poco\DOM\NodeFilter.h" />
                                    <File Id="NODEITERATOR.H" Name="NodeIterator.h" Source="$(var.POCO)\XML\include\Poco\DOM\NodeIterator.h" />
                                    <File Id="NODELIST.H" Name="NodeList.h" Source="$(var.POCO)\XML\include\Poco\DOM\NodeList.h" />
                                    <File Id="NOTATION.H" Name="Notation.h" Source="$(var.POCO)\XML\include\Poco\DOM\Notation.h" />
                                    <File Id="PROCESSINGINSTRUCTION.H" Name="ProcessingInstruction.h" Source="$(var.POCO)\XML\include\Poco\DOM\ProcessingInstruction.h" />
                                    <File Id="TEXT.H" Name="Text.h" Source="$(var.POCO)\XML\include\Poco\DOM\Text.h" />
                                    <File Id="TREEWALKER.H" Name="TreeWalker.h" Source="$(var.POCO)\XML\include\Poco\DOM\TreeWalker.h" />
                                </Component>
                            </Directory>
                            <Directory Id="SAX" Name="SAX">
                                <Component Win64="$(var.Win64)" Id="Poco.SAX.inc" DiskId="1" Guid="E66A91AF-715D-404A-81A0-73AF89D6ED7A">
                                    <File Id="ATTRIBUTES.H_1" Name="Attributes.h" Source="$(var.POCO)\XML\include\Poco\SAX\Attributes.h" />
                                    <File Id="ATTRIBUTESIMPL.H" Name="AttributesImpl.h" Source="$(var.POCO)\XML\include\Poco\SAX\AttributesImpl.h" />
                                    <File Id="CONTENTHANDLER.H" Name="ContentHandler.h" Source="$(var.POCO)\XML\include\Poco\SAX\ContentHandler.h" />
                                    <File Id="DECLHANDLER.H" Name="DeclHandler.h" Source="$(var.POCO)\XML\include\Poco\SAX\DeclHandler.h" />
                                    <File Id="DEFAULTHANDLER.H" Name="DefaultHandler.h" Source="$(var.POCO)\XML\include\Poco\SAX\DefaultHandler.h" />
                                    <File Id="DTDHANDLER.H" Name="DTDHandler.h" Source="$(var.POCO)\XML\include\Poco\SAX\DTDHandler.h" />
                                    <File Id="ENTITYRESOLVER.H" Name="EntityResolver.h" Source="$(var.POCO)\XML\include\Poco\SAX\EntityResolver.h" />
                                    <File Id="ENTITYRESOLVERIMPL.H" Name="EntityResolverImpl.h" Source="$(var.POCO)\XML\include\Poco\SAX\EntityResolverImpl.h" />
                                    <File Id="ERRORHANDLER.H_1" Name="ErrorHandler.h" Source="$(var.POCO)\XML\include\Poco\SAX\ErrorHandler.h" />
                                    <File Id="INPUTSOURCE.H" Name="InputSource.h" Source="$(var.POCO)\XML\include\Poco\SAX\InputSource.h" />
                                    <File Id="LEXICALHANDLER.H" Name="LexicalHandler.h" Source="$(var.POCO)\XML\include\Poco\SAX\LexicalHandler.h" />
                                    <File Id="LOCATOR.H" Name="Locator.h" Source="$(var.POCO)\XML\include\Poco\SAX\Locator.h" />
                                    <File Id="LOCATORIMPL.H" Name="LocatorImpl.h" Source="$(var.POCO)\XML\include\Poco\SAX\LocatorImpl.h" />
                                    <File Id="NAMESPACESUPPORT.H" Name="NamespaceSupport.h" Source="$(var.POCO)\XML\include\Poco\SAX\NamespaceSupport.h" />
                                    <File Id="SAXPARSER.H" Name="SAXParser.h" Source="$(var.POCO)\XML\include\Poco\SAX\SAXParser.h" />
                                    <File Id="WHITESPACEFILTER.H" Name="WhitespaceFilter.h" Source="$(var.POCO)\XML\include\Poco\SAX\WhitespaceFilter.h" />
                                    <File Id="XMLFILTER.H" Name="XMLFilter.h" Source="$(var.POCO)\XML\include\Poco\SAX\XMLFilter.h" />
                                    <File Id="XMLFILTERIMPL.H" Name="XMLFilterImpl.h" Source="$(var.POCO)\XML\include\Poco\SAX\XMLFilterImpl.h" />
                                    <File Id="XMLREADER.H" Name="XMLReader.h" Source="$(var.POCO)\XML\include\Poco\SAX\XMLReader.h" />
                                </Component>
                            </Directory>
                            <Directory Id="XML" Name="XML">
                                <Component Win64="$(var.Win64)" Id="Poco.XML.inc" DiskId="1" Guid="B492AFC9-2345-44DC-BF23-BBE77BF00CB4">
                                    <File Id="EXPAT.H" Name="expat.h" Source="$(var.POCO)\XML\include\Poco\XML\expat.h" />
                                    <File Id="EXPAT_EXTERNAL.H" Name="expat_external.h" Source="$(var.POCO)\XML\include\Poco\XML\expat_external.h" />
                                    <File Id="NAME.H" Name="Name.h" Source="$(var.POCO)\XML\include\Poco\XML\Name.h" />
                                    <File Id="NAMEPOOL.H" Name="NamePool.h" Source="$(var.POCO)\XML\include\Poco\XML\NamePool.h" />
                                    <File Id="NAMESPACESTRATEGY.H" Name="NamespaceStrategy.h" Source="$(var.POCO)\XML\include\Poco\XML\NamespaceStrategy.h" />
                                    <File Id="PARSERENGINE.H" Name="ParserEngine.h" Source="$(var.POCO)\XML\include\Poco\XML\ParserEngine.h" />
                                    <File Id="XML.H" Name="XML.h" Source="$(var.POCO)\XML\include\Poco\XML\XML.h" />
                                    <File Id="XMLEXCEPTION.H" Name="XMLException.h" Source="$(var.POCO)\XML\include\Poco\XML\XMLException.h" />
                                    <File Id="XMLSTREAM.H" Name="XMLStream.h" Source="$(var.POCO)\XML\include\Poco\XML\XMLStream.h" />
                                    <File Id="XMLWRITER.H" Name="XMLWriter.h" Source="$(var.POCO)\XML\include\Poco\XML\XMLWriter.h" />
                                    <File Id="XMLSTRING.H" Name="XMLString.h" Source="$(var.POCO)\XML\include\Poco\XML\XMLString.h" />
                                </Component>
                            </Directory>
                            <Directory Id="ZIP" Name="Zip">
                                <Component Win64="$(var.Win64)" Id="Poco.ZIP.inc" DiskId="1" Guid="53ADD961-2FF9-450F-BF2C-D31CC350527B">
                                    <File Id="ADD.H" Name="Add.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Add.h" />
                                    <File Id="AUTODETECTSTREAM.H" Name="AutoDetectStream.h" Source="$(var.POCO)\Zip\include\Poco\Zip\AutoDetectStream.h" />
                                    <File Id="COMPRESS.H" Name="Compress.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Compress.h" />
                                    <File Id="DECOMPRESS.H" Name="Decompress.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Decompress.h" />
                                    <File Id="DELETE.H" Name="Delete.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Delete.h" />
                                    <File Id="KEEP.H" Name="Keep.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Keep.h" />
                                    <File Id="PARSECALLBACK.H" Name="ParseCallback.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ParseCallback.h" />
                                    <File Id="PARTIALSTREAM.H" Name="PartialStream.h" Source="$(var.POCO)\Zip\include\Poco\Zip\PartialStream.h" />
                                    <File Id="RENAME.H" Name="Rename.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Rename.h" />
                                    <File Id="REPLACE.H" Name="Replace.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Replace.h" />
                                    <File Id="SKIPCALLBACK.H" Name="SkipCallback.h" Source="$(var.POCO)\Zip\include\Poco\Zip\SkipCallback.h" />
                                    <File Id="ZIP.H" Name="Zip.h" Source="$(var.POCO)\Zip\include\Poco\Zip\Zip.h" />
                                    <File Id="ZIPARCHIVE.H" Name="ZipArchive.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipArchive.h" />
                                    <File Id="ZIPARCHIVEINFO.H" Name="ZipArchiveInfo.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipArchiveInfo.h" />
                                    <File Id="ZIPCOMMON.H" Name="ZipCommon.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipCommon.h" />
                                    <File Id="ZIPDATAINFO.H" Name="ZipDataInfo.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipDataInfo.h" />
                                    <File Id="ZIPEXCEPTION.H" Name="ZipException.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipException.h" />
                                    <File Id="ZIPFILEINFO.H" Name="ZipFileInfo.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipFileInfo.h" />
                                    <File Id="ZIPLOCALFILEHEADER.H" Name="ZipLocalFileHeader.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipLocalFileHeader.h" />
                                    <File Id="ZIPMANIPULATOR.H" Name="ZipManipulator.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipManipulator.h" />
                                    <File Id="ZIPOPERATION.H" Name="ZipOperation.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipOperation.h" />
                                    <File Id="ZIPSTREAM.H" Name="ZipStream.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipStream.h" />
                                    <File Id="ZIPUTIL.H" Name="ZipUtil.h" Source="$(var.POCO)\Zip\include\Poco\Zip\ZipUtil.h" />
                                </Component>
                            </Directory>
                        </Directory>
                    </Directory>
                    <Directory Id="demos" Name="demos">
                        <Directory Id="Foundation" Name="Foundation">
                            <Directory Id="ACTIVEMETHOD" Name="ActiveMethod">
                                <?if $(var.Platform) = x86 ?>
                                <?if $(var.VisualStudio) = VSCE2009 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_CE_VS90.VCPROJ" DiskId="1" Guid="EF4DF9C7-A75C-46C4-BF80-7A3555FA5433">
                                    <File Id="ACTIVEMETHOD_CE_VS90.VCPROJ" Name="ActiveMethod_CE_vs90.vcproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_CE_vs90.vcproj" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2010 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS100.VCXPROJ" DiskId="1" Guid="C361E69A-1817-4C70-8C90-27D7233F5770">
                                    <File Id="ACTIVEMETHOD_VS100.VCXPROJ" Name="ActiveMethod_vs100.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs100.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS100.VCXPROJ.FILTERS" DiskId="1" Guid="318F67BE-0853-40DD-90E4-E0FCEC3EF4A0">
                                    <File Id="ACTIVEMETHOD_VS100.VCXPROJ.FILTERS" Name="ActiveMethod_vs100.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs100.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2012 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS110.VCXPROJ" DiskId="1" Guid="0132D94F-2625-4286-8A25-5F69B412B410">
                                    <File Id="ACTIVEMETHOD_VS110.VCXPROJ" Name="ActiveMethod_vs110.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs110.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS110.VCXPROJ.FILTERS" DiskId="1" Guid="3B6A7182-BC6D-4A94-9B29-25FB0E919BBE">
                                    <File Id="ACTIVEMETHOD_VS110.VCXPROJ.FILTERS" Name="ActiveMethod_vs110.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs110.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2013 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS120.VCXPROJ" DiskId="1" Guid="7C6C3208-A0E1-435A-9E15-9991F4AF3984">
                                    <File Id="ACTIVEMETHOD_VS120.VCXPROJ" Name="ActiveMethod_vs120.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs120.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS120.VCXPROJ.FILTERS" DiskId="1" Guid="74C2BABF-CC9A-4618-A95E-5097D8241D3F">
                                    <File Id="ACTIVEMETHOD_VS120.VCXPROJ.FILTERS" Name="ActiveMethod_vs120.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs120.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2015 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS140.VCXPROJ" DiskId="1" Guid="0363812B-467D-4F3F-BD5E-F4EE7F18D6FB">
                                    <File Id="ACTIVEMETHOD_VS140.VCXPROJ" Name="ActiveMethod_vs140.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs140.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS140.VCXPROJ.FILTERS" DiskId="1" Guid="E9933950-7778-4A4B-BAEE-B5BC05A214E2">
                                    <File Id="ACTIVEMETHOD_VS140.VCXPROJ.FILTERS" Name="ActiveMethod_vs140.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs140.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2009 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_VS90.VCPROJ" DiskId="1" Guid="F492DB99-354A-4E14-8B16-45E55601FA75">
                                    <File Id="ACTIVEMETHOD_VS90.VCPROJ" Name="ActiveMethod_vs90.vcproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_vs90.vcproj" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = WSCE2012 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_WEC2013_VS110.VCXPROJ" DiskId="1" Guid="A844A750-8348-4131-A265-42DEB71A4815">
                                    <File Id="ACTIVEMETHOD_WEC2013_VS110.VCXPROJ" Name="ActiveMethod_WEC2013_vs110.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_WEC2013_vs110.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_WEC2013_VS110.VCXPROJ.FILTERS" DiskId="1" Guid="412F6D06-581F-46F5-AF8C-EDFD0B44C0B0">
                                    <File Id="ACTIVEMETHOD_WEC2013_VS110.VCXPROJ.FILTERS" Name="ActiveMethod_WEC2013_vs110.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_WEC2013_vs110.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = WSCEC2013 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_WEC2013_VS120.VCXPROJ" DiskId="1" Guid="D7BAF969-49A1-46C2-BDFB-32DD8CE67AD4">
                                    <File Id="ACTIVEMETHOD_WEC2013_VS120.VCXPROJ" Name="ActiveMethod_WEC2013_vs120.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_WEC2013_vs120.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_WEC2013_VS120.VCXPROJ.FILTERS" DiskId="1" Guid="90797C28-FD5E-4B8B-9A22-B961B68120B4">
                                    <File Id="ACTIVEMETHOD_WEC2013_VS120.VCXPROJ.FILTERS" Name="ActiveMethod_WEC2013_vs120.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_WEC2013_vs120.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?else ?>
                                <?if $(var.VisualStudio) = VS2010 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS100.VCXPROJ" DiskId="1" Guid="ED3EDA38-846D-44D0-A1DC-075A45E9E04B">
                                    <File Id="ACTIVEMETHOD_X64_VS100.VCXPROJ" Name="ActiveMethod_x64_vs100.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs100.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS100.VCXPROJ.FILTERS" DiskId="1" Guid="57B54ABD-1DB1-4B78-90BB-4799E921CA3F">
                                    <File Id="ACTIVEMETHOD_X64_VS100.VCXPROJ.FILTERS" Name="ActiveMethod_x64_vs100.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs100.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2012 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS110.VCXPROJ" DiskId="1" Guid="8678F541-0CC2-41DD-A411-7FD29EE1A44F">
                                    <File Id="ACTIVEMETHOD_X64_VS110.VCXPROJ" Name="ActiveMethod_x64_vs110.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs110.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS110.VCXPROJ.FILTERS" DiskId="1" Guid="D94F75F8-7740-4832-B7FD-9329A9D41803">
                                    <File Id="ACTIVEMETHOD_X64_VS110.VCXPROJ.FILTERS" Name="ActiveMethod_x64_vs110.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs110.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2013 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS120.VCXPROJ" DiskId="1" Guid="D9AD00A2-C849-4514-977E-40F202634AE6">
                                    <File Id="ACTIVEMETHOD_X64_VS120.VCXPROJ" Name="ActiveMethod_x64_vs120.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs120.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS120.VCXPROJ.FILTERS" DiskId="1" Guid="290CD235-9A9F-4EC1-B783-3E07B5AF3078">
                                    <File Id="ACTIVEMETHOD_X64_VS120.VCXPROJ.FILTERS" Name="ActiveMethod_x64_vs120.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs120.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2015 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS140.VCXPROJ" DiskId="1" Guid="7919F410-6BAA-4F6B-B4B8-EA055882D109">
                                    <File Id="ACTIVEMETHOD_X64_VS140.VCXPROJ" Name="ActiveMethod_x64_vs140.vcxproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs140.vcxproj" />
                                </Component>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS140.VCXPROJ.FILTERS" DiskId="1" Guid="6AF7534A-6AC2-41D0-A162-CAD0BDD9109D">
                                    <File Id="ACTIVEMETHOD_X64_VS140.VCXPROJ.FILTERS" Name="ActiveMethod_x64_vs140.vcxproj.filters" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs140.vcxproj.filters" />
                                </Component>
                                <?endif ?>
                                <?if $(var.VisualStudio) = VS2009 ?>
                                <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD_X64_VS90.VCPROJ" DiskId="1" Guid="5CCEA805-7ED8-475B-9206-F74939226E73">
                                    <File Id="ACTIVEMETHOD_X64_VS90.VCPROJ" Name="ActiveMethod_x64_vs90.vcproj" Source="$(var.POCO)\Foundation\samples\ActiveMethod\ActiveMethod_x64_vs90.vcproj" />
                                </Component>
                                <?endif ?>
                                <?endif ?>
                                <Directory Id="SRC" Name="src">
                                    <Component Win64="$(var.Win64)" Id="ACTIVEMETHOD.CPP" DiskId="1" Guid="B57B7300-417E-4DDC-A31D-11D7B9357140">
                                        <File Id="ACTIVEMETHOD.CPP" Name="ActiveMethod.cpp" Source="$(var.POCO)\Foundation\samples\ActiveMethod\src\ActiveMethod.cpp" />
                                    </Component>
                                </Directory>
                            </Directory>
                        </Directory>
                    </Directory>
                    <Directory Id="doc" Name="doc">
                        <Component Win64="$(var.Win64)" Id="_00100_GUIDEDTOUR.HTML" DiskId="1" Guid="B5170D79-6D29-4406-84CA-88EC28C25566">
                            <File Id="_00100_GUIDEDTOUR.HTML" Name="00100-GuidedTour.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\00100-GuidedTour.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_00200_DATAUSERMANUAL.HTML" DiskId="1" Guid="A311233C-3F8C-4C01-9C23-D75639249204">
                            <File Id="_00200_DATAUSERMANUAL.HTML" Name="00200-DataUserManual.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\00200-DataUserManual.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_00200_GETTINGSTARTED.HTML" DiskId="1" Guid="2EE54158-E4FB-40A6-B009-EB92098E2618">
                            <File Id="_00200_GETTINGSTARTED.HTML" Name="00200-GettingStarted.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\00200-GettingStarted.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_00300_DATADEVELOPERMANUAL.HTML" DiskId="1" Guid="BB557F40-88D8-48F2-B1E3-1F4EF008CCF7">
                            <File Id="_00300_DATADEVELOPERMANUAL.HTML" Name="00300-DataDeveloperManual.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\00300-DataDeveloperManual.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_80100_HOWTOGETHELP.HTML" DiskId="1" Guid="E974D068-409B-465C-8A83-61F868E50DB7">
                            <File Id="_80100_HOWTOGETHELP.HTML" Name="80100-HowToGetHelp.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\80100-HowToGetHelp.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_90100_ACKNOWLEDGEMENTS.HTML" DiskId="1" Guid="14285B9C-614E-4141-A05F-676FC5ADA99B">
                            <File Id="_90100_ACKNOWLEDGEMENTS.HTML" Name="90100-Acknowledgements.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\90100-Acknowledgements.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99100_DATARELEASENOTES.HTML" DiskId="1" Guid="084EBA6B-9061-4742-BCE0-183B779E24E0">
                            <File Id="_99100_DATARELEASENOTES.HTML" Name="99100-DataReleaseNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99100-DataReleaseNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99100_RELEASENOTES.HTML" DiskId="1" Guid="5476D274-5BF3-4A88-9003-F674F9CA61B6">
                            <File Id="_99100_RELEASENOTES.HTML" Name="99100-ReleaseNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99100-ReleaseNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99150_GMAKEBUILDNOTES.HTML" DiskId="1" Guid="4258DB8D-AB57-4B79-8F36-4901310D17BE">
                            <File Id="_99150_GMAKEBUILDNOTES.HTML" Name="99150-GMakeBuildNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99150-GMakeBuildNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99150_WINDOWSPLATFORMNOTES.HTML" DiskId="1" Guid="F6283D37-1AE0-465E-8B0A-D82596788FFB">
                            <File Id="_99150_WINDOWSPLATFORMNOTES.HTML" Name="99150-WindowsPlatformNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99150-WindowsPlatformNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99200_WINCEPLATFORMNOTES.HTML" DiskId="1" Guid="3BB4EBED-A596-470B-9025-B140145335A5">
                            <File Id="_99200_WINCEPLATFORMNOTES.HTML" Name="99200-WinCEPlatformNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99200-WinCEPlatformNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99250_VXWORKSPLATFORMNOTES.HTML" DiskId="1" Guid="A1A4D9D9-F06D-4D22-A64D-B6C0AC1B4123">
                            <File Id="_99250_VXWORKSPLATFORMNOTES.HTML" Name="99250-VxWorksPlatformNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99250-VxWorksPlatformNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="_99300_ANDROIDPLATFORMNOTES.HTML" DiskId="1" Guid="DA4679D6-AD5E-41E3-924F-6DE074101813">
                            <File Id="_99300_ANDROIDPLATFORMNOTES.HTML" Name="99300-AndroidPlatformNotes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\99300-AndroidPlatformNotes.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="INDEX.HTML" DiskId="1" Guid="A2AA2612-D9A1-4FAB-B788-E1B7EB88DAB3">
                            <File Id="INDEX.HTML" Name="index.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\index.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="NAVIGATION.HTML" DiskId="1" Guid="FAE9B69F-5BF3-418B-AFD9-38B50B31DDD3">
                            <File Id="NAVIGATION.HTML" Name="navigation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\navigation.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_CRYPTO.HTML" DiskId="1" Guid="DA0D2CB8-EAAE-4DEA-A9BB-3C5244068A22">
                            <File Id="PACKAGE_CRYPTO.CERTIFICATE.HTML" Name="package-Crypto.Certificate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Crypto.Certificate.html" />
                            <File Id="PACKAGE_CRYPTO.CIPHER.HTML" Name="package-Crypto.Cipher.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Crypto.Cipher.html" />
                            <File Id="PACKAGE_CRYPTO.CRYPTOCORE.HTML" Name="package-Crypto.CryptoCore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Crypto.CryptoCore.html" />
                            <File Id="PACKAGE_CRYPTO.DIGEST.HTML" Name="package-Crypto.Digest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Crypto.Digest.html" />
                            <File Id="PACKAGE_CRYPTO.RSA.HTML" Name="package-Crypto.RSA.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Crypto.RSA.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_DATA.HTML" DiskId="1" Guid="966CBB21-CB4B-49D4-943C-56AF0AA15105">
                            <File Id="PACKAGE_DATA.DATACORE.HTML" Name="package-Data.DataCore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Data.DataCore.html" />
                            <File Id="PACKAGE_DATA.SESSIONPOOLING.HTML" Name="package-Data.SessionPooling.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Data.SessionPooling.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_FOUNDATION.HTML" DiskId="1" Guid="5BC34C18-1B95-403D-BF18-705E85EB3ED5">
                            <File Id="PACKAGE_FOUNDATION.CACHE.HTML" Name="package-Foundation.Cache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Cache.html" />
                            <File Id="PACKAGE_FOUNDATION.CORE.HTML" Name="package-Foundation.Core.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Core.html" />
                            <File Id="PACKAGE_FOUNDATION.CRYPT.HTML" Name="package-Foundation.Crypt.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Crypt.html" />
                            <File Id="PACKAGE_FOUNDATION.DATETIME.HTML" Name="package-Foundation.DateTime.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.DateTime.html" />
                            <File Id="PACKAGE_FOUNDATION.DYNAMIC.HTML" Name="package-Foundation.Dynamic.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Dynamic.html" />
                            <File Id="PACKAGE_FOUNDATION.EVENTS.HTML" Name="package-Foundation.Events.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Events.html" />
                            <File Id="PACKAGE_FOUNDATION.FILESYSTEM.HTML" Name="package-Foundation.Filesystem.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Filesystem.html" />
                            <File Id="PACKAGE_FOUNDATION.HASHING.HTML" Name="package-Foundation.Hashing.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Hashing.html" />
                            <File Id="PACKAGE_FOUNDATION.LOGGING.HTML" Name="package-Foundation.Logging.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Logging.html" />
                            <File Id="PACKAGE_FOUNDATION.NOTIFICATIONS.HTML" Name="package-Foundation.Notifications.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Notifications.html" />
                            <File Id="PACKAGE_FOUNDATION.PROCESSES.HTML" Name="package-Foundation.Processes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Processes.html" />
                            <File Id="PACKAGE_FOUNDATION.REGEXP.HTML" Name="package-Foundation.RegExp.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.RegExp.html" />
                            <File Id="PACKAGE_FOUNDATION.SHAREDLIBRARY.HTML" Name="package-Foundation.SharedLibrary.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.SharedLibrary.html" />
                            <File Id="PACKAGE_FOUNDATION.STREAMS.HTML" Name="package-Foundation.Streams.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Streams.html" />
                            <File Id="PACKAGE_FOUNDATION.TASKS.HTML" Name="package-Foundation.Tasks.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Tasks.html" />
                            <File Id="PACKAGE_FOUNDATION.TEXT.HTML" Name="package-Foundation.Text.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Text.html" />
                            <File Id="PACKAGE_FOUNDATION.THREADING.HTML" Name="package-Foundation.Threading.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.Threading.html" />
                            <File Id="PACKAGE_FOUNDATION.URI.HTML" Name="package-Foundation.URI.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.URI.html" />
                            <File Id="PACKAGE_FOUNDATION.UUID.HTML" Name="package-Foundation.UUID.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Foundation.UUID.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_JSON.HTML" DiskId="1" Guid="52742503-7CA9-4CA5-8589-DAFEBE26150C">
                            <File Id="PACKAGE_JSON.JSON.HTML" Name="package-JSON.JSON.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-JSON.JSON.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_MONGODB.HTML" DiskId="1" Guid="7D5BE961-2E00-484D-87E9-88CFE284E9B2">
                            <File Id="PACKAGE_MONGODB.MONGODB.HTML" Name="package-MongoDB.MongoDB.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-MongoDB.MongoDB.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_NET.HTML" DiskId="1" Guid="0082655A-A251-4FE7-8AE7-7E42E6349377">
                            <File Id="PACKAGE_NET.FTP.HTML" Name="package-Net.FTP.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.FTP.html" />
                            <File Id="PACKAGE_NET.HTML.HTML" Name="package-Net.HTML.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.HTML.html" />
                            <File Id="PACKAGE_NET.HTTP.HTML" Name="package-Net.HTTP.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.HTTP.html" />
                            <File Id="PACKAGE_NET.HTTPCLIENT.HTML" Name="package-Net.HTTPClient.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.HTTPClient.html" />
                            <File Id="PACKAGE_NET.HTTPSERVER.HTML" Name="package-Net.HTTPServer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.HTTPServer.html" />
                            <File Id="PACKAGE_NET.ICMP.HTML" Name="package-Net.ICMP.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.ICMP.html" />
                            <File Id="PACKAGE_NET.LOGGING.HTML" Name="package-Net.Logging.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.Logging.html" />
                            <File Id="PACKAGE_NET.MAIL.HTML" Name="package-Net.Mail.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.Mail.html" />
                            <File Id="PACKAGE_NET.MESSAGES.HTML" Name="package-Net.Messages.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.Messages.html" />
                            <File Id="PACKAGE_NET.NETCORE.HTML" Name="package-Net.NetCore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.NetCore.html" />
                            <File Id="PACKAGE_NET.NTP.HTML" Name="package-Net.NTP.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.NTP.html" />
                            <File Id="PACKAGE_NET.OAUTH.HTML" Name="package-Net.OAuth.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.OAuth.html" />
                            <File Id="PACKAGE_NET.REACTOR.HTML" Name="package-Net.Reactor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.Reactor.html" />
                            <File Id="PACKAGE_NET.SOCKETS.HTML" Name="package-Net.Sockets.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.Sockets.html" />
                            <File Id="PACKAGE_NET.TCPSERVER.HTML" Name="package-Net.TCPServer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.TCPServer.html" />
                            <File Id="PACKAGE_NET.WEBSOCKET.HTML" Name="package-Net.WebSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Net.WebSocket.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_NETSSL_OPENSSL.HTML" DiskId="1" Guid="01B758D7-8BB9-4D2B-B826-09032D19F71D">
                            <File Id="PACKAGE_NETSSL_OPENSSL.HTTPSCLIENT.HTML" Name="package-NetSSL_OpenSSL.HTTPSClient.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-NetSSL_OpenSSL.HTTPSClient.html" />
                            <File Id="PACKAGE_NETSSL_OPENSSL.MAIL.HTML" Name="package-NetSSL_OpenSSL.Mail.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-NetSSL_OpenSSL.Mail.html" />
                            <File Id="PACKAGE_NETSSL_OPENSSL.SSLCORE.HTML" Name="package-NetSSL_OpenSSL.SSLCore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-NetSSL_OpenSSL.SSLCore.html" />
                            <File Id="PACKAGE_NETSSL_OPENSSL.SSLSOCKETS.HTML" Name="package-NetSSL_OpenSSL.SSLSockets.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-NetSSL_OpenSSL.SSLSockets.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_UTIL.HTML" DiskId="1" Guid="E537B9A0-508F-467C-B707-EA3968503FE3">
                            <File Id="PACKAGE_UTIL.OPTIONS.HTML" Name="package-Util.Options.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Options.html" />
                            <File Id="PACKAGE_UTIL.TIMER.HTML" Name="package-Util.Timer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Timer.html" />
                            <File Id="PACKAGE_UTIL.UNITS.HTML" Name="package-Util.Units.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Units.html" />
                            <File Id="PACKAGE_UTIL.UTIL.HTML" Name="package-Util.Util.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Util.html" />
                            <File Id="PACKAGE_UTIL.APPLICATION.HTML" Name="package-Util.Application.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Application.html" />
                            <File Id="PACKAGE_UTIL.CONFIGURATION.HTML" Name="package-Util.Configuration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Configuration.html" />
                            <File Id="PACKAGE_UTIL.WINDOWS.HTML" Name="package-Util.Windows.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Util.Windows.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_XML.HTML" DiskId="1" Guid="6157F1E0-7B7E-47C1-8ABD-C2C69B91BC5C">
                            <File Id="PACKAGE_XML.DOM.HTML" Name="package-XML.DOM.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-XML.DOM.html" />
                            <File Id="PACKAGE_XML.SAX.HTML" Name="package-XML.SAX.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-XML.SAX.html" />
                            <File Id="PACKAGE_XML.XML.HTML" Name="package-XML.XML.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-XML.XML.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PACKAGE_ZIP.HTML" DiskId="1" Guid="44B84823-592A-4A46-B2B7-F4CD7E053D62">
                            <File Id="PACKAGE_ZIP.MANIPULATION.HTML" Name="package-Zip.Manipulation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Zip.Manipulation.html" />
                            <File Id="PACKAGE_ZIP.ZIP.HTML" Name="package-Zip.Zip.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\package-Zip.Zip.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="PAGECOMPILERUSERGUIDE.HTML" DiskId="1" Guid="F76D66C4-A5AF-40CE-8F24-6CDAA8695F91">
                            <File Id="PAGECOMPILERUSERGUIDE.HTML" Name="PageCompilerUserGuide.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\PageCompilerUserGuide.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.CRYPTO.HTML" DiskId="1" Guid="5572712F-1C9C-459F-80DA-050CD4D10938">
                            <File Id="POCO.CRYPTO.CIPHER.HTML" Name="Poco.Crypto.Cipher.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.Cipher.html" />
                            <File Id="POCO.CRYPTO.CIPHERFACTORY.HTML" Name="Poco.Crypto.CipherFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CipherFactory.html" />
                            <File Id="POCO.CRYPTO.CIPHERIMPL.HTML" Name="Poco.Crypto.CipherImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CipherImpl.html" />
                            <File Id="POCO.CRYPTO.CIPHERKEY.HTML" Name="Poco.Crypto.CipherKey.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CipherKey.html" />
                            <File Id="POCO.CRYPTO.CIPHERKEYIMPL.HTML" Name="Poco.Crypto.CipherKeyImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CipherKeyImpl.html" />
                            <File Id="POCO.CRYPTO.CRYPTOINPUTSTREAM.HTML" Name="Poco.Crypto.CryptoInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CryptoInputStream.html" />
                            <File Id="POCO.CRYPTO.CRYPTOIOS.HTML" Name="Poco.Crypto.CryptoIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CryptoIOS.html" />
                            <File Id="POCO.CRYPTO.CRYPTOOUTPUTSTREAM.HTML" Name="Poco.Crypto.CryptoOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CryptoOutputStream.html" />
                            <File Id="POCO.CRYPTO.CRYPTOSTREAMBUF.HTML" Name="Poco.Crypto.CryptoStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CryptoStreamBuf.html" />
                            <File Id="POCO.CRYPTO.CRYPTOTRANSFORM.HTML" Name="Poco.Crypto.CryptoTransform.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.CryptoTransform.html" />
                            <File Id="POCO.CRYPTO.DECRYPTINGINPUTSTREAM.HTML" Name="Poco.Crypto.DecryptingInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.DecryptingInputStream.html" />
                            <File Id="POCO.CRYPTO.DECRYPTINGOUTPUTSTREAM.HTML" Name="Poco.Crypto.DecryptingOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.DecryptingOutputStream.html" />
                            <File Id="POCO.CRYPTO.DIGESTENGINE.HTML" Name="Poco.Crypto.DigestEngine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.DigestEngine.html" />
                            <File Id="POCO.CRYPTO.ENCRYPTINGINPUTSTREAM.HTML" Name="Poco.Crypto.EncryptingInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.EncryptingInputStream.html" />
                            <File Id="POCO.CRYPTO.ENCRYPTINGOUTPUTSTREAM.HTML" Name="Poco.Crypto.EncryptingOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.EncryptingOutputStream.html" />
                            <File Id="POCO.CRYPTO.HTML" Name="Poco.Crypto.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.html" />
                            <File Id="POCO.CRYPTO.OPENSSLINITIALIZER.HTML" Name="Poco.Crypto.OpenSSLInitializer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.OpenSSLInitializer.html" />
                            <File Id="POCO.CRYPTO.RSACIPHERIMPL.HTML" Name="Poco.Crypto.RSACipherImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.RSACipherImpl.html" />
                            <File Id="POCO.CRYPTO.RSADIGESTENGINE.HTML" Name="Poco.Crypto.RSADigestEngine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.RSADigestEngine.html" />
                            <File Id="POCO.CRYPTO.RSAKEY.HTML" Name="Poco.Crypto.RSAKey.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.RSAKey.html" />
                            <File Id="POCO.CRYPTO.RSAKEYIMPL.HTML" Name="Poco.Crypto.RSAKeyImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.RSAKeyImpl.html" />
                            <File Id="POCO.CRYPTO.X509CERTIFICATE.HTML" Name="Poco.Crypto.X509Certificate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Crypto.X509Certificate.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.DATA.HTML" DiskId="1" Guid="297643C8-2243-4651-B4DF-1C0F5ACDF24F">
                            <!--
                        <File Id="POCO.DATA.ABSTRACTBINDER.HTML" Name="Poco.Data.AbstractBinder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractBinder.html" />
-->
                            <File Id="POCO.DATA.ABSTRACTBINDING.HTML" Name="Poco.Data.AbstractBinding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractBinding.html" />
                            <File Id="POCO.DATA.ABSTRACTEXTRACTION.HTML" Name="Poco.Data.AbstractExtraction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractExtraction.html" />
                            <File Id="POCO.DATA.ABSTRACTEXTRACTOR.HTML" Name="Poco.Data.AbstractExtractor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractExtractor.html" />
                            <File Id="POCO.DATA.ABSTRACTPREPARATION.HTML" Name="Poco.Data.AbstractPreparation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractPreparation.html" />
                            <File Id="POCO.DATA.ABSTRACTPREPARATOR.HTML" Name="Poco.Data.AbstractPreparator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractPreparator.html" />
                            <File Id="POCO.DATA.ABSTRACTSESSIONIMPL.FEATURE.HTML" Name="Poco.Data.AbstractSessionImpl.Feature.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractSessionImpl.Feature.html" />
                            <File Id="POCO.DATA.ABSTRACTSESSIONIMPL.HTML" Name="Poco.Data.AbstractSessionImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractSessionImpl.html" />
                            <File Id="POCO.DATA.ABSTRACTSESSIONIMPL.PROPERTY.HTML" Name="Poco.Data.AbstractSessionImpl.Property.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractSessionImpl.Property.html" />
                            <File Id="POCO.DATA.ABSTRACTTYPEHANDLER.HTML" Name="Poco.Data.AbstractTypeHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.AbstractTypeHandler.html" />
                            <File Id="POCO.DATA.ARCHIVEBYAGESTRATEGY.HTML" Name="Poco.Data.ArchiveByAgeStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ArchiveByAgeStrategy.html" />
                            <File Id="POCO.DATA.ARCHIVESTRATEGY.HTML" Name="Poco.Data.ArchiveStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ArchiveStrategy.html" />
                            <File Id="POCO.DATA.BINDING.HTML" Name="Poco.Data.Binding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Binding.html" />
                            <File Id="POCO.DATA.BINDINGEXCEPTION.HTML" Name="Poco.Data.BindingException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.BindingException.html" />
                            <File Id="POCO.DATA.BULK.HTML" Name="Poco.Data.Bulk.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Bulk.html" />
                            <File Id="POCO.DATA.BULKBINDING.HTML" Name="Poco.Data.BulkBinding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.BulkBinding.html" />
                            <File Id="POCO.DATA.BULKEXTRACTION.HTML" Name="Poco.Data.BulkExtraction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.BulkExtraction.html" />
                            <File Id="POCO.DATA.COLUMN.HTML" Name="Poco.Data.Column.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Column.html" />
                            <File Id="POCO.DATA.CONNECTIONFAILEDEXCEPTION.HTML" Name="Poco.Data.ConnectionFailedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ConnectionFailedException.html" />
                            <File Id="POCO.DATA.CONNECTOR.HTML" Name="Poco.Data.Connector.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Connector.html" />
                            <File Id="POCO.DATA.COPYBINDING.HTML" Name="Poco.Data.CopyBinding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.CopyBinding.html" />
                            <File Id="POCO.DATA.DATAEXCEPTION.HTML" Name="Poco.Data.DataException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.DataException.html" />
                            <File Id="POCO.DATA.DATE.HTML" Name="Poco.Data.Date.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Date.html" />
                            <File Id="POCO.DATA.EXECUTIONEXCEPTION.HTML" Name="Poco.Data.ExecutionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ExecutionException.html" />
                            <File Id="POCO.DATA.EXTRACTEXCEPTION.HTML" Name="Poco.Data.ExtractException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ExtractException.html" />
                            <File Id="POCO.DATA.EXTRACTION.HTML" Name="Poco.Data.Extraction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Extraction.html" />
                            <File Id="POCO.DATA.HTML" Name="Poco.Data.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.html" />
                            <File Id="POCO.DATA.INTERNALBULKEXTRACTION.HTML" Name="Poco.Data.InternalBulkExtraction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.InternalBulkExtraction.html" />
                            <File Id="POCO.DATA.INTERNALEXTRACTION.HTML" Name="Poco.Data.InternalExtraction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.InternalExtraction.html" />
                            <File Id="POCO.DATA.KEYWORDS.HTML" Name="Poco.Data.Keywords.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Keywords.html" />
                            <File Id="POCO.DATA.LENGTHEXCEEDEDEXCEPTION.HTML" Name="Poco.Data.LengthExceededException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LengthExceededException.html" />
                            <File Id="POCO.DATA.LIMIT.HTML" Name="Poco.Data.Limit.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Limit.html" />
                            <File Id="POCO.DATA.LIMITEXCEPTION.HTML" Name="Poco.Data.LimitException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LimitException.html" />
                            <File Id="POCO.DATA.LOB.HTML" Name="Poco.Data.LOB.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LOB.html" />
                            <File Id="POCO.DATA.LOBINPUTSTREAM.HTML" Name="Poco.Data.LOBInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LOBInputStream.html" />
                            <File Id="POCO.DATA.LOBIOS.HTML" Name="Poco.Data.LOBIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LOBIOS.html" />
                            <File Id="POCO.DATA.LOBOUTPUTSTREAM.HTML" Name="Poco.Data.LOBOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LOBOutputStream.html" />
                            <File Id="POCO.DATA.LOBSTREAMBUF.HTML" Name="Poco.Data.LOBStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.LOBStreamBuf.html" />
                            <File Id="POCO.DATA.METACOLUMN.HTML" Name="Poco.Data.MetaColumn.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MetaColumn.html" />
                            <File Id="POCO.DATA.NODATAEXCEPTION.HTML" Name="Poco.Data.NoDataException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.NoDataException.html" />
                            <File Id="POCO.DATA.NOTCONNECTEDEXCEPTION.HTML" Name="Poco.Data.NotConnectedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.NotConnectedException.html" />
                            <File Id="POCO.DATA.NOTSUPPORTEDEXCEPTION.HTML" Name="Poco.Data.NotSupportedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.NotSupportedException.html" />
                            <File Id="POCO.DATA.POOLEDSESSIONHOLDER.HTML" Name="Poco.Data.PooledSessionHolder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.PooledSessionHolder.html" />
                            <File Id="POCO.DATA.POOLEDSESSIONIMPL.HTML" Name="Poco.Data.PooledSessionImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.PooledSessionImpl.html" />
                            <File Id="POCO.DATA.POSITION.HTML" Name="Poco.Data.Position.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Position.html" />
                            <File Id="POCO.DATA.PREPARATION.HTML" Name="Poco.Data.Preparation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Preparation.html" />
                            <File Id="POCO.DATA.RANGE.HTML" Name="Poco.Data.Range.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Range.html" />
                            <File Id="POCO.DATA.RECORDSET.HTML" Name="Poco.Data.RecordSet.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.RecordSet.html" />
                            <File Id="POCO.DATA.ROW.HTML" Name="Poco.Data.Row.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Row.html" />
                            <File Id="POCO.DATA.ROWDATAMISSINGEXCEPTION.HTML" Name="Poco.Data.RowDataMissingException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.RowDataMissingException.html" />
                            <File Id="POCO.DATA.ROWFILTER.HTML" Name="Poco.Data.RowFilter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.RowFilter.html" />
                            <File Id="POCO.DATA.ROWFORMATTER.HTML" Name="Poco.Data.RowFormatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.RowFormatter.html" />
                            <File Id="POCO.DATA.ROWITERATOR.HTML" Name="Poco.Data.RowIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.RowIterator.html" />
                            <File Id="POCO.DATA.SESSION.HTML" Name="Poco.Data.Session.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Session.html" />
                            <File Id="POCO.DATA.SESSIONFACTORY.HTML" Name="Poco.Data.SessionFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionFactory.html" />
                            <File Id="POCO.DATA.SESSIONFACTORY.SESSIONINFO.HTML" Name="Poco.Data.SessionFactory.SessionInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionFactory.SessionInfo.html" />
                            <File Id="POCO.DATA.SESSIONIMPL.HTML" Name="Poco.Data.SessionImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionImpl.html" />
                            <File Id="POCO.DATA.SESSIONPOOL.HTML" Name="Poco.Data.SessionPool.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionPool.html" />
                            <File Id="POCO.DATA.SESSIONPOOLCONTAINER.HTML" Name="Poco.Data.SessionPoolContainer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionPoolContainer.html" />
                            <File Id="POCO.DATA.SESSIONPOOLEXHAUSTEDEXCEPTION.HTML" Name="Poco.Data.SessionPoolExhaustedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionPoolExhaustedException.html" />
                            <File Id="POCO.DATA.SESSIONPOOLEXISTSEXCEPTION.HTML" Name="Poco.Data.SessionPoolExistsException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionPoolExistsException.html" />
                            <File Id="POCO.DATA.SESSIONUNAVAILABLEEXCEPTION.HTML" Name="Poco.Data.SessionUnavailableException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SessionUnavailableException.html" />
                            <File Id="POCO.DATA.SIMPLEROWFORMATTER.HTML" Name="Poco.Data.SimpleRowFormatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SimpleRowFormatter.html" />
                            <File Id="POCO.DATA.SQLCHANNEL.HTML" Name="Poco.Data.SQLChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLChannel.html" />
                            <File Id="POCO.DATA.STATEMENT.HTML" Name="Poco.Data.Statement.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Statement.html" />
                            <File Id="POCO.DATA.STATEMENTCREATOR.HTML" Name="Poco.Data.StatementCreator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.StatementCreator.html" />
                            <File Id="POCO.DATA.STATEMENTIMPL.HTML" Name="Poco.Data.StatementImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.StatementImpl.html" />
                            <File Id="POCO.DATA.TIME.HTML" Name="Poco.Data.Time.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Time.html" />
                            <File Id="POCO.DATA.TRANSACTION.HTML" Name="Poco.Data.Transaction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.Transaction.html" />
                            <File Id="POCO.DATA.TYPEHANDLER.HTML" Name="Poco.Data.TypeHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.TypeHandler.html" />
                            <File Id="POCO.DATA.UNKNOWNDATABASEEXCEPTION.HTML" Name="Poco.Data.UnknownDataBaseException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.UnknownDataBaseException.html" />
                            <File Id="POCO.DATA.UNKNOWNTYPEEXCEPTION.HTML" Name="Poco.Data.UnknownTypeException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.UnknownTypeException.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.DATA.ODBC.HTML" DiskId="1" Guid="29B37AEB-193D-4132-B3A7-8FBEC7F5E003">
                            <File Id="POCO.DATA.ODBC.BINDER.HTML" Name="Poco.Data.ODBC.Binder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Binder.html" />
                            <File Id="POCO.DATA.ODBC.CONNECTIONHANDLE.HTML" Name="Poco.Data.ODBC.ConnectionHandle.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.ConnectionHandle.html" />
                            <File Id="POCO.DATA.ODBC.CONNECTOR.HTML" Name="Poco.Data.ODBC.Connector.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Connector.html" />
                            <File Id="POCO.DATA.ODBC.DATATRUNCATEDEXCEPTION.HTML" Name="Poco.Data.ODBC.DataTruncatedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.DataTruncatedException.html" />
                            <File Id="POCO.DATA.ODBC.DIAGNOSTICS.DIAGNOSTICFIELDS.HTML" Name="Poco.Data.ODBC.Diagnostics.DiagnosticFields.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Diagnostics.DiagnosticFields.html" />
                            <File Id="POCO.DATA.ODBC.DIAGNOSTICS.HTML" Name="Poco.Data.ODBC.Diagnostics.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Diagnostics.html" />
                            <File Id="POCO.DATA.ODBC.ENVIRONMENTHANDLE.HTML" Name="Poco.Data.ODBC.EnvironmentHandle.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.EnvironmentHandle.html" />
                            <File Id="POCO.DATA.ODBC.ERROR.HTML" Name="Poco.Data.ODBC.Error.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Error.html" />
                            <File Id="POCO.DATA.ODBC.EXTRACTOR.HTML" Name="Poco.Data.ODBC.Extractor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Extractor.html" />
                            <File Id="POCO.DATA.ODBC.HANDLE.HTML" Name="Poco.Data.ODBC.Handle.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Handle.html" />
                            <File Id="POCO.DATA.ODBC.HANDLEEXCEPTION.HTML" Name="Poco.Data.ODBC.HandleException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.HandleException.html" />
                            <File Id="POCO.DATA.ODBC.HTML" Name="Poco.Data.ODBC.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.html" />
                            <File Id="POCO.DATA.ODBC.INSUFFICIENTSTORAGEEXCEPTION.HTML" Name="Poco.Data.ODBC.InsufficientStorageException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.InsufficientStorageException.html" />
                            <File Id="POCO.DATA.ODBC.ODBCEXCEPTION.HTML" Name="Poco.Data.ODBC.ODBCException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.ODBCException.html" />
                            <File Id="POCO.DATA.ODBC.ODBCMETACOLUMN.COLUMNDESCRIPTION.HTML" Name="Poco.Data.ODBC.ODBCMetaColumn.ColumnDescription.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.ODBCMetaColumn.ColumnDescription.html" />
                            <File Id="POCO.DATA.ODBC.ODBCMETACOLUMN.HTML" Name="Poco.Data.ODBC.ODBCMetaColumn.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.ODBCMetaColumn.html" />
                            <File Id="POCO.DATA.ODBC.ODBCSTATEMENTIMPL.HTML" Name="Poco.Data.ODBC.ODBCStatementImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.ODBCStatementImpl.html" />
                            <File Id="POCO.DATA.ODBC.PARAMETER.HTML" Name="Poco.Data.ODBC.Parameter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Parameter.html" />
                            <File Id="POCO.DATA.ODBC.PREPARATOR.HTML" Name="Poco.Data.ODBC.Preparator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Preparator.html" />
                            <File Id="POCO.DATA.ODBC.SESSIONIMPL.HTML" Name="Poco.Data.ODBC.SessionImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.SessionImpl.html" />
                            <File Id="POCO.DATA.ODBC.TYPEINFO.HTML" Name="Poco.Data.ODBC.TypeInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.TypeInfo.html" />
                            <File Id="POCO.DATA.ODBC.UNKNOWNDATALENGTHEXCEPTION.HTML" Name="Poco.Data.ODBC.UnknownDataLengthException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.UnknownDataLengthException.html" />
                            <File Id="POCO.DATA.ODBC.UTILITY.HTML" Name="Poco.Data.ODBC.Utility.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.ODBC.Utility.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.DATA.SQLITE.HTML" DiskId="1" Guid="A17CD656-D7AE-4EDE-81CA-B27917C7AB60">
                            <File Id="POCO.DATA.SQLITE.AUTHORIZATIONDENIEDEXCEPTION.HTML" Name="Poco.Data.SQLite.AuthorizationDeniedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.AuthorizationDeniedException.html" />
                            <File Id="POCO.DATA.SQLITE.BINDER.HTML" Name="Poco.Data.SQLite.Binder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.Binder.html" />
                            <File Id="POCO.DATA.SQLITE.CANTOPENDBFILEEXCEPTION.HTML" Name="Poco.Data.SQLite.CantOpenDBFileException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.CantOpenDBFileException.html" />
                            <File Id="POCO.DATA.SQLITE.CONNECTOR.HTML" Name="Poco.Data.SQLite.Connector.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.Connector.html" />
                            <File Id="POCO.DATA.SQLITE.CONSTRAINTVIOLATIONEXCEPTION.HTML" Name="Poco.Data.SQLite.ConstraintViolationException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.ConstraintViolationException.html" />
                            <File Id="POCO.DATA.SQLITE.CORRUPTIMAGEEXCEPTION.HTML" Name="Poco.Data.SQLite.CorruptImageException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.CorruptImageException.html" />
                            <File Id="POCO.DATA.SQLITE.DATABASEFULLEXCEPTION.HTML" Name="Poco.Data.SQLite.DatabaseFullException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.DatabaseFullException.html" />
                            <File Id="POCO.DATA.SQLITE.DATATYPEMISMATCHEXCEPTION.HTML" Name="Poco.Data.SQLite.DataTypeMismatchException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.DataTypeMismatchException.html" />
                            <File Id="POCO.DATA.SQLITE.DBACCESSDENIEDEXCEPTION.HTML" Name="Poco.Data.SQLite.DBAccessDeniedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.DBAccessDeniedException.html" />
                            <File Id="POCO.DATA.SQLITE.DBLOCKEDEXCEPTION.HTML" Name="Poco.Data.SQLite.DBLockedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.DBLockedException.html" />
                            <File Id="POCO.DATA.SQLITE.EXECUTIONABORTEDEXCEPTION.HTML" Name="Poco.Data.SQLite.ExecutionAbortedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.ExecutionAbortedException.html" />
                            <File Id="POCO.DATA.SQLITE.EXTRACTOR.HTML" Name="Poco.Data.SQLite.Extractor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.Extractor.html" />
                            <File Id="POCO.DATA.SQLITE.HTML" Name="Poco.Data.SQLite.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.html" />
                            <File Id="POCO.DATA.SQLITE.INTERNALDBERROREXCEPTION.HTML" Name="Poco.Data.SQLite.InternalDBErrorException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.InternalDBErrorException.html" />
                            <File Id="POCO.DATA.SQLITE.INTERRUPTEXCEPTION.HTML" Name="Poco.Data.SQLite.InterruptException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.InterruptException.html" />
                            <File Id="POCO.DATA.SQLITE.INVALIDLIBRARYUSEEXCEPTION.HTML" Name="Poco.Data.SQLite.InvalidLibraryUseException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.InvalidLibraryUseException.html" />
                            <File Id="POCO.DATA.SQLITE.INVALIDSQLSTATEMENTEXCEPTION.HTML" Name="Poco.Data.SQLite.InvalidSQLStatementException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.InvalidSQLStatementException.html" />
                            <File Id="POCO.DATA.SQLITE.IOERROREXCEPTION.HTML" Name="Poco.Data.SQLite.IOErrorException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.IOErrorException.html" />
                            <File Id="POCO.DATA.SQLITE.LOCKPROTOCOLEXCEPTION.HTML" Name="Poco.Data.SQLite.LockProtocolException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.LockProtocolException.html" />
                            <File Id="POCO.DATA.SQLITE.NOMEMORYEXCEPTION.HTML" Name="Poco.Data.SQLite.NoMemoryException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.NoMemoryException.html" />
                            <File Id="POCO.DATA.SQLITE.NOTIFIER.HTML" Name="Poco.Data.SQLite.Notifier.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.Notifier.html" />
                            <File Id="POCO.DATA.SQLITE.OSFEATURESMISSINGEXCEPTION.HTML" Name="Poco.Data.SQLite.OSFeaturesMissingException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.OSFeaturesMissingException.html" />
                            <File Id="POCO.DATA.SQLITE.PARAMETERCOUNTMISMATCHEXCEPTION.HTML" Name="Poco.Data.SQLite.ParameterCountMismatchException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.ParameterCountMismatchException.html" />
                            <File Id="POCO.DATA.SQLITE.READONLYEXCEPTION.HTML" Name="Poco.Data.SQLite.ReadOnlyException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.ReadOnlyException.html" />
                            <File Id="POCO.DATA.SQLITE.ROWTOOBIGEXCEPTION.HTML" Name="Poco.Data.SQLite.RowTooBigException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.RowTooBigException.html" />
                            <File Id="POCO.DATA.SQLITE.SCHEMADIFFERSEXCEPTION.HTML" Name="Poco.Data.SQLite.SchemaDiffersException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.SchemaDiffersException.html" />
                            <File Id="POCO.DATA.SQLITE.SESSIONIMPL.HTML" Name="Poco.Data.SQLite.SessionImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.SessionImpl.html" />
                            <File Id="POCO.DATA.SQLITE.SQLITEEXCEPTION.HTML" Name="Poco.Data.SQLite.SQLiteException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.SQLiteException.html" />
                            <File Id="POCO.DATA.SQLITE.SQLITESTATEMENTIMPL.HTML" Name="Poco.Data.SQLite.SQLiteStatementImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.SQLiteStatementImpl.html" />
                            <File Id="POCO.DATA.SQLITE.TABLELOCKEDEXCEPTION.HTML" Name="Poco.Data.SQLite.TableLockedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.TableLockedException.html" />
                            <File Id="POCO.DATA.SQLITE.TABLENOTFOUNDEXCEPTION.HTML" Name="Poco.Data.SQLite.TableNotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.TableNotFoundException.html" />
                            <File Id="POCO.DATA.SQLITE.TRANSACTIONEXCEPTION.HTML" Name="Poco.Data.SQLite.TransactionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.TransactionException.html" />
                            <File Id="POCO.DATA.SQLITE.UTILITY.HTML" Name="Poco.Data.SQLite.Utility.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.SQLite.Utility.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.DATA.MYSQL.HTML" DiskId="1" Guid="8C68EF6F-8213-4E73-9360-AD884BBE0306">
                            <File Id="POCO.DATA.MYSQL.BINDER.HTML" Name="Poco.Data.MySQL.Binder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.Binder.html" />
                            <File Id="POCO.DATA.MYSQL.CONNECTIONEXCEPTION.HTML" Name="Poco.Data.MySQL.ConnectionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.ConnectionException.html" />
                            <File Id="POCO.DATA.MYSQL.CONNECTOR.HTML" Name="Poco.Data.MySQL.Connector.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.Connector.html" />
                            <File Id="POCO.DATA.MYSQL.EXTRACTOR.HTML" Name="Poco.Data.MySQL.Extractor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.Extractor.html" />
                            <File Id="POCO.DATA.MYSQL.HTML" Name="Poco.Data.MySQL.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.html" />
                            <File Id="POCO.DATA.MYSQL.MYSQLEXCEPTION.HTML" Name="Poco.Data.MySQL.MySQLException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.MySQLException.html" />
                            <File Id="POCO.DATA.MYSQL.MYSQLSTATEMENTIMPL.HTML" Name="Poco.Data.MySQL.MySQLStatementImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.MySQLStatementImpl.html" />
                            <File Id="POCO.DATA.MYSQL.RESULTMETADATA.HTML" Name="Poco.Data.MySQL.ResultMetadata.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.ResultMetadata.html" />
                            <File Id="POCO.DATA.MYSQL.SESSIONHANDLE.HTML" Name="Poco.Data.MySQL.SessionHandle.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.SessionHandle.html" />
                            <File Id="POCO.DATA.MYSQL.SESSIONIMPL.HTML" Name="Poco.Data.MySQL.SessionImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.SessionImpl.html" />
                            <File Id="POCO.DATA.MYSQL.STATEMENTEXCEPTION.HTML" Name="Poco.Data.MySQL.StatementException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.StatementException.html" />
                            <File Id="POCO.DATA.MYSQL.STATEMENTEXECUTOR.HTML" Name="Poco.Data.MySQL.StatementExecutor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.StatementExecutor.html" />
                            <File Id="POCO.DATA.MYSQL.TRANSACTIONEXCEPTION.HTML" Name="Poco.Data.MySQL.TransactionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.TransactionException.html" />
                            <File Id="POCO.DATA.MYSQL.UTILITY.HTML" Name="Poco.Data.MySQL.Utility.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Data.MySQL.Utility.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.DYNAMIC.HTML" DiskId="1" Guid="8C2D8FE6-3C2C-48DF-9188-D9E597D777DD">
                            <File Id="POCO.DYNAMIC.HTML" Name="Poco.Dynamic.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.html" />
                            <File Id="POCO.DYNAMIC.IMPL.HTML" Name="Poco.Dynamic.Impl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.Impl.html" />
                            <File Id="POCO.DYNAMIC.PAIR.HTML" Name="Poco.Dynamic.Pair.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.Pair.html" />
                            <File Id="POCO.DYNAMIC.STRUCT.HTML" Name="Poco.Dynamic.Struct.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.Struct.html" />
                            <File Id="POCO.DYNAMIC.VAR.HTML" Name="Poco.Dynamic.Var.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.Var.html" />
                            <File Id="POCO.DYNAMIC.VARHOLDER.HTML" Name="Poco.Dynamic.VarHolder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.VarHolder.html" />
                            <File Id="POCO.DYNAMIC.VARHOLDERIMPL.HTML" Name="Poco.Dynamic.VarHolderImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.VarHolderImpl.html" />
                            <File Id="POCO.DYNAMIC.VARITERATOR.HTML" Name="Poco.Dynamic.VarIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Dynamic.VarIterator.html" />
                            <File Id="POCO.DYNAMICFACTORY.HTML" Name="Poco.DynamicFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DynamicFactory.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.JSON.HTML" DiskId="1" Guid="76C53917-5CBE-4FE2-927A-F00C5750FA3C">
                            <File Id="POCO.JSON.ARRAY.HTML" Name="Poco.JSON.Array.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Array.html" />
                            <File Id="POCO.JSON.HANDLER.HTML" Name="Poco.JSON.Handler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Handler.html" />
                            <File Id="POCO.JSON.HTML" Name="Poco.JSON.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.html" />
                            <File Id="POCO.JSON.JSONEXCEPTION.HTML" Name="Poco.JSON.JSONException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.JSONException.html" />
                            <File Id="POCO.JSON.JSONTEMPLATEEXCEPTION.HTML" Name="Poco.JSON.JSONTemplateException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.JSONTemplateException.html" />
                            <File Id="POCO.JSON.OBJECT.HTML" Name="Poco.JSON.Object.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Object.html" />
                            <File Id="POCO.JSON.PARSEHANDLER.HTML" Name="Poco.JSON.ParseHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.ParseHandler.html" />
                            <File Id="POCO.JSON.PARSER.HTML" Name="Poco.JSON.Parser.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Parser.html" />
                            <File Id="POCO.JSON.PRINTHANDLER.HTML" Name="Poco.JSON.PrintHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.PrintHandler.html" />
                            <File Id="POCO.JSON.QUERY.HTML" Name="Poco.JSON.Query.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Query.html" />
                            <File Id="POCO.JSON.TEMPLATE.HTML" Name="Poco.JSON.Template.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Template.html" />
                            <File Id="POCO.JSON.TEMPLATECACHE.HTML" Name="Poco.JSON.TemplateCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.TemplateCache.html" />
                            <File Id="POCO.JSON.STRINGIFIER.HTML" Name="Poco.JSON.Stringifier.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.JSON.Stringifier.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.MONGODB.HTML" DiskId="1" Guid="5C2424E0-7FD7-4F01-8B80-08A8A4803917">
                            <File Id="POCO.MONGODB.ARRAY.HTML" Name="Poco.MongoDB.Array.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Array.html" />
                            <File Id="POCO.MONGODB.BINARY.HTML" Name="Poco.MongoDB.Binary.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Binary.html" />
                            <File Id="POCO.MONGODB.BSONREADER.HTML" Name="Poco.MongoDB.BSONReader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.BSONReader.html" />
                            <File Id="POCO.MONGODB.BSONWRITER.HTML" Name="Poco.MongoDB.BSONWriter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.BSONWriter.html" />
                            <File Id="POCO.MONGODB.CONCRETEELEMENT.HTML" Name="Poco.MongoDB.ConcreteElement.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ConcreteElement.html" />
                            <File Id="POCO.MONGODB.CONNECTION.HTML" Name="Poco.MongoDB.Connection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Connection.html" />
                            <File Id="POCO.MONGODB.CURSOR.HTML" Name="Poco.MongoDB.Cursor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Cursor.html" />
                            <File Id="POCO.MONGODB.DATABASE.HTML" Name="Poco.MongoDB.Database.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Database.html" />
                            <File Id="POCO.MONGODB.DELETEREQUEST.HTML" Name="Poco.MongoDB.DeleteRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.DeleteRequest.html" />
                            <File Id="POCO.MONGODB.DOCUMENT.HTML" Name="Poco.MongoDB.Document.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Document.html" />
                            <File Id="POCO.MONGODB.ELEMENT.HTML" Name="Poco.MongoDB.Element.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Element.html" />
                            <?if $(var.ProductVersion) =  1.6.1 ?>
                            <File Id="POCO.MONGODB.ELEMENTCOMPARATOR.HTML" Name="Poco.MongoDB.ElementComparator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ElementComparator.html" />
                            <?endif ?>
                            <File Id="POCO.MONGODB.ELEMENTFINDBYNAME.HTML" Name="Poco.MongoDB.ElementFindByName.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ElementFindByName.html" />
                            <File Id="POCO.MONGODB.ELEMENTTRAITS.HTML" Name="Poco.MongoDB.ElementTraits.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ElementTraits.html" />
                            <File Id="POCO.MONGODB.GETMOREREQUEST.HTML" Name="Poco.MongoDB.GetMoreRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.GetMoreRequest.html" />
                            <File Id="POCO.MONGODB.HTML" Name="Poco.MongoDB.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.html" />
                            <File Id="POCO.MONGODB.INSERTREQUEST.HTML" Name="Poco.MongoDB.InsertRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.InsertRequest.html" />
                            <File Id="POCO.MONGODB.JAVASCRIPTCODE.HTML" Name="Poco.MongoDB.JavaScriptCode.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.JavaScriptCode.html" />
                            <File Id="POCO.MONGODB.KILLCURSORSREQUEST.HTML" Name="Poco.MongoDB.KillCursorsRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.KillCursorsRequest.html" />
                            <File Id="POCO.MONGODB.MESSAGE.HTML" Name="Poco.MongoDB.Message.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.Message.html" />
                            <File Id="POCO.MONGODB.MESSAGEHEADER.HTML" Name="Poco.MongoDB.MessageHeader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.MessageHeader.html" />
                            <File Id="POCO.MONGODB.OBJECTID.HTML" Name="Poco.MongoDB.ObjectId.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ObjectId.html" />
                            <File Id="POCO.MONGODB.POOLEDCONNECTION.HTML" Name="Poco.MongoDB.PooledConnection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.PooledConnection.html" />
                            <File Id="POCO.MONGODB.QUERYREQUEST.HTML" Name="Poco.MongoDB.QueryRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.QueryRequest.html" />
                            <File Id="POCO.MONGODB.REGULAREXPRESSION.HTML" Name="Poco.MongoDB.RegularExpression.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.RegularExpression.html" />
                            <File Id="POCO.MONGODB.REPLICASET.HTML" Name="Poco.MongoDB.ReplicaSet.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ReplicaSet.html" />
                            <File Id="POCO.MONGODB.REQUESTMESSAGE.HTML" Name="Poco.MongoDB.RequestMessage.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.RequestMessage.html" />
                            <File Id="POCO.MONGODB.RESPONSEMESSAGE.HTML" Name="Poco.MongoDB.ResponseMessage.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.ResponseMessage.html" />
                            <File Id="POCO.MONGODB.UPDATEREQUEST.HTML" Name="Poco.MongoDB.UpdateRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MongoDB.UpdateRequest.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.NET.HTML" DiskId="1" Guid="0817E69D-1876-44F2-8A13-4071F97A2C73">
                            <File Id="POCO.NET.ABSTRACTHTTPREQUESTHANDLER.HTML" Name="Poco.Net.AbstractHTTPRequestHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.AbstractHTTPRequestHandler.html" />
                            <File Id="POCO.NET.ACCEPTCERTIFICATEHANDLER.HTML" Name="Poco.Net.AcceptCertificateHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.AcceptCertificateHandler.html" />
                            <File Id="POCO.NET.CERTIFICATEHANDLERFACTORY.HTML" Name="Poco.Net.CertificateHandlerFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.CertificateHandlerFactory.html" />
                            <File Id="POCO.NET.CERTIFICATEHANDLERFACTORYIMPL.HTML" Name="Poco.Net.CertificateHandlerFactoryImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.CertificateHandlerFactoryImpl.html" />
                            <File Id="POCO.NET.CERTIFICATEHANDLERFACTORYMGR.HTML" Name="Poco.Net.CertificateHandlerFactoryMgr.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.CertificateHandlerFactoryMgr.html" />
                            <File Id="POCO.NET.CERTIFICATEHANDLERFACTORYREGISTRAR.HTML" Name="Poco.Net.CertificateHandlerFactoryRegistrar.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.CertificateHandlerFactoryRegistrar.html" />
                            <File Id="POCO.NET.CERTIFICATEVALIDATIONEXCEPTION.HTML" Name="Poco.Net.CertificateValidationException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.CertificateValidationException.html" />
                            <File Id="POCO.NET.CONNECTIONABORTEDEXCEPTION.HTML" Name="Poco.Net.ConnectionAbortedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ConnectionAbortedException.html" />
                            <File Id="POCO.NET.CONNECTIONREFUSEDEXCEPTION.HTML" Name="Poco.Net.ConnectionRefusedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ConnectionRefusedException.html" />
                            <File Id="POCO.NET.CONNECTIONRESETEXCEPTION.HTML" Name="Poco.Net.ConnectionResetException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ConnectionResetException.html" />
                            <File Id="POCO.NET.CONSOLECERTIFICATEHANDLER.HTML" Name="Poco.Net.ConsoleCertificateHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ConsoleCertificateHandler.html" />
                            <File Id="POCO.NET.CONTEXT.HTML" Name="Poco.Net.Context.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.Context.html" />
                            <File Id="POCO.NET.DATAGRAMSOCKET.HTML" Name="Poco.Net.DatagramSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.DatagramSocket.html" />
                            <File Id="POCO.NET.DATAGRAMSOCKETIMPL.HTML" Name="Poco.Net.DatagramSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.DatagramSocketImpl.html" />
                            <File Id="POCO.NET.DIALOGSOCKET.HTML" Name="Poco.Net.DialogSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.DialogSocket.html" />
                            <File Id="POCO.NET.DNS.HTML" Name="Poco.Net.DNS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.DNS.html" />
                            <File Id="POCO.NET.DNSEXCEPTION.HTML" Name="Poco.Net.DNSException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.DNSException.html" />
                            <File Id="POCO.NET.ERRORNOTIFICATION.HTML" Name="Poco.Net.ErrorNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ErrorNotification.html" />
                            <File Id="POCO.NET.FILEPARTSOURCE.HTML" Name="Poco.Net.FilePartSource.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FilePartSource.html" />
                            <File Id="POCO.NET.FILEPARTSTORE.HTML" Name="Poco.Net.FilePartStore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FilePartStore.html" />
                            <File Id="POCO.NET.FILEPARTSTOREFACTORY.HTML" Name="Poco.Net.FilePartStoreFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FilePartStoreFactory.html" />
                            <File Id="POCO.NET.FTPCLIENTSESSION.HTML" Name="Poco.Net.FTPClientSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FTPClientSession.html" />
                            <File Id="POCO.NET.FTPEXCEPTION.HTML" Name="Poco.Net.FTPException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FTPException.html" />
                            <File Id="POCO.NET.FTPPASSWORDPROVIDER.HTML" Name="Poco.Net.FTPPasswordProvider.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FTPPasswordProvider.html" />
                            <File Id="POCO.NET.FTPSTREAMFACTORY.HTML" Name="Poco.Net.FTPStreamFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.FTPStreamFactory.html" />
                            <File Id="POCO.NET.HOSTENTRY.HTML" Name="Poco.Net.HostEntry.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HostEntry.html" />
                            <File Id="POCO.NET.HOSTNOTFOUNDEXCEPTION.HTML" Name="Poco.Net.HostNotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HostNotFoundException.html" />
                            <File Id="POCO.NET.HTML" Name="Poco.Net.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.html" />
                            <File Id="POCO.NET.HTMLFORM.HTML" Name="Poco.Net.HTMLForm.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTMLForm.html" />
                            <File Id="POCO.NET.HTMLFORM.PART.HTML" Name="Poco.Net.HTMLForm.Part.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTMLForm.Part.html" />
                            <File Id="POCO.NET.HTMLFORMEXCEPTION.HTML" Name="Poco.Net.HTMLFormException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTMLFormException.html" />
                            <File Id="POCO.NET.HTTPAUTHENTICATIONPARAMS.HTML" Name="Poco.Net.HTTPAuthenticationParams.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPAuthenticationParams.html" />
                            <File Id="POCO.NET.HTTPBASICCREDENTIALS.HTML" Name="Poco.Net.HTTPBasicCredentials.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPBasicCredentials.html" />
                            <File Id="POCO.NET.HTTPBUFFERALLOCATOR.HTML" Name="Poco.Net.HTTPBufferAllocator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPBufferAllocator.html" />
                            <File Id="POCO.NET.HTTPCHUNKEDINPUTSTREAM.HTML" Name="Poco.Net.HTTPChunkedInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPChunkedInputStream.html" />
                            <File Id="POCO.NET.HTTPCHUNKEDIOS.HTML" Name="Poco.Net.HTTPChunkedIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPChunkedIOS.html" />
                            <File Id="POCO.NET.HTTPCHUNKEDOUTPUTSTREAM.HTML" Name="Poco.Net.HTTPChunkedOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPChunkedOutputStream.html" />
                            <File Id="POCO.NET.HTTPCHUNKEDSTREAMBUF.HTML" Name="Poco.Net.HTTPChunkedStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPChunkedStreamBuf.html" />
                            <File Id="POCO.NET.HTTPCLIENTSESSION.HTML" Name="Poco.Net.HTTPClientSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPClientSession.html" />
                            <File Id="POCO.NET.HTTPCLIENTSESSION.PROXYCONFIG.HTML" Name="Poco.Net.HTTPClientSession.ProxyConfig.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPClientSession.ProxyConfig.html" />
                            <File Id="POCO.NET.HTTPCOOKIE.HTML" Name="Poco.Net.HTTPCookie.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPCookie.html" />
                            <File Id="POCO.NET.HTTPCREDENTIALS.HTML" Name="Poco.Net.HTTPCredentials.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPCredentials.html" />
                            <File Id="POCO.NET.HTTPDIGESTCREDENTIALS.HTML" Name="Poco.Net.HTTPDigestCredentials.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPDigestCredentials.html" />
                            <File Id="POCO.NET.HTTPEXCEPTION.HTML" Name="Poco.Net.HTTPException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPException.html" />
                            <File Id="POCO.NET.HTTPFIXEDLENGTHINPUTSTREAM.HTML" Name="Poco.Net.HTTPFixedLengthInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPFixedLengthInputStream.html" />
                            <File Id="POCO.NET.HTTPFIXEDLENGTHIOS.HTML" Name="Poco.Net.HTTPFixedLengthIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPFixedLengthIOS.html" />
                            <File Id="POCO.NET.HTTPFIXEDLENGTHOUTPUTSTREAM.HTML" Name="Poco.Net.HTTPFixedLengthOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPFixedLengthOutputStream.html" />
                            <File Id="POCO.NET.HTTPFIXEDLENGTHSTREAMBUF.HTML" Name="Poco.Net.HTTPFixedLengthStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPFixedLengthStreamBuf.html" />
                            <File Id="POCO.NET.HTTPHEADERINPUTSTREAM.HTML" Name="Poco.Net.HTTPHeaderInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPHeaderInputStream.html" />
                            <File Id="POCO.NET.HTTPHEADERIOS.HTML" Name="Poco.Net.HTTPHeaderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPHeaderIOS.html" />
                            <File Id="POCO.NET.HTTPHEADEROUTPUTSTREAM.HTML" Name="Poco.Net.HTTPHeaderOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPHeaderOutputStream.html" />
                            <File Id="POCO.NET.HTTPHEADERSTREAMBUF.HTML" Name="Poco.Net.HTTPHeaderStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPHeaderStreamBuf.html" />
                            <File Id="POCO.NET.HTTPINPUTSTREAM.HTML" Name="Poco.Net.HTTPInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPInputStream.html" />
                            <File Id="POCO.NET.HTTPIOS.HTML" Name="Poco.Net.HTTPIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPIOS.html" />
                            <File Id="POCO.NET.HTTPMESSAGE.HTML" Name="Poco.Net.HTTPMessage.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPMessage.html" />
                            <File Id="POCO.NET.HTTPOUTPUTSTREAM.HTML" Name="Poco.Net.HTTPOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPOutputStream.html" />
                            <File Id="POCO.NET.HTTPREQUEST.HTML" Name="Poco.Net.HTTPRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPRequest.html" />
                            <File Id="POCO.NET.HTTPREQUESTHANDLER.HTML" Name="Poco.Net.HTTPRequestHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPRequestHandler.html" />
                            <File Id="POCO.NET.HTTPREQUESTHANDLERFACTORY.HTML" Name="Poco.Net.HTTPRequestHandlerFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPRequestHandlerFactory.html" />
                            <File Id="POCO.NET.HTTPRESPONSE.HTML" Name="Poco.Net.HTTPResponse.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPResponse.html" />
                            <File Id="POCO.NET.HTTPRESPONSEIOS.HTML" Name="Poco.Net.HTTPResponseIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPResponseIOS.html" />
                            <File Id="POCO.NET.HTTPRESPONSESTREAM.HTML" Name="Poco.Net.HTTPResponseStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPResponseStream.html" />
                            <File Id="POCO.NET.HTTPRESPONSESTREAMBUF.HTML" Name="Poco.Net.HTTPResponseStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPResponseStreamBuf.html" />
                            <File Id="POCO.NET.HTTPSCLIENTSESSION.HTML" Name="Poco.Net.HTTPSClientSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSClientSession.html" />
                            <File Id="POCO.NET.HTTPSERVER.HTML" Name="Poco.Net.HTTPServer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServer.html" />
                            <File Id="POCO.NET.HTTPSERVERCONNECTION.HTML" Name="Poco.Net.HTTPServerConnection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerConnection.html" />
                            <File Id="POCO.NET.HTTPSERVERCONNECTIONFACTORY.HTML" Name="Poco.Net.HTTPServerConnectionFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerConnectionFactory.html" />
                            <File Id="POCO.NET.HTTPSERVERPARAMS.HTML" Name="Poco.Net.HTTPServerParams.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerParams.html" />
                            <File Id="POCO.NET.HTTPSERVERREQUEST.HTML" Name="Poco.Net.HTTPServerRequest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerRequest.html" />
                            <File Id="POCO.NET.HTTPSERVERREQUESTIMPL.HTML" Name="Poco.Net.HTTPServerRequestImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerRequestImpl.html" />
                            <File Id="POCO.NET.HTTPSERVERRESPONSE.HTML" Name="Poco.Net.HTTPServerResponse.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerResponse.html" />
                            <File Id="POCO.NET.HTTPSERVERRESPONSEIMPL.HTML" Name="Poco.Net.HTTPServerResponseImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerResponseImpl.html" />
                            <File Id="POCO.NET.HTTPSERVERSESSION.HTML" Name="Poco.Net.HTTPServerSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPServerSession.html" />
                            <File Id="POCO.NET.HTTPSESSION.HTML" Name="Poco.Net.HTTPSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSession.html" />
                            <File Id="POCO.NET.HTTPSESSIONFACTORY.HTML" Name="Poco.Net.HTTPSessionFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSessionFactory.html" />
                            <File Id="POCO.NET.HTTPSESSIONFACTORY.INSTANTIATORINFO.HTML" Name="Poco.Net.HTTPSessionFactory.InstantiatorInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSessionFactory.InstantiatorInfo.html" />
                            <File Id="POCO.NET.HTTPSESSIONINSTANTIATOR.HTML" Name="Poco.Net.HTTPSessionInstantiator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSessionInstantiator.html" />
                            <File Id="POCO.NET.HTTPSSESSIONINSTANTIATOR.HTML" Name="Poco.Net.HTTPSSessionInstantiator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSSessionInstantiator.html" />
                            <File Id="POCO.NET.HTTPSSTREAMFACTORY.HTML" Name="Poco.Net.HTTPSStreamFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPSStreamFactory.html" />
                            <File Id="POCO.NET.HTTPSTREAMBUF.HTML" Name="Poco.Net.HTTPStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPStreamBuf.html" />
                            <File Id="POCO.NET.HTTPSTREAMFACTORY.HTML" Name="Poco.Net.HTTPStreamFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.HTTPStreamFactory.html" />
                            <File Id="POCO.NET.ICMPCLIENT.HTML" Name="Poco.Net.ICMPClient.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPClient.html" />
                            <File Id="POCO.NET.ICMPEVENTARGS.HTML" Name="Poco.Net.ICMPEventArgs.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPEventArgs.html" />
                            <File Id="POCO.NET.ICMPEXCEPTION.HTML" Name="Poco.Net.ICMPException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPException.html" />
                            <File Id="POCO.NET.ICMPPACKET.HTML" Name="Poco.Net.ICMPPacket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPPacket.html" />
                            <File Id="POCO.NET.ICMPPACKETIMPL.HTML" Name="Poco.Net.ICMPPacketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPPacketImpl.html" />
                            <File Id="POCO.NET.ICMPSOCKET.HTML" Name="Poco.Net.ICMPSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPSocket.html" />
                            <File Id="POCO.NET.ICMPSOCKETIMPL.HTML" Name="Poco.Net.ICMPSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPSocketImpl.html" />
                            <File Id="POCO.NET.ICMPV4PACKETIMPL.HEADER.HTML" Name="Poco.Net.ICMPv4PacketImpl.Header.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPv4PacketImpl.Header.html" />
                            <File Id="POCO.NET.ICMPV4PACKETIMPL.HTML" Name="Poco.Net.ICMPv4PacketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ICMPv4PacketImpl.html" />
                            <File Id="POCO.NET.IDLENOTIFICATION.HTML" Name="Poco.Net.IdleNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.IdleNotification.html" />
                            <File Id="POCO.NET.INTERFACENOTFOUNDEXCEPTION.HTML" Name="Poco.Net.InterfaceNotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.InterfaceNotFoundException.html" />
                            <File Id="POCO.NET.INVALIDADDRESSEXCEPTION.HTML" Name="Poco.Net.InvalidAddressException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.InvalidAddressException.html" />
                            <File Id="POCO.NET.INVALIDCERTIFICATEEXCEPTION.HTML" Name="Poco.Net.InvalidCertificateException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.InvalidCertificateException.html" />
                            <File Id="POCO.NET.INVALIDCERTIFICATEHANDLER.HTML" Name="Poco.Net.InvalidCertificateHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.InvalidCertificateHandler.html" />
                            <File Id="POCO.NET.INVALIDSOCKETEXCEPTION.HTML" Name="Poco.Net.InvalidSocketException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.InvalidSocketException.html" />
                            <File Id="POCO.NET.IPADDRESS.HTML" Name="Poco.Net.IPAddress.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.IPAddress.html" />
                            <File Id="POCO.NET.KEYCONSOLEHANDLER.HTML" Name="Poco.Net.KeyConsoleHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.KeyConsoleHandler.html" />
                            <File Id="POCO.NET.KEYFILEHANDLER.HTML" Name="Poco.Net.KeyFileHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.KeyFileHandler.html" />
                            <File Id="POCO.NET.MAILINPUTSTREAM.HTML" Name="Poco.Net.MailInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailInputStream.html" />
                            <File Id="POCO.NET.MAILIOS.HTML" Name="Poco.Net.MailIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailIOS.html" />
                            <File Id="POCO.NET.MAILMESSAGE.HTML" Name="Poco.Net.MailMessage.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailMessage.html" />
                            <File Id="POCO.NET.MAILMESSAGE.PART.HTML" Name="Poco.Net.MailMessage.Part.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailMessage.Part.html" />
                            <File Id="POCO.NET.MAILOUTPUTSTREAM.HTML" Name="Poco.Net.MailOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailOutputStream.html" />
                            <File Id="POCO.NET.MAILRECIPIENT.HTML" Name="Poco.Net.MailRecipient.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailRecipient.html" />
                            <File Id="POCO.NET.MAILSTREAMBUF.HTML" Name="Poco.Net.MailStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MailStreamBuf.html" />
                            <File Id="POCO.NET.MEDIATYPE.HTML" Name="Poco.Net.MediaType.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MediaType.html" />
                            <File Id="POCO.NET.MESSAGEEXCEPTION.HTML" Name="Poco.Net.MessageException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MessageException.html" />
                            <File Id="POCO.NET.MESSAGEHEADER.HTML" Name="Poco.Net.MessageHeader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MessageHeader.html" />
                            <File Id="POCO.NET.MULTIPARTEXCEPTION.HTML" Name="Poco.Net.MultipartException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MultipartException.html" />
                            <File Id="POCO.NET.MULTIPARTINPUTSTREAM.HTML" Name="Poco.Net.MultipartInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MultipartInputStream.html" />
                            <File Id="POCO.NET.MULTIPARTIOS.HTML" Name="Poco.Net.MultipartIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MultipartIOS.html" />
                            <File Id="POCO.NET.MULTIPARTREADER.HTML" Name="Poco.Net.MultipartReader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MultipartReader.html" />
                            <File Id="POCO.NET.MULTIPARTSTREAMBUF.HTML" Name="Poco.Net.MultipartStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MultipartStreamBuf.html" />
                            <File Id="POCO.NET.MULTIPARTWRITER.HTML" Name="Poco.Net.MultipartWriter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.MultipartWriter.html" />
                            <File Id="POCO.NET.NAMEVALUECOLLECTION.HTML" Name="Poco.Net.NameValueCollection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NameValueCollection.html" />
                            <File Id="POCO.NET.NETEXCEPTION.HTML" Name="Poco.Net.NetException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NetException.html" />
                            <File Id="POCO.NET.NOADDRESSFOUNDEXCEPTION.HTML" Name="Poco.Net.NoAddressFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NoAddressFoundException.html" />
                            <File Id="POCO.NET.NOMESSAGEEXCEPTION.HTML" Name="Poco.Net.NoMessageException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NoMessageException.html" />
                            <File Id="POCO.NET.NOTAUTHENTICATEDEXCEPTION.HTML" Name="Poco.Net.NotAuthenticatedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NotAuthenticatedException.html" />
                            <File Id="POCO.NET.NTPCLIENT.HTML" Name="Poco.Net.NTPClient.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NTPClient.html" />
                            <File Id="POCO.NET.NTPEVENTARGS.HTML" Name="Poco.Net.NTPEventArgs.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NTPEventArgs.html" />
                            <File Id="POCO.NET.NTPEXCEPTION.HTML" Name="Poco.Net.NTPException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NTPException.html" />
                            <File Id="POCO.NET.NTPPACKET.HTML" Name="Poco.Net.NTPPacket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NTPPacket.html" />
                            <File Id="POCO.NET.NULLPARTHANDLER.HTML" Name="Poco.Net.NullPartHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.NullPartHandler.html" />
                            <File Id="POCO.NET.OAUTH10CREDENTIALS.HTML" Name="Poco.Net.OAuth10Credentials.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.OAuth10Credentials.html" />
                            <File Id="POCO.NET.OAUTH20CREDENTIALS.HTML" Name="Poco.Net.OAuth20Credentials.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.OAuth20Credentials.html" />
                            <File Id="POCO.NET.PARALLELSOCKETACCEPTOR.HTML" Name="Poco.Net.ParallelSocketAcceptor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ParallelSocketAcceptor.html" />
                            <File Id="POCO.NET.PARALLELSOCKETREACTOR.HTML" Name="Poco.Net.ParallelSocketReactor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ParallelSocketReactor.html" />
                            <File Id="POCO.NET.PARTHANDLER.HTML" Name="Poco.Net.PartHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PartHandler.html" />
                            <File Id="POCO.NET.PARTSOURCE.HTML" Name="Poco.Net.PartSource.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PartSource.html" />
                            <File Id="POCO.NET.PARTSTORE.HTML" Name="Poco.Net.PartStore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PartStore.html" />
                            <File Id="POCO.NET.PARTSTOREFACTORY.HTML" Name="Poco.Net.PartStoreFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PartStoreFactory.html" />
                            <File Id="POCO.NET.POP3CLIENTSESSION.HTML" Name="Poco.Net.POP3ClientSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.POP3ClientSession.html" />
                            <File Id="POCO.NET.POP3CLIENTSESSION.MESSAGEINFO.HTML" Name="Poco.Net.POP3ClientSession.MessageInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.POP3ClientSession.MessageInfo.html" />
                            <File Id="POCO.NET.POP3EXCEPTION.HTML" Name="Poco.Net.POP3Exception.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.POP3Exception.html" />
                            <File Id="POCO.NET.PRIVATEKEYFACTORY.HTML" Name="Poco.Net.PrivateKeyFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PrivateKeyFactory.html" />
                            <File Id="POCO.NET.PRIVATEKEYFACTORYIMPL.HTML" Name="Poco.Net.PrivateKeyFactoryImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PrivateKeyFactoryImpl.html" />
                            <File Id="POCO.NET.PRIVATEKEYFACTORYMGR.HTML" Name="Poco.Net.PrivateKeyFactoryMgr.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PrivateKeyFactoryMgr.html" />
                            <File Id="POCO.NET.PRIVATEKEYFACTORYREGISTRAR.HTML" Name="Poco.Net.PrivateKeyFactoryRegistrar.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PrivateKeyFactoryRegistrar.html" />
                            <File Id="POCO.NET.PRIVATEKEYPASSPHRASEHANDLER.HTML" Name="Poco.Net.PrivateKeyPassphraseHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.PrivateKeyPassphraseHandler.html" />
                            <File Id="POCO.NET.QUOTEDPRINTABLEDECODER.HTML" Name="Poco.Net.QuotedPrintableDecoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.QuotedPrintableDecoder.html" />
                            <File Id="POCO.NET.QUOTEDPRINTABLEDECODERBUF.HTML" Name="Poco.Net.QuotedPrintableDecoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.QuotedPrintableDecoderBuf.html" />
                            <File Id="POCO.NET.QUOTEDPRINTABLEDECODERIOS.HTML" Name="Poco.Net.QuotedPrintableDecoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.QuotedPrintableDecoderIOS.html" />
                            <File Id="POCO.NET.QUOTEDPRINTABLEENCODER.HTML" Name="Poco.Net.QuotedPrintableEncoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.QuotedPrintableEncoder.html" />
                            <File Id="POCO.NET.QUOTEDPRINTABLEENCODERBUF.HTML" Name="Poco.Net.QuotedPrintableEncoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.QuotedPrintableEncoderBuf.html" />
                            <File Id="POCO.NET.QUOTEDPRINTABLEENCODERIOS.HTML" Name="Poco.Net.QuotedPrintableEncoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.QuotedPrintableEncoderIOS.html" />
                            <File Id="POCO.NET.RAWSOCKET.HTML" Name="Poco.Net.RawSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.RawSocket.html" />
                            <File Id="POCO.NET.RAWSOCKETIMPL.HTML" Name="Poco.Net.RawSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.RawSocketImpl.html" />
                            <File Id="POCO.NET.READABLENOTIFICATION.HTML" Name="Poco.Net.ReadableNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ReadableNotification.html" />
                            <File Id="POCO.NET.REJECTCERTIFICATEHANDLER.HTML" Name="Poco.Net.RejectCertificateHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.RejectCertificateHandler.html" />
                            <File Id="POCO.NET.REMOTESYSLOGCHANNEL.HTML" Name="Poco.Net.RemoteSyslogChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.RemoteSyslogChannel.html" />
                            <File Id="POCO.NET.REMOTESYSLOGLISTENER.HTML" Name="Poco.Net.RemoteSyslogListener.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.RemoteSyslogListener.html" />
                            <File Id="POCO.NET.SECURESERVERSOCKET.HTML" Name="Poco.Net.SecureServerSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SecureServerSocket.html" />
                            <File Id="POCO.NET.SECURESERVERSOCKETIMPL.HTML" Name="Poco.Net.SecureServerSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SecureServerSocketImpl.html" />
                            <File Id="POCO.NET.SECURESMTPCLIENTSESSION.HTML" Name="Poco.Net.SecureSMTPClientSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SecureSMTPClientSession.html" />
                            <File Id="POCO.NET.SECURESOCKETIMPL.HTML" Name="Poco.Net.SecureSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SecureSocketImpl.html" />
                            <File Id="POCO.NET.SECURESTREAMSOCKET.HTML" Name="Poco.Net.SecureStreamSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SecureStreamSocket.html" />
                            <File Id="POCO.NET.SECURESTREAMSOCKETIMPL.HTML" Name="Poco.Net.SecureStreamSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SecureStreamSocketImpl.html" />
                            <File Id="POCO.NET.SERVERSOCKET.HTML" Name="Poco.Net.ServerSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ServerSocket.html" />
                            <File Id="POCO.NET.SERVERSOCKETIMPL.HTML" Name="Poco.Net.ServerSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ServerSocketImpl.html" />
                            <File Id="POCO.NET.SERVICENOTFOUNDEXCEPTION.HTML" Name="Poco.Net.ServiceNotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ServiceNotFoundException.html" />
                            <File Id="POCO.NET.SESSION.HTML" Name="Poco.Net.Session.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.Session.html" />
                            <File Id="POCO.NET.SHUTDOWNNOTIFICATION.HTML" Name="Poco.Net.ShutdownNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.ShutdownNotification.html" />
                            <File Id="POCO.NET.SMTPCHANNEL.HTML" Name="Poco.Net.SMTPChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SMTPChannel.html" />
                            <File Id="POCO.NET.SMTPCLIENTSESSION.HTML" Name="Poco.Net.SMTPClientSession.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SMTPClientSession.html" />
                            <File Id="POCO.NET.SMTPEXCEPTION.HTML" Name="Poco.Net.SMTPException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SMTPException.html" />
                            <File Id="POCO.NET.SOCKET.HTML" Name="Poco.Net.Socket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.Socket.html" />
                            <File Id="POCO.NET.SOCKETACCEPTOR.HTML" Name="Poco.Net.SocketAcceptor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketAcceptor.html" />
                            <File Id="POCO.NET.SOCKETADDRESS.HTML" Name="Poco.Net.SocketAddress.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketAddress.html" />
                            <File Id="POCO.NET.SOCKETCONNECTOR.HTML" Name="Poco.Net.SocketConnector.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketConnector.html" />
                            <File Id="POCO.NET.SOCKETIMPL.HTML" Name="Poco.Net.SocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketImpl.html" />
                            <File Id="POCO.NET.SOCKETINPUTSTREAM.HTML" Name="Poco.Net.SocketInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketInputStream.html" />
                            <File Id="POCO.NET.SOCKETIOS.HTML" Name="Poco.Net.SocketIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketIOS.html" />
                            <File Id="POCO.NET.SOCKETNOTIFICATION.HTML" Name="Poco.Net.SocketNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketNotification.html" />
                            <File Id="POCO.NET.SOCKETNOTIFIER.HTML" Name="Poco.Net.SocketNotifier.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketNotifier.html" />
                            <File Id="POCO.NET.SOCKETOUTPUTSTREAM.HTML" Name="Poco.Net.SocketOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketOutputStream.html" />
                            <File Id="POCO.NET.SOCKETREACTOR.HTML" Name="Poco.Net.SocketReactor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketReactor.html" />
                            <File Id="POCO.NET.SOCKETSTREAM.HTML" Name="Poco.Net.SocketStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketStream.html" />
                            <File Id="POCO.NET.SOCKETSTREAMBUF.HTML" Name="Poco.Net.SocketStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SocketStreamBuf.html" />
                            <File Id="POCO.NET.SSLCONNECTIONUNEXPECTEDLYCLOSEDEXCEPTION.HTML" Name="Poco.Net.SSLConnectionUnexpectedlyClosedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SSLConnectionUnexpectedlyClosedException.html" />
                            <File Id="POCO.NET.SSLCONTEXTEXCEPTION.HTML" Name="Poco.Net.SSLContextException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SSLContextException.html" />
                            <File Id="POCO.NET.SSLEXCEPTION.HTML" Name="Poco.Net.SSLException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SSLException.html" />
                            <File Id="POCO.NET.SSLMANAGER.HTML" Name="Poco.Net.SSLManager.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.SSLManager.html" />
                            <File Id="POCO.NET.STREAMSOCKET.HTML" Name="Poco.Net.StreamSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.StreamSocket.html" />
                            <File Id="POCO.NET.STREAMSOCKETIMPL.HTML" Name="Poco.Net.StreamSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.StreamSocketImpl.html" />
                            <File Id="POCO.NET.STRINGPARTSOURCE.HTML" Name="Poco.Net.StringPartSource.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.StringPartSource.html" />
                            <File Id="POCO.NET.TCPSERVER.HTML" Name="Poco.Net.TCPServer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TCPServer.html" />
                            <File Id="POCO.NET.TCPSERVERCONNECTION.HTML" Name="Poco.Net.TCPServerConnection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TCPServerConnection.html" />
                            <File Id="POCO.NET.TCPSERVERCONNECTIONFACTORY.HTML" Name="Poco.Net.TCPServerConnectionFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TCPServerConnectionFactory.html" />
                            <File Id="POCO.NET.TCPSERVERCONNECTIONFACTORYIMPL.HTML" Name="Poco.Net.TCPServerConnectionFactoryImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TCPServerConnectionFactoryImpl.html" />
                            <File Id="POCO.NET.TCPSERVERDISPATCHER.HTML" Name="Poco.Net.TCPServerDispatcher.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TCPServerDispatcher.html" />
                            <File Id="POCO.NET.TCPSERVERPARAMS.HTML" Name="Poco.Net.TCPServerParams.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TCPServerParams.html" />
                            <File Id="POCO.NET.TIMEOUTNOTIFICATION.HTML" Name="Poco.Net.TimeoutNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.TimeoutNotification.html" />
                            <File Id="POCO.NET.UNSUPPORTEDFAMILYEXCEPTION.HTML" Name="Poco.Net.UnsupportedFamilyException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.UnsupportedFamilyException.html" />
                            <File Id="POCO.NET.UNSUPPORTEDREDIRECTEXCEPTION.HTML" Name="Poco.Net.UnsupportedRedirectException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.UnsupportedRedirectException.html" />
                            <File Id="POCO.NET.UTILITY.HTML" Name="Poco.Net.Utility.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.Utility.html" />
                            <File Id="POCO.NET.VERIFICATIONERRORARGS.HTML" Name="Poco.Net.VerificationErrorArgs.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.VerificationErrorArgs.html" />
                            <File Id="POCO.NET.WEBSOCKET.HTML" Name="Poco.Net.WebSocket.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.WebSocket.html" />
                            <File Id="POCO.NET.WEBSOCKETEXCEPTION.HTML" Name="Poco.Net.WebSocketException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.WebSocketException.html" />
                            <File Id="POCO.NET.WEBSOCKETIMPL.HTML" Name="Poco.Net.WebSocketImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.WebSocketImpl.html" />
                            <File Id="POCO.NET.WRITABLENOTIFICATION.HTML" Name="Poco.Net.WritableNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.WritableNotification.html" />
                            <File Id="POCO.NET.X509CERTIFICATE.HTML" Name="Poco.Net.X509Certificate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Net.X509Certificate.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.XML.HTML" DiskId="1" Guid="C11D095A-C1E7-4421-B72A-DE5A9C60633B">
                            <File Id="POCO.XML.ABSTRACTCONTAINERNODE.HTML" Name="Poco.XML.AbstractContainerNode.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.AbstractContainerNode.html" />
                            <File Id="POCO.XML.ABSTRACTNODE.HTML" Name="Poco.XML.AbstractNode.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.AbstractNode.html" />
                            <File Id="POCO.XML.ATTR.HTML" Name="Poco.XML.Attr.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Attr.html" />
                            <File Id="POCO.XML.ATTRIBUTES.HTML" Name="Poco.XML.Attributes.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Attributes.html" />
                            <File Id="POCO.XML.ATTRIBUTESIMPL.ATTRIBUTE.HTML" Name="Poco.XML.AttributesImpl.Attribute.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.AttributesImpl.Attribute.html" />
                            <File Id="POCO.XML.ATTRIBUTESIMPL.HTML" Name="Poco.XML.AttributesImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.AttributesImpl.html" />
                            <File Id="POCO.XML.ATTRMAP.HTML" Name="Poco.XML.AttrMap.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.AttrMap.html" />
                            <File Id="POCO.XML.CDATASECTION.HTML" Name="Poco.XML.CDATASection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.CDATASection.html" />
                            <File Id="POCO.XML.CHARACTERDATA.HTML" Name="Poco.XML.CharacterData.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.CharacterData.html" />
                            <File Id="POCO.XML.CHILDNODESLIST.HTML" Name="Poco.XML.ChildNodesList.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ChildNodesList.html" />
                            <File Id="POCO.XML.COMMENT.HTML" Name="Poco.XML.Comment.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Comment.html" />
                            <File Id="POCO.XML.CONTENTHANDLER.HTML" Name="Poco.XML.ContentHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ContentHandler.html" />
                            <File Id="POCO.XML.DECLHANDLER.HTML" Name="Poco.XML.DeclHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DeclHandler.html" />
                            <File Id="POCO.XML.DEFAULTHANDLER.HTML" Name="Poco.XML.DefaultHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DefaultHandler.html" />
                            <File Id="POCO.XML.DOCUMENT.HTML" Name="Poco.XML.Document.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Document.html" />
                            <File Id="POCO.XML.DOCUMENTEVENT.HTML" Name="Poco.XML.DocumentEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DocumentEvent.html" />
                            <File Id="POCO.XML.DOCUMENTFRAGMENT.HTML" Name="Poco.XML.DocumentFragment.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DocumentFragment.html" />
                            <File Id="POCO.XML.DOCUMENTTYPE.HTML" Name="Poco.XML.DocumentType.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DocumentType.html" />
                            <File Id="POCO.XML.DOMBUILDER.HTML" Name="Poco.XML.DOMBuilder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMBuilder.html" />
                            <File Id="POCO.XML.DOMEXCEPTION.HTML" Name="Poco.XML.DOMException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMException.html" />
                            <File Id="POCO.XML.DOMIMPLEMENTATION.HTML" Name="Poco.XML.DOMImplementation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMImplementation.html" />
                            <File Id="POCO.XML.DOMOBJECT.HTML" Name="Poco.XML.DOMObject.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMObject.html" />
                            <File Id="POCO.XML.DOMPARSER.HTML" Name="Poco.XML.DOMParser.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMParser.html" />
                            <File Id="POCO.XML.DOMSERIALIZER.HTML" Name="Poco.XML.DOMSerializer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMSerializer.html" />
                            <File Id="POCO.XML.DOMWRITER.HTML" Name="Poco.XML.DOMWriter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DOMWriter.html" />
                            <File Id="POCO.XML.DTDHANDLER.HTML" Name="Poco.XML.DTDHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DTDHandler.html" />
                            <File Id="POCO.XML.DTDMAP.HTML" Name="Poco.XML.DTDMap.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.DTDMap.html" />
                            <File Id="POCO.XML.ELEMENT.HTML" Name="Poco.XML.Element.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Element.html" />
                            <File Id="POCO.XML.ELEMENTSBYTAGNAMELIST.HTML" Name="Poco.XML.ElementsByTagNameList.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ElementsByTagNameList.html" />
                            <File Id="POCO.XML.ELEMENTSBYTAGNAMELISTNS.HTML" Name="Poco.XML.ElementsByTagNameListNS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ElementsByTagNameListNS.html" />
                            <File Id="POCO.XML.ENTITY.HTML" Name="Poco.XML.Entity.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Entity.html" />
                            <File Id="POCO.XML.ENTITYREFERENCE.HTML" Name="Poco.XML.EntityReference.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EntityReference.html" />
                            <File Id="POCO.XML.ENTITYRESOLVER.HTML" Name="Poco.XML.EntityResolver.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EntityResolver.html" />
                            <File Id="POCO.XML.ENTITYRESOLVERIMPL.HTML" Name="Poco.XML.EntityResolverImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EntityResolverImpl.html" />
                            <File Id="POCO.XML.ERRORHANDLER.HTML" Name="Poco.XML.ErrorHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ErrorHandler.html" />
                            <File Id="POCO.XML.EVENT.HTML" Name="Poco.XML.Event.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Event.html" />
                            <File Id="POCO.XML.EVENTDISPATCHER.EVENTLISTENERITEM.HTML" Name="Poco.XML.EventDispatcher.EventListenerItem.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EventDispatcher.EventListenerItem.html" />
                            <File Id="POCO.XML.EVENTDISPATCHER.HTML" Name="Poco.XML.EventDispatcher.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EventDispatcher.html" />
                            <File Id="POCO.XML.EVENTEXCEPTION.HTML" Name="Poco.XML.EventException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EventException.html" />
                            <File Id="POCO.XML.EVENTLISTENER.HTML" Name="Poco.XML.EventListener.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EventListener.html" />
                            <File Id="POCO.XML.EVENTTARGET.HTML" Name="Poco.XML.EventTarget.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.EventTarget.html" />
                            <File Id="POCO.XML.HTML" Name="Poco.XML.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.html" />
                            <File Id="POCO.XML.INPUTSOURCE.HTML" Name="Poco.XML.InputSource.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.InputSource.html" />
                            <File Id="POCO.XML.LEXICALHANDLER.HTML" Name="Poco.XML.LexicalHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.LexicalHandler.html" />
                            <File Id="POCO.XML.LOCATOR.HTML" Name="Poco.XML.Locator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Locator.html" />
                            <File Id="POCO.XML.LOCATORIMPL.HTML" Name="Poco.XML.LocatorImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.LocatorImpl.html" />
                            <File Id="POCO.XML.MUTATIONEVENT.HTML" Name="Poco.XML.MutationEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.MutationEvent.html" />
                            <File Id="POCO.XML.NAME.HTML" Name="Poco.XML.Name.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Name.html" />
                            <File Id="POCO.XML.NAMEDNODEMAP.HTML" Name="Poco.XML.NamedNodeMap.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NamedNodeMap.html" />
                            <File Id="POCO.XML.NAMEPOOL.HTML" Name="Poco.XML.NamePool.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NamePool.html" />
                            <File Id="POCO.XML.NAMESPACEPREFIXESSTRATEGY.HTML" Name="Poco.XML.NamespacePrefixesStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NamespacePrefixesStrategy.html" />
                            <File Id="POCO.XML.NAMESPACESTRATEGY.HTML" Name="Poco.XML.NamespaceStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NamespaceStrategy.html" />
                            <File Id="POCO.XML.NAMESPACESUPPORT.HTML" Name="Poco.XML.NamespaceSupport.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NamespaceSupport.html" />
                            <File Id="POCO.XML.NODE.HTML" Name="Poco.XML.Node.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Node.html" />
                            <File Id="POCO.XML.NODEAPPENDER.HTML" Name="Poco.XML.NodeAppender.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NodeAppender.html" />
                            <File Id="POCO.XML.NODEFILTER.HTML" Name="Poco.XML.NodeFilter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NodeFilter.html" />
                            <File Id="POCO.XML.NODEITERATOR.HTML" Name="Poco.XML.NodeIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NodeIterator.html" />
                            <File Id="POCO.XML.NODELIST.HTML" Name="Poco.XML.NodeList.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NodeList.html" />
                            <File Id="POCO.XML.NONAMESPACEPREFIXESSTRATEGY.HTML" Name="Poco.XML.NoNamespacePrefixesStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NoNamespacePrefixesStrategy.html" />
                            <File Id="POCO.XML.NONAMESPACESSTRATEGY.HTML" Name="Poco.XML.NoNamespacesStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.NoNamespacesStrategy.html" />
                            <File Id="POCO.XML.NOTATION.HTML" Name="Poco.XML.Notation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Notation.html" />
                            <File Id="POCO.XML.PARSERENGINE.HTML" Name="Poco.XML.ParserEngine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ParserEngine.html" />
                            <File Id="POCO.XML.PROCESSINGINSTRUCTION.HTML" Name="Poco.XML.ProcessingInstruction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.ProcessingInstruction.html" />
                            <File Id="POCO.XML.SAXEXCEPTION.HTML" Name="Poco.XML.SAXException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.SAXException.html" />
                            <File Id="POCO.XML.SAXNOTRECOGNIZEDEXCEPTION.HTML" Name="Poco.XML.SAXNotRecognizedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.SAXNotRecognizedException.html" />
                            <File Id="POCO.XML.SAXNOTSUPPORTEDEXCEPTION.HTML" Name="Poco.XML.SAXNotSupportedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.SAXNotSupportedException.html" />
                            <File Id="POCO.XML.SAXPARSEEXCEPTION.HTML" Name="Poco.XML.SAXParseException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.SAXParseException.html" />
                            <File Id="POCO.XML.SAXPARSER.HTML" Name="Poco.XML.SAXParser.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.SAXParser.html" />
                            <File Id="POCO.XML.TEXT.HTML" Name="Poco.XML.Text.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.Text.html" />
                            <File Id="POCO.XML.TREEWALKER.HTML" Name="Poco.XML.TreeWalker.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.TreeWalker.html" />
                            <File Id="POCO.XML.WHITESPACEFILTER.HTML" Name="Poco.XML.WhitespaceFilter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.WhitespaceFilter.html" />
                            <File Id="POCO.XML.XMLEXCEPTION.HTML" Name="Poco.XML.XMLException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.XMLException.html" />
                            <File Id="POCO.XML.XMLFILTER.HTML" Name="Poco.XML.XMLFilter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.XMLFilter.html" />
                            <File Id="POCO.XML.XMLFILTERIMPL.HTML" Name="Poco.XML.XMLFilterImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.XMLFilterImpl.html" />
                            <File Id="POCO.XML.XMLREADER.HTML" Name="Poco.XML.XMLReader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.XMLReader.html" />
                            <File Id="POCO.XML.XMLWRITER.HTML" Name="Poco.XML.XMLWriter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.XMLWriter.html" />
                            <File Id="POCO.XML.XMLWRITER.NAMESPACE.HTML" Name="Poco.XML.XMLWriter.Namespace.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.XML.XMLWriter.Namespace.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.ZIP.HTML" DiskId="1" Guid="C32378C5-0F66-4D5B-AAF8-9175A7078203">
                            <File Id="POCO.ZIP.ADD.HTML" Name="Poco.Zip.Add.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Add.html" />
                            <File Id="POCO.ZIP.AUTODETECTINPUTSTREAM.HTML" Name="Poco.Zip.AutoDetectInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.AutoDetectInputStream.html" />
                            <File Id="POCO.ZIP.AUTODETECTIOS.HTML" Name="Poco.Zip.AutoDetectIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.AutoDetectIOS.html" />
                            <File Id="POCO.ZIP.AUTODETECTSTREAMBUF.HTML" Name="Poco.Zip.AutoDetectStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.AutoDetectStreamBuf.html" />
                            <File Id="POCO.ZIP.COMPRESS.HTML" Name="Poco.Zip.Compress.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Compress.html" />
                            <File Id="POCO.ZIP.DECOMPRESS.HTML" Name="Poco.Zip.Decompress.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Decompress.html" />
                            <File Id="POCO.ZIP.DELETE.HTML" Name="Poco.Zip.Delete.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Delete.html" />
                            <File Id="POCO.ZIP.HTML" Name="Poco.Zip.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.html" />
                            <File Id="POCO.ZIP.KEEP.HTML" Name="Poco.Zip.Keep.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Keep.html" />
                            <File Id="POCO.ZIP.PARSECALLBACK.HTML" Name="Poco.Zip.ParseCallback.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ParseCallback.html" />
                            <File Id="POCO.ZIP.PARTIALINPUTSTREAM.HTML" Name="Poco.Zip.PartialInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.PartialInputStream.html" />
                            <File Id="POCO.ZIP.PARTIALIOS.HTML" Name="Poco.Zip.PartialIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.PartialIOS.html" />
                            <File Id="POCO.ZIP.PARTIALOUTPUTSTREAM.HTML" Name="Poco.Zip.PartialOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.PartialOutputStream.html" />
                            <File Id="POCO.ZIP.PARTIALSTREAMBUF.HTML" Name="Poco.Zip.PartialStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.PartialStreamBuf.html" />
                            <File Id="POCO.ZIP.RENAME.HTML" Name="Poco.Zip.Rename.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Rename.html" />
                            <File Id="POCO.ZIP.REPLACE.HTML" Name="Poco.Zip.Replace.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.Replace.html" />
                            <File Id="POCO.ZIP.SKIPCALLBACK.HTML" Name="Poco.Zip.SkipCallback.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.SkipCallback.html" />
                            <File Id="POCO.ZIP.ZIPARCHIVE.HTML" Name="Poco.Zip.ZipArchive.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipArchive.html" />
                            <File Id="POCO.ZIP.ZIPARCHIVEINFO.HTML" Name="Poco.Zip.ZipArchiveInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipArchiveInfo.html" />
                            <File Id="POCO.ZIP.ZIPCOMMON.HTML" Name="Poco.Zip.ZipCommon.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipCommon.html" />
                            <File Id="POCO.ZIP.ZIPDATAINFO.HTML" Name="Poco.Zip.ZipDataInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipDataInfo.html" />
                            <File Id="POCO.ZIP.ZIPEXCEPTION.HTML" Name="Poco.Zip.ZipException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipException.html" />
                            <File Id="POCO.ZIP.ZIPFILEINFO.HTML" Name="Poco.Zip.ZipFileInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipFileInfo.html" />
                            <File Id="POCO.ZIP.ZIPINPUTSTREAM.HTML" Name="Poco.Zip.ZipInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipInputStream.html" />
                            <File Id="POCO.ZIP.ZIPLOCALFILEHEADER.HTML" Name="Poco.Zip.ZipLocalFileHeader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipLocalFileHeader.html" />
                            <File Id="POCO.ZIP.ZIPMANIPULATIONEXCEPTION.HTML" Name="Poco.Zip.ZipManipulationException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipManipulationException.html" />
                            <File Id="POCO.ZIP.ZIPMANIPULATOR.HTML" Name="Poco.Zip.ZipManipulator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipManipulator.html" />
                            <File Id="POCO.ZIP.ZIPIOS.HTML" Name="Poco.Zip.ZipIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipIOS.html" />
                            <File Id="POCO.ZIP.ZIPOPERATION.HTML" Name="Poco.Zip.ZipOperation.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipOperation.html" />
                            <File Id="POCO.ZIP.ZIPOUTPUTSTREAM.HTML" Name="Poco.Zip.ZipOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipOutputStream.html" />
                            <File Id="POCO.ZIP.ZIPSTREAMBUF.HTML" Name="Poco.Zip.ZipStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipStreamBuf.html" />
                            <File Id="POCO.ZIP.ZIPUTIL.HTML" Name="Poco.Zip.ZipUtil.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Zip.ZipUtil.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.UTIL.HTML" DiskId="1" Guid="E59C6C4A-F694-45AE-8890-C42C014DA8EB">
                            <File Id="POCO.UTIL.ABSTRACTCONFIGURATION.HTML" Name="Poco.Util.AbstractConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.AbstractConfiguration.html" />
                            <File Id="POCO.UTIL.ABSTRACTCONFIGURATION.KEYVALUE.HTML" Name="Poco.Util.AbstractConfiguration.KeyValue.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.AbstractConfiguration.KeyValue.html" />
                            <File Id="POCO.UTIL.ABSTRACTOPTIONCALLBACK.HTML" Name="Poco.Util.AbstractOptionCallback.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.AbstractOptionCallback.html" />
                            <File Id="POCO.UTIL.AMBIGUOUSOPTIONEXCEPTION.HTML" Name="Poco.Util.AmbiguousOptionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.AmbiguousOptionException.html" />
                            <File Id="POCO.UTIL.APPLICATION.HTML" Name="Poco.Util.Application.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Application.html" />
                            <File Id="POCO.UTIL.CONFIGURATIONMAPPER.HTML" Name="Poco.Util.ConfigurationMapper.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.ConfigurationMapper.html" />
                            <File Id="POCO.UTIL.CONFIGURATIONVIEW.HTML" Name="Poco.Util.ConfigurationView.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.ConfigurationView.html" />
                            <File Id="POCO.UTIL.DUPLICATEOPTIONEXCEPTION.HTML" Name="Poco.Util.DuplicateOptionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.DuplicateOptionException.html" />
                            <File Id="POCO.UTIL.EMPTYOPTIONEXCEPTION.HTML" Name="Poco.Util.EmptyOptionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.EmptyOptionException.html" />
                            <File Id="POCO.UTIL.FILESYSTEMCONFIGURATION.HTML" Name="Poco.Util.FilesystemConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.FilesystemConfiguration.html" />
                            <File Id="POCO.UTIL.HELPFORMATTER.HTML" Name="Poco.Util.HelpFormatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.HelpFormatter.html" />
                            <File Id="POCO.UTIL.HTML" Name="Poco.Util.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.html" />
                            <File Id="POCO.UTIL.INCOMPATIBLEOPTIONSEXCEPTION.HTML" Name="Poco.Util.IncompatibleOptionsException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.IncompatibleOptionsException.html" />
                            <File Id="POCO.UTIL.INIFILECONFIGURATION.HTML" Name="Poco.Util.IniFileConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.IniFileConfiguration.html" />
                            <File Id="POCO.UTIL.INIFILECONFIGURATION.ICOMPARE.HTML" Name="Poco.Util.IniFileConfiguration.ICompare.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.IniFileConfiguration.ICompare.html" />
                            <File Id="POCO.UTIL.INTVALIDATOR.HTML" Name="Poco.Util.IntValidator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.IntValidator.html" />
                            <File Id="POCO.UTIL.INVALIDARGUMENTEXCEPTION.HTML" Name="Poco.Util.InvalidArgumentException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.InvalidArgumentException.html" />
                            <File Id="POCO.UTIL.JSONCONFIGURATION.HTML" Name="Poco.Util.JSONConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.JSONConfiguration.html" />
                            <File Id="POCO.UTIL.LAYEREDCONFIGURATION.CONFIGITEM.HTML" Name="Poco.Util.LayeredConfiguration.ConfigItem.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.LayeredConfiguration.ConfigItem.html" />
                            <File Id="POCO.UTIL.LAYEREDCONFIGURATION.HTML" Name="Poco.Util.LayeredConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.LayeredConfiguration.html" />
                            <File Id="POCO.UTIL.LOGGINGCONFIGURATOR.HTML" Name="Poco.Util.LoggingConfigurator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.LoggingConfigurator.html" />
                            <File Id="POCO.UTIL.LOGGINGSUBSYSTEM.HTML" Name="Poco.Util.LoggingSubsystem.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.LoggingSubsystem.html" />
                            <File Id="POCO.UTIL.MAPCONFIGURATION.HTML" Name="Poco.Util.MapConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.MapConfiguration.html" />
                            <File Id="POCO.UTIL.MISSINGARGUMENTEXCEPTION.HTML" Name="Poco.Util.MissingArgumentException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.MissingArgumentException.html" />
                            <File Id="POCO.UTIL.MISSINGOPTIONEXCEPTION.HTML" Name="Poco.Util.MissingOptionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.MissingOptionException.html" />
                            <File Id="POCO.UTIL.OPTION.HTML" Name="Poco.Util.Option.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Option.html" />
                            <File Id="POCO.UTIL.OPTIONCALLBACK.HTML" Name="Poco.Util.OptionCallback.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.OptionCallback.html" />
                            <File Id="POCO.UTIL.OPTIONEXCEPTION.HTML" Name="Poco.Util.OptionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.OptionException.html" />
                            <File Id="POCO.UTIL.OPTIONPROCESSOR.HTML" Name="Poco.Util.OptionProcessor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.OptionProcessor.html" />
                            <File Id="POCO.UTIL.OPTIONSET.HTML" Name="Poco.Util.OptionSet.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.OptionSet.html" />
                            <File Id="POCO.UTIL.PROPERTYFILECONFIGURATION.HTML" Name="Poco.Util.PropertyFileConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.PropertyFileConfiguration.html" />
                            <File Id="POCO.UTIL.REGEXPVALIDATOR.HTML" Name="Poco.Util.RegExpValidator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.RegExpValidator.html" />
                            <File Id="POCO.UTIL.SERVERAPPLICATION.HTML" Name="Poco.Util.ServerApplication.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.ServerApplication.html" />
                            <File Id="POCO.UTIL.SUBSYSTEM.HTML" Name="Poco.Util.Subsystem.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Subsystem.html" />
                            <File Id="POCO.UTIL.SYSTEMCONFIGURATION.HTML" Name="Poco.Util.SystemConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.SystemConfiguration.html" />
                            <File Id="POCO.UTIL.TIMER.HTML" Name="Poco.Util.Timer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Timer.html" />
                            <File Id="POCO.UTIL.TIMERTASK.HTML" Name="Poco.Util.TimerTask.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.TimerTask.html" />
                            <File Id="POCO.UTIL.TIMERTASKADAPTER.HTML" Name="Poco.Util.TimerTaskAdapter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.TimerTaskAdapter.html" />
                            <File Id="POCO.UTIL.UNEXPECTEDARGUMENTEXCEPTION.HTML" Name="Poco.Util.UnexpectedArgumentException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.UnexpectedArgumentException.html" />
                            <File Id="POCO.UTIL.UNITS.CONSTANTS.HTML" Name="Poco.Util.Units.Constants.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Constants.html" />
                            <File Id="POCO.UTIL.UNITS.HTML" Name="Poco.Util.Units.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.CHECKTERMSEQUAL.HTML" Name="Poco.Util.Units.Internal.CheckTermsEqual.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.CheckTermsEqual.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.CONVERT.HTML" Name="Poco.Util.Units.Internal.Convert.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.Convert.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.CONVERT2.HTML" Name="Poco.Util.Units.Internal.Convert2.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.Convert2.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.CONVERT3.HTML" Name="Poco.Util.Units.Internal.Convert3.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.Convert3.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.CONVERTIBLE.HTML" Name="Poco.Util.Units.Internal.Convertible.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.Convertible.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.COUNTTERMS.HTML" Name="Poco.Util.Units.Internal.CountTerms.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.CountTerms.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.FIXEDPOWER.HTML" Name="Poco.Util.Units.Internal.FixedPower.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.FixedPower.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.HTML" Name="Poco.Util.Units.Internal.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.OUTPUTUNIT2.HTML" Name="Poco.Util.Units.Internal.OutputUnit2.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.OutputUnit2.html" />
                            <File Id="POCO.UTIL.UNITS.INTERNAL.SCALINGFACTOR.HTML" Name="Poco.Util.Units.Internal.ScalingFactor.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Internal.ScalingFactor.html" />
                            <File Id="POCO.UTIL.UNITS.OUTPUTUNIT.HTML" Name="Poco.Util.Units.OutputUnit.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.OutputUnit.html" />
                            <File Id="POCO.UTIL.UNITS.VALUE.HTML" Name="Poco.Util.Units.Value.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Value.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.ATTO.HTML" Name="Poco.Util.Units.Values.atto.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.atto.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.CENTI.HTML" Name="Poco.Util.Units.Values.centi.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.centi.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.DECA.HTML" Name="Poco.Util.Units.Values.deca.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.deca.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.DECI.HTML" Name="Poco.Util.Units.Values.deci.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.deci.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.EXA.HTML" Name="Poco.Util.Units.Values.exa.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.exa.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.FEMTO.HTML" Name="Poco.Util.Units.Values.femto.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.femto.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.GIGA.HTML" Name="Poco.Util.Units.Values.giga.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.giga.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.HECTO.HTML" Name="Poco.Util.Units.Values.hecto.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.hecto.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.HTML" Name="Poco.Util.Units.Values.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.KILO.HTML" Name="Poco.Util.Units.Values.kilo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.kilo.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.MEGA.HTML" Name="Poco.Util.Units.Values.mega.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.mega.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.MICRO.HTML" Name="Poco.Util.Units.Values.micro.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.micro.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.MILLI.HTML" Name="Poco.Util.Units.Values.milli.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.milli.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.NANO.HTML" Name="Poco.Util.Units.Values.nano.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.nano.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.PETA.HTML" Name="Poco.Util.Units.Values.peta.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.peta.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.PICO.HTML" Name="Poco.Util.Units.Values.pico.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.pico.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.TERA.HTML" Name="Poco.Util.Units.Values.tera.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.tera.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.YOCTO.HTML" Name="Poco.Util.Units.Values.yocto.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.yocto.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.YOTTA.HTML" Name="Poco.Util.Units.Values.yotta.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.yotta.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.ZEPTO.HTML" Name="Poco.Util.Units.Values.zepto.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.zepto.html" />
                            <File Id="POCO.UTIL.UNITS.VALUES.ZETTA.HTML" Name="Poco.Util.Units.Values.zetta.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Units.Values.zetta.html" />
                            <File Id="POCO.UTIL.UNKNOWNOPTIONEXCEPTION.HTML" Name="Poco.Util.UnknownOptionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.UnknownOptionException.html" />
                            <File Id="POCO.UTIL.VALIDATOR.HTML" Name="Poco.Util.Validator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.Validator.html" />
                            <File Id="POCO.UTIL.WINREGISTRYCONFIGURATION.HTML" Name="Poco.Util.WinRegistryConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.WinRegistryConfiguration.html" />
                            <File Id="POCO.UTIL.WINREGISTRYKEY.HTML" Name="Poco.Util.WinRegistryKey.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.WinRegistryKey.html" />
                            <File Id="POCO.UTIL.WINSERVICE.HTML" Name="Poco.Util.WinService.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.WinService.html" />
                            <File Id="POCO.UTIL.XMLCONFIGURATION.HTML" Name="Poco.Util.XMLConfiguration.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Util.XMLConfiguration.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="POCO.HTML" DiskId="1" Guid="AD5C4F0A-D5E5-4E03-80B4-81D96B28FEA4">
                            <File Id="POCO.ABSTRACTCACHE.HTML" Name="Poco.AbstractCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractCache.html" />
                            <File Id="POCO.ABSTRACTDELEGATE.HTML" Name="Poco.AbstractDelegate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractDelegate.html" />
                            <File Id="POCO.ABSTRACTEVENT.HTML" Name="Poco.AbstractEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractEvent.html" />
                            <File Id="POCO.ABSTRACTEVENT.NOTIFYASYNCPARAMS.HTML" Name="Poco.AbstractEvent.NotifyAsyncParams.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractEvent.NotifyAsyncParams.html" />
                            <File Id="POCO.ABSTRACTINSTANTIATOR.HTML" Name="Poco.AbstractInstantiator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractInstantiator.html" />
                            <File Id="POCO.ABSTRACTMETAOBJECT.HTML" Name="Poco.AbstractMetaObject.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractMetaObject.html" />
                            <File Id="POCO.ABSTRACTOBSERVER.HTML" Name="Poco.AbstractObserver.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractObserver.html" />
                            <File Id="POCO.ABSTRACTPRIORITYDELEGATE.HTML" Name="Poco.AbstractPriorityDelegate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractPriorityDelegate.html" />
                            <File Id="POCO.ABSTRACTSTRATEGY.HTML" Name="Poco.AbstractStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractStrategy.html" />
                            <File Id="POCO.ABSTRACTTIMERCALLBACK.HTML" Name="Poco.AbstractTimerCallback.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AbstractTimerCallback.html" />
                            <File Id="POCO.ACCESSEXPIRATIONDECORATOR.HTML" Name="Poco.AccessExpirationDecorator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AccessExpirationDecorator.html" />
                            <File Id="POCO.ACCESSEXPIRECACHE.HTML" Name="Poco.AccessExpireCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AccessExpireCache.html" />
                            <File Id="POCO.ACCESSEXPIRELRUCACHE.HTML" Name="Poco.AccessExpireLRUCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AccessExpireLRUCache.html" />
                            <File Id="POCO.ACCESSEXPIRESTRATEGY.HTML" Name="Poco.AccessExpireStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AccessExpireStrategy.html" />
                            <File Id="POCO.ACTIVEDISPATCHER.HTML" Name="Poco.ActiveDispatcher.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveDispatcher.html" />
                            <File Id="POCO.ACTIVEMETHOD.HTML" Name="Poco.ActiveMethod.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveMethod.html" />
                            <File Id="POCO.ACTIVERESULT.HTML" Name="Poco.ActiveResult.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveResult.html" />
                            <File Id="POCO.ACTIVERESULTHOLDER.HTML" Name="Poco.ActiveResultHolder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveResultHolder.html" />
                            <File Id="POCO.ACTIVERUNNABLE.HTML" Name="Poco.ActiveRunnable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveRunnable.html" />
                            <File Id="POCO.ACTIVERUNNABLEBASE.HTML" Name="Poco.ActiveRunnableBase.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveRunnableBase.html" />
                            <File Id="POCO.ACTIVESTARTER.HTML" Name="Poco.ActiveStarter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ActiveStarter.html" />
                            <File Id="POCO.ACTIVITY.HTML" Name="Poco.Activity.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Activity.html" />
                            <?if $(var.ProductVersion) =  1.6.1 ?>
                            <File Id="POCO.ALIGNEDCHARARRAYUNION.ALIGNERIMPL.HTML" Name="Poco.AlignedCharArrayUnion.AlignerImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AlignedCharArrayUnion.AlignerImpl.html" />
                            <File Id="POCO.ALIGNEDCHARARRAYUNION.HTML" Name="Poco.AlignedCharArrayUnion.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AlignedCharArrayUnion.html" />
                            <File Id="POCO.ALIGNEDCHARARRAYUNION.SIZERIMPL.HTML" Name="Poco.AlignedCharArrayUnion.SizerImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AlignedCharArrayUnion.SizerImpl.html" />
                            <File Id="POCO.ALIGNMENTCALCIMPL.HTML" Name="Poco.AlignmentCalcImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AlignmentCalcImpl.html" />
                            <File Id="POCO.ALIGNOF.HTML" Name="Poco.AlignOf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AlignOf.html" />
                            <?endif ?>
                            <File Id="POCO.ANY.HOLDER.HTML" Name="Poco.Any.Holder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Any.Holder.html" />
                            <File Id="POCO.ANY.HTML" Name="Poco.Any.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Any.html" />
                            <File Id="POCO.ANY.VALUEHOLDER.HTML" Name="Poco.Any.ValueHolder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Any.ValueHolder.html" />
                            <File Id="POCO.APPLICATIONEXCEPTION.HTML" Name="Poco.ApplicationException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ApplicationException.html" />
                            <File Id="POCO.ARCHIVEBYNUMBERSTRATEGY.HTML" Name="Poco.ArchiveByNumberStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ArchiveByNumberStrategy.html" />
                            <File Id="POCO.ARCHIVEBYTIMESTAMPSTRATEGY.HTML" Name="Poco.ArchiveByTimestampStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ArchiveByTimestampStrategy.html" />
                            <File Id="POCO.ARCHIVESTRATEGY.HTML" Name="Poco.ArchiveStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ArchiveStrategy.html" />
                            <File Id="POCO.ARRAY.HTML" Name="Poco.Array.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Array.html" />
                            <File Id="POCO.ASCII.HTML" Name="Poco.Ascii.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Ascii.html" />
                            <File Id="POCO.ASCIIENCODING.HTML" Name="Poco.ASCIIEncoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ASCIIEncoding.html" />
                            <File Id="POCO.ASSERTIONVIOLATIONEXCEPTION.HTML" Name="Poco.AssertionViolationException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AssertionViolationException.html" />
                            <File Id="POCO.ASYNCCHANNEL.HTML" Name="Poco.AsyncChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AsyncChannel.html" />
                            <File Id="POCO.ATOMICCOUNTER.HTML" Name="Poco.AtomicCounter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AtomicCounter.html" />
                            <File Id="POCO.AUTOPTR.HTML" Name="Poco.AutoPtr.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AutoPtr.html" />
                            <File Id="POCO.AUTORELEASEPOOL.HTML" Name="Poco.AutoReleasePool.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.AutoReleasePool.html" />
                            <File Id="POCO.BADCASTEXCEPTION.HTML" Name="Poco.BadCastException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BadCastException.html" />
                            <File Id="POCO.BASE32DECODER.HTML" Name="Poco.Base32Decoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base32Decoder.html" />
                            <File Id="POCO.BASE32DECODERBUF.HTML" Name="Poco.Base32DecoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base32DecoderBuf.html" />
                            <File Id="POCO.BASE32DECODERIOS.HTML" Name="Poco.Base32DecoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base32DecoderIOS.html" />
                            <File Id="POCO.BASE32ENCODER.HTML" Name="Poco.Base32Encoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base32Encoder.html" />
                            <File Id="POCO.BASE32ENCODERBUF.HTML" Name="Poco.Base32EncoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base32EncoderBuf.html" />
                            <File Id="POCO.BASE32ENCODERIOS.HTML" Name="Poco.Base32EncoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base32EncoderIOS.html" />
                            <File Id="POCO.BASE64DECODER.HTML" Name="Poco.Base64Decoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base64Decoder.html" />
                            <File Id="POCO.BASE64DECODERBUF.HTML" Name="Poco.Base64DecoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base64DecoderBuf.html" />
                            <File Id="POCO.BASE64DECODERIOS.HTML" Name="Poco.Base64DecoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base64DecoderIOS.html" />
                            <File Id="POCO.BASE64ENCODER.HTML" Name="Poco.Base64Encoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base64Encoder.html" />
                            <File Id="POCO.BASE64ENCODERBUF.HTML" Name="Poco.Base64EncoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base64EncoderBuf.html" />
                            <File Id="POCO.BASE64ENCODERIOS.HTML" Name="Poco.Base64EncoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Base64EncoderIOS.html" />
                            <File Id="POCO.BASICBUFFEREDBIDIRECTIONALSTREAMBUF.HTML" Name="Poco.BasicBufferedBidirectionalStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicBufferedBidirectionalStreamBuf.html" />
                            <File Id="POCO.BASICBUFFEREDSTREAMBUF.HTML" Name="Poco.BasicBufferedStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicBufferedStreamBuf.html" />
                            <File Id="POCO.BASICEVENT.HTML" Name="Poco.BasicEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicEvent.html" />
                            <File Id="POCO.BASICFIFOBUFFER.HTML" Name="Poco.BasicFIFOBuffer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicFIFOBuffer.html" />
                            <File Id="POCO.BASICMEMORYBINARYREADER.HTML" Name="Poco.BasicMemoryBinaryReader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicMemoryBinaryReader.html" />
                            <File Id="POCO.BASICMEMORYBINARYWRITER.HTML" Name="Poco.BasicMemoryBinaryWriter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicMemoryBinaryWriter.html" />
                            <File Id="POCO.BASICMEMORYSTREAMBUF.HTML" Name="Poco.BasicMemoryStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicMemoryStreamBuf.html" />
                            <File Id="POCO.BASICUNBUFFEREDSTREAMBUF.HTML" Name="Poco.BasicUnbufferedStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BasicUnbufferedStreamBuf.html" />
                            <File Id="POCO.BINARYREADER.HTML" Name="Poco.BinaryReader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BinaryReader.html" />
                            <File Id="POCO.BINARYWRITER.HTML" Name="Poco.BinaryWriter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BinaryWriter.html" />
                            <File Id="POCO.BUFFER.HTML" Name="Poco.Buffer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Buffer.html" />
                            <File Id="POCO.BUFFERALLOCATOR.HTML" Name="Poco.BufferAllocator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BufferAllocator.html" />
                            <File Id="POCO.BUGCHECK.HTML" Name="Poco.Bugcheck.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Bugcheck.html" />
                            <File Id="POCO.BUGCHECKEXCEPTION.HTML" Name="Poco.BugcheckException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.BugcheckException.html" />
                            <File Id="POCO.BYTEORDER.HTML" Name="Poco.ByteOrder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ByteOrder.html" />
                            <File Id="POCO.CHANNEL.HTML" Name="Poco.Channel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Channel.html" />
                            <File Id="POCO.CHECKSUM.HTML" Name="Poco.Checksum.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Checksum.html" />
                            <File Id="POCO.CHILDRENFIRSTTRAVERSE.HTML" Name="Poco.ChildrenFirstTraverse.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ChildrenFirstTraverse.html" />
                            <File Id="POCO.CILESS.HTML" Name="Poco.CILess.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CILess.html" />
                            <File Id="POCO.CIRCULARREFERENCEEXCEPTION.HTML" Name="Poco.CircularReferenceException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CircularReferenceException.html" />
                            <File Id="POCO.CLASSLOADER.HTML" Name="Poco.ClassLoader.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ClassLoader.html" />
                            <File Id="POCO.CLASSLOADER.ITERATOR.HTML" Name="Poco.ClassLoader.Iterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ClassLoader.Iterator.html" />
                            <File Id="POCO.CLASSLOADER.LIBRARYINFO.HTML" Name="Poco.ClassLoader.LibraryInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ClassLoader.LibraryInfo.html" />
                            <File Id="POCO.CLOCK.HTML" Name="Poco.Clock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Clock.html" />
                            <File Id="POCO.COLORCONSOLECHANNEL.HTML" Name="Poco.ColorConsoleChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ColorConsoleChannel.html" />
                            <File Id="POCO.CONDITION.HTML" Name="Poco.Condition.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Condition.html" />
                            <File Id="POCO.CONFIGURABLE.HTML" Name="Poco.Configurable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Configurable.html" />
                            <File Id="POCO.CONSOLECHANNEL.HTML" Name="Poco.ConsoleChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ConsoleChannel.html" />
                            <File Id="POCO.COUNTINGINPUTSTREAM.HTML" Name="Poco.CountingInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CountingInputStream.html" />
                            <File Id="POCO.COUNTINGIOS.HTML" Name="Poco.CountingIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CountingIOS.html" />
                            <File Id="POCO.COUNTINGOUTPUTSTREAM.HTML" Name="Poco.CountingOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CountingOutputStream.html" />
                            <File Id="POCO.COUNTINGSTREAMBUF.HTML" Name="Poco.CountingStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CountingStreamBuf.html" />
                            <File Id="POCO.CREATEFILEEXCEPTION.HTML" Name="Poco.CreateFileException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.CreateFileException.html" />
                            <File Id="POCO.DATAEXCEPTION.HTML" Name="Poco.DataException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DataException.html" />
                            <File Id="POCO.DATAFORMATEXCEPTION.HTML" Name="Poco.DataFormatException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DataFormatException.html" />
                            <File Id="POCO.DATETIME.HTML" Name="Poco.DateTime.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DateTime.html" />
                            <File Id="POCO.DATETIMEFORMAT.HTML" Name="Poco.DateTimeFormat.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DateTimeFormat.html" />
                            <File Id="POCO.DATETIMEFORMATTER.HTML" Name="Poco.DateTimeFormatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DateTimeFormatter.html" />
                            <File Id="POCO.DATETIMEPARSER.HTML" Name="Poco.DateTimeParser.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DateTimeParser.html" />
                            <File Id="POCO.DEBUGGER.HTML" Name="Poco.Debugger.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Debugger.html" />
                            <File Id="POCO.DEFAULTSTRATEGY.HTML" Name="Poco.DefaultStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DefaultStrategy.html" />
                            <File Id="POCO.DEFLATINGINPUTSTREAM.HTML" Name="Poco.DeflatingInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DeflatingInputStream.html" />
                            <File Id="POCO.DEFLATINGIOS.HTML" Name="Poco.DeflatingIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DeflatingIOS.html" />
                            <File Id="POCO.DEFLATINGOUTPUTSTREAM.HTML" Name="Poco.DeflatingOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DeflatingOutputStream.html" />
                            <File Id="POCO.DEFLATINGSTREAMBUF.HTML" Name="Poco.DeflatingStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DeflatingStreamBuf.html" />
                            <File Id="POCO.DELEGATE.HTML" Name="Poco.Delegate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Delegate.html" />
                            <File Id="POCO.DIGESTBUF.HTML" Name="Poco.DigestBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DigestBuf.html" />
                            <File Id="POCO.DIGESTENGINE.HTML" Name="Poco.DigestEngine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DigestEngine.html" />
                            <File Id="POCO.DIGESTINPUTSTREAM.HTML" Name="Poco.DigestInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DigestInputStream.html" />
                            <File Id="POCO.DIGESTIOS.HTML" Name="Poco.DigestIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DigestIOS.html" />
                            <File Id="POCO.DIGESTOUTPUTSTREAM.HTML" Name="Poco.DigestOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DigestOutputStream.html" />
                            <File Id="POCO.DIRECTORYITERATOR.HTML" Name="Poco.DirectoryIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DirectoryIterator.html" />
                            <File Id="POCO.DIRECTORYWATCHER.DIRECTORYEVENT.HTML" Name="Poco.DirectoryWatcher.DirectoryEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DirectoryWatcher.DirectoryEvent.html" />
                            <File Id="POCO.DIRECTORYWATCHER.HTML" Name="Poco.DirectoryWatcher.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.DirectoryWatcher.html" />
                            <File Id="POCO.ENVIRONMENT.HTML" Name="Poco.Environment.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Environment.html" />
                            <File Id="POCO.EOFTOKEN.HTML" Name="Poco.EOFToken.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.EOFToken.html" />
                            <File Id="POCO.ERROR.HTML" Name="Poco.Error.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Error.html" />
                            <File Id="POCO.ERRORHANDLER.HTML" Name="Poco.ErrorHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ErrorHandler.html" />
                            <File Id="POCO.EVENT.HTML" Name="Poco.Event.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Event.html" />
                            <File Id="POCO.EVENTARGS.HTML" Name="Poco.EventArgs.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.EventArgs.html" />
                            <File Id="POCO.EVENTLOGCHANNEL.HTML" Name="Poco.EventLogChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.EventLogChannel.html" />
                            <File Id="POCO.EXCEPTION.HTML" Name="Poco.Exception.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Exception.html" />
                            <File Id="POCO.EXISTSEXCEPTION.HTML" Name="Poco.ExistsException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ExistsException.html" />
                            <File Id="POCO.EXPIRATIONDECORATOR.HTML" Name="Poco.ExpirationDecorator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ExpirationDecorator.html" />
                            <File Id="POCO.EXPIRE.HTML" Name="Poco.Expire.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Expire.html" />
                            <File Id="POCO.EXPIRECACHE.HTML" Name="Poco.ExpireCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ExpireCache.html" />
                            <File Id="POCO.EXPIRELRUCACHE.HTML" Name="Poco.ExpireLRUCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ExpireLRUCache.html" />
                            <File Id="POCO.EXPIRESTRATEGY.HTML" Name="Poco.ExpireStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ExpireStrategy.html" />
                            <File Id="POCO.FASTMUTEX.HTML" Name="Poco.FastMutex.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FastMutex.html" />
                            <File Id="POCO.FIFOBUFFERSTREAM.HTML" Name="Poco.FIFOBufferStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FIFOBufferStream.html" />
                            <File Id="POCO.FIFOBUFFERSTREAMBUF.HTML" Name="Poco.FIFOBufferStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FIFOBufferStreamBuf.html" />
                            <File Id="POCO.FIFOEVENT.HTML" Name="Poco.FIFOEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FIFOEvent.html" />
                            <File Id="POCO.FIFOIOS.HTML" Name="Poco.FIFOIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FIFOIOS.html" />
                            <File Id="POCO.FIFOSTRATEGY.HTML" Name="Poco.FIFOStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FIFOStrategy.html" />
                            <File Id="POCO.FILE.HTML" Name="Poco.File.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.File.html" />
                            <File Id="POCO.FILEACCESSDENIEDEXCEPTION.HTML" Name="Poco.FileAccessDeniedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileAccessDeniedException.html" />
                            <File Id="POCO.FILECHANNEL.HTML" Name="Poco.FileChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileChannel.html" />
                            <File Id="POCO.FILEEXCEPTION.HTML" Name="Poco.FileException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileException.html" />
                            <File Id="POCO.FILEEXISTSEXCEPTION.HTML" Name="Poco.FileExistsException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileExistsException.html" />
                            <File Id="POCO.FILEINPUTSTREAM.HTML" Name="Poco.FileInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileInputStream.html" />
                            <File Id="POCO.FILEIOS.HTML" Name="Poco.FileIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileIOS.html" />
                            <File Id="POCO.FILENOTFOUNDEXCEPTION.HTML" Name="Poco.FileNotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileNotFoundException.html" />
                            <File Id="POCO.FILEOUTPUTSTREAM.HTML" Name="Poco.FileOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileOutputStream.html" />
                            <File Id="POCO.FILEREADONLYEXCEPTION.HTML" Name="Poco.FileReadOnlyException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileReadOnlyException.html" />
                            <File Id="POCO.FILESTREAM.HTML" Name="Poco.FileStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileStream.html" />
                            <File Id="POCO.FILESTREAMFACTORY.HTML" Name="Poco.FileStreamFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FileStreamFactory.html" />
                            <File Id="POCO.FORMATTER.HTML" Name="Poco.Formatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Formatter.html" />
                            <File Id="POCO.FORMATTINGCHANNEL.HTML" Name="Poco.FormattingChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FormattingChannel.html" />
                            <File Id="POCO.FPENVIRONMENT.HTML" Name="Poco.FPEnvironment.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FPEnvironment.html" />
                            <File Id="POCO.FUNCTIONDELEGATE.HTML" Name="Poco.FunctionDelegate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FunctionDelegate.html" />
                            <File Id="POCO.FUNCTIONPRIORITYDELEGATE.HTML" Name="Poco.FunctionPriorityDelegate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.FunctionPriorityDelegate.html" />
                            <File Id="POCO.GETTER.HTML" Name="Poco.Getter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Getter.html" />
                            <File Id="POCO.GLOB.HTML" Name="Poco.Glob.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Glob.html" />
                            <File Id="POCO.HASH.HTML" Name="Poco.Hash.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Hash.html" />
                            <File Id="POCO.HASHFUNCTION.HTML" Name="Poco.HashFunction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashFunction.html" />
                            <File Id="POCO.HASHMAP.HTML" Name="Poco.HashMap.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashMap.html" />
                            <File Id="POCO.HASHMAPENTRY.HTML" Name="Poco.HashMapEntry.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashMapEntry.html" />
                            <File Id="POCO.HASHMAPENTRYHASH.HTML" Name="Poco.HashMapEntryHash.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashMapEntryHash.html" />
                            <File Id="POCO.HASHSET.HTML" Name="Poco.HashSet.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashSet.html" />
                            <File Id="POCO.HASHSTATISTIC.HTML" Name="Poco.HashStatistic.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashStatistic.html" />
                            <File Id="POCO.HASHTABLE.HTML" Name="Poco.HashTable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HashTable.html" />
                            <File Id="POCO.HEXBINARYDECODER.HTML" Name="Poco.HexBinaryDecoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HexBinaryDecoder.html" />
                            <File Id="POCO.HEXBINARYDECODERBUF.HTML" Name="Poco.HexBinaryDecoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HexBinaryDecoderBuf.html" />
                            <File Id="POCO.HEXBINARYDECODERIOS.HTML" Name="Poco.HexBinaryDecoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HexBinaryDecoderIOS.html" />
                            <File Id="POCO.HEXBINARYENCODER.HTML" Name="Poco.HexBinaryEncoder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HexBinaryEncoder.html" />
                            <File Id="POCO.HEXBINARYENCODERBUF.HTML" Name="Poco.HexBinaryEncoderBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HexBinaryEncoderBuf.html" />
                            <File Id="POCO.HEXBINARYENCODERIOS.HTML" Name="Poco.HexBinaryEncoderIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HexBinaryEncoderIOS.html" />
                            <File Id="POCO.HMACENGINE.HTML" Name="Poco.HMACEngine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.HMACEngine.html" />
                            <File Id="POCO.HTML" Name="Poco.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.html" />
                            <File Id="POCO.ILLEGALSTATEEXCEPTION.HTML" Name="Poco.IllegalStateException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.IllegalStateException.html" />
                            <File Id="POCO.IMPL.HTML" Name="Poco.Impl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Impl.html" />
                            <File Id="POCO.IMPL.PTR.HTML" Name="Poco.Impl.Ptr.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Impl.Ptr.html" />
                            <File Id="POCO.INFLATINGINPUTSTREAM.HTML" Name="Poco.InflatingInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InflatingInputStream.html" />
                            <File Id="POCO.INFLATINGIOS.HTML" Name="Poco.InflatingIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InflatingIOS.html" />
                            <File Id="POCO.INFLATINGOUTPUTSTREAM.HTML" Name="Poco.InflatingOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InflatingOutputStream.html" />
                            <File Id="POCO.INFLATINGSTREAMBUF.HTML" Name="Poco.InflatingStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InflatingStreamBuf.html" />
                            <File Id="POCO.INPUTLINEENDINGCONVERTER.HTML" Name="Poco.InputLineEndingConverter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InputLineEndingConverter.html" />
                            <File Id="POCO.INPUTSTREAMCONVERTER.HTML" Name="Poco.InputStreamConverter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InputStreamConverter.html" />
                            <File Id="POCO.INSTANTIATOR.HTML" Name="Poco.Instantiator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Instantiator.html" />
                            <File Id="POCO.INVALIDACCESSEXCEPTION.HTML" Name="Poco.InvalidAccessException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InvalidAccessException.html" />
                            <File Id="POCO.INVALIDARGUMENTEXCEPTION.HTML" Name="Poco.InvalidArgumentException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InvalidArgumentException.html" />
                            <File Id="POCO.INVALIDTOKEN.HTML" Name="Poco.InvalidToken.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.InvalidToken.html" />
                            <File Id="POCO.IOEXCEPTION.HTML" Name="Poco.IOException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.IOException.html" />
                            <File Id="POCO.ISCONST.HTML" Name="Poco.IsConst.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.IsConst.html" />
                            <File Id="POCO.ISREFERENCE.HTML" Name="Poco.IsReference.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.IsReference.html" />
                            <File Id="POCO.I_CHAR_TRAITS.HTML" Name="Poco.i_char_traits.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.i_char_traits.html" />
                            <File Id="POCO.KEYVALUEARGS.HTML" Name="Poco.KeyValueArgs.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.KeyValueArgs.html" />
                            <File Id="POCO.LATIN1ENCODING.HTML" Name="Poco.Latin1Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Latin1Encoding.html" />
                            <File Id="POCO.LATIN2ENCODING.HTML" Name="Poco.Latin2Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Latin2Encoding.html" />
                            <File Id="POCO.LATIN9ENCODING.HTML" Name="Poco.Latin9Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Latin9Encoding.html" />
                            <File Id="POCO.LIBRARYALREADYLOADEDEXCEPTION.HTML" Name="Poco.LibraryAlreadyLoadedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LibraryAlreadyLoadedException.html" />
                            <File Id="POCO.LIBRARYLOADEXCEPTION.HTML" Name="Poco.LibraryLoadException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LibraryLoadException.html" />
                            <File Id="POCO.LINEARHASHTABLE.CONSTITERATOR.HTML" Name="Poco.LinearHashTable.ConstIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LinearHashTable.ConstIterator.html" />
                            <File Id="POCO.LINEARHASHTABLE.HTML" Name="Poco.LinearHashTable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LinearHashTable.html" />
                            <File Id="POCO.LINEARHASHTABLE.ITERATOR.HTML" Name="Poco.LinearHashTable.Iterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LinearHashTable.Iterator.html" />
                            <File Id="POCO.LINEENDING.HTML" Name="Poco.LineEnding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LineEnding.html" />
                            <File Id="POCO.LINEENDINGCONVERTERIOS.HTML" Name="Poco.LineEndingConverterIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LineEndingConverterIOS.html" />
                            <File Id="POCO.LINEENDINGCONVERTERSTREAMBUF.HTML" Name="Poco.LineEndingConverterStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LineEndingConverterStreamBuf.html" />
                            <File Id="POCO.LISTMAP.HTML" Name="Poco.ListMap.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ListMap.html" />
                            <File Id="POCO.LOCALDATETIME.HTML" Name="Poco.LocalDateTime.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LocalDateTime.html" />
                            <File Id="POCO.LOGFILE.HTML" Name="Poco.LogFile.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LogFile.html" />
                            <File Id="POCO.LOGGER.HTML" Name="Poco.Logger.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Logger.html" />
                            <File Id="POCO.LOGGINGFACTORY.HTML" Name="Poco.LoggingFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LoggingFactory.html" />
                            <File Id="POCO.LOGGINGREGISTRY.HTML" Name="Poco.LoggingRegistry.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LoggingRegistry.html" />
                            <File Id="POCO.LOGICEXCEPTION.HTML" Name="Poco.LogicException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LogicException.html" />
                            <File Id="POCO.LOGIOS.HTML" Name="Poco.LogIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LogIOS.html" />
                            <File Id="POCO.LOGSTREAM.HTML" Name="Poco.LogStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LogStream.html" />
                            <File Id="POCO.LOGSTREAMBUF.HTML" Name="Poco.LogStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LogStreamBuf.html" />
                            <File Id="POCO.LRUCACHE.HTML" Name="Poco.LRUCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LRUCache.html" />
                            <File Id="POCO.LRUSTRATEGY.HTML" Name="Poco.LRUStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.LRUStrategy.html" />
                            <File Id="POCO.MANIFEST.HTML" Name="Poco.Manifest.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Manifest.html" />
                            <File Id="POCO.MANIFEST.ITERATOR.HTML" Name="Poco.Manifest.Iterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Manifest.Iterator.html" />
                            <File Id="POCO.MANIFESTBASE.HTML" Name="Poco.ManifestBase.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ManifestBase.html" />
                            <File Id="POCO.MD4ENGINE.CONTEXT.HTML" Name="Poco.MD4Engine.Context.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MD4Engine.Context.html" />
                            <File Id="POCO.MD4ENGINE.HTML" Name="Poco.MD4Engine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MD4Engine.html" />
                            <File Id="POCO.MD5ENGINE.CONTEXT.HTML" Name="Poco.MD5Engine.Context.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MD5Engine.Context.html" />
                            <File Id="POCO.MD5ENGINE.HTML" Name="Poco.MD5Engine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MD5Engine.html" />
                            <File Id="POCO.MEMORYINPUTSTREAM.HTML" Name="Poco.MemoryInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MemoryInputStream.html" />
                            <File Id="POCO.MEMORYIOS.HTML" Name="Poco.MemoryIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MemoryIOS.html" />
                            <File Id="POCO.MEMORYOUTPUTSTREAM.HTML" Name="Poco.MemoryOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MemoryOutputStream.html" />
                            <File Id="POCO.MEMORYPOOL.HTML" Name="Poco.MemoryPool.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MemoryPool.html" />
                            <File Id="POCO.MESSAGE.HTML" Name="Poco.Message.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Message.html" />
                            <File Id="POCO.METAOBJECT.HTML" Name="Poco.MetaObject.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MetaObject.html" />
                            <File Id="POCO.METASINGLETON.HTML" Name="Poco.MetaSingleton.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.MetaSingleton.html" />
                            <File Id="POCO.MUTEX.HTML" Name="Poco.Mutex.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Mutex.html" />
                            <File Id="POCO.NAMEDEVENT.HTML" Name="Poco.NamedEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NamedEvent.html" />
                            <File Id="POCO.NAMEDMUTEX.HTML" Name="Poco.NamedMutex.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NamedMutex.html" />
                            <File Id="POCO.NAMEDTUPLE.HTML" Name="Poco.NamedTuple.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NamedTuple.html" />
                            <File Id="POCO.NDCSCOPE.HTML" Name="Poco.NDCScope.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NDCScope.html" />
                            <File Id="POCO.NESTEDDIAGNOSTICCONTEXT.CONTEXT.HTML" Name="Poco.NestedDiagnosticContext.Context.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NestedDiagnosticContext.Context.html" />
                            <File Id="POCO.NESTEDDIAGNOSTICCONTEXT.HTML" Name="Poco.NestedDiagnosticContext.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NestedDiagnosticContext.html" />
                            <File Id="POCO.NOBSERVER.HTML" Name="Poco.NObserver.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NObserver.html" />
                            <File Id="POCO.NOPERMISSIONEXCEPTION.HTML" Name="Poco.NoPermissionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NoPermissionException.html" />
                            <File Id="POCO.NOTFOUNDEXCEPTION.HTML" Name="Poco.NotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NotFoundException.html" />
                            <File Id="POCO.NOTHREADAVAILABLEEXCEPTION.HTML" Name="Poco.NoThreadAvailableException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NoThreadAvailableException.html" />
                            <File Id="POCO.NOTIFICATION.HTML" Name="Poco.Notification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Notification.html" />
                            <File Id="POCO.NOTIFICATIONCENTER.HTML" Name="Poco.NotificationCenter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NotificationCenter.html" />
                            <File Id="POCO.NOTIFICATIONQUEUE.HTML" Name="Poco.NotificationQueue.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NotificationQueue.html" />
                            <File Id="POCO.NOTIFICATIONQUEUE.WAITINFO.HTML" Name="Poco.NotificationQueue.WaitInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NotificationQueue.WaitInfo.html" />
                            <File Id="POCO.NOTIFICATIONSTRATEGY.HTML" Name="Poco.NotificationStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NotificationStrategy.html" />
                            <File Id="POCO.NOTIMPLEMENTEDEXCEPTION.HTML" Name="Poco.NotImplementedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NotImplementedException.html" />
                            <File Id="POCO.NULLABLE.HTML" Name="Poco.Nullable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Nullable.html" />
                            <File Id="POCO.NULLCHANNEL.HTML" Name="Poco.NullChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullChannel.html" />
                            <File Id="POCO.NULLINPUTSTREAM.HTML" Name="Poco.NullInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullInputStream.html" />
                            <File Id="POCO.NULLIOS.HTML" Name="Poco.NullIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullIOS.html" />
                            <File Id="POCO.NULLMUTEX.HTML" Name="Poco.NullMutex.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullMutex.html" />
                            <File Id="POCO.NULLOUTPUTSTREAM.HTML" Name="Poco.NullOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullOutputStream.html" />
                            <File Id="POCO.NULLPOINTEREXCEPTION.HTML" Name="Poco.NullPointerException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullPointerException.html" />
                            <File Id="POCO.NULLSTREAMBUF.HTML" Name="Poco.NullStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullStreamBuf.html" />
                            <File Id="POCO.NULLTYPELIST.HTML" Name="Poco.NullTypeList.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullTypeList.html" />
                            <File Id="POCO.NULLVALUEEXCEPTION.HTML" Name="Poco.NullValueException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NullValueException.html" />
                            <File Id="POCO.NUMBERFORMATTER.HTML" Name="Poco.NumberFormatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NumberFormatter.html" />
                            <File Id="POCO.NUMBERPARSER.HTML" Name="Poco.NumberParser.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.NumberParser.html" />
                            <File Id="POCO.OBJECTPOOL.HTML" Name="Poco.ObjectPool.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ObjectPool.html" />
                            <File Id="POCO.OBSERVER.HTML" Name="Poco.Observer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Observer.html" />
                            <File Id="POCO.OPENFILEEXCEPTION.HTML" Name="Poco.OpenFileException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.OpenFileException.html" />
                            <File Id="POCO.OPTIONAL.HTML" Name="Poco.Optional.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Optional.html" />
                            <File Id="POCO.OUTOFMEMORYEXCEPTION.HTML" Name="Poco.OutOfMemoryException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.OutOfMemoryException.html" />
                            <File Id="POCO.OUTPUTLINEENDINGCONVERTER.HTML" Name="Poco.OutputLineEndingConverter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.OutputLineEndingConverter.html" />
                            <File Id="POCO.OUTPUTSTREAMCONVERTER.HTML" Name="Poco.OutputStreamConverter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.OutputStreamConverter.html" />
                            <File Id="POCO.PATH.HTML" Name="Poco.Path.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Path.html" />
                            <File Id="POCO.PATHNOTFOUNDEXCEPTION.HTML" Name="Poco.PathNotFoundException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PathNotFoundException.html" />
                            <File Id="POCO.PATHSYNTAXEXCEPTION.HTML" Name="Poco.PathSyntaxException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PathSyntaxException.html" />
                            <File Id="POCO.PATTERNFORMATTER.HTML" Name="Poco.PatternFormatter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PatternFormatter.html" />
                            <File Id="POCO.PATTERNFORMATTER.PATTERNACTION.HTML" Name="Poco.PatternFormatter.PatternAction.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PatternFormatter.PatternAction.html" />
                            <File Id="POCO.PBKDF2ENGINE.HTML" Name="Poco.PBKDF2Engine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PBKDF2Engine.html" />
                            <File Id="POCO.PIPE.HTML" Name="Poco.Pipe.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Pipe.html" />
                            <File Id="POCO.PIPEINPUTSTREAM.HTML" Name="Poco.PipeInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PipeInputStream.html" />
                            <File Id="POCO.PIPEIOS.HTML" Name="Poco.PipeIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PipeIOS.html" />
                            <File Id="POCO.PIPEOUTPUTSTREAM.HTML" Name="Poco.PipeOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PipeOutputStream.html" />
                            <File Id="POCO.PIPESTREAMBUF.HTML" Name="Poco.PipeStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PipeStreamBuf.html" />
                            <File Id="POCO.PLACEHOLDER.HTML" Name="Poco.Placeholder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Placeholder.html" />
                            <File Id="POCO.POOLABLEOBJECTFACTORY.HTML" Name="Poco.PoolableObjectFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PoolableObjectFactory.html" />
                            <File Id="POCO.POOLOVERFLOWEXCEPTION.HTML" Name="Poco.PoolOverflowException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PoolOverflowException.html" />
                            <File Id="POCO.PRIORITYDELEGATE.HTML" Name="Poco.PriorityDelegate.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PriorityDelegate.html" />
                            <File Id="POCO.PRIORITYEVENT.HTML" Name="Poco.PriorityEvent.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PriorityEvent.html" />
                            <File Id="POCO.PRIORITYEXPIRE.HTML" Name="Poco.PriorityExpire.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PriorityExpire.html" />
                            <File Id="POCO.PRIORITYNOTIFICATIONQUEUE.HTML" Name="Poco.PriorityNotificationQueue.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PriorityNotificationQueue.html" />
                            <File Id="POCO.PRIORITYNOTIFICATIONQUEUE.WAITINFO.HTML" Name="Poco.PriorityNotificationQueue.WaitInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PriorityNotificationQueue.WaitInfo.html" />
                            <File Id="POCO.PRIORITYSTRATEGY.HTML" Name="Poco.PriorityStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PriorityStrategy.html" />
                            <File Id="POCO.PROCESS.HTML" Name="Poco.Process.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Process.html" />
                            <File Id="POCO.PROCESSHANDLE.HTML" Name="Poco.ProcessHandle.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ProcessHandle.html" />
                            <File Id="POCO.PROPERTYNOTSUPPORTEDEXCEPTION.HTML" Name="Poco.PropertyNotSupportedException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PropertyNotSupportedException.html" />
                            <File Id="POCO.PROTOCOLEXCEPTION.HTML" Name="Poco.ProtocolException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ProtocolException.html" />
                            <File Id="POCO.PURGEBYAGESTRATEGY.HTML" Name="Poco.PurgeByAgeStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PurgeByAgeStrategy.html" />
                            <File Id="POCO.PURGEBYCOUNTSTRATEGY.HTML" Name="Poco.PurgeByCountStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PurgeByCountStrategy.html" />
                            <File Id="POCO.PURGESTRATEGY.HTML" Name="Poco.PurgeStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.PurgeStrategy.html" />
                            <File Id="POCO.RANDOM.HTML" Name="Poco.Random.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Random.html" />
                            <File Id="POCO.RANDOMBUF.HTML" Name="Poco.RandomBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RandomBuf.html" />
                            <File Id="POCO.RANDOMINPUTSTREAM.HTML" Name="Poco.RandomInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RandomInputStream.html" />
                            <File Id="POCO.RANDOMIOS.HTML" Name="Poco.RandomIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RandomIOS.html" />
                            <File Id="POCO.RANGEEXCEPTION.HTML" Name="Poco.RangeException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RangeException.html" />
                            <File Id="POCO.READFILEEXCEPTION.HTML" Name="Poco.ReadFileException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ReadFileException.html" />
                            <File Id="POCO.RECURSIVEDIRECTORYITERATOR.HTML" Name="Poco.RecursiveDirectoryIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RecursiveDirectoryIterator.html" />
                            <File Id="POCO.RECURSIVEDIRECTORYITERATORIMPL.HTML" Name="Poco.RecursiveDirectoryIteratorImpl.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RecursiveDirectoryIteratorImpl.html" />
                            <File Id="POCO.REFCOUNTEDOBJECT.HTML" Name="Poco.RefCountedObject.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RefCountedObject.html" />
                            <File Id="POCO.REFERENCECOUNTER.HTML" Name="Poco.ReferenceCounter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ReferenceCounter.html" />
                            <File Id="POCO.REGULAREXPRESSION.HTML" Name="Poco.RegularExpression.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RegularExpression.html" />
                            <File Id="POCO.REGULAREXPRESSION.MATCH.HTML" Name="Poco.RegularExpression.Match.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RegularExpression.Match.html" />
                            <File Id="POCO.REGULAREXPRESSIONEXCEPTION.HTML" Name="Poco.RegularExpressionException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RegularExpressionException.html" />
                            <File Id="POCO.RELEASEARRAYPOLICY.HTML" Name="Poco.ReleaseArrayPolicy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ReleaseArrayPolicy.html" />
                            <File Id="POCO.RELEASEPOLICY.HTML" Name="Poco.ReleasePolicy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ReleasePolicy.html" />
                            <File Id="POCO.ROTATEATTIMESTRATEGY.HTML" Name="Poco.RotateAtTimeStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RotateAtTimeStrategy.html" />
                            <File Id="POCO.ROTATEBYINTERVALSTRATEGY.HTML" Name="Poco.RotateByIntervalStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RotateByIntervalStrategy.html" />
                            <File Id="POCO.ROTATEBYSIZESTRATEGY.HTML" Name="Poco.RotateBySizeStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RotateBySizeStrategy.html" />
                            <File Id="POCO.ROTATESTRATEGY.HTML" Name="Poco.RotateStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RotateStrategy.html" />
                            <File Id="POCO.RUNNABLE.HTML" Name="Poco.Runnable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Runnable.html" />
                            <File Id="POCO.RUNNABLEADAPTER.HTML" Name="Poco.RunnableAdapter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RunnableAdapter.html" />
                            <File Id="POCO.RUNTIMEEXCEPTION.HTML" Name="Poco.RuntimeException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RuntimeException.html" />
                            <File Id="POCO.RWLOCK.HTML" Name="Poco.RWLock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.RWLock.html" />
                            <File Id="POCO.SCOPEDLOCK.HTML" Name="Poco.ScopedLock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ScopedLock.html" />
                            <File Id="POCO.SCOPEDLOCKWITHUNLOCK.HTML" Name="Poco.ScopedLockWithUnlock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ScopedLockWithUnlock.html" />
                            <File Id="POCO.SCOPEDREADRWLOCK.HTML" Name="Poco.ScopedReadRWLock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ScopedReadRWLock.html" />
                            <File Id="POCO.SCOPEDRWLOCK.HTML" Name="Poco.ScopedRWLock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ScopedRWLock.html" />
                            <File Id="POCO.SCOPEDUNLOCK.HTML" Name="Poco.ScopedUnlock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ScopedUnlock.html" />
                            <File Id="POCO.SCOPEDWRITERWLOCK.HTML" Name="Poco.ScopedWriteRWLock.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ScopedWriteRWLock.html" />
                            <File Id="POCO.SEMAPHORE.HTML" Name="Poco.Semaphore.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Semaphore.html" />
                            <File Id="POCO.SHA1ENGINE.CONTEXT.HTML" Name="Poco.SHA1Engine.Context.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SHA1Engine.Context.html" />
                            <File Id="POCO.SHA1ENGINE.HTML" Name="Poco.SHA1Engine.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SHA1Engine.html" />
                            <File Id="POCO.SHAREDLIBRARY.HTML" Name="Poco.SharedLibrary.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SharedLibrary.html" />
                            <File Id="POCO.SHAREDMEMORY.HTML" Name="Poco.SharedMemory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SharedMemory.html" />
                            <File Id="POCO.SHAREDPTR.HTML" Name="Poco.SharedPtr.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SharedPtr.html" />
                            <File Id="POCO.SIBLINGSFIRSTTRAVERSE.HTML" Name="Poco.SiblingsFirstTraverse.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SiblingsFirstTraverse.html" />
                            <File Id="POCO.SIGNALEXCEPTION.HTML" Name="Poco.SignalException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SignalException.html" />
                            <!--
                        <File Id="POCO.SIGNALHANDLER.HTML" Name="Poco.SignalHandler.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SignalHandler.html" />
                        <File Id="POCO.SIGNALHANDLER.JUMPBUFFER.HTML" Name="Poco.SignalHandler.JumpBuffer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SignalHandler.JumpBuffer.html" />
-->
                            <File Id="POCO.SIMPLEFILECHANNEL.HTML" Name="Poco.SimpleFileChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SimpleFileChannel.html" />
                            <File Id="POCO.SIMPLEHASHTABLE.HASHENTRY.HTML" Name="Poco.SimpleHashTable.HashEntry.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SimpleHashTable.HashEntry.html" />
                            <File Id="POCO.SIMPLEHASHTABLE.HTML" Name="Poco.SimpleHashTable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SimpleHashTable.html" />
                            <File Id="POCO.SINGLETONHOLDER.HTML" Name="Poco.SingletonHolder.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SingletonHolder.html" />
                            <File Id="POCO.SORTEDDIRECTORYITERATOR.HTML" Name="Poco.SortedDirectoryIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SortedDirectoryIterator.html" />
                            <File Id="POCO.SPLITTERCHANNEL.HTML" Name="Poco.SplitterChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SplitterChannel.html" />
                            <File Id="POCO.STOPWATCH.HTML" Name="Poco.Stopwatch.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Stopwatch.html" />
                            <File Id="POCO.STRATEGYCOLLECTION.HTML" Name="Poco.StrategyCollection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StrategyCollection.html" />
                            <File Id="POCO.STREAMCHANNEL.HTML" Name="Poco.StreamChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StreamChannel.html" />
                            <File Id="POCO.STREAMCONVERTERBUF.HTML" Name="Poco.StreamConverterBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StreamConverterBuf.html" />
                            <File Id="POCO.STREAMCONVERTERIOS.HTML" Name="Poco.StreamConverterIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StreamConverterIOS.html" />
                            <File Id="POCO.STREAMCOPIER.HTML" Name="Poco.StreamCopier.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StreamCopier.html" />
                            <File Id="POCO.STREAMTOKENIZER.HTML" Name="Poco.StreamTokenizer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StreamTokenizer.html" />
                            <!--
                        <File Id="POCO.STREAMTOKENIZER.TOKENINFO.HTML" Name="Poco.StreamTokenizer.TokenInfo.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StreamTokenizer.TokenInfo.html" />
-->
                            <File Id="POCO.STRINGTOKENIZER.HTML" Name="Poco.StringTokenizer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.StringTokenizer.html" />
                            <File Id="POCO.SYNCHRONIZEDOBJECT.HTML" Name="Poco.SynchronizedObject.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SynchronizedObject.html" />
                            <File Id="POCO.SYNTAXEXCEPTION.HTML" Name="Poco.SyntaxException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SyntaxException.html" />
                            <File Id="POCO.SYSLOGCHANNEL.HTML" Name="Poco.SyslogChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SyslogChannel.html" />
                            <File Id="POCO.SYSTEMEXCEPTION.HTML" Name="Poco.SystemException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.SystemException.html" />
                            <File Id="POCO.TASK.HTML" Name="Poco.Task.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Task.html" />
                            <File Id="POCO.TASKCANCELLEDNOTIFICATION.HTML" Name="Poco.TaskCancelledNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskCancelledNotification.html" />
                            <File Id="POCO.TASKCUSTOMNOTIFICATION.HTML" Name="Poco.TaskCustomNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskCustomNotification.html" />
                            <File Id="POCO.TASKFAILEDNOTIFICATION.HTML" Name="Poco.TaskFailedNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskFailedNotification.html" />
                            <File Id="POCO.TASKFINISHEDNOTIFICATION.HTML" Name="Poco.TaskFinishedNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskFinishedNotification.html" />
                            <File Id="POCO.TASKMANAGER.HTML" Name="Poco.TaskManager.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskManager.html" />
                            <File Id="POCO.TASKNOTIFICATION.HTML" Name="Poco.TaskNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskNotification.html" />
                            <File Id="POCO.TASKPROGRESSNOTIFICATION.HTML" Name="Poco.TaskProgressNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskProgressNotification.html" />
                            <File Id="POCO.TASKSTARTEDNOTIFICATION.HTML" Name="Poco.TaskStartedNotification.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TaskStartedNotification.html" />
                            <File Id="POCO.TEEINPUTSTREAM.HTML" Name="Poco.TeeInputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TeeInputStream.html" />
                            <File Id="POCO.TEEIOS.HTML" Name="Poco.TeeIOS.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TeeIOS.html" />
                            <File Id="POCO.TEEOUTPUTSTREAM.HTML" Name="Poco.TeeOutputStream.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TeeOutputStream.html" />
                            <File Id="POCO.TEESTREAMBUF.HTML" Name="Poco.TeeStreamBuf.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TeeStreamBuf.html" />
                            <File Id="POCO.TEMPORARYFILE.HTML" Name="Poco.TemporaryFile.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TemporaryFile.html" />
                            <File Id="POCO.TEXTBUFFERITERATOR.HTML" Name="Poco.TextBufferIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TextBufferIterator.html" />
                            <File Id="POCO.TEXTCONVERTER.HTML" Name="Poco.TextConverter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TextConverter.html" />
                            <File Id="POCO.TEXTENCODING.HTML" Name="Poco.TextEncoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TextEncoding.html" />
                            <File Id="POCO.TEXTITERATOR.HTML" Name="Poco.TextIterator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TextIterator.html" />
                            <File Id="POCO.THREAD.FUNCTORRUNNABLE.HTML" Name="Poco.Thread.FunctorRunnable.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Thread.FunctorRunnable.html" />
                            <File Id="POCO.THREAD.HTML" Name="Poco.Thread.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Thread.html" />
                            <File Id="POCO.THREADLOCAL.HTML" Name="Poco.ThreadLocal.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ThreadLocal.html" />
                            <File Id="POCO.THREADLOCALSTORAGE.HTML" Name="Poco.ThreadLocalStorage.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ThreadLocalStorage.html" />
                            <File Id="POCO.THREADPOOL.HTML" Name="Poco.ThreadPool.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ThreadPool.html" />
                            <File Id="POCO.THREADTARGET.HTML" Name="Poco.ThreadTarget.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ThreadTarget.html" />
                            <File Id="POCO.TIMEDNOTIFICATIONQUEUE.HTML" Name="Poco.TimedNotificationQueue.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TimedNotificationQueue.html" />
                            <File Id="POCO.TIMEOUTEXCEPTION.HTML" Name="Poco.TimeoutException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TimeoutException.html" />
                            <File Id="POCO.TIMER.HTML" Name="Poco.Timer.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Timer.html" />
                            <File Id="POCO.TIMERCALLBACK.HTML" Name="Poco.TimerCallback.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TimerCallback.html" />
                            <File Id="POCO.TIMESPAN.HTML" Name="Poco.Timespan.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Timespan.html" />
                            <File Id="POCO.TIMESTAMP.HTML" Name="Poco.Timestamp.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Timestamp.html" />
                            <File Id="POCO.TIMEZONE.HTML" Name="Poco.Timezone.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Timezone.html" />
                            <File Id="POCO.TLSABSTRACTSLOT.HTML" Name="Poco.TLSAbstractSlot.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TLSAbstractSlot.html" />
                            <File Id="POCO.TLSSLOT.HTML" Name="Poco.TLSSlot.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TLSSlot.html" />
                            <File Id="POCO.TOKEN.HTML" Name="Poco.Token.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Token.html" />
                            <File Id="POCO.TRAVERSEBASE.HTML" Name="Poco.TraverseBase.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TraverseBase.html" />
                            <File Id="POCO.TUPLE.HTML" Name="Poco.Tuple.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Tuple.html" />
                            <File Id="POCO.TYPELIST.HTML" Name="Poco.TypeList.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TypeList.html" />
                            <File Id="POCO.TYPELISTTYPE.HTML" Name="Poco.TypeListType.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TypeListType.html" />
                            <File Id="POCO.TYPEWRAPPER.HTML" Name="Poco.TypeWrapper.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.TypeWrapper.html" />
                            <File Id="POCO.UNHANDLEDEXCEPTION.HTML" Name="Poco.UnhandledException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UnhandledException.html" />
                            <File Id="POCO.UNICODE.CHARACTERPROPERTIES.HTML" Name="Poco.Unicode.CharacterProperties.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Unicode.CharacterProperties.html" />
                            <File Id="POCO.UNICODE.HTML" Name="Poco.Unicode.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Unicode.html" />
                            <File Id="POCO.UNICODECONVERTER.HTML" Name="Poco.UnicodeConverter.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UnicodeConverter.html" />
                            <File Id="POCO.UNIQUEACCESSEXPIRECACHE.HTML" Name="Poco.UniqueAccessExpireCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UniqueAccessExpireCache.html" />
                            <File Id="POCO.UNIQUEACCESSEXPIRELRUCACHE.HTML" Name="Poco.UniqueAccessExpireLRUCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UniqueAccessExpireLRUCache.html" />
                            <File Id="POCO.UNIQUEACCESSEXPIRESTRATEGY.HTML" Name="Poco.UniqueAccessExpireStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UniqueAccessExpireStrategy.html" />
                            <File Id="POCO.UNIQUEEXPIRECACHE.HTML" Name="Poco.UniqueExpireCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UniqueExpireCache.html" />
                            <File Id="POCO.UNIQUEEXPIRELRUCACHE.HTML" Name="Poco.UniqueExpireLRUCache.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UniqueExpireLRUCache.html" />
                            <File Id="POCO.UNIQUEEXPIRESTRATEGY.HTML" Name="Poco.UniqueExpireStrategy.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UniqueExpireStrategy.html" />
                            <File Id="POCO.UNKNOWNURISCHEMEEXCEPTION.HTML" Name="Poco.UnknownURISchemeException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UnknownURISchemeException.html" />
                            <File Id="POCO.URI.HTML" Name="Poco.URI.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.URI.html" />
                            <File Id="POCO.URIREDIRECTION.HTML" Name="Poco.URIRedirection.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.URIRedirection.html" />
                            <File Id="POCO.URISTREAMFACTORY.HTML" Name="Poco.URIStreamFactory.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.URIStreamFactory.html" />
                            <File Id="POCO.URISTREAMOPENER.HTML" Name="Poco.URIStreamOpener.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.URIStreamOpener.html" />
                            <File Id="POCO.UTF16CHARTRAITS.HTML" Name="Poco.UTF16CharTraits.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UTF16CharTraits.html" />
                            <File Id="POCO.UTF16ENCODING.HTML" Name="Poco.UTF16Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UTF16Encoding.html" />
                            <File Id="POCO.UTF32CHARTRAITS.HTML" Name="Poco.UTF32CharTraits.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UTF32CharTraits.html" />
                            <File Id="POCO.UTF32ENCODING.HTML" Name="Poco.UTF32Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UTF32Encoding.html" />
                            <File Id="POCO.UTF8.HTML" Name="Poco.UTF8.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UTF8.html" />
                            <File Id="POCO.UTF8ENCODING.HTML" Name="Poco.UTF8Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UTF8Encoding.html" />
                            <File Id="POCO.UUID.HTML" Name="Poco.UUID.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UUID.html" />
                            <File Id="POCO.UUIDGENERATOR.HTML" Name="Poco.UUIDGenerator.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.UUIDGenerator.html" />
                            <File Id="POCO.VALIDARGS.HTML" Name="Poco.ValidArgs.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.ValidArgs.html" />
                            <File Id="POCO.VOID.HTML" Name="Poco.Void.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Void.html" />
                            <File Id="POCO.WHITESPACETOKEN.HTML" Name="Poco.WhitespaceToken.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.WhitespaceToken.html" />
                            <File Id="POCO.WINDOWS1250ENCODING.HTML" Name="Poco.Windows1250Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Windows1250Encoding.html" />
                            <File Id="POCO.WINDOWS1251ENCODING.HTML" Name="Poco.Windows1251Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Windows1251Encoding.html" />
                            <File Id="POCO.WINDOWS1252ENCODING.HTML" Name="Poco.Windows1252Encoding.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.Windows1252Encoding.html" />
                            <File Id="POCO.WINDOWSCOLORCONSOLECHANNEL.HTML" Name="Poco.WindowsColorConsoleChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.WindowsColorConsoleChannel.html" />
                            <File Id="POCO.WINDOWSCONSOLECHANNEL.HTML" Name="Poco.WindowsConsoleChannel.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.WindowsConsoleChannel.html" />
                            <File Id="POCO.WRITEFILEEXCEPTION.HTML" Name="Poco.WriteFileException.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\Poco.WriteFileException.html" />
                            <File Id="POCO_STATIC_ASSERT_TEST.HTML" Name="poco_static_assert_test.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\poco_static_assert_test.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="STD.HTML" DiskId="1" Guid="41D4CDE7-E023-4E90-AC4E-62E6085F5FFE">
                            <File Id="STD.HTML" Name="std.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\std.html" />
                        </Component>
                        <Component Win64="$(var.Win64)" Id="ZIPUSERGUIDE.HTML" DiskId="1" Guid="26CECDBF-FE7C-4A72-B897-CB97A27C0C8F">
                            <File Id="ZIPUSERGUIDE.HTML" Name="ZipUserGuide.html" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\ZipUserGuide.html" />
                        </Component>
                        <Directory Id="CSS" Name="css">
                            <Component Win64="$(var.Win64)" Id="CSS" DiskId="1" Guid="591A74F8-4485-432D-9754-3D5401BF1898">
                                <File Id="PRETTIFY.CSS" Name="prettify.css" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\css\prettify.css" />
                                <File Id="STYLES.CSS" Name="styles.css" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\css\styles.css" />
                            </Component>
                        </Directory>
                        <Directory Id="IMAGES" Name="images">
                            <Component Win64="$(var.Win64)" Id="IMAGES" DiskId="1" Guid="B1E37359-1B0B-4763-8379-8239CCC2D96D">
                                <File Id="ARROW.PNG" Name="arrow.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\arrow.png" />
                                <File Id="BACKGROUND.JPG" Name="background.jpg" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\background.jpg" />
                                <File Id="BOTTOM.JPG" Name="bottom.jpg" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\bottom.jpg" />
                                <File Id="BULLET.PNG" Name="bullet.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\bullet.png" />
                                <File Id="HEADER.JPG" Name="header.jpg" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\header.jpg" />
                                <File Id="INLINE.PNG" Name="inline.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\inline.png" />
                                <File Id="POCO.PNG" Name="poco.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\poco.png" />
                                <File Id="PROTECTED.PNG" Name="protected.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\protected.png" />
                                <File Id="STATIC.PNG" Name="static.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\static.png" />
                                <File Id="TITLE.JPG" Name="title.jpg" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\title.jpg" />
                                <File Id="TRIANGLE_DOWN.PNG" Name="triangle_down.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\triangle_down.png" />
                                <File Id="TRIANGLE_LEFT.PNG" Name="triangle_left.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\triangle_left.png" />
                                <File Id="VIRTUAL.PNG" Name="virtual.png" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\images\virtual.png" />
                            </Component>
                        </Directory>
                        <Directory Id="JS" Name="js">
                            <Component Win64="$(var.Win64)" Id="JS" DiskId="1" Guid="54A3CFCD-28A7-4357-93B6-3FFCE09A9ED5">
                                <File Id="COLLAPSIBLELISTS.COMPRESSED.JS" Name="CollapsibleLists.compressed.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\CollapsibleLists.compressed.js" />
                                <File Id="COLLAPSIBLELISTS.JS" Name="CollapsibleLists.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\CollapsibleLists.js" />
                                <File Id="IFRAMERESIZER.CONTENTWINDOW.JS" Name="iframeResizer.contentWindow.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\iframeResizer.contentWindow.js" />
                                <File Id="IFRAMERESIZER.CONTENTWINDOW.MIN.JS" Name="iframeResizer.contentWindow.min.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\iframeResizer.contentWindow.min.js" />
                                <File Id="IFRAMERESIZER.JS" Name="iframeResizer.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\iframeResizer.js" />
                                <File Id="IFRAMERESIZER.MIN.JS" Name="iframeResizer.min.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\iframeResizer.min.js" />
                                <File Id="PRETTIFY.JS" Name="prettify.js" Source="$(var.POCO)\releases\poco-$(var.VERSION)-all-doc\js\prettify.js" />
                            </Component>
                        </Directory>
                    </Directory>
                </Directory>
            </Directory>
            <Component Win64="$(var.Win64)" Id="Registry" Guid="77B25E74-86F3-431A-962A-DAB72E030C60">
                <RegistryValue Root="HKCU" Key="Software\[Manufacturer]\[ProductName]\Current" Type="string" Value="[$(var.PlatformProgramFilesFolder)]\$(var.VERSION)" />
            </Component>
            <!-- Readme file -->
            <Component Win64="$(var.Win64)" Id="ReadmeComponent" Guid="DBF48AAC-11F1-43D3-B659-6389D701F0E0">
                <File Id="ReadmeFile" Name="README.txt" Source="../../README.txt" KeyPath="yes">
                </File>
            </Component>
        </DirectoryRef>
        <!-- Start menu program shortcuts -->
        <DirectoryRef Id="ShortcutsFolder">
            <!-- Shortcuts for readme file and uninstaller -->
            <Component Win64="$(var.Win64)" Id="ShortcutsComponent" Guid="579FC557-4E1B-423F-965E-B7986D6B7AD2">
                <Shortcut Id="ReadmeShortcut" Name="Read me" Description="Readme" Target="[APPLICATIONFOLDER]readme.txt" WorkingDirectory="APPLICATIONFOLDER" />
                <Shortcut Id="UninstallShortcut" Name="Uninstall" Description="Uninstall Poco" Target="[$(var.WindowsSystemFolder)]msiexec.exe" Arguments="/x [ProductCode]" />
                <RemoveFolder Id="ShortcutsRemoveFolder" On="uninstall" />
                <RegistryValue Root="HKCU" Key="Software\$(var.ProductName)" Name="installed" Type="integer" Value="1" KeyPath="yes" />
            </Component>
        </DirectoryRef>
        <!-- Common components -->
        <ComponentGroup Id="CommonComponentGroup">
            <ComponentRef Id="ShortcutsComponent" />
            <ComponentRef Id="ReadmeComponent" />
        </ComponentGroup>
        <Feature Id="Poco" Title="Poco" Level="1" Description="POCO C++ Libraries - Cross-platform C++ libraries with a network/internet focus" TypicalDefault="install" Display="expand">
            <Feature Id="Runtime" Level="1" Title="Runtime" TypicalDefault="install" Description="Runtime" Display="expand">
                <Feature Id="Includes" Level="1" Description="Includes" Title="Includes" InstallDefault="followParent">
                    <ComponentRef Id="Poco.inc" />
                    <ComponentRef Id="Poco.Dynamic.inc" />
                    <ComponentRef Id="Poco.Crypto.inc" />
                    <ComponentRef Id="Poco.Data.inc" />
                    <ComponentRef Id="Poco.Data.MySQL.inc" />
                    <ComponentRef Id="Poco.Data.ODBC.inc" />
                    <ComponentRef Id="Poco.Data.SQLite.inc" />
                    <ComponentRef Id="Poco.Data.PostgreSQL.inc" />
                    <ComponentRef Id="Poco.JSON.inc" />
                    <ComponentRef Id="Poco.MongoDB.inc" />
                    <ComponentRef Id="Poco.Net.inc" />
                    <ComponentRef Id="Poco.NetSSL_OpenSSL.inc" />
                    <ComponentRef Id="Poco.PDF.inc" />
                    <ComponentRef Id="Poco.SevenZip.inc" />
                    <ComponentRef Id="Poco.UTIL.inc" />
                    <ComponentRef Id="Poco.DOM.inc" />
                    <ComponentRef Id="Poco.SAX.inc" />
                    <ComponentRef Id="Poco.XML.inc" />
                    <ComponentRef Id="Poco.ZIP.inc" />
                </Feature>
                <Feature Id="SharedRelease" Level="1" Description="As shared libraries" TypicalDefault="install" Title="SharedRelease">
                    <?if $(var.Platform) = x64 ?>
                    <ComponentRef Id="PocoCppUnit64.shared.release" />
                    <ComponentRef Id="Crypto64.shared.release" />
                    <ComponentRef Id="Data64.shared.release" />
                    <ComponentRef Id="DataMySQL64.shared.release" />
                    <ComponentRef Id="DataODBC64.shared.release" />
                    <ComponentRef Id="DataSQLite64.shared.release" />
                    <ComponentRef Id="DataPostgreSQL64.shared.release" />
                    <ComponentRef Id="Foundation64.shared.release" />
                    <ComponentRef Id="JSON64.shared.release" />
                    <ComponentRef Id="MongoDB64.shared.release" />
                    <ComponentRef Id="Net64.shared.release" />
                    <ComponentRef Id="NetSSL64.shared.release" />
                    <ComponentRef Id="NetSSLWin64.shared.release" />
                    <ComponentRef Id="PDF64.shared.release" />
                    <ComponentRef Id="UTIL64.shared.release" />
                    <ComponentRef Id="XML64.shared.release" />
                    <ComponentRef Id="ZIP64.shared.release" />
                    <ComponentRef Id="PocoCppUnit64.import.release" />
                    <ComponentRef Id="Crypto64.import.release" />
                    <ComponentRef Id="Data64.import.release" />
                    <ComponentRef Id="DataMySQL64.import.release" />
                    <ComponentRef Id="DataODBC64.import.release" />
                    <ComponentRef Id="DataSQLite64.import.release" />
                    <ComponentRef Id="DataPostgreSQL64.import.release" />
                    <ComponentRef Id="Foundation64.import.release" />
                    <ComponentRef Id="JSON64.import.release" />
                    <ComponentRef Id="MongoDB64.import.release" />
                    <ComponentRef Id="Net64.import.release" />
                    <ComponentRef Id="NetSSL64.import.release" />
                    <ComponentRef Id="NetSSLWin64.import.release" />
                    <ComponentRef Id="PDF64.import.release" />
                    <ComponentRef Id="UTIL64.import.release" />
                    <ComponentRef Id="XML64.import.release" />
                    <ComponentRef Id="ZIP64.import.release" />
                    <?else ?>
                    <ComponentRef Id="PocoCppUnit.shared.release" />
                    <ComponentRef Id="Crypto.shared.release" />
                    <ComponentRef Id="Data.shared.release" />
                    <ComponentRef Id="DataMySQL.shared.release" />
                    <ComponentRef Id="DataODBC.shared.release" />
                    <ComponentRef Id="DataSQLite.shared.release" />
                    <ComponentRef Id="DataPostgreSQL.shared.release" />
                    <ComponentRef Id="Foundation.shared.release" />
                    <ComponentRef Id="JSON.shared.release" />
                    <ComponentRef Id="MongoDB.shared.release" />
                    <ComponentRef Id="Net.shared.release" />
                    <ComponentRef Id="NetSSL.shared.release" />
                    <ComponentRef Id="NetSSLWin.shared.release" />
                    <ComponentRef Id="PDF.shared.release" />
                    <ComponentRef Id="Util.shared.release" />
                    <ComponentRef Id="XML.shared.release" />
                    <ComponentRef Id="Zip.shared.release" />
                    <ComponentRef Id="PocoCppUnit.import.release" />
                    <ComponentRef Id="Crypto.import.release" />
                    <ComponentRef Id="Data.import.release" />
                    <ComponentRef Id="DataMySQL.import.release" />
                    <ComponentRef Id="DataODBC.import.release" />
                    <ComponentRef Id="DataSQLite.import.release" />
                    <ComponentRef Id="DataPostgreSQL.import.release" />
                    <ComponentRef Id="Foundation.import.release" />
                    <ComponentRef Id="JSON.import.release" />
                    <ComponentRef Id="MongoDB.import.release" />
                    <ComponentRef Id="Net.import.release" />
                    <ComponentRef Id="NetSSL.import.release" />
                    <ComponentRef Id="NetSSLWin.import.release" />
                    <ComponentRef Id="PDF.import.release" />
                    <ComponentRef Id="Util.import.release" />
                    <ComponentRef Id="XML.import.release" />
                    <ComponentRef Id="Zip.import.release" />
                    <?endif ?>
                </Feature>
                <Feature Id="StaticRelease" Level="1" Description="As static libraries" TypicalDefault="advertise" Title="StaticRelease">
                    <?if $(var.Platform) = x64 ?>
                    <ComponentRef Id="PocoCppUnit64.static.release" />
                    <ComponentRef Id="Crypto64.static.release" />
                    <ComponentRef Id="Data64.static.release" />
                    <ComponentRef Id="DataMySQL64.static.release" />
                    <ComponentRef Id="DataODBC64.static.release" />
                    <ComponentRef Id="DataSQLite64.static.release" />
                    <ComponentRef Id="DataPostgreSQL64.static.release" />
                    <ComponentRef Id="Foundation64.static.release" />
                    <ComponentRef Id="JSON64.static.release" />
                    <ComponentRef Id="MongoDB64.static.release" />
                    <ComponentRef Id="Net64.static.release" />
                    <ComponentRef Id="NetSSL64.static.release" />
                    <ComponentRef Id="NetSSLWin64.static.release" />
                    <ComponentRef Id="PDF64.static.release" />
                    <ComponentRef Id="Util64.static.release" />
                    <ComponentRef Id="XML64.static.release" />
                    <ComponentRef Id="Zip64.static.release" />
                    <?else ?>
                    <ComponentRef Id="PocoCppUnit.static.release" />
                    <ComponentRef Id="Crypto.static.release" />
                    <ComponentRef Id="Data.static.release" />
                    <ComponentRef Id="DataMySQL.static.release" />
                    <ComponentRef Id="DataODBC.static.release" />
                    <ComponentRef Id="DataSQLite.static.release" />
                    <ComponentRef Id="DataPostgreSQL.static.release" />
                    <ComponentRef Id="Foundation.static.release" />
                    <ComponentRef Id="JSON.static.release" />
                    <ComponentRef Id="MongoDB.static.release" />
                    <ComponentRef Id="Net.static.release" />
                    <ComponentRef Id="NetSSL.static.release" />
                    <ComponentRef Id="NetSSLWin.static.release" />
                    <ComponentRef Id="PDF.static.release" />
                    <ComponentRef Id="Util.static.release" />
                    <ComponentRef Id="XML.static.release" />
                    <ComponentRef Id="Zip.static.release" />
                    <?endif ?>
                </Feature>
                <ComponentRef Id="Registry" />
            </Feature>
            <Feature Id="Development" Level="1" Title="Development" Description="Development files" TypicalDefault="advertise" Display="expand">
                <Feature Id="SharedDebug" Description="As shared libraries" Level="1" Title="SharedDebug" InstallDefault="followParent">
                    <?if $(var.Platform) = x64 ?>
                    <ComponentRef Id="PocoCppUnit64.shared.debug" />
                    <ComponentRef Id="Crypto64.shared.debug" />
                    <ComponentRef Id="Data64.shared.debug" />
                    <ComponentRef Id="DataMySQL64.shared.debug" />
                    <ComponentRef Id="DataODBC64.shared.debug" />
                    <ComponentRef Id="DataSQLite64.shared.debug" />
                    <ComponentRef Id="DataPostgreSQL64.shared.debug" />
                    <ComponentRef Id="Foundation64.shared.debug" />
                    <ComponentRef Id="JSON64.shared.debug" />
                    <ComponentRef Id="MongoDB64.shared.debug" />
                    <ComponentRef Id="Net64.shared.debug" />
                    <ComponentRef Id="NetSSL64.shared.debug" />
                    <ComponentRef Id="NetSSLWin64.shared.debug" />
                    <ComponentRef Id="PDF64.shared.debug" />
                    <ComponentRef Id="UTIL64.shared.debug" />
                    <ComponentRef Id="XML64.shared.debug" />
                    <ComponentRef Id="ZIP64.shared.debug" />
                    <ComponentRef Id="PocoCppUnit64.import.debug" />
                    <ComponentRef Id="Crypto64.import.debug" />
                    <ComponentRef Id="Data64.import.debug" />
                    <ComponentRef Id="DataMySQL64.import.debug" />
                    <ComponentRef Id="DataODBC64.import.debug" />
                    <ComponentRef Id="DataSQLite64.import.debug" />
                    <ComponentRef Id="DataPostgreSQL64.import.debug" />
                    <ComponentRef Id="Foundation64.import.debug" />
                    <ComponentRef Id="JSON64.import.debug" />
                    <ComponentRef Id="MongoDB64.import.debug" />
                    <ComponentRef Id="Net64.import.debug" />
                    <ComponentRef Id="NetSSL64.import.debug" />
                    <ComponentRef Id="NetSSLWin64.import.debug" />
                    <ComponentRef Id="PDF64.import.debug" />
                    <ComponentRef Id="UTIL64.import.debug" />
                    <ComponentRef Id="XML64.import.debug" />
                    <ComponentRef Id="ZIP64.import.debug" />
                    <?else ?>
                    <ComponentRef Id="PocoCppUnit.shared.debug" />
                    <ComponentRef Id="Crypto.shared.debug" />
                    <ComponentRef Id="Data.shared.debug" />
                    <ComponentRef Id="DataMySQL.shared.debug" />
                    <ComponentRef Id="DataODBC.shared.debug" />
                    <ComponentRef Id="DataSQLite.shared.debug" />
                    <ComponentRef Id="DataPostgreSQL.shared.debug" />
                    <ComponentRef Id="Foundation.shared.debug" />
                    <ComponentRef Id="JSON.shared.debug" />
                    <ComponentRef Id="MongoDB.shared.debug" />
                    <ComponentRef Id="Net.shared.debug" />
                    <ComponentRef Id="NetSSL.shared.debug" />
                    <ComponentRef Id="NetSSLWin.shared.debug" />
                    <ComponentRef Id="PDF.shared.debug" />
                    <ComponentRef Id="Util.shared.debug" />
                    <ComponentRef Id="XML.shared.debug" />
                    <ComponentRef Id="Zip.shared.debug" />
                    <ComponentRef Id="PocoCppUnit.import.debug" />
                    <ComponentRef Id="Crypto.import.debug" />
                    <ComponentRef Id="Data.import.debug" />
                    <ComponentRef Id="DataMySQL.import.debug" />
                    <ComponentRef Id="DataODBC.import.debug" />
                    <ComponentRef Id="DataSQLite.import.debug" />
                    <ComponentRef Id="DataPostgreSQL.import.debug" />
                    <ComponentRef Id="Foundation.import.debug" />
                    <ComponentRef Id="JSON.import.debug" />
                    <ComponentRef Id="MongoDB.import.debug" />
                    <ComponentRef Id="Net.import.debug" />
                    <ComponentRef Id="NetSSL.import.debug" />
                    <ComponentRef Id="NetSSLWin.import.debug" />
                    <ComponentRef Id="PDF.import.debug" />
                    <ComponentRef Id="Util.import.debug" />
                    <ComponentRef Id="XML.import.debug" />
                    <ComponentRef Id="Zip.import.debug" />
                    <?endif ?>
                </Feature>
                <Feature Id="StaticDebug" Description="As static libraries" Title="StaticDebug" Level="1" TypicalDefault="advertise">
                    <?if $(var.Platform) = x64 ?>
                    <ComponentRef Id="PocoCppUnit64.static.debug" />
                    <ComponentRef Id="Crypto64.static.debug" />
                    <ComponentRef Id="Data64.static.debug" />
                    <ComponentRef Id="DataMySQL64.static.debug" />
                    <ComponentRef Id="DataODBC64.static.debug" />
                    <ComponentRef Id="DataSQLite64.static.debug" />
                    <ComponentRef Id="DataPostgreSQL64.static.debug" />
                    <ComponentRef Id="Foundation64.static.debug" />
                    <ComponentRef Id="JSON64.static.debug" />
                    <ComponentRef Id="MongoDB64.static.debug" />
                    <ComponentRef Id="Net64.static.debug" />
                    <ComponentRef Id="NetSSL64.static.debug" />
                    <ComponentRef Id="NetSSLWin64.static.debug" />
                    <ComponentRef Id="PDF64.static.debug" />
                    <ComponentRef Id="Util64.static.debug" />
                    <ComponentRef Id="XML64.static.debug" />
                    <ComponentRef Id="Zip64.static.debug" />
                    <?else ?>
                    <ComponentRef Id="PocoCppUnit.static.debug" />
                    <ComponentRef Id="Crypto.static.debug" />
                    <ComponentRef Id="Data.static.debug" />
                    <ComponentRef Id="DataMySQL.static.debug" />
                    <ComponentRef Id="DataODBC.static.debug" />
                    <ComponentRef Id="DataSQLite.static.debug" />
                    <ComponentRef Id="DataPostgreSQL.static.debug" />
                    <ComponentRef Id="Foundation.static.debug" />
                    <ComponentRef Id="JSON.static.debug" />
                    <ComponentRef Id="MongoDB.static.debug" />
                    <ComponentRef Id="Net.static.debug" />
                    <ComponentRef Id="NetSSL.static.debug" />
                    <ComponentRef Id="NetSSLWin.static.debug" />
                    <ComponentRef Id="PDF.static.debug" />
                    <ComponentRef Id="Util.static.debug" />
                    <ComponentRef Id="XML.static.debug" />
                    <ComponentRef Id="Zip.static.debug" />
                    <?endif ?>
                </Feature>
            </Feature>
            <Feature Id="Demos" Description="Demos" Level="0" TypicalDefault="advertise">
                <Feature Id="Foundation" Level="1" InstallDefault="followParent" Description="Foundation" Title="Foundation">
                    <Feature Id="ActiveMethod" Level="1" InstallDefault="followParent" Description="ActiveMethod" Title="ActiveMethod">
                        <ComponentRef Id="ACTIVEMETHOD.CPP" />
                        <?if $(var.Platform) = x86 ?>
                        <?if $(var.VisualStudio) = VSCE2009 ?>
                        <ComponentRef Id="ACTIVEMETHOD_CE_VS90.VCPROJ" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2010 ?>
                        <ComponentRef Id="ACTIVEMETHOD_VS100.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_VS100.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2012 ?>
                        <ComponentRef Id="ACTIVEMETHOD_VS110.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_VS110.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2013 ?>
                        <ComponentRef Id="ACTIVEMETHOD_VS120.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_VS120.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2015 ?>
                        <ComponentRef Id="ACTIVEMETHOD_VS140.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_VS140.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2009 ?>
                        <ComponentRef Id="ACTIVEMETHOD_VS90.VCPROJ" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VSCE2012 ?>
                        <ComponentRef Id="ACTIVEMETHOD_WEC2013_VS110.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_WEC2013_VS110.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VSCE2013 ?>
                        <ComponentRef Id="ACTIVEMETHOD_WEC2013_VS120.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_WEC2013_VS120.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?else ?>
                        <?if $(var.VisualStudio) = VS2010 ?>
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS100.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS100.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2012 ?>
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS110.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS110.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2013 ?>
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS120.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS120.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2015 ?>
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS140.VCXPROJ" />
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS140.VCXPROJ.FILTERS" />
                        <?endif ?>
                        <?if $(var.VisualStudio) = VS2009 ?>
                        <ComponentRef Id="ACTIVEMETHOD_X64_VS90.VCPROJ" />
                        <?endif ?>
                        <?endif ?>
                    </Feature>
                </Feature>
            </Feature>
            <Feature Id="Documentation" Level="1" Title="Documentation" Description="Documentation" TypicalDefault="advertise">
                <ComponentRef Id="_00100_GUIDEDTOUR.HTML" />
                <ComponentRef Id="_00200_DATAUSERMANUAL.HTML" />
                <ComponentRef Id="_00200_GETTINGSTARTED.HTML" />
                <ComponentRef Id="_00300_DATADEVELOPERMANUAL.HTML" />
                <ComponentRef Id="_80100_HOWTOGETHELP.HTML" />
                <ComponentRef Id="_90100_ACKNOWLEDGEMENTS.HTML" />
                <ComponentRef Id="_99100_DATARELEASENOTES.HTML" />
                <ComponentRef Id="_99100_RELEASENOTES.HTML" />
                <ComponentRef Id="_99150_GMAKEBUILDNOTES.HTML" />
                <ComponentRef Id="_99150_WINDOWSPLATFORMNOTES.HTML" />
                <ComponentRef Id="_99200_WINCEPLATFORMNOTES.HTML" />
                <ComponentRef Id="_99250_VXWORKSPLATFORMNOTES.HTML" />
                <ComponentRef Id="_99300_ANDROIDPLATFORMNOTES.HTML" />
                <ComponentRef Id="INDEX.HTML" />
                <ComponentRef Id="NAVIGATION.HTML" />
                <ComponentRef Id="PACKAGE_CRYPTO.HTML" />
                <ComponentRef Id="PACKAGE_DATA.HTML" />
                <ComponentRef Id="PACKAGE_FOUNDATION.HTML" />
                <ComponentRef Id="PACKAGE_JSON.HTML" />
                <ComponentRef Id="PACKAGE_MONGODB.HTML" />
                <ComponentRef Id="PACKAGE_NET.HTML" />
                <ComponentRef Id="PACKAGE_NETSSL_OPENSSL.HTML" />
                <ComponentRef Id="PACKAGE_UTIL.HTML" />
                <ComponentRef Id="PACKAGE_XML.HTML" />
                <ComponentRef Id="PACKAGE_ZIP.HTML" />
                <ComponentRef Id="PAGECOMPILERUSERGUIDE.HTML" />
                <ComponentRef Id="POCO.HTML" />
                <ComponentRef Id="POCO.CRYPTO.HTML" />
                <ComponentRef Id="POCO.DATA.HTML" />
                <ComponentRef Id="POCO.DATA.MYSQL.HTML" />
                <ComponentRef Id="POCO.DATA.ODBC.HTML" />
                <ComponentRef Id="POCO.DATA.SQLITE.HTML" />
                <ComponentRef Id="POCO.DYNAMIC.HTML" />
                <ComponentRef Id="POCO.JSON.HTML" />
                <ComponentRef Id="POCO.MONGODB.HTML" />
                <ComponentRef Id="POCO.NET.HTML" />
                <ComponentRef Id="POCO.UTIL.HTML" />
                <ComponentRef Id="POCO.XML.HTML" />
                <ComponentRef Id="POCO.ZIP.HTML" />
                <ComponentRef Id="STD.HTML" />
                <ComponentRef Id="ZIPUSERGUIDE.HTML" />
                <ComponentRef Id="CSS" />
                <ComponentRef Id="IMAGES" />
                <ComponentRef Id="JS" />
            </Feature>
            <ComponentRef Id="ReadmeComponent" />
            <ComponentRef Id="ShortcutsComponent" />
        </Feature>
        <!-- Setup wizard sequence -->
        <UI Id="UISequence">
            <UIRef Id="WixUI_Advanced" />
        </UI>
        <Icon Id="icon.ico" SourceFile="Poco 64x64.ico" />
    </Product>
</Wix>